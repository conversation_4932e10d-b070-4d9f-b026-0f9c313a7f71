import { useState, useEffect } from 'react'
import { ArrowUpRight, ArrowDownLeft, Trophy, CreditCard, Gift, RefreshCw } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
// TODO: platformService not yet extracted - using old import temporarily
import { platformService } from '../../services/supabase'

interface Transaction {
  id: string
  type: string
  amount: number
  status: string
  description: string
  match_id?: string
  match_game?: string
  match_mode?: string
  created_at: string
}

export default function TransactionHistory() {
  const { user } = useAuth()
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'income' | 'expense'>('all')

  useEffect(() => {
    if (user?.id) {
      loadTransactions()
    }
  }, [user])

  const loadTransactions = async () => {
    if (!user?.id) return

    setLoading(true)
    try {
      const result = await platformService.getUserTransactionHistory(user.id)
      if (result.data) {
        setTransactions(result.data)
      }
    } catch (error) {
      console.error('Error loading transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'match_win':
      case 'tournament_win':
        return <Trophy className="w-5 h-5 text-green-500" />
      case 'match_entry':
      case 'tournament_entry':
        return <ArrowDownLeft className="w-5 h-5 text-red-500" />
      case 'topup':
      case 'purchase':
        return <CreditCard className="w-5 h-5 text-blue-500" />
      case 'refund':
        return <RefreshCw className="w-5 h-5 text-amber-500" />
      case 'bonus':
      case 'reward':
        return <Gift className="w-5 h-5 text-purple-500" />
      default:
        return <ArrowUpRight className="w-5 h-5 text-gray-500" />
    }
  }

  const getTransactionColor = (type: string) => {
    const incomeTypes = ['match_win', 'tournament_win', 'refund', 'bonus', 'reward', 'topup']
    return incomeTypes.includes(type) ? 'text-green-600' : 'text-red-600'
  }

  const getTransactionSign = (type: string) => {
    const incomeTypes = ['match_win', 'tournament_win', 'refund', 'bonus', 'reward', 'topup']
    return incomeTypes.includes(type) ? '+' : '-'
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const filteredTransactions = transactions.filter(transaction => {
    if (filter === 'all') return true
    const incomeTypes = ['match_win', 'tournament_win', 'refund', 'bonus', 'reward', 'topup']
    if (filter === 'income') return incomeTypes.includes(transaction.type)
    if (filter === 'expense') return !incomeTypes.includes(transaction.type)
    return true
  })

  const totalIncome = transactions
    .filter(t => ['match_win', 'tournament_win', 'refund', 'bonus', 'reward', 'topup'].includes(t.type))
    .reduce((sum, t) => sum + t.amount, 0)

  const totalExpense = transactions
    .filter(t => !['match_win', 'tournament_win', 'refund', 'bonus', 'reward', 'topup'].includes(t.type))
    .reduce((sum, t) => sum + t.amount, 0)

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Transaction History</h2>
          <button
            onClick={loadTransactions}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <RefreshCw className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Summary Stats */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-sm text-gray-600">Total Income</p>
            <p className="text-lg font-semibold text-green-600">+{totalIncome}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Total Expense</p>
            <p className="text-lg font-semibold text-red-600">-{totalExpense}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Net</p>
            <p className={`text-lg font-semibold ${totalIncome - totalExpense >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {totalIncome - totalExpense >= 0 ? '+' : ''}{totalIncome - totalExpense}
            </p>
          </div>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="px-6 py-3 border-b border-gray-200">
        <div className="flex space-x-1">
          {[
            { key: 'all', label: 'All' },
            { key: 'income', label: 'Income' },
            { key: 'expense', label: 'Expense' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key as any)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                filter === tab.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Transactions List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredTransactions.length === 0 ? (
          <div className="text-center py-12">
            <CreditCard className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Transactions</h3>
            <p className="text-gray-600">Your transaction history will appear here.</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="px-6 py-4 hover:bg-gray-50">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {transaction.description}
                      </p>
                      {transaction.match_game && (
                        <p className="text-xs text-gray-500">
                          {transaction.match_game} - {transaction.match_mode}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        {formatDate(transaction.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`text-sm font-semibold ${getTransactionColor(transaction.type)}`}>
                      {getTransactionSign(transaction.type)}{transaction.amount} 💎
                    </p>
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      transaction.status === 'completed' ? 'bg-green-100 text-green-800' :
                      transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {transaction.status}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
