// Mobile performance utilities and optimizations

/**
 * Detect if user is on a mobile device
 */
export function isMobileDevice(): boolean {
  if (typeof window === 'undefined') return false
  
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  ) || window.innerWidth < 768
}

/**
 * Detect if user is on a slow connection
 */
export function isSlowConnection(): boolean {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) {
    return false
  }
  
  const connection = (navigator as any).connection
  return connection?.effectiveType === 'slow-2g' || 
         connection?.effectiveType === '2g' ||
         connection?.saveData === true
}

/**
 * Get device pixel ratio for image optimization
 */
export function getDevicePixelRatio(): number {
  return typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1
}

/**
 * Optimize image URL based on device capabilities
 */
export function optimizeImageUrl(
  baseUrl: string, 
  width: number, 
  height: number,
  quality: number = 80
): string {
  if (!baseUrl) return baseUrl
  
  const dpr = getDevicePixelRatio()
  const isSlow = isSlowConnection()
  
  // Adjust quality based on connection speed
  const adjustedQuality = isSlow ? Math.min(quality, 60) : quality
  
  // Adjust dimensions based on device pixel ratio
  const adjustedWidth = Math.round(width * (isSlow ? 1 : dpr))
  const adjustedHeight = Math.round(height * (isSlow ? 1 : dpr))
  
  // If using a CDN service like Cloudinary, ImageKit, etc.
  // This is a placeholder - replace with your actual image optimization service
  if (baseUrl.includes('cloudinary.com')) {
    return `${baseUrl}?w=${adjustedWidth}&h=${adjustedHeight}&q=${adjustedQuality}&f=auto`
  }
  
  // For local images, return as-is (could be enhanced with a local optimization service)
  return baseUrl
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Throttle function for scroll/resize events
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * Preload critical images
 */
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.onload = () => resolve()
    img.onerror = reject
    img.src = src
  })
}

/**
 * Preload multiple images
 */
export async function preloadImages(urls: string[]): Promise<void> {
  const promises = urls.map(url => preloadImage(url))
  await Promise.allSettled(promises)
}

/**
 * Lazy load images with intersection observer
 */
export function createImageObserver(
  callback: (entry: IntersectionObserverEntry) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  }
  
  return new IntersectionObserver((entries) => {
    entries.forEach(callback)
  }, defaultOptions)
}

/**
 * Optimize scroll performance
 */
export function optimizeScroll(element: HTMLElement): () => void {
  let ticking = false
  
  const handleScroll = () => {
    if (!ticking) {
      requestAnimationFrame(() => {
        // Scroll handling logic here
        ticking = false
      })
      ticking = true
    }
  }
  
  element.addEventListener('scroll', handleScroll, { passive: true })
  
  return () => {
    element.removeEventListener('scroll', handleScroll)
  }
}

/**
 * Memory management utilities
 */
export class MemoryManager {
  private static observers: IntersectionObserver[] = []
  private static timeouts: NodeJS.Timeout[] = []
  private static intervals: NodeJS.Timeout[] = []
  
  static addObserver(observer: IntersectionObserver) {
    this.observers.push(observer)
  }
  
  static addTimeout(timeout: NodeJS.Timeout) {
    this.timeouts.push(timeout)
  }
  
  static addInterval(interval: NodeJS.Timeout) {
    this.intervals.push(interval)
  }
  
  static cleanup() {
    // Disconnect all observers
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    
    // Clear all timeouts
    this.timeouts.forEach(timeout => clearTimeout(timeout))
    this.timeouts = []
    
    // Clear all intervals
    this.intervals.forEach(interval => clearInterval(interval))
    this.intervals = []
  }
}

/**
 * Performance monitoring
 */
export class PerformanceMonitor {
  private static metrics: { [key: string]: number } = {}
  
  static startTiming(label: string) {
    this.metrics[`${label}_start`] = performance.now()
  }
  
  static endTiming(label: string): number {
    const startTime = this.metrics[`${label}_start`]
    if (!startTime) return 0
    
    const duration = performance.now() - startTime
    this.metrics[label] = duration
    delete this.metrics[`${label}_start`]
    
    return duration
  }
  
  static getMetrics(): { [key: string]: number } {
    return { ...this.metrics }
  }
  
  static logMetrics() {
    console.table(this.getMetrics())
  }
}

/**
 * Touch gesture utilities
 */
export interface TouchGesture {
  startX: number
  startY: number
  endX: number
  endY: number
  deltaX: number
  deltaY: number
  duration: number
  direction: 'left' | 'right' | 'up' | 'down' | 'none'
}

export function detectSwipeGesture(
  startTouch: Touch,
  endTouch: Touch,
  startTime: number,
  endTime: number,
  threshold: number = 50
): TouchGesture {
  const deltaX = endTouch.clientX - startTouch.clientX
  const deltaY = endTouch.clientY - startTouch.clientY
  const duration = endTime - startTime
  
  let direction: TouchGesture['direction'] = 'none'
  
  if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > threshold) {
    direction = deltaX > 0 ? 'right' : 'left'
  } else if (Math.abs(deltaY) > threshold) {
    direction = deltaY > 0 ? 'down' : 'up'
  }
  
  return {
    startX: startTouch.clientX,
    startY: startTouch.clientY,
    endX: endTouch.clientX,
    endY: endTouch.clientY,
    deltaX,
    deltaY,
    duration,
    direction
  }
}

/**
 * Viewport utilities
 */
export function getViewportSize(): { width: number; height: number } {
  return {
    width: window.innerWidth || document.documentElement.clientWidth,
    height: window.innerHeight || document.documentElement.clientHeight
  }
}

export function isInViewport(element: HTMLElement): boolean {
  const rect = element.getBoundingClientRect()
  const viewport = getViewportSize()
  
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= viewport.height &&
    rect.right <= viewport.width
  )
}
