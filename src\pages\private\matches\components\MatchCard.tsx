import { memo, useMemo, useCallback } from 'react'
import { Users, Trophy, Clock, Shield, Gamepad2, Star, Diamond, User, Play, Eye, X, Heart, Zap, Crown, Target } from 'lucide-react'
import { useAuth } from '../../../../contexts/AuthContext'
import { TouchButton } from '../../../../components/mobile/TouchOptimized'
import { isMobileDevice } from '../../../../utils/mobilePerformance'
// Removed duplicate countdown imports - using existing timer system

interface MatchCardProps {
  match: {
    id: string
    game: string
    host: string
    betAmount: number
    maxPlayers: number
    currentPlayers: number
    timeCreated: string
    status: 'open' | 'full' | 'ongoing' | 'completed'
    isVerified?: boolean
    gameMode?: string
    rules?: string
    hostId?: string
    host_id?: string
    entryFee?: number
    title?: string
    mode?: string
    region?: string
    pot_amount?: number
    diamond_pot?: number
    timeLeft?: string
  }
  onJoin?: (match: any) => void
  onView?: (matchId: string) => void
  onCancel?: (matchId: string) => void
  onFavorite?: (matchId: string) => void
  onShare?: (matchId: string) => void
  showJoinButton?: boolean
  isFavorited?: boolean
  isMobile?: boolean
}

const MatchCard = memo(function MatchCard({
  match,
  onJoin,
  onView,
  onCancel,
  onFavorite,
  // onShare, // TODO: Confirm usage
  showJoinButton = true,
  isFavorited = false
  // isMobile = false // TODO: Confirm usage
}: MatchCardProps) {
  const { user } = useAuth()

  // 🚀 MOBILE OPTIMIZATION: Detect mobile device for optimized interactions
  const isMobile = useMemo(() => isMobileDevice(), [])

  // 🚀 MEMOIZED: Check ownership with multiple possible field names
  const isOwnMatch = useMemo(() =>
    user?.id === match.hostId || user?.id === match.host_id,
    [user?.id, match.hostId, match.host_id]
  )

  // Debug: Always log for now to see what's happening
  console.log(`🔍 Match ${match.id?.slice(0, 8)} ownership:`, {
    userId: user?.id?.slice(0, 8),
    hostId: match.hostId?.slice(0, 8),
    host_id: match.host_id?.slice(0, 8),
    isOwnMatch,
    title: match.title || match.game,
    hasOnCancel: !!onCancel
  })

  // Moved debug log after canJoin is defined

  // Enhanced status styling
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'open':
        return {
          bg: 'bg-gradient-to-r from-emerald-500 to-green-500',
          text: 'text-white',
          icon: <Play className="w-3 h-3" />,
          label: 'Open'
        }
      case 'full':
        return {
          bg: 'bg-gradient-to-r from-amber-500 to-orange-500',
          text: 'text-white',
          icon: <Users className="w-3 h-3" />,
          label: 'Full'
        }
      case 'ongoing':
        return {
          bg: 'bg-gradient-to-r from-red-500 to-rose-500',
          text: 'text-white',
          icon: <Zap className="w-3 h-3" />,
          label: 'Live'
        }
      case 'completed':
        return {
          bg: 'bg-gradient-to-r from-slate-500 to-gray-500',
          text: 'text-white',
          icon: <Trophy className="w-3 h-3" />,
          label: 'Done'
        }
      default:
        return {
          bg: 'bg-gradient-to-r from-gray-400 to-slate-400',
          text: 'text-white',
          icon: <Clock className="w-3 h-3" />,
          label: 'Unknown'
        }
    }
  }

  // 🚀 MEMOIZED: Enhanced game icons and colors
  const getGameConfig = useMemo(() => (game: string) => {
    switch (game?.toUpperCase()) {
      case 'MLBB':
        return {
          icon: <Target className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-700',
          name: 'Mobile Legends'
        }
      case 'COD':
        return {
          icon: <Zap className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-orange-500 via-red-500 to-red-600',
          name: 'Call of Duty'
        }
      case 'VALORANT':
        return {
          icon: <Shield className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-red-500 via-rose-500 to-pink-600',
          name: 'Valorant'
        }
      case 'PUBG':
        return {
          icon: <Trophy className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-amber-500 via-orange-500 to-orange-600',
          name: 'PUBG Mobile'
        }
      case 'FORTNITE':
        return {
          icon: <Star className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-purple-500 via-violet-500 to-pink-600',
          name: 'Fortnite'
        }
      case 'APEX':
        return {
          icon: <Target className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-600',
          name: 'Apex Legends'
        }
      default:
        return {
          icon: <Gamepad2 className="w-4 h-4" />,
          bg: 'bg-gradient-to-br from-slate-500 via-gray-500 to-gray-600',
          name: game || 'Unknown Game'
        }
    }
  }, [])

  // 🚀 MEMOIZED: Format time ago calculation
  const formatTimeAgo = useCallback((timeString: string) => {
    const time = new Date(timeString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`

    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`

    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }, [])

  // 🚀 MEMOIZED: Generate match title if not provided
  const matchTitle = useMemo(() => {
    if (match.title) return match.title
    const gameConfig = getGameConfig(match.game)
    const mode = match.gameMode || match.mode || '1v1'
    return `${gameConfig.name} ${mode} Match`
  }, [match.title, match.game, match.gameMode, match.mode, getGameConfig])

  // 🚀 MEMOIZED: Calculate derived values
  const canJoin = useMemo(() =>
    match.status === 'open' && match.currentPlayers < match.maxPlayers && !isOwnMatch,
    [match.status, match.currentPlayers, match.maxPlayers, isOwnMatch]
  )

  const entryFee = useMemo(() =>
    match.entryFee || match.betAmount || match.diamond_pot || 0,
    [match.entryFee, match.betAmount, match.diamond_pot]
  )

  const totalPot = useMemo(() =>
    match.pot_amount || (entryFee * match.maxPlayers),
    [match.pot_amount, entryFee, match.maxPlayers]
  )

  // Debug button rendering logic
  if (isOwnMatch) {
    console.log(`🔴 Should show CANCEL button for match ${match.id?.slice(0, 8)}`)
  } else {
    console.log(`🔵 Should show JOIN button for match ${match.id?.slice(0, 8)} - canJoin: ${canJoin}, showJoinButton: ${showJoinButton}`)
  }

  const statusConfig = getStatusConfig(match.status)
  const gameConfig = getGameConfig(match.game)

  return (
    <div className={`group relative bg-white rounded-md shadow-sm border border-gray-200 overflow-hidden transition-all duration-200 hover:shadow-md hover:border-gray-300 ${isOwnMatch ? 'ring-1 ring-blue-300 bg-blue-50/30' : ''}`}>
      {/* Compact Game Header */}
      <div className={`${gameConfig.bg} px-2 py-1.5 text-white relative overflow-hidden`}>
        <div className="absolute inset-0 bg-black/5"></div>
        <div className="relative z-10 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="p-1 bg-white/20 rounded backdrop-blur-sm">
              <div className="w-3 h-3 flex items-center justify-center">
                {gameConfig.icon}
              </div>
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm leading-tight truncate">{matchTitle}</h3>
              <p className="text-white/75 text-xs">{match.gameMode || match.mode || '1v1'}</p>
            </div>
          </div>

          {/* Simple Status Badge */}
          <div className={`${statusConfig.bg} px-2 py-0.5 rounded-full flex items-center space-x-1 text-xs font-medium shadow-sm`}>
            <div className="w-2.5 h-2.5 flex items-center justify-center">
              {statusConfig.icon}
            </div>
            <span className="text-xs">{statusConfig.label}</span>
          </div>
        </div>

        {/* Simple Crown for own matches */}
        {isOwnMatch && (
          <div className="absolute top-1 right-1">
            <div className="bg-yellow-400 text-yellow-900 p-0.5 rounded-full shadow-sm">
              <Crown className="w-3 h-3" />
            </div>
          </div>
        )}
      </div>

      {/* Simple Match Details */}
      <div className="p-3 space-y-3">
        {/* Prize & Entry Row */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-1.5">
            <div className="p-1 bg-yellow-100 rounded">
              <Diamond className="w-3 h-3 text-yellow-600" />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Prize</p>
              <p className="font-semibold text-yellow-600 text-sm">💎 {totalPot.toLocaleString()}</p>
            </div>
          </div>

          <div className="text-right">
            <p className="text-gray-500 text-xs">Entry</p>
            <p className="font-semibold text-gray-900 text-sm">💎 {entryFee.toLocaleString()}</p>
          </div>
        </div>

        {/* Players & Host Row */}
        <div className="flex items-center justify-between text-xs">
          <div className="flex items-center space-x-1.5">
            <div className="p-1 bg-blue-100 rounded">
              <Users className="w-3 h-3 text-blue-600" />
            </div>
            <div>
              <p className="text-gray-500 text-xs">Players</p>
              <p className="font-semibold text-gray-900 text-sm">{match.currentPlayers}/{match.maxPlayers}</p>
            </div>
          </div>

          <div className="flex items-center space-x-1.5 min-w-0 flex-1 justify-end">
            <div className="p-1 bg-purple-100 rounded">
              <User className="w-3 h-3 text-purple-600" />
            </div>
            <div className="text-right min-w-0">
              <p className="text-gray-500 text-xs">Host</p>
              <p className="font-medium text-gray-900 text-sm truncate max-w-20">{match.host}</p>
            </div>
          </div>
        </div>

        {/* Time - Show countdown if available, otherwise show time ago */}
        <div className="flex items-center justify-center space-x-1 text-gray-500 text-xs">
          <Clock className="w-3 h-3" />
          <span>
            {match.timeLeft ? `Starts in ${match.timeLeft}` : formatTimeAgo(match.timeCreated)}
          </span>
        </div>

        {/* 🚀 MOBILE OPTIMIZED: Touch-friendly Action Buttons */}
        <div className="flex items-center justify-between pt-2 border-t border-gray-100">
          {/* Quick Actions */}
          <div className="flex items-center space-x-1">
            {/* Favorite Button - Mobile optimized */}
            {isMobile ? (
              <TouchButton
                onClick={() => {
                  onFavorite?.(match.id)
                }}
                variant={isFavorited ? 'danger' : 'ghost'}
                size="sm"
                className="!p-2 !min-h-[36px] !min-w-[36px]"
              >
                <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
              </TouchButton>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onFavorite?.(match.id)
                }}
                className={`p-1.5 rounded transition-colors ${
                  isFavorited
                    ? 'bg-red-100 text-red-600 hover:bg-red-200'
                    : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                }`}
              >
                <Heart className={`w-3 h-3 ${isFavorited ? 'fill-current' : ''}`} />
              </button>
            )}

            {/* View Button - Mobile optimized */}
            {isMobile ? (
              <TouchButton
                onClick={() => {
                  onView?.(match.id)
                }}
                variant="ghost"
                size="sm"
                className="!p-2 !min-h-[36px] !min-w-[36px]"
              >
                <Eye className="w-4 h-4" />
              </TouchButton>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onView?.(match.id)
                }}
                className="p-1.5 bg-gray-100 text-gray-600 rounded hover:bg-gray-200 transition-colors"
              >
                <Eye className="w-3 h-3" />
              </button>
            )}
          </div>

          {/* 🚀 MOBILE OPTIMIZED: Main Action Button */}
          {isOwnMatch ? (
            isMobile ? (
              <TouchButton
                onClick={() => {
                  console.log('🔴 Cancel button clicked for match:', match.id)
                  onCancel?.(match.id)
                }}
                variant="danger"
                size="sm"
                className="flex items-center space-x-1"
              >
                <X className="w-4 h-4" />
                <span>Cancel</span>
              </TouchButton>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  console.log('🔴 Cancel button clicked for match:', match.id)
                  onCancel?.(match.id)
                }}
                className="px-3 py-1.5 bg-red-500 text-white rounded hover:bg-red-600 transition-colors flex items-center space-x-1 text-xs font-medium"
                style={{ backgroundColor: 'red', color: 'white' }} // Force visible styling
              >
                <X className="w-3 h-3" />
                <span>Cancel</span>
              </button>
            )
          ) : canJoin && showJoinButton ? (
            isMobile ? (
              <TouchButton
                onClick={() => {
                  onJoin?.(match)
                }}
                variant="primary"
                size="sm"
                className="flex items-center space-x-1 bg-green-600 hover:bg-green-700"
              >
                <Play className="w-4 h-4" />
                <span>Join</span>
              </TouchButton>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onJoin?.(match)
                }}
                className="px-3 py-1.5 bg-green-500 text-white rounded hover:bg-green-600 transition-colors flex items-center space-x-1 text-xs font-medium"
              >
                <Play className="w-3 h-3" />
                <span>Join</span>
              </button>
            )
          ) : (
            isMobile ? (
              <TouchButton
                onClick={() => {
                  onView?.(match.id)
                }}
                variant="secondary"
                size="sm"
                className="flex items-center space-x-1"
              >
                <Eye className="w-4 h-4" />
                <span>View</span>
              </TouchButton>
            ) : (
              <button
                onClick={(e) => {
                  e.stopPropagation()
                  onView?.(match.id)
                }}
                className="px-3 py-1.5 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors flex items-center space-x-1 text-xs font-medium"
              >
                <Eye className="w-3 h-3" />
                <span>View</span>
              </button>
            )
          )}
        </div>
      </div>

      {/* 🚀 MOBILE OPTIMIZED: Hover Effect Overlay - Disabled on mobile for better performance */}
      {!isMobile && (
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/0 to-purple-500/0 group-hover:from-blue-500/5 group-hover:to-purple-500/5 transition-all duration-300 pointer-events-none"></div>
      )}
    </div>
  )
})

export default MatchCard
