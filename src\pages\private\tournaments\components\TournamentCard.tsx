import {
  Users, Star, Gamepad2, Gem, Eye, Play, MapPin, Crown, Zap, Target, Shield, Sparkles,
  Timer, CheckCircle, Info
} from 'lucide-react'

interface TournamentCardProps {
  tournament: {
    id: string
    title: string
    game: string
    prizePool: number
    maxParticipants: number
    currentParticipants: number
    startDate: string
    registrationDeadline: string
    status: 'upcoming' | 'registration' | 'ongoing' | 'completed'
    difficulty: 'beginner' | 'intermediate' | 'advanced' | 'pro'
    format: string
    description?: string
    isJoined?: boolean
    isFeatured?: boolean
    isLive?: boolean
    region?: string
    organizer?: string
    entryFee?: number
    estimatedDuration?: string
    requirements?: {
      minLevel: number
      minRank: string
      minWinRate: number
    }
    rewards?: {
      first: number
      second: number
      third: number
      participation: number
    }
    schedule?: Array<{
      round: string
      date: string
      status: 'completed' | 'ongoing' | 'upcoming'
    }>
    streamUrl?: string
    tags?: string[]
    popularity?: number
    viewerCount?: number
  }
  onJoin?: (tournamentId: string) => void
  onLeave?: (tournamentId: string) => void
  onView?: (tournamentId: string) => void
  onWatchLive?: (streamUrl: string) => void
  viewMode?: 'grid' | 'list'
}

export default function TournamentCard({
  tournament,
  onJoin,
  onLeave,
  onView,
  onWatchLive,
  viewMode = 'grid'
}: TournamentCardProps) {

  // Enhanced utility functions
  const formatTimeUntil = (dateString: string) => {
    const now = new Date()
    const target = new Date(dateString)
    const diff = target.getTime() - now.getTime()

    if (diff < 0) return 'Started'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) return `${days}d ${hours}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'registration':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'ongoing':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'advanced':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'pro':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return <Shield className="w-3 h-3" />
      case 'intermediate': return <Target className="w-3 h-3" />
      case 'advanced': return <Zap className="w-3 h-3" />
      case 'pro': return <Crown className="w-3 h-3" />
      default: return <Star className="w-3 h-3" />
    }
  }

  const getGameIcon = (game: string) => {
    switch (game) {
      case 'Mobile Legends': return '🏆'
      case 'Valorant': return '🎯'
      case 'Call of Duty': return '💥'
      case 'Dota 2': return '⚔️'
      case 'CS:GO': return '🔫'
      default: return '🎮'
    }
  }

  const canJoin = tournament.status === 'registration' &&
                  tournament.currentParticipants < tournament.maxParticipants &&
                  !tournament.isJoined

  const canLeave = tournament.isJoined && tournament.status === 'registration'
  const isLive = tournament.status === 'ongoing'
  const isFull = tournament.currentParticipants >= tournament.maxParticipants

  // List view for mobile-first responsive design
  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 p-4">
        <div className="flex items-center space-x-4">
          {/* Tournament Icon & Status */}
          <div className="relative flex-shrink-0">
            <div className={`w-16 h-16 rounded-xl flex items-center justify-center text-2xl ${
              isLive ? 'bg-gradient-to-br from-red-500 to-pink-600' :
              tournament.isFeatured ? 'bg-gradient-to-br from-blue-500 to-purple-600' :
              'bg-gradient-to-br from-gray-400 to-gray-600'
            }`}>
              {getGameIcon(tournament.game)}
            </div>
            {isLive && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              </div>
            )}
            {tournament.isFeatured && !isLive && (
              <div className="absolute -top-1 -right-1">
                <Sparkles className="w-4 h-4 text-yellow-500" />
              </div>
            )}
          </div>

          {/* Tournament Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h3 className="font-bold text-lg text-gray-900 truncate">{tournament.title}</h3>
              {tournament.tags && tournament.tags.includes('LIVE') && (
                <span className="bg-red-100 text-red-700 text-xs px-2 py-1 rounded-full font-medium">
                  LIVE
                </span>
              )}
            </div>
            <div className="flex items-center space-x-3 text-sm text-gray-600 mb-2">
              <span className="flex items-center space-x-1">
                <Gamepad2 className="w-4 h-4" />
                <span>{tournament.game}</span>
              </span>
              <span>•</span>
              <span>{tournament.format}</span>
              {tournament.region && (
                <>
                  <span>•</span>
                  <span className="flex items-center space-x-1">
                    <MapPin className="w-3 h-3" />
                    <span>{tournament.region}</span>
                  </span>
                </>
              )}
            </div>
            <div className="flex items-center space-x-4 text-sm">
              <span className="flex items-center space-x-1 text-purple-600 font-medium">
                <Gem className="w-4 h-4" />
                <span>{tournament.prizePool.toLocaleString()} 💎</span>
              </span>
              <span className="flex items-center space-x-1 text-blue-600">
                <Users className="w-4 h-4" />
                <span>{tournament.currentParticipants}/{tournament.maxParticipants}</span>
              </span>
              {isLive && tournament.viewerCount && (
                <span className="flex items-center space-x-1 text-red-600">
                  <Eye className="w-4 h-4" />
                  <span>{tournament.viewerCount.toLocaleString()}</span>
                </span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-2">
            {isLive && onWatchLive && tournament.streamUrl && (
              <button
                onClick={() => onWatchLive(tournament.streamUrl!)}
                className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
              >
                <Play className="w-4 h-4" />
                <span>Watch</span>
              </button>
            )}
            {canJoin && (
              <button
                onClick={() => onJoin?.(tournament.id)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Join
              </button>
            )}
            {canLeave && (
              <button
                onClick={() => onLeave?.(tournament.id)}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
              >
                Leave
              </button>
            )}
            <button
              onClick={() => onView?.(tournament.id)}
              className="p-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Eye className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Grid view - Enhanced card design
  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-lg transition-all duration-300 overflow-hidden group ${
      isLive ? 'ring-2 ring-red-500 ring-opacity-50' :
      tournament.isFeatured ? 'ring-2 ring-blue-500 ring-opacity-30' : ''
    }`}>
      {/* Enhanced Header */}
      <div className="relative">
        {/* Background Gradient */}
        <div className={`h-24 ${
          isLive ? 'bg-gradient-to-r from-red-500 to-pink-600' :
          tournament.isFeatured ? 'bg-gradient-to-r from-blue-500 to-purple-600' :
          'bg-gradient-to-r from-gray-400 to-gray-600'
        }`}>
          {/* Live Indicator */}
          {isLive && (
            <div className="absolute top-3 left-3 flex items-center space-x-2 bg-white bg-opacity-20 rounded-full px-3 py-1">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span className="text-white text-sm font-medium">LIVE</span>
            </div>
          )}

          {/* Featured Badge */}
          {tournament.isFeatured && !isLive && (
            <div className="absolute top-3 left-3 flex items-center space-x-1 bg-white bg-opacity-20 rounded-full px-3 py-1">
              <Sparkles className="w-3 h-3 text-white" />
              <span className="text-white text-sm font-medium">Featured</span>
            </div>
          )}

          {/* Viewer Count for Live */}
          {isLive && tournament.viewerCount && (
            <div className="absolute top-3 right-3 flex items-center space-x-1 bg-white bg-opacity-20 rounded-full px-3 py-1">
              <Eye className="w-3 h-3 text-white" />
              <span className="text-white text-sm font-medium">{tournament.viewerCount.toLocaleString()}</span>
            </div>
          )}

          {/* Status & Difficulty Badges */}
          {!isLive && (
            <div className="absolute top-3 right-3 flex flex-col space-y-1">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(tournament.status)}`}>
                {tournament.status.charAt(0).toUpperCase() + tournament.status.slice(1)}
              </span>
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getDifficultyColor(tournament.difficulty)}`}>
                {getDifficultyIcon(tournament.difficulty)}
                <span className="ml-1">{tournament.difficulty.charAt(0).toUpperCase() + tournament.difficulty.slice(1)}</span>
              </span>
            </div>
          )}
        </div>

        {/* Tournament Icon */}
        <div className="absolute -bottom-6 left-6">
          <div className="w-12 h-12 bg-white rounded-xl shadow-lg flex items-center justify-center text-xl border-2 border-gray-100">
            {getGameIcon(tournament.game)}
          </div>
        </div>
      </div>

      {/* Enhanced Content */}
      <div className="pt-8 p-6">
        {/* Tournament Title & Info */}
        <div className="mb-4">
          <h3 className="font-bold text-xl text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
            {tournament.title}
          </h3>
          <div className="flex items-center space-x-3 text-sm text-gray-600 mb-3">
            <span className="flex items-center space-x-1">
              <Gamepad2 className="w-4 h-4" />
              <span>{tournament.game}</span>
            </span>
            <span>•</span>
            <span>{tournament.format}</span>
            {tournament.region && (
              <>
                <span>•</span>
                <span className="flex items-center space-x-1">
                  <MapPin className="w-3 h-3" />
                  <span>{tournament.region}</span>
                </span>
              </>
            )}
          </div>
          {tournament.description && (
            <p className="text-sm text-gray-600 line-clamp-2">{tournament.description}</p>
          )}
        </div>

        {/* Prize Pool Highlight */}
        <div className={`rounded-xl p-4 mb-4 ${
          isLive ? 'bg-gradient-to-r from-red-50 to-pink-50 border border-red-200' :
          'bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200'
        }`}>
          <div className="flex items-center justify-center space-x-2">
            <Gem className={`w-5 h-5 ${isLive ? 'text-red-600' : 'text-yellow-600'}`} />
            <span className="text-lg font-bold text-gray-900">
              {tournament.prizePool.toLocaleString()} 💎
            </span>
            <span className={`text-sm ${isLive ? 'text-red-600' : 'text-yellow-600'}`}>Prize Pool</span>
          </div>
          {tournament.rewards && (
            <div className="flex justify-center space-x-4 mt-2 text-xs text-gray-600">
              <span>🥇 {tournament.rewards.first.toLocaleString()}</span>
              <span>🥈 {tournament.rewards.second.toLocaleString()}</span>
              <span>🥉 {tournament.rewards.third.toLocaleString()}</span>
            </div>
          )}
        </div>

        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Users className="w-4 h-4 text-blue-600" />
              <span className="text-xs font-medium text-gray-700">Participants</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-lg font-bold text-gray-900">
                {tournament.currentParticipants}/{tournament.maxParticipants}
              </span>
              {isFull && (
                <span className="bg-red-100 text-red-700 text-xs px-2 py-1 rounded-full">Full</span>
              )}
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center space-x-2 mb-1">
              <Timer className="w-4 h-4 text-green-600" />
              <span className="text-xs font-medium text-gray-700">
                {isLive ? 'Live Now' : 'Starts In'}
              </span>
            </div>
            <div className="text-lg font-bold text-gray-900">
              {isLive ? '🔴 LIVE' : formatTimeUntil(tournament.startDate)}
            </div>
          </div>
        </div>

        {/* Entry Fee & Requirements */}
        {tournament.entryFee && tournament.entryFee > 0 && (
          <div className="flex items-center justify-between bg-blue-50 rounded-lg p-3 mb-4">
            <div className="flex items-center space-x-2">
              <Gem className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Entry Fee</span>
            </div>
            <span className="text-sm font-bold text-blue-700">{tournament.entryFee} 💎</span>
          </div>
        )}

        {/* Requirements */}
        {tournament.requirements && (
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <div className="flex items-center space-x-2 mb-2">
              <Shield className="w-4 h-4 text-gray-600" />
              <span className="text-xs font-medium text-gray-700">Requirements</span>
            </div>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="text-center">
                <div className="font-medium text-gray-900">Lv.{tournament.requirements.minLevel}+</div>
                <div className="text-gray-600">Level</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-900">{tournament.requirements.minRank}</div>
                <div className="text-gray-600">Rank</div>
              </div>
              <div className="text-center">
                <div className="font-medium text-gray-900">{tournament.requirements.minWinRate}%</div>
                <div className="text-gray-600">Win Rate</div>
              </div>
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-xs text-gray-600 mb-2">
            <span>Registration Progress</span>
            <span>{Math.round((tournament.currentParticipants / tournament.maxParticipants) * 100)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-500 ${
                isLive ? 'bg-gradient-to-r from-red-500 to-pink-600' :
                isFull ? 'bg-gradient-to-r from-red-500 to-red-600' :
                'bg-gradient-to-r from-blue-500 to-purple-600'
              }`}
              style={{ width: `${Math.min((tournament.currentParticipants / tournament.maxParticipants) * 100, 100)}%` }}
            ></div>
          </div>
        </div>

        {/* Tags */}
        {tournament.tags && tournament.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {tournament.tags.slice(0, 3).map((tag, index) => (
              <span key={index} className="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">
                {tag}
              </span>
            ))}
            {tournament.tags.length > 3 && (
              <span className="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded-full">
                +{tournament.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Enhanced Actions */}
        <div className="space-y-2">
          {/* Primary Action */}
          {isLive && onWatchLive && tournament.streamUrl ? (
            <button
              onClick={() => onWatchLive(tournament.streamUrl!)}
              className="w-full flex items-center justify-center space-x-2 bg-gradient-to-r from-red-500 to-pink-600 text-white py-3 px-4 rounded-lg font-medium hover:from-red-600 hover:to-pink-700 transition-all duration-200 transform hover:scale-105"
            >
              <Play className="w-4 h-4" />
              <span>Watch Live</span>
            </button>
          ) : canJoin ? (
            <button
              onClick={() => onJoin?.(tournament.id)}
              disabled={isFull}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                isFull
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 transform hover:scale-105'
              }`}
            >
              {isFull ? 'Tournament Full' : 'Join Tournament'}
            </button>
          ) : canLeave ? (
            <button
              onClick={() => onLeave?.(tournament.id)}
              className="w-full bg-red-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-red-700 transition-colors"
            >
              Leave Tournament
            </button>
          ) : tournament.isJoined ? (
            <div className="w-full bg-green-100 text-green-800 py-3 px-4 rounded-lg font-medium text-center flex items-center justify-center space-x-2">
              <CheckCircle className="w-4 h-4" />
              <span>Joined</span>
            </div>
          ) : null}

          {/* Secondary Actions */}
          <div className="flex space-x-2">
            <button
              onClick={() => onView?.(tournament.id)}
              className="flex-1 flex items-center justify-center space-x-2 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium"
            >
              <Info className="w-4 h-4" />
              <span>Details</span>
            </button>
            {tournament.organizer && (
              <button className="flex-1 flex items-center justify-center space-x-2 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                <Users className="w-4 h-4" />
                <span>Organizer</span>
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
