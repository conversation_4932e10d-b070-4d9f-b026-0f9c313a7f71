import { supabase, Transaction } from './supabaseClient'

export const transactionService = {
  // Create transaction
  async createTransaction(transactionData: Omit<Transaction, 'id' | 'created_at'>) {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .insert([transactionData])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user transactions
  async getUserTransactions(userId: string) {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

export const walletService = {
  // Get user's current diamond balance
  async getBalance(userId: string) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (error) throw error
      return { data: data.diamond_balance, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update user's diamond balance
  async updateBalance(userId: string, newBalance: number) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({ 
          diamond_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select('diamond_balance')
        .single()

      if (error) throw error
      return { data: data.diamond_balance, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Add diamonds to user's balance
  async addDiamonds(userId: string, amount: number, description: string) {
    try {
      // Get current balance
      const { data: currentBalance, error: balanceError } = await this.getBalance(userId)
      if (balanceError) throw balanceError

      const newBalance = currentBalance + amount

      // Update balance
      const { error: updateError } = await this.updateBalance(userId, newBalance)
      if (updateError) throw updateError

      // Create transaction record
      const { error: transactionError } = await transactionService.createTransaction({
        user_id: userId,
        type: 'deposit',
        amount: amount,
        balance_after: newBalance,
        description: description,
        status: 'completed'
      })

      if (transactionError) throw transactionError

      return { data: newBalance, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Deduct diamonds from user's balance
  async deductDiamonds(userId: string, amount: number, description: string) {
    try {
      // Get current balance
      const { data: currentBalance, error: balanceError } = await this.getBalance(userId)
      if (balanceError) throw balanceError

      if (currentBalance < amount) {
        throw new Error('Insufficient balance')
      }

      const newBalance = currentBalance - amount

      // Update balance
      const { error: updateError } = await this.updateBalance(userId, newBalance)
      if (updateError) throw updateError

      // Create transaction record
      const { error: transactionError } = await transactionService.createTransaction({
        user_id: userId,
        type: 'withdrawal',
        amount: -amount,
        balance_after: newBalance,
        description: description,
        status: 'completed'
      })

      if (transactionError) throw transactionError

      return { data: newBalance, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Process diamond purchase
  async processPurchase(userId: string, packageData: {
    diamonds: number
    amount: number
    paymentMethod: string
    paymentReference: string
  }) {
    try {
      const { diamonds, paymentMethod, paymentReference } = packageData

      // Add diamonds to balance
      const { data: newBalance, error: addError } = await this.addDiamonds(
        userId, 
        diamonds, 
        `Diamond purchase - ${diamonds} diamonds via ${paymentMethod}`
      )

      if (addError) throw addError

      // Create purchase transaction record
      const { error: transactionError } = await transactionService.createTransaction({
        user_id: userId,
        type: 'deposit',
        amount: diamonds,
        balance_after: newBalance,
        description: `Diamond purchase - ${diamonds} diamonds`,
        status: 'completed',
        payment_method: paymentMethod,
        payment_reference: paymentReference
      })

      if (transactionError) throw transactionError

      return { data: { newBalance, diamonds }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Deduct balance (alias for deductDiamonds for compatibility)
  async deductBalance(userId: string, amount: number) {
    return this.deductDiamonds(userId, amount, 'Balance deduction')
  },

  // Get withdrawal info for user
  async getWithdrawalInfo(userId: string) {
    try {
      const { data: user, error } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (error) throw error

      const availableBalance = user.diamond_balance || 0
      const platformFeePercentage = 5 // 5% platform fee
      const minimumWithdrawal = 1000 // Minimum 1000 diamonds

      // Calculate conversion rates (100 diamonds = 0.95 USD after 5% fee)
      const conversionRate = 0.95 / 100 // USD per diamond after fee

      return {
        data: {
          availableBalance,
          minimumWithdrawal,
          platformFeePercentage,
          conversionRate,
          estimatedUSD: (amount: number) => {
            const afterFee = amount * (1 - platformFeePercentage / 100)
            return afterFee * conversionRate
          },
          estimatedPHP: (amount: number) => {
            const afterFee = amount * (1 - platformFeePercentage / 100)
            return afterFee * conversionRate * 55 // USD to PHP
          }
        },
        error: null
      }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create withdrawal request
  async createWithdrawal(userId: string, amount: number, method: string, walletAddress: string, cryptoCurrency?: string) {
    try {
      // Check minimum withdrawal
      if (amount < 1000) {
        throw new Error('Minimum withdrawal is 1000 diamonds')
      }

      // Check if user has enough balance
      const { data: currentBalance, error: balanceError } = await this.getBalance(userId)
      if (balanceError) throw balanceError

      if (currentBalance < amount) {
        throw new Error('Insufficient balance for withdrawal')
      }

      // Calculate fees and amounts
      const platformFeePercentage = 5
      const platformFeeDiamonds = Math.floor(amount * (platformFeePercentage / 100))
      const netAmountDiamonds = amount - platformFeeDiamonds
      const amountUSD = (netAmountDiamonds * 0.95) / 100 // 100 diamonds = 0.95 USD after fee
      const amountPHP = amountUSD * 55 // USD to PHP conversion

      // Create withdrawal request
      const { data, error } = await supabase
        .from('withdrawal_requests')
        .insert([{
          user_id: userId,
          amount_diamonds: amount,
          amount_usd: amountUSD,
          amount_php: amountPHP,
          platform_fee_diamonds: platformFeeDiamonds,
          net_amount_diamonds: netAmountDiamonds,
          method: method,
          wallet_address: walletAddress,
          crypto_currency: cryptoCurrency,
          status: 'pending'
        }])
        .select()
        .single()

      if (error) throw error

      // Deduct from user's balance (hold in escrow)
      await this.deductDiamonds(userId, amount, `Withdrawal request - ${method} (${walletAddress})`)

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
