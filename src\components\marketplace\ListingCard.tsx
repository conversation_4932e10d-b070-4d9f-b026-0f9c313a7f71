// 🛒 LISTING CARD COMPONENT
// Individual marketplace listing card

import React from 'react'
import { Link } from 'react-router-dom'
import {
  Star,
  Eye,
  Heart,
  TrendingUp,
  Clock,
  Crown,
  Shield
} from 'lucide-react'
import { MarketplaceListing } from '../../services/marketplace'
import { MARKETPLACE_GAMES } from '../../constants/marketplace' // TODO: Confirm ITEM_TYPES usage

interface ListingCardProps {
  listing: MarketplaceListing
  viewMode: 'grid' | 'list'
}

const ListingCard: React.FC<ListingCardProps> = ({ listing, viewMode }) => {
  const game = MARKETPLACE_GAMES.find(g => g.id === listing.game || g.name === listing.game)
  // const itemType = ITEM_TYPES.find(t => t.id === listing.item_type) // TODO: Confirm usage

  const GameIcon = game?.icon || MARKETPLACE_GAMES[0].icon
  // const ItemIcon = itemType?.icon || MARKETPLACE_GAMES[0].icon // TODO: Confirm usage

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat().format(price)
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  const getDiscountPercentage = () => {
    if (!listing.original_price_diamonds || listing.original_price_diamonds <= listing.price_diamonds) {
      return null
    }
    return Math.round(((listing.original_price_diamonds - listing.price_diamonds) / listing.original_price_diamonds) * 100)
  }

  if (viewMode === 'list') {
    return (
      <Link to={`/marketplace/listing/${listing.id}`}>
        <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 p-6 border border-gray-200 hover:border-blue-300">
          <div className="flex items-center space-x-6">
            {/* Image */}
            <div className="relative flex-shrink-0">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <GameIcon className="w-8 h-8 text-white" />
              </div>
              {listing.is_featured && (
                <div className="absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                  Featured
                </div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1 truncate">{listing.title}</h3>
                  <p className="text-sm text-gray-600 mb-2 truncate">{listing.description}</p>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                    <div className="flex items-center space-x-1">
                      <GameIcon className="w-4 h-4" />
                      <span>{listing.game}</span>
                    </div>
                    {listing.rank && (
                      <div className="flex items-center space-x-1">
                        <Crown className="w-4 h-4" />
                        <span>{listing.rank}</span>
                      </div>
                    )}
                    {listing.account_level && (
                      <div className="flex items-center space-x-1">
                        <TrendingUp className="w-4 h-4" />
                        <span>Level {listing.account_level}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Eye className="w-4 h-4" />
                      <span>{listing.views_count}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Heart className="w-4 h-4" />
                      <span>{listing.favorites_count}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-sm text-gray-500">
                      <Clock className="w-4 h-4" />
                      <span>{formatTimeAgo(listing.created_at)}</span>
                    </div>
                  </div>
                </div>

                {/* Price */}
                <div className="text-right">
                  <div className="flex items-center space-x-2 mb-1">
                    {getDiscountPercentage() && (
                      <span className="text-sm text-gray-500 line-through">
                        {formatPrice(listing.original_price_diamonds!)} 💎
                      </span>
                    )}
                    <span className="text-xl font-bold text-blue-600">
                      {formatPrice(listing.price_diamonds)} 💎
                    </span>
                  </div>
                  {getDiscountPercentage() && (
                    <span className="text-sm text-green-600 font-medium">
                      -{getDiscountPercentage()}% OFF
                    </span>
                  )}
                  
                  <div className="flex items-center space-x-2 mt-2">
                    {listing.seller?.is_verified && (
                      <div className="flex items-center space-x-1 text-xs text-green-600">
                        <Shield className="w-3 h-3" />
                        <span>Verified</span>
                      </div>
                    )}
                    {listing.is_verified && (
                      <div className="flex items-center space-x-1 text-xs text-blue-600">
                        <Star className="w-3 h-3" />
                        <span>Verified Listing</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    )
  }

  // Grid view
  return (
    <Link to={`/marketplace/listing/${listing.id}`}>
      <div className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 overflow-hidden border border-gray-200 hover:border-blue-300 group">
        {/* Image */}
        <div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <GameIcon className="w-12 h-12 text-white group-hover:scale-110 transition-transform" />
          
          {/* Badges */}
          <div className="absolute top-3 left-3 flex flex-col space-y-1">
            {listing.is_featured && (
              <span className="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                Featured
              </span>
            )}
            {listing.is_verified && (
              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                Verified
              </span>
            )}
          </div>

          {/* Discount Badge */}
          {getDiscountPercentage() && (
            <div className="absolute top-3 right-3 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium">
              -{getDiscountPercentage()}% OFF
            </div>
          )}

          {/* Stats Overlay */}
          <div className="absolute bottom-3 right-3 flex items-center space-x-2 text-white text-xs">
            <div className="flex items-center space-x-1">
              <Eye className="w-3 h-3" />
              <span>{listing.views_count}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Heart className="w-3 h-3" />
              <span>{listing.favorites_count}</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <h3 className="font-semibold text-gray-900 mb-2 truncate group-hover:text-blue-600 transition-colors">
            {listing.title}
          </h3>
          
          <div className="flex items-center space-x-2 text-sm text-gray-600 mb-3">
            <GameIcon className="w-4 h-4" />
            <span>{listing.game}</span>
            {listing.rank && (
              <>
                <span>•</span>
                <span>{listing.rank}</span>
              </>
            )}
          </div>

          {/* Game Stats */}
          {(listing.account_level || listing.heroes_count || listing.skins_count) && (
            <div className="flex items-center space-x-3 text-xs text-gray-500 mb-3">
              {listing.account_level && (
                <span>Level {listing.account_level}</span>
              )}
              {listing.heroes_count && (
                <span>{listing.heroes_count} Heroes</span>
              )}
              {listing.skins_count && (
                <span>{listing.skins_count} Skins</span>
              )}
            </div>
          )}

          {/* Price */}
          <div className="flex items-center justify-between">
            <div>
              {getDiscountPercentage() && (
                <div className="text-xs text-gray-500 line-through">
                  {formatPrice(listing.original_price_diamonds!)} 💎
                </div>
              )}
              <div className="text-lg font-bold text-blue-600">
                {formatPrice(listing.price_diamonds)} 💎
              </div>
            </div>
            
            <div className="flex items-center space-x-1">
              {listing.seller?.is_verified && (
                <div className="flex items-center space-x-1 text-xs text-green-600">
                  <Shield className="w-3 h-3" />
                  <span>Verified</span>
                </div>
              )}
            </div>
          </div>

          {/* Time */}
          <div className="text-xs text-gray-500 mt-2">
            {formatTimeAgo(listing.created_at)}
          </div>
        </div>
      </div>
    </Link>
  )
}

export default ListingCard
