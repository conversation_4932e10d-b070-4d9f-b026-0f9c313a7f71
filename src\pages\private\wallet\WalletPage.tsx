import { useState, useEffect } from 'react'
import {
  Wallet, Gem, Clock, CheckCircle, XCircle, AlertCircle,
  Plus, RefreshCw, Eye, EyeOff, Copy, Download, Loader2, Calculator
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'
import { walletService, transactionService } from '../../../services/walletService'
import { WithdrawalModal } from '../../../components/wallet/WithdrawalModal'
import { useNotifications } from '../../../contexts/NotificationContext'
import { manualPaymentService } from '../../../services/manualPaymentService'
import {
  TRADITIONAL_PAYMENT_METHODS,
  CRYPTO_PAYMENT_METHODS,
} from '../../../services/cryptoPayments'
import { cryptoPaymentsService } from '../../../services/cryptoPayments'

interface Transaction {
  id: string
  amount: number
  diamonds: number
  paymentMethod: string
  status: 'paid' | 'pending' | 'failed'
  date: string
  reference: string
}

export default function WalletPage() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()

  // Wallet State
  const [showBalance, setShowBalance] = useState(true)
  const [showHistory, setShowHistory] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [currentBalance, setCurrentBalance] = useState<number>(0)
  const [showWithdrawalModal, setShowWithdrawalModal] = useState(false)
  const [activeTab, setActiveTab] = useState<'overview' | 'buy' | 'history'>('overview')

  // Buy Diamonds State
  const [customAmount, setCustomAmount] = useState<string>('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('')
  const [paymentType, setPaymentType] = useState<'traditional' | 'crypto'>('traditional')
  const [isProcessing, setIsProcessing] = useState(false)
  const [diamondPreview, setDiamondPreview] = useState<any>(null)
  const [fullName, setFullName] = useState<string>('')
  const [referenceNumber, setReferenceNumber] = useState<string>('')
  const [phoneNumber, setPhoneNumber] = useState<string>('')
  const [validationError, setValidationError] = useState<string>('')

  // Wallet tabs
  const tabs = [
    { id: 'overview', name: 'Overview', icon: Wallet, description: 'Balance & Quick Actions' },
    { id: 'buy', name: 'Buy Diamonds', icon: Plus, description: 'Purchase diamonds' },
    { id: 'history', name: 'History', icon: Clock, description: 'Transaction history' }
  ]

  // Get available payment methods based on type
  const availablePaymentMethods = paymentType === 'traditional'
    ? TRADITIONAL_PAYMENT_METHODS
    : CRYPTO_PAYMENT_METHODS

  // Load wallet data on mount
  useEffect(() => {
    const loadWalletData = async () => {
      if (!user?.id) return

      setIsLoading(true)
      try {
        // Load current balance
        const { data: balance, error: balanceError } = await walletService.getBalance(user.id)
        if (balanceError) {
          console.error('Error loading balance:', balanceError)
        } else {
          setCurrentBalance(balance || 0)
        }

        // Load transactions
        const { data: transactionData, error: transactionError } = await transactionService.getUserTransactions(user.id)
        if (transactionError) {
          console.error('Error loading transactions:', transactionError)
        } else {
          setTransactions(transactionData || [])
        }
      } catch (error) {
        console.error('Error loading wallet data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadWalletData()
  }, [user?.id])

  // Update diamond preview when amount changes
  useEffect(() => {
    if (customAmount && !isNaN(Number(customAmount))) {
      const amount = Number(customAmount)
      const preview = manualPaymentService.calculateDiamondsPreview(amount)
      setDiamondPreview(preview)

      // Validate amount for selected payment method
      if (selectedPaymentMethod) {
        const validation = manualPaymentService.validateAmount(amount, selectedPaymentMethod)
        setValidationError(validation.valid ? '' : validation.error || '')
      }
    } else {
      setDiamondPreview(null)
      setValidationError('')
    }
  }, [customAmount, selectedPaymentMethod])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-PH', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-4 h-4" />
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'failed':
        return <XCircle className="w-4 h-4" />
      default:
        return <AlertCircle className="w-4 h-4" />
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('Copied to clipboard!')
  }



  // Handle payment submission
  const handleSubmitDeposit = async () => {
    if (!user || !customAmount || !selectedPaymentMethod) {
      addNotification({
        type: 'error',
        title: 'Missing Information',
        message: 'Please fill in all required fields'
      })
      return
    }

    const amount = Number(customAmount)
    if (isNaN(amount) || amount <= 0) {
      addNotification({
        type: 'error',
        title: 'Invalid Amount',
        message: 'Please enter a valid amount'
      })
      return
    }

    // Validate amount
    const validation = manualPaymentService.validateAmount(amount, selectedPaymentMethod)
    if (!validation.valid) {
      addNotification({
        type: 'error',
        title: 'Invalid Amount',
        message: validation.error || 'Invalid amount'
      })
      return
    }

    setIsProcessing(true)

    try {
      const method = availablePaymentMethods.find(m => m.id === selectedPaymentMethod)

      if (method?.type === 'traditional') {
        // Handle traditional payments (manual processing)
        if (!fullName || !referenceNumber || !phoneNumber) {
          addNotification({
            type: 'error',
            title: 'Required Information Missing',
            message: 'Please provide full name, reference number and phone number'
          })
          setIsProcessing(false)
          return
        }

        const result = await manualPaymentService.submitDepositRequest(
          user.id,
          amount,
          selectedPaymentMethod,
          { fullname: fullName, reference_number: referenceNumber, phone_number: phoneNumber }
        )

        if (result.error) {
          throw new Error((result.error as any)?.message || 'Failed to submit deposit request')
        }

        addNotification({
          type: 'success',
          title: 'Deposit Request Submitted',
          message: 'Your deposit request has been submitted for review. You will receive diamonds once approved.'
        })

      } else if (method?.type === 'crypto') {
        // Handle crypto payments (automated processing via NowPayments.io gateway)
        const result = await cryptoPaymentsService.createPayment(
          user.id,
          'custom', // package ID for custom amounts
          (method as any).symbol, // cryptocurrency symbol (e.g., 'usdttrc20')
          amount // custom amount in PHP
        )

        if (result.error) {
          throw new Error(result.error.message || 'Failed to create crypto payment')
        }

        addNotification({
          type: 'success',
          title: 'Payment Created',
          message: 'Redirecting to NowPayments.io gateway...'
        })

        // Redirect to payment gateway
        if ((result.data as any)?.payment_url) {
          window.location.href = (result.data as any).payment_url
        } else {
          throw new Error('No payment URL received from gateway')
        }
      }

      // Reset form
      setCustomAmount('')
      setSelectedPaymentMethod('')
      setFullName('')
      setReferenceNumber('')
      setPhoneNumber('')
      setDiamondPreview(null)

      // Switch back to overview tab
      setActiveTab('overview')

    } catch (error) {
      console.error('Payment submission error:', error)
      addNotification({
        type: 'error',
        title: 'Payment Failed',
        message: error instanceof Error ? error.message : 'Failed to process payment'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your wallet...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">

        {/* Compact Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg text-white p-3 sm:p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg sm:text-xl lg:text-2xl font-bold">💎 Diamond Wallet</h1>
              <p className="text-blue-100 text-xs sm:text-sm">{user?.username} • ID: {user?.id?.slice(0, 8)}...</p>
            </div>
            <div className="text-right">
              <div className="text-white text-sm sm:text-base font-bold">1₱ = 1💎</div>
              <div className="text-blue-100 text-xs">Member since {user?.created_at ? new Date(user.created_at).getFullYear() : 'N/A'}</div>
            </div>
          </div>
        </div>

        {/* Compact Tab Navigation */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-3 sm:p-4">
          <div className="grid grid-cols-3 gap-2 sm:gap-3">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex flex-col items-center p-3 sm:p-4 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-blue-600 text-white shadow-md'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                }`}
              >
                <tab.icon className="w-5 h-5 sm:w-6 sm:h-6 mb-1 sm:mb-2" />
                <div className="text-center">
                  <div className="font-bold text-xs sm:text-sm">{tab.name}</div>
                  <div className={`text-xs hidden sm:block ${activeTab === tab.id ? 'text-blue-100' : 'text-gray-500'}`}>
                    {tab.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <>
            {/* Compact Balance Card */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl shadow-lg border border-blue-200 p-3 sm:p-4">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                    <Gem className="w-5 h-5 sm:w-6 sm:h-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-base sm:text-lg font-bold text-gray-900">Balance</h2>
                    <p className="text-xs sm:text-sm text-gray-600">Available for gaming</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowWithdrawalModal(true)}
                    className="bg-green-600 hover:bg-green-700 text-white px-2 sm:px-3 py-1 sm:py-2 rounded-lg text-xs sm:text-sm font-medium flex items-center gap-1"
                  >
                    <Download className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="hidden sm:inline">Withdraw</span>
                  </button>
                  <button
                    onClick={() => setShowBalance(!showBalance)}
                    className="p-1 sm:p-2 hover:bg-white/50 rounded-lg"
                  >
                    {showBalance ? <Eye className="w-4 h-4 text-gray-600" /> : <EyeOff className="w-4 h-4 text-gray-600" />}
                  </button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="text-2xl sm:text-3xl font-bold text-gray-900">
                    {showBalance ? currentBalance.toLocaleString() : '••••••'}
                  </div>
                  <div className="text-xl sm:text-2xl">💎</div>
                </div>
                <div className="text-right">
                  <div className="text-xs text-gray-600">PHP Value</div>
                  <div className="text-lg sm:text-xl font-bold text-green-600">
                    {showBalance ? `₱${currentBalance.toLocaleString()}` : '₱••••••'}
                  </div>
                </div>
              </div>

              <div className="mt-3 flex items-center justify-between text-xs text-gray-600">
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Active</span>
                </div>
                <span>Updated: {new Date().toLocaleTimeString()}</span>
              </div>
            </div>

            {/* Compact Quick Actions */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-3 sm:p-4">
              <h3 className="text-sm sm:text-base font-bold text-gray-900 mb-3">Quick Actions</h3>
              <div className="grid grid-cols-3 gap-2 sm:gap-3">
                <button
                  onClick={() => setActiveTab('buy')}
                  className="flex flex-col items-center p-2 sm:p-3 bg-blue-50 hover:bg-blue-100 rounded-lg border border-blue-200 hover:border-blue-300 transition-all group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-blue-600 rounded-full flex items-center justify-center mb-1 sm:mb-2 group-hover:scale-110 transition-transform">
                    <Plus className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                  </div>
                  <span className="font-bold text-blue-700 text-xs sm:text-sm">Buy</span>
                  <span className="text-blue-600 text-xs hidden sm:block">Add funds</span>
                </button>

                <button
                  onClick={() => setShowWithdrawalModal(true)}
                  className="flex flex-col items-center p-2 sm:p-3 bg-green-50 hover:bg-green-100 rounded-lg border border-green-200 hover:border-green-300 transition-all group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-green-600 rounded-full flex items-center justify-center mb-1 sm:mb-2 group-hover:scale-110 transition-transform">
                    <Download className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                  </div>
                  <span className="font-bold text-green-700 text-xs sm:text-sm">Withdraw</span>
                  <span className="text-green-600 text-xs hidden sm:block">Cash out</span>
                </button>

                <button
                  onClick={() => {
                    const refreshData = async () => {
                      const { data: balance } = await walletService.getBalance(user!.id)
                      setCurrentBalance(balance || 0)
                      addNotification({
                        type: 'success',
                        title: 'Balance Updated',
                        message: 'Your wallet balance has been refreshed'
                      })
                    }
                    refreshData()
                  }}
                  className="flex flex-col items-center p-2 sm:p-3 bg-purple-50 hover:bg-purple-100 rounded-lg border border-purple-200 hover:border-purple-300 transition-all group"
                >
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-purple-600 rounded-full flex items-center justify-center mb-1 sm:mb-2 group-hover:scale-110 transition-transform">
                    <RefreshCw className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                  </div>
                  <span className="font-bold text-purple-700 text-xs sm:text-sm">Refresh</span>
                  <span className="text-purple-600 text-xs hidden sm:block">Update</span>
                </button>
              </div>
            </div>

            {/* Compact Quick Buy */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-3 sm:p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm sm:text-base font-bold text-gray-900">Quick Buy</h3>
                <button
                  onClick={() => setActiveTab('buy')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-2 sm:px-3 py-1 rounded-lg text-xs sm:text-sm font-medium flex items-center gap-1"
                >
                  <span className="hidden sm:inline">Advanced</span>
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
                </button>
              </div>

              {/* Compact Custom Input */}
              <div className="mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 sm:gap-3">
                  {/* Payment Type */}
                  <div>
                    <div className="grid grid-cols-2 gap-1">
                      <button
                        onClick={() => setPaymentType('traditional')}
                        className={`px-2 py-1 text-xs rounded-lg border transition-all ${
                          paymentType === 'traditional'
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 bg-white text-gray-600'
                        }`}
                      >
                        Traditional
                      </button>
                      <button
                        onClick={() => setPaymentType('crypto')}
                        className={`px-2 py-1 text-xs rounded-lg border transition-all ${
                          paymentType === 'crypto'
                            ? 'border-blue-500 bg-blue-50 text-blue-700'
                            : 'border-gray-200 bg-white text-gray-600'
                        }`}
                      >
                        Crypto
                      </button>
                    </div>
                  </div>

                  {/* Amount Input */}
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">₱</span>
                    </div>
                    <input
                      type="number"
                      value={customAmount}
                      onChange={(e) => setCustomAmount(e.target.value)}
                      placeholder={paymentType === 'traditional' ? '10,000' : '50'}
                      min={paymentType === 'traditional' ? '10000' : '50'}
                      max={paymentType === 'traditional' ? '40000' : undefined}
                      className="w-full pl-6 pr-2 py-1 border border-gray-300 rounded-lg focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                    />
                  </div>

                  {/* Buy Button */}
                  <button
                    onClick={() => {
                      if (customAmount && !isNaN(Number(customAmount))) {
                        setActiveTab('buy')
                      } else {
                        addNotification({
                          type: 'error',
                          title: 'Invalid Amount',
                          message: 'Please enter a valid amount first'
                        })
                      }
                    }}
                    disabled={!customAmount || isNaN(Number(customAmount))}
                    className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-1 px-2 rounded-lg transition-colors flex items-center justify-center gap-1 text-sm"
                  >
                    <Plus className="w-3 h-3" />
                    Buy
                  </button>
                </div>

                {/* Compact Diamond Preview */}
                {diamondPreview && customAmount && (
                  <div className="mt-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-blue-700 font-medium">You'll get:</span>
                      <div className="text-right">
                        <div className="font-bold text-blue-900">
                          {diamondPreview.total_diamonds.toLocaleString()} 💎
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="mb-3">
                <h4 className="text-sm font-bold text-gray-900 mb-2">Popular Amounts</h4>
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                  {[
                    { amount: 1000, diamonds: 1000, popular: false },
                    { amount: 5000, diamonds: 5000, popular: true },
                    { amount: 10000, diamonds: 10000, popular: false },
                    { amount: 20000, diamonds: 20000, popular: false }
                  ].map((offer) => (
                    <div
                      key={offer.amount}
                      onClick={() => {
                        setCustomAmount(offer.amount.toString())
                        setActiveTab('buy')
                      }}
                      className={`relative p-2 rounded-lg border cursor-pointer transition-all hover:scale-105 ${
                        offer.popular
                          ? 'border-yellow-400 bg-yellow-50'
                          : 'border-gray-200 hover:border-blue-300 bg-white hover:bg-blue-50'
                      }`}
                    >
                      {offer.popular && (
                        <div className="absolute -top-1 -right-1">
                          <span className="bg-yellow-400 text-yellow-900 text-xs font-bold px-1 py-0.5 rounded-full">
                            ⭐
                          </span>
                        </div>
                      )}

                      <div className="text-center">
                        <div className="text-sm font-bold text-gray-900">
                          ₱{offer.amount.toLocaleString()}
                        </div>
                        <div className="text-xs text-blue-600 font-bold">
                          {offer.diamonds.toLocaleString()} 💎
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                <div className="text-xs text-blue-700">
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Payment Methods:</span>
                    <div className="flex items-center space-x-2">
                      <span>GCash • Maya • Bank • USDT • USDC • BNB • ETH</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Compact Stats & Activity */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-3 sm:p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm sm:text-base font-bold text-gray-900">Wallet Overview</h3>
                <div className="flex items-center space-x-1 text-xs text-green-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Secure</span>
                </div>
              </div>

              <div className="grid grid-cols-2 sm:grid-cols-4 gap-2 sm:gap-3 mb-3">
                <div className="bg-blue-50 rounded-lg p-2 text-center">
                  <div className="text-xs text-blue-600 font-medium">Earned</div>
                  <div className="text-sm sm:text-base font-bold text-blue-900">
                    {transactions.filter(t => t.status === 'paid').reduce((sum, t) => sum + t.diamonds, 0).toLocaleString()} 💎
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-2 text-center">
                  <div className="text-xs text-green-600 font-medium">Completed</div>
                  <div className="text-sm sm:text-base font-bold text-green-900">
                    {transactions.filter(t => t.status === 'paid').length}
                  </div>
                </div>

                <div className="bg-yellow-50 rounded-lg p-2 text-center">
                  <div className="text-xs text-yellow-600 font-medium">Pending</div>
                  <div className="text-sm sm:text-base font-bold text-yellow-900">
                    {transactions.filter(t => t.status === 'pending').length}
                  </div>
                </div>

                <div className="bg-purple-50 rounded-lg p-2 text-center">
                  <div className="text-xs text-purple-600 font-medium">Spent</div>
                  <div className="text-sm sm:text-base font-bold text-purple-900">
                    ₱{transactions.filter(t => t.status === 'paid').reduce((sum, t) => sum + t.amount, 0).toLocaleString()}
                  </div>
                </div>
              </div>

              {/* Compact Transaction History */}
              <div className="flex items-center justify-between">
                <h4 className="text-sm font-bold text-gray-900">Recent Transactions</h4>
                <button
                  onClick={() => setShowHistory(!showHistory)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded-lg text-xs font-medium flex items-center gap-1"
                >
                  <span>{showHistory ? 'Hide' : 'Show'}</span>
                  <Clock className="w-3 h-3" />
                </button>
              </div>

              {/* Compact Transaction List */}
              {showHistory && (
                <div className="mt-3 space-y-2 max-h-48 overflow-y-auto">
                  {transactions.length === 0 ? (
                    <div className="text-center py-6">
                      <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <Wallet className="w-6 h-6 text-gray-400" />
                      </div>
                      <p className="text-gray-500 text-sm">No transactions yet</p>
                      <p className="text-gray-400 text-xs">Start by purchasing diamonds!</p>
                    </div>
                  ) : (
                    transactions.map((transaction) => (
                      <div
                        key={transaction.id}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center space-x-3">
                          <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs ${getStatusColor(transaction.status)}`}>
                            {getStatusIcon(transaction.status)}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="font-bold text-gray-900">
                                {formatCurrency(transaction.amount)}
                              </span>
                              <span className="text-gray-600">→</span>
                              <span className="text-blue-600 font-bold">
                                {transaction.diamonds.toLocaleString()} 💎
                              </span>
                            </div>
                            <div className="text-xs text-gray-600">
                              {transaction.paymentMethod} • {formatDate(transaction.date)}
                            </div>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className={`inline-flex items-center space-x-1 px-2 py-1 rounded-lg text-xs font-medium ${getStatusColor(transaction.status)}`}>
                            <span className="capitalize">{transaction.status}</span>
                          </div>
                          <div className="flex items-center space-x-1 mt-1">
                            <span className="text-xs text-gray-500 font-mono">{transaction.reference.slice(0, 8)}...</span>
                            <button
                              onClick={() => copyToClipboard(transaction.reference)}
                              className="p-1 hover:bg-gray-200 rounded transition-colors"
                            >
                              <Copy className="w-3 h-3 text-gray-400" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              )}
            </div>
          </>
        )}

        {/* Compact Buy Diamonds Tab */}
        {activeTab === 'buy' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
            {/* Compact Amount Input */}
            <div className="lg:col-span-2">
              <div className="bg-white rounded-xl shadow-sm border p-3 sm:p-4 lg:p-6">
                <h2 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center gap-2">
                  <Calculator className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" />
                  Enter Amount
                </h2>

                {/* Compact Payment Type */}
                <div className="mb-4 sm:mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Type
                  </label>
                  <div className="grid grid-cols-2 gap-2 sm:gap-3">
                    <button
                      onClick={() => {
                        setPaymentType('traditional')
                        setSelectedPaymentMethod('')
                      }}
                      className={`p-2 sm:p-3 border-2 rounded-lg text-center transition-all ${
                        paymentType === 'traditional'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm sm:text-base">Traditional</div>
                      <div className="text-xs text-gray-500">₱10k-₱40k</div>
                      <div className="text-xs text-gray-500 hidden sm:block">GCash, Maya, Bank</div>
                    </button>
                    <button
                      onClick={() => {
                        setPaymentType('crypto')
                        setSelectedPaymentMethod('')
                      }}
                      className={`p-2 sm:p-3 border-2 rounded-lg text-center transition-all ${
                        paymentType === 'crypto'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="font-medium text-sm sm:text-base">Cryptocurrency</div>
                      <div className="text-xs text-gray-500">₱50+</div>
                      <div className="text-xs text-gray-500 hidden sm:block">USDT, USDC, BNB, ETH</div>
                    </button>
                  </div>
                </div>

                {/* Compact Amount Input */}
                <div className="mb-4 sm:mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount (PHP)
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span className="text-gray-500 text-sm">₱</span>
                    </div>
                    <input
                      type="number"
                      value={customAmount}
                      onChange={(e) => setCustomAmount(e.target.value)}
                      placeholder={paymentType === 'traditional' ? '10,000' : '50'}
                      min={paymentType === 'traditional' ? '10000' : '50'}
                      max={paymentType === 'traditional' ? '40000' : undefined}
                      className="block w-full pl-8 pr-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-base sm:text-lg"
                    />
                  </div>
                  {validationError && (
                    <p className="mt-1 sm:mt-2 text-sm text-red-600">{validationError}</p>
                  )}
                </div>

                {/* Payment Methods */}
                {customAmount && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Payment Method
                    </label>
                    <div className="space-y-2">
                      {availablePaymentMethods.map((method) => (
                        <div
                          key={method.id}
                          onClick={() => setSelectedPaymentMethod(method.id)}
                          className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                            selectedPaymentMethod === method.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center space-x-3">
                              <div className="text-2xl">{method.icon}</div>
                              <div>
                                <div className="font-medium text-gray-900">{method.name}</div>
                                <div className="text-sm text-gray-500">{method.description}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-sm text-gray-600">{method.processing_time}</div>
                              <div className="text-xs text-gray-500">
                                Fee: {method.fee_percentage}%
                                {method.fixed_fee_php > 0 && ` + ₱${method.fixed_fee_php}`}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* GCash Payment Information */}
                {paymentType === 'traditional' && selectedPaymentMethod === 'gcash' && (
                  <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <h4 className="text-lg font-semibold text-green-800 mb-3 flex items-center gap-2">
                      📲 PAY VIA GCash
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-green-700">Name:</span>
                        <span className="text-green-900">Nabeel Aymoonhi</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-green-700">Phone Number:</span>
                        <div className="flex items-center gap-2">
                          <span className="text-green-900 font-mono">0994 042 0347</span>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText('09940420347')
                              addNotification({
                                type: 'success',
                                title: 'Copied!',
                                message: 'GCash number copied to clipboard'
                              })
                            }}
                            className="text-green-600 hover:text-green-800 p-1 rounded"
                            title="Copy to clipboard"
                          >
                            📋
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="font-medium text-green-700">Min:</span>
                        <span className="text-green-900">₱100</span>
                        <span className="font-medium text-green-700">Max:</span>
                        <span className="text-green-900">₱20,000</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Maya Payment Information */}
                {paymentType === 'traditional' && selectedPaymentMethod === 'maya' && (
                  <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="text-lg font-semibold text-blue-800 mb-3 flex items-center gap-2">
                      💳 PAY VIA Maya
                    </h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-blue-700">Name:</span>
                        <span className="text-blue-900">Nabeel Aymoonhi</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-blue-700">Phone Number:</span>
                        <div className="flex items-center gap-2">
                          <span className="text-blue-900 font-mono">0994 042 0347</span>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText('09940420347')
                              addNotification({
                                type: 'success',
                                title: 'Copied!',
                                message: 'Maya number copied to clipboard'
                              })
                            }}
                            className="text-blue-600 hover:text-blue-800 p-1 rounded"
                            title="Copy to clipboard"
                          >
                            📋
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="font-medium text-blue-700">Min:</span>
                        <span className="text-blue-900">₱100</span>
                        <span className="font-medium text-blue-700">Max:</span>
                        <span className="text-blue-900">₱20,000</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* Traditional Payment Fields */}
                {paymentType === 'traditional' && selectedPaymentMethod && (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        placeholder="Enter your full name"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Phone Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="tel"
                        value={phoneNumber}
                        onChange={(e) => setPhoneNumber(e.target.value)}
                        placeholder="Enter phone number used for payment"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Reference Number <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={referenceNumber}
                        onChange={(e) => setReferenceNumber(e.target.value)}
                        placeholder="Enter transaction reference number"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Compact Diamond Preview */}
            <div className="lg:col-span-1">
              {diamondPreview && (
                <div className="bg-white rounded-xl shadow-sm border p-3 sm:p-4 lg:p-6 sticky top-6">
                  <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-3 sm:mb-4 flex items-center gap-2">
                    <Gem className="w-4 h-4 sm:w-5 sm:h-5 text-blue-500" />
                    Preview
                  </h3>

                  <div className="space-y-2 sm:space-y-3 text-xs sm:text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Amount:</span>
                      <span className="font-medium">₱{Number(customAmount).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Diamonds:</span>
                      <span className="font-medium">{diamondPreview.total_diamonds.toLocaleString()} 💎</span>
                    </div>
                    <hr className="border-blue-200" />
                    <div className="flex justify-between text-sm sm:text-base font-semibold">
                      <span className="text-blue-900">Total:</span>
                      <span className="text-blue-900">{diamondPreview.total_diamonds.toLocaleString()} 💎</span>
                    </div>
                  </div>

                  {/* Compact Submit Button */}
                  {customAmount && selectedPaymentMethod && (
                    <button
                      onClick={handleSubmitDeposit}
                      disabled={isProcessing || (paymentType === 'traditional' && (!fullName || !referenceNumber || !phoneNumber))}
                      className="w-full mt-3 sm:mt-4 lg:mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-2 sm:py-3 px-3 sm:px-4 rounded-lg transition-colors flex items-center justify-center gap-2 text-sm sm:text-base"
                    >
                      {isProcessing ? (
                        <>
                          <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                          <span className="hidden sm:inline">Processing...</span>
                          <span className="sm:hidden">...</span>
                        </>
                      ) : (
                        <>
                          <Plus className="w-3 h-3 sm:w-4 sm:h-4" />
                          <span className="hidden sm:inline">{paymentType === 'traditional' ? 'Submit for Review' : 'Pay with Crypto'}</span>
                          <span className="sm:hidden">{paymentType === 'traditional' ? 'Submit' : 'Pay'}</span>
                        </>
                      )}
                    </button>
                  )}

                  <p className="text-xs text-gray-500 text-center mt-1 sm:mt-2">
                    {paymentType === 'traditional'
                      ? 'Review within 1-24 hours'
                      : 'Secure payment gateway'
                    }
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Withdrawal Modal */}
      {showWithdrawalModal && (
        <WithdrawalModal
          isOpen={showWithdrawalModal}
          onClose={() => setShowWithdrawalModal(false)}
          currentBalance={currentBalance}
          onWithdrawalSuccess={() => {
            // Refresh balance after successful withdrawal
            const refreshData = async () => {
              const { data: balance } = await walletService.getBalance(user!.id)
              setCurrentBalance(balance || 0)
            }
            refreshData()
          }}
        />
      )}
    </div>
  )
}
