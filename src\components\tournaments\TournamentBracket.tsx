import { useState, useEffect } from 'react'
import { Trophy, Users, Crown, Zap, Clock, CheckCircle } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
// TODO: platformService not yet extracted - using old import temporarily
import { platformService } from '../../services/supabase'

interface BracketMatch {
  id: string
  round: number
  position: number
  player1?: {
    id: string
    username: string
    seed: number
  }
  player2?: {
    id: string
    username: string
    seed: number
  }
  winner?: string
  status: 'pending' | 'ongoing' | 'completed'
  scheduled_time?: string
}

interface TournamentBracketProps {
  tournamentId: string
  participants: any[]
  isAdmin?: boolean
}

export default function TournamentBracket({ tournamentId, participants, isAdmin = false }: TournamentBracketProps) {
  const { } = useAuth() // TODO: Confirm user usage
  const [, setBracketData] = useState<any>(null)
  const [matches, setMatches] = useState<BracketMatch[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    generateBracket()
  }, [tournamentId, participants])

  const generateBracket = async () => {
    setLoading(true)
    try {
      const result = await platformService.generateTournamentBracket(tournamentId)
      if (result.data) {
        setBracketData(result.data)
        generateMatches(result.data)
      }
    } catch (error) {
      console.error('Error generating bracket:', error)
      // Generate mock bracket for demo
      generateMockBracket()
    } finally {
      setLoading(false)
    }
  }

  const generateMockBracket = () => {
    const rounds = Math.ceil(Math.log2(participants.length))
    const mockMatches: BracketMatch[] = []

    // Generate first round matches
    for (let i = 0; i < participants.length; i += 2) {
      if (participants[i + 1]) {
        mockMatches.push({
          id: `match-${i / 2}`,
          round: 1,
          position: i / 2,
          player1: {
            id: participants[i].id,
            username: participants[i].username,
            seed: i + 1
          },
          player2: {
            id: participants[i + 1].id,
            username: participants[i + 1].username,
            seed: i + 2
          },
          status: 'pending'
        })
      }
    }

    // Generate subsequent rounds
    let currentRoundMatches = mockMatches.length
    for (let round = 2; round <= rounds; round++) {
      const nextRoundMatches = Math.ceil(currentRoundMatches / 2)
      for (let i = 0; i < nextRoundMatches; i++) {
        mockMatches.push({
          id: `match-${round}-${i}`,
          round,
          position: i,
          status: 'pending'
        })
      }
      currentRoundMatches = nextRoundMatches
    }

    setMatches(mockMatches)
  }

  const generateMatches = (_bracketData: any) => { // TODO: Confirm bracketData usage
    // This would parse the bracket data and create match objects
    // For now, use mock data
    generateMockBracket()
  }

  const getRoundName = (round: number, totalRounds: number) => {
    if (round === totalRounds) return 'Final'
    if (round === totalRounds - 1) return 'Semi-Final'
    if (round === totalRounds - 2) return 'Quarter-Final'
    return `Round ${round}`
  }

  const getMatchStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 border-green-300 text-green-800'
      case 'ongoing':
        return 'bg-blue-100 border-blue-300 text-blue-800'
      case 'pending':
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'ongoing':
        return <Zap className="w-4 h-4 text-blue-600" />
      case 'pending':
      default:
        return <Clock className="w-4 h-4 text-gray-600" />
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  const rounds = Math.max(...matches.map(m => m.round))
  const matchesByRound = Array.from({ length: rounds }, (_, i) => 
    matches.filter(m => m.round === i + 1)
  )

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
          <Trophy className="w-5 h-5 text-yellow-500" />
          <span>Tournament Bracket</span>
        </h2>
      </div>

      {/* Bracket */}
      <div className="p-6">
        {matchesByRound.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Bracket Not Generated</h3>
            <p className="text-gray-600">Waiting for tournament to start...</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <div className="flex space-x-8 min-w-max">
              {matchesByRound.map((roundMatches, roundIndex) => (
                <div key={roundIndex} className="flex flex-col space-y-4">
                  {/* Round Header */}
                  <div className="text-center mb-4">
                    <h3 className="text-sm font-semibold text-gray-900">
                      {getRoundName(roundIndex + 1, rounds)}
                    </h3>
                    <p className="text-xs text-gray-600">
                      {roundMatches.length} match{roundMatches.length !== 1 ? 'es' : ''}
                    </p>
                  </div>

                  {/* Matches */}
                  <div className="space-y-6">
                    {roundMatches.map((match) => (
                      <div
                        key={match.id}
                        className={`border-2 rounded-lg p-4 min-w-[200px] ${getMatchStatusColor(match.status)}`}
                      >
                        {/* Match Header */}
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-xs font-medium">
                            Match {match.position + 1}
                          </span>
                          {getStatusIcon(match.status)}
                        </div>

                        {/* Players */}
                        <div className="space-y-2">
                          {/* Player 1 */}
                          <div className={`flex items-center justify-between p-2 rounded ${
                            match.winner === match.player1?.id ? 'bg-green-200' : 'bg-white'
                          }`}>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-600">#{match.player1?.seed || '?'}</span>
                              <span className="text-sm font-medium">
                                {match.player1?.username || 'TBD'}
                              </span>
                            </div>
                            {match.winner === match.player1?.id && (
                              <Crown className="w-4 h-4 text-yellow-500" />
                            )}
                          </div>

                          {/* VS */}
                          <div className="text-center text-xs text-gray-500 font-medium">VS</div>

                          {/* Player 2 */}
                          <div className={`flex items-center justify-between p-2 rounded ${
                            match.winner === match.player2?.id ? 'bg-green-200' : 'bg-white'
                          }`}>
                            <div className="flex items-center space-x-2">
                              <span className="text-xs text-gray-600">#{match.player2?.seed || '?'}</span>
                              <span className="text-sm font-medium">
                                {match.player2?.username || 'TBD'}
                              </span>
                            </div>
                            {match.winner === match.player2?.id && (
                              <Crown className="w-4 h-4 text-yellow-500" />
                            )}
                          </div>
                        </div>

                        {/* Match Actions */}
                        {isAdmin && match.status === 'pending' && match.player1 && match.player2 && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <button className="w-full text-xs bg-blue-600 text-white py-1 rounded hover:bg-blue-700">
                              Start Match
                            </button>
                          </div>
                        )}

                        {match.scheduled_time && (
                          <div className="mt-2 text-xs text-gray-600 text-center">
                            {new Date(match.scheduled_time).toLocaleString()}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-xl">
        <div className="flex items-center justify-center space-x-6 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-200 border border-gray-300 rounded"></div>
            <span className="text-gray-600">Pending</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-200 border border-blue-300 rounded"></div>
            <span className="text-gray-600">Ongoing</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-200 border border-green-300 rounded"></div>
            <span className="text-gray-600">Completed</span>
          </div>
        </div>
      </div>
    </div>
  )
}
