import { supabase, User } from './supabaseClient'

export const userService = {
  // Get user profile
  async getProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update user profile
  async updateProfile(userId: string, updates: Partial<User>) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update diamond balance
  async updateDiamondBalance(userId: string, amount: number, operation: 'add' | 'subtract') {
    try {
      const { data: profile } = await this.getProfile(userId)
      if (!profile) throw new Error('User not found')

      const newBalance = operation === 'add'
        ? profile.diamond_balance + amount
        : profile.diamond_balance - amount

      if (newBalance < 0) throw new Error('Insufficient balance')

      const { data, error } = await supabase
        .from('users')
        .update({
          diamond_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create user profile (for signup)
  async createProfile(userData: Omit<User, 'created_at' | 'updated_at'>) {
    try {
      const { data, error } = await supabase
        .from('users')
        .insert([{
          ...userData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
