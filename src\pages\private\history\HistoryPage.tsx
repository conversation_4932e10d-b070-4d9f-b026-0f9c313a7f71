import { useState } from 'react'
import { History, Trophy, Wallet, Calendar, Filter, Search } from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'

export default function HistoryPage() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState<'matches' | 'transactions'>('matches')
  const [searchTerm, setSearchTerm] = useState('')

  // Mock data - replace with real data from your API
  const matchHistory = [
    {
      id: '1',
      game: 'Mobile Legends',
      format: '5v5',
      result: 'Won',
      diamonds: 500,
      opponent: 'ProGamer123',
      date: '2024-01-15',
      time: '14:30'
    },
    {
      id: '2',
      game: 'Valorant',
      format: '1v1',
      result: 'Lost',
      diamonds: -250,
      opponent: 'SkillMaster',
      date: '2024-01-14',
      time: '20:15'
    },
    {
      id: '3',
      game: 'Call of Duty',
      format: '2v2',
      result: 'Won',
      diamonds: 750,
      opponent: 'CoDLegend',
      date: '2024-01-13',
      time: '16:45'
    }
  ]

  const transactionHistory = [
    {
      id: '1',
      type: 'Purchase',
      amount: 1000,
      description: 'Diamond purchase via GCash',
      date: '2024-01-15',
      time: '10:30',
      status: 'Completed'
    },
    {
      id: '2',
      type: 'Match Win',
      amount: 500,
      description: 'Won match vs ProGamer123',
      date: '2024-01-15',
      time: '14:30',
      status: 'Completed'
    },
    {
      id: '3',
      type: 'Match Loss',
      amount: -250,
      description: 'Lost match vs SkillMaster',
      date: '2024-01-14',
      time: '20:15',
      status: 'Completed'
    }
  ]

  const getResultColor = (result: string) => {
    switch (result) {
      case 'Won':
        return 'text-green-600 bg-green-50'
      case 'Lost':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getTransactionColor = (amount: number) => {
    return amount > 0 ? 'text-green-600' : 'text-red-600'
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">History</h1>
        <p className="text-gray-600">View your match and transaction history</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Trophy className="w-6 h-6 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">{user?.total_matches || 0}</p>
              <p className="text-sm text-gray-600">Total Matches</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <Trophy className="w-6 h-6 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">{user?.wins || 0}</p>
              <p className="text-sm text-gray-600">Wins</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <Wallet className="w-6 h-6 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-2xl font-bold text-gray-900">{user?.diamond_balance || 0}</p>
              <p className="text-sm text-gray-600">Current Diamonds</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-md">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            <button
              onClick={() => setActiveTab('matches')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'matches'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <History className="w-4 h-4 inline mr-2" />
              Match History
            </button>
            <button
              onClick={() => setActiveTab('transactions')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'transactions'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <Wallet className="w-4 h-4 inline mr-2" />
              Transactions
            </button>
          </nav>
        </div>

        {/* Search and Filter */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search history..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'matches' ? (
            <div className="space-y-4">
              {matchHistory.map((match) => (
                <div key={match.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <Trophy className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{match.game}</h4>
                        <p className="text-sm text-gray-600">
                          {match.format} vs {match.opponent}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getResultColor(match.result)}`}>
                        {match.result}
                      </span>
                      <p className={`text-sm font-semibold ${match.diamonds > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {match.diamonds > 0 ? '+' : ''}{match.diamonds} diamonds
                      </p>
                      <p className="text-xs text-gray-500 flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {match.date} at {match.time}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {transactionHistory.map((transaction) => (
                <div key={transaction.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <Wallet className="w-5 h-5 text-white" />
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900">{transaction.type}</h4>
                        <p className="text-sm text-gray-600">{transaction.description}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`text-lg font-semibold ${getTransactionColor(transaction.amount)}`}>
                        {transaction.amount > 0 ? '+' : ''}{transaction.amount} diamonds
                      </p>
                      <p className="text-xs text-gray-500 flex items-center justify-end">
                        <Calendar className="w-3 h-3 mr-1" />
                        {transaction.date} at {transaction.time}
                      </p>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {transaction.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
