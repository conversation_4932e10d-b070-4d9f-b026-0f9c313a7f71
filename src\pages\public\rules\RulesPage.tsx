import { Link } from "react-router-dom"
import {
  Trophy, Scale, Gamepad2, FileText, Swords, Flag, AlertTriangle, HelpCircle,
  ShoppingCart, Crown, Shield, Users, Wallet, Star, Clock, Eye, CheckCircle,
  XCircle, DollarSign, Gavel, UserCheck, MessageSquare, Gift, Target
} from "lucide-react"

export default function RulesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Trophy className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Gambets</span>
            </Link>
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-gray-700 hover:text-blue-600 font-medium">Home</Link>
              <Link to="/browse" className="text-gray-700 hover:text-blue-600 font-medium">Marketplace</Link>
              <Link to="/leaderboards" className="text-gray-700 hover:text-blue-600 font-medium">Leaderboards</Link>
              <Link to="/rules" className="text-blue-600 font-medium">Rules</Link>
              <Link to="/support" className="text-gray-700 hover:text-blue-600 font-medium">Support</Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <button className="text-gray-700 hover:text-blue-600 font-medium">Login</button>
              </Link>
              <Link to="/signup">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                  Sign Up
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 lg:px-6 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">📋 Gambets Rules & Guidelines</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto mb-6">
            Comprehensive rules for matches, tournaments, marketplace, and community. Fair play is our foundation.
          </p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
              <Gamepad2 className="w-4 h-4 inline mr-2" />
              Gaming Rules
            </div>
            <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
              <ShoppingCart className="w-4 h-4 inline mr-2" />
              Marketplace Policy
            </div>
            <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
              <Crown className="w-4 h-4 inline mr-2" />
              Tournament Rules
            </div>
            <div className="bg-white bg-opacity-20 px-4 py-2 rounded-full">
              <Shield className="w-4 h-4 inline mr-2" />
              Referee Guidelines
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 lg:px-6 py-12">
        <div className="max-w-4xl mx-auto">

          {/* Table of Contents */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <FileText className="w-8 h-8 text-blue-600 mr-3" />
              Table of Contents
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <a href="#general" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <Scale className="w-4 h-4 mr-2" />
                  General Rules & Conduct
                </a>
                <a href="#matches" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <Gamepad2 className="w-4 h-4 mr-2" />
                  Match Rules & Procedures
                </a>
                <a href="#tournaments" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <Crown className="w-4 h-4 mr-2" />
                  Tournament Guidelines
                </a>
                <a href="#marketplace" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Marketplace Policy
                </a>
              </div>
              <div className="space-y-2">
                <a href="#referee" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <Shield className="w-4 h-4 mr-2" />
                  Referee System
                </a>
                <a href="#wallet" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <Wallet className="w-4 h-4 mr-2" />
                  Diamond & Payment Rules
                </a>
                <a href="#community" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <Users className="w-4 h-4 mr-2" />
                  Community Guidelines
                </a>
                <a href="#penalties" className="flex items-center text-blue-600 hover:text-blue-700 transition-colors">
                  <AlertTriangle className="w-4 h-4 mr-2" />
                  Penalties & Violations
                </a>
              </div>
            </div>
          </div>

          {/* General Rules */}
          <div id="general" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Scale className="w-8 h-8 text-blue-600 mr-3" />
              General Rules & Conduct
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                  Platform Standards
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Fair Play:</strong> No cheating, hacking, exploiting bugs, or using unauthorized third-party software</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Respectful Behavior:</strong> Treat all players, referees, and staff with respect and professionalism</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Age Requirement:</strong> Must be 18+ or have parental consent for diamond transactions</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>One Account Policy:</strong> Each person may only have one active Gambets account</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Shield className="w-5 h-5 text-blue-600 mr-2" />
                  Account Security
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Password Security:</strong> Use strong passwords and enable 2FA when available</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Account Sharing:</strong> Sharing accounts is prohibited and may result in suspension</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Suspicious Activity:</strong> Report any unauthorized access immediately</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Personal Information:</strong> Never share personal details with other players</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Match Rules */}
          <div id="matches" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Gamepad2 className="w-8 h-8 text-blue-600 mr-3" />
              Match Rules & Procedures
            </h2>
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <FileText className="w-5 h-5 text-blue-600 mr-2" />
                  Before the Match
                </h3>
                <div className="space-y-2 text-gray-700 ml-4">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Both players must agree on game mode, map, and match format</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Diamond stakes must be deposited before match starts</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Players have 10 minutes to join after match is confirmed</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Swords className="w-5 h-5 text-blue-600 mr-2" />
                  During the Match
                </h3>
                <div className="space-y-2 text-gray-700 ml-4">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Take screenshots of game results immediately after match ends</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>No pausing or disconnecting intentionally</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Report any technical issues immediately</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Flag className="w-5 h-5 text-blue-600 mr-2" />
                  After the Match
                </h3>
                <div className="space-y-2 text-gray-700 ml-4">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Submit match results with proof within 5 minutes</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Wait for manual review and verification (usually 1-24 hours)</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p>Winner receives diamonds after verification</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tournament Rules */}
          <div id="tournaments" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Crown className="w-8 h-8 text-blue-600 mr-3" />
              Tournament Guidelines
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Target className="w-5 h-5 text-purple-600 mr-2" />
                  Registration & Entry
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Entry Requirements:</strong> Meet minimum rank and account level requirements</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Registration Deadline:</strong> Register at least 1 hour before tournament start</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Entry Fees:</strong> Pay entry fee in diamonds before tournament begins</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Check-in:</strong> Must check-in 15 minutes before start time</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Clock className="w-5 h-5 text-orange-600 mr-2" />
                  Tournament Format
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Bracket System:</strong> Single or double elimination based on tournament type</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Match Timing:</strong> 10 minutes between matches, 5 minutes for game setup</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>No-Shows:</strong> Automatic forfeit after 10 minutes late</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Prize Distribution:</strong> Automatic after tournament completion</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Marketplace Rules */}
          <div id="marketplace" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <ShoppingCart className="w-8 h-8 text-blue-600 mr-3" />
              Marketplace Policy
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Star className="w-5 h-5 text-yellow-600 mr-2" />
                  Listing Requirements
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Accurate Information:</strong> All account details must be truthful and verifiable</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Account Ownership:</strong> Only sell accounts you legitimately own</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>No Stolen Accounts:</strong> Selling hacked or stolen accounts is prohibited</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Pricing Guidelines:</strong> Set fair market prices, no price manipulation</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Shield className="w-5 h-5 text-green-600 mr-2" />
                  Transaction Safety
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Referee Protection:</strong> All transactions are mediated by verified referees</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Escrow System:</strong> Diamonds held safely until successful transfer</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Delivery Timeframe:</strong> Accounts must be delivered within 24 hours</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Dispute Resolution:</strong> Report issues immediately for quick resolution</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Referee System */}
          <div id="referee" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Shield className="w-8 h-8 text-blue-600 mr-3" />
              Referee System
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <UserCheck className="w-5 h-5 text-blue-600 mr-2" />
                  Referee Requirements
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Experience:</strong> Minimum 6 months active gaming experience</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Clean Record:</strong> No violations or penalties on Gambets platform</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Availability:</strong> Commit to minimum 10 hours per week</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Communication:</strong> Fluent in English and responsive</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Gavel className="w-5 h-5 text-purple-600 mr-2" />
                  Referee Responsibilities
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Match Verification:</strong> Review and verify match results fairly</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Marketplace Mediation:</strong> Facilitate safe account transfers</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Dispute Resolution:</strong> Handle conflicts impartially</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Response Time:</strong> Respond to assignments within 2 hours</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Diamond & Payment Rules */}
          <div id="wallet" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Wallet className="w-8 h-8 text-blue-600 mr-3" />
              Diamond & Payment Rules
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <DollarSign className="w-5 h-5 text-green-600 mr-2" />
                  Diamond System
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Exchange Rate:</strong> 1 Peso = 1 Diamond (fixed rate)</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Minimum Purchase:</strong> ₱20 minimum diamond purchase</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Bonus System:</strong> Higher purchases receive bonus diamonds</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Non-Transferable:</strong> Diamonds cannot be transferred between accounts</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Gift className="w-5 h-5 text-purple-600 mr-2" />
                  Withdrawals & Refunds
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Withdrawal Methods:</strong> GCash, Maya, or bank transfer</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Minimum Withdrawal:</strong> ₱100 minimum withdrawal amount</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Processing Time:</strong> 1-3 business days for withdrawals</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Refund Policy:</strong> No refunds for completed matches or transactions</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Community Guidelines */}
          <div id="community" className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Users className="w-8 h-8 text-blue-600 mr-3" />
              Community Guidelines
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <MessageSquare className="w-5 h-5 text-blue-600 mr-2" />
                  Chat & Communication
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Respectful Language:</strong> No profanity, hate speech, or offensive content</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>No Spam:</strong> Avoid repetitive messages or excessive self-promotion</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>English Primary:</strong> Use English in global chats for accessibility</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>No Personal Info:</strong> Don't share personal contact information</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                  <Eye className="w-5 h-5 text-orange-600 mr-2" />
                  Content & Behavior
                </h3>
                <div className="space-y-3 text-gray-700">
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>No Harassment:</strong> Bullying, stalking, or intimidation is prohibited</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Gaming Focus:</strong> Keep discussions relevant to gaming and platform</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Report System:</strong> Report inappropriate behavior immediately</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-blue-600 mr-3 mt-1">•</span>
                    <p><strong>Constructive Feedback:</strong> Provide helpful and positive feedback</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Penalties */}
          <div id="penalties" className="bg-red-50 rounded-2xl border border-red-200 p-8 mb-8">
            <h2 className="text-2xl font-bold text-red-900 mb-6 flex items-center">
              <AlertTriangle className="w-8 h-8 text-red-600 mr-3" />
              Penalties & Violations
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-red-900 mb-3 flex items-center">
                  <XCircle className="w-5 h-5 text-red-600 mr-2" />
                  Severe Violations
                </h3>
                <div className="space-y-3 text-red-800">
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>Cheating/Hacking:</strong> Permanent account ban + forfeiture of all diamonds</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>Account Fraud:</strong> Permanent ban + legal action if applicable</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>Marketplace Scam:</strong> Permanent ban + diamond forfeiture + blacklist</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>Multiple Accounts:</strong> All accounts banned + diamond forfeiture</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-red-900 mb-3 flex items-center">
                  <AlertTriangle className="w-5 h-5 text-orange-600 mr-2" />
                  Minor Violations
                </h3>
                <div className="space-y-3 text-red-800">
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>False Results:</strong> 7-day suspension + diamond penalty</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>No-Show:</strong> Automatic loss + 24-hour matchmaking cooldown</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>Toxic Behavior:</strong> Warning → 3-day suspension → permanent ban</p>
                  </div>
                  <div className="flex items-start">
                    <span className="text-red-600 mr-3 mt-1">•</span>
                    <p><strong>Spam/Advertising:</strong> 24-hour chat ban → 7-day suspension</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Appeal Process */}
            <div className="mt-6 p-4 bg-white rounded-lg border border-red-200">
              <h4 className="font-semibold text-red-900 mb-2 flex items-center">
                <Gavel className="w-4 h-4 mr-2" />
                Appeal Process
              </h4>
              <p className="text-red-700 text-sm">
                If you believe a penalty was issued in error, you may appeal through our support system within 7 days.
                Appeals are reviewed by senior staff and decisions are final. Provide evidence and detailed explanation for best results.
              </p>
            </div>
          </div>

          {/* Rule Updates & Contact */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl p-8 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <FileText className="w-6 h-6 mr-2" />
                  Rule Updates
                </h3>
                <div className="space-y-2 text-blue-100">
                  <p><strong>Last Updated:</strong> December 2024</p>
                  <p><strong>Version:</strong> 2.1</p>
                  <p><strong>Next Review:</strong> March 2025</p>
                  <p className="text-sm">
                    Rules are updated regularly to improve fairness and security.
                    Users will be notified of major changes via email and platform announcements.
                  </p>
                </div>
              </div>
              <div>
                <h3 className="text-xl font-bold mb-4 flex items-center">
                  <HelpCircle className="w-6 h-6 mr-2" />
                  Need Help?
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <MessageSquare className="w-4 h-4" />
                    <span>Live Chat Support (24/7)</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4" />
                    <span>Community Discord Server</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <FileText className="w-4 h-4" />
                    <span>Support Ticket System</span>
                  </div>
                  <Link
                    to="/support"
                    className="inline-block bg-white text-blue-600 px-4 py-2 rounded-lg font-medium hover:bg-blue-50 transition-colors mt-3"
                  >
                    Contact Support
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Contact */}
          <div className="bg-blue-50 rounded-2xl p-8 text-center">
            <h2 className="text-2xl font-bold text-blue-900 mb-4 flex items-center justify-center">
              <HelpCircle className="w-8 h-8 text-blue-600 mr-3" />
              Questions About Rules?
            </h2>
            <p className="text-blue-700 mb-6">
              Our support team is here to help clarify any rules or resolve disputes.
            </p>
            <Link to="/support">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold">
                Contact Support
              </button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
