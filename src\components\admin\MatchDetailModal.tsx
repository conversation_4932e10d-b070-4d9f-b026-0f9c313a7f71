import {
  XCircle, CheckCircle, AlertTriangle, Shield, User, Crown,
  Flag, Zap
} from 'lucide-react'

interface Match {
  id: string
  title: string
  game_type: string
  diamond_pot: number
  status: 'open' | 'waiting' | 'in_progress' | 'completed' | 'cancelled' | 'disputed'
  created_at: string
  scheduled_time?: string
  host_id: string
  referee_id?: string
  winner_id?: string
  dispute_reason?: string
  host: {
    username: string
    email: string
  }
  referee?: {
    username: string
    email: string
  }
  participants: Array<{
    user_id: string
    user: {
      username: string
      email: string
    }
  }>
}

interface MatchDetailModalProps {
  match: Match
  onClose: () => void
  onAction: (matchId: string, action: string, reason?: string) => void
  getStatusBadge: (status: string) => JSX.Element
}

export default function MatchDetailModal({ 
  match, 
  onClose, 
  onAction, 
  getStatusBadge 
}: MatchDetailModalProps) {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900">{match.title}</h2>
              <p className="text-gray-600 mt-1">Match ID: {match.id}</p>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(match.status)}
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Match Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Match Details</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Game Type:</span>
                  <span className="font-medium">{match.game_type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Diamond Pot:</span>
                  <span className="font-medium">{match.diamond_pot.toLocaleString()} 💎</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Created:</span>
                  <span className="font-medium">{new Date(match.created_at).toLocaleString()}</span>
                </div>
                {match.scheduled_time && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Scheduled:</span>
                    <span className="font-medium">{new Date(match.scheduled_time).toLocaleString()}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Participants</h3>
              <div className="space-y-2">
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Crown className="w-4 h-4 text-blue-600" />
                    <span className="font-medium text-blue-900">Host: {match.host.username}</span>
                  </div>
                  <p className="text-blue-700 text-sm mt-1">{match.host.email}</p>
                </div>

                {match.referee && (
                  <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <Shield className="w-4 h-4 text-green-600" />
                      <span className="font-medium text-green-900">Referee: {match.referee.username}</span>
                    </div>
                    <p className="text-green-700 text-sm mt-1">{match.referee.email}</p>
                  </div>
                )}

                {match.participants?.map((participant) => (
                  <div key={participant.user_id} className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <User className="w-4 h-4 text-gray-600" />
                      <span className="font-medium text-gray-900">Player: {participant.user.username}</span>
                    </div>
                    <p className="text-gray-700 text-sm mt-1">{participant.user.email}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Dispute Information */}
          {match.dispute_reason && (
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-orange-900 mb-2 flex items-center">
                <AlertTriangle className="w-5 h-5 mr-2" />
                Dispute Details
              </h3>
              <p className="text-orange-800">{match.dispute_reason}</p>
            </div>
          )}

          {/* Admin Actions */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Actions</h3>
            <div className="flex flex-wrap gap-3">
              {match.status === 'disputed' && (
                <button
                  onClick={() => onAction(match.id, 'resolve_dispute')}
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>Resolve Dispute</span>
                </button>
              )}

              {['open', 'waiting', 'in_progress'].includes(match.status) && (
                <>
                  <button
                    onClick={() => onAction(match.id, 'cancel')}
                    className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                  >
                    <XCircle className="w-4 h-4" />
                    <span>Cancel Match</span>
                  </button>

                  <button
                    onClick={() => onAction(match.id, 'mark_disputed', 'Admin review required')}
                    className="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                  >
                    <Flag className="w-4 h-4" />
                    <span>Mark as Disputed</span>
                  </button>
                </>
              )}

              {match.status === 'in_progress' && (
                <button
                  onClick={() => onAction(match.id, 'force_complete')}
                  className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                >
                  <Zap className="w-4 h-4" />
                  <span>Force Complete</span>
                </button>
              )}

              <button
                onClick={() => {
                  const reason = prompt('Enter reason for admin intervention:')
                  if (reason) {
                    onAction(match.id, 'admin_note', reason)
                  }
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
              >
                <Flag className="w-4 h-4" />
                <span>Add Admin Note</span>
              </button>
            </div>
          </div>

          {/* Match Timeline */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Match Timeline</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-gray-600">Created:</span>
                <span className="font-medium">{new Date(match.created_at).toLocaleString()}</span>
              </div>
              {match.scheduled_time && (
                <div className="flex items-center space-x-3 text-sm">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-gray-600">Scheduled:</span>
                  <span className="font-medium">{new Date(match.scheduled_time).toLocaleString()}</span>
                </div>
              )}
              <div className="flex items-center space-x-3 text-sm">
                <div className={`w-2 h-2 rounded-full ${
                  match.status === 'completed' ? 'bg-green-500' : 
                  match.status === 'cancelled' ? 'bg-red-500' :
                  match.status === 'disputed' ? 'bg-orange-500' : 'bg-gray-400'
                }`}></div>
                <span className="text-gray-600">Status:</span>
                <span className="font-medium capitalize">{match.status.replace('_', ' ')}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
