// Supported Games Configuration
export const SUPPORTED_GAMES = {
  MOBILE_LEGENDS: {
    id: 'mobile_legends',
    name: 'Mobile Legends: Bang Bang',
    shortName: 'ML',
    icon: '🏆',
    ranks: [
      'Warrior',
      'Elite',
      'Master',
      'Grandmaster',
      '<PERSON>',
      '<PERSON>',
      'Mythic',
      'Mythical Glory'
    ],
    roles: [
      '<PERSON>',
      '<PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>'
    ],
    maxTeamSize: 5,
    tournamentFormats: ['BO1', 'BO3', 'BO5'],
    isActive: true
  },
  
  WILD_RIFT: {
    id: 'wild_rift',
    name: 'League of Legends: Wild Rift',
    shortName: 'WR',
    icon: '⚔️',
    ranks: [
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
      '<PERSON>',
      'Master',
      'Grandmaster',
      '<PERSON>'
    ],
    roles: [
      'Baron Lane',
      'Jungle',
      'Mid Lane',
      'Dragon Lane',
      'Support'
    ],
    maxTeamSize: 5,
    tournamentFormats: ['BO1', 'BO3', 'BO5'],
    isActive: true
  },
  
  DOTA2: {
    id: 'dota2',
    name: 'Dota 2',
    shortName: 'Dota2',
    icon: '🛡️',
    ranks: [
      'Herald',
      'Guardian',
      'Crusader',
      'Archon',
      'Legend',
      'Ancient',
      'Divine',
      'Immortal'
    ],
    roles: [
      'Carry',
      'Support',
      'Offlaner',
      'Mid',
      'Roamer'
    ],
    maxTeamSize: 5,
    tournamentFormats: ['BO1', 'BO3', 'BO5'],
    isActive: true
  },

  HONOR_OF_KINGS: {
    id: 'honor_of_kings',
    name: 'Honor of Kings',
    shortName: 'HOK',
    icon: '👑',
    ranks: [
      'Bronze',
      'Silver',
      'Gold',
      'Platinum',
      'Diamond',
      'Master',
      'Grandmaster',
      'King',
      'Glory King'
    ],
    roles: [
      'Tank',
      'Fighter',
      'Assassin',
      'Mage',
      'Marksman',
      'Support'
    ],
    maxTeamSize: 5,
    tournamentFormats: ['BO1', 'BO3', 'BO5'],
    isActive: true
  },
  
  CS2: {
    id: 'cs2',
    name: 'Counter-Strike 2',
    shortName: 'CS2',
    icon: '🔫',
    ranks: [
      'Silver I',
      'Silver II',
      'Silver III',
      'Silver IV',
      'Silver Elite',
      'Silver Elite Master',
      'Gold Nova I',
      'Gold Nova II',
      'Gold Nova III',
      'Gold Nova Master',
      'Master Guardian I',
      'Master Guardian II',
      'Master Guardian Elite',
      'Distinguished Master Guardian',
      'Legendary Eagle',
      'Legendary Eagle Master',
      'Supreme Master First Class',
      'The Global Elite'
    ],
    roles: [
      'Entry Fragger',
      'Support',
      'AWPer',
      'IGL',
      'Lurker'
    ],
    maxTeamSize: 5,
    tournamentFormats: ['BO1', 'BO3', 'BO5'],
    isActive: true
  }
} as const

export type GameId = keyof typeof SUPPORTED_GAMES
export type Game = typeof SUPPORTED_GAMES[GameId]

export const getGameById = (id: GameId): Game => SUPPORTED_GAMES[id]
export const getAllGames = (): Game[] => Object.values(SUPPORTED_GAMES)
export const getActiveGames = (): Game[] => getAllGames().filter(game => game.isActive)
