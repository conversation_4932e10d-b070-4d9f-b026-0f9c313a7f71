import { supabase } from './supabaseClient'

// Helper function to calculate user level based on wins and matches
const calculateUserLevel = (wins: number, totalMatches: number): number => {
  const baseLevel = Math.floor(wins / 5) + 1 // 1 level per 5 wins
  const bonusLevel = Math.floor(totalMatches / 10) // Bonus level per 10 matches played
  return Math.min(baseLevel + bonusLevel, 100) // Cap at level 100
}

export const dashboardService = {
  // Get user dashboard stats
  async getUserStats(userId: string) {
    try {
      // Get user basic stats
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (userError) throw userError

      // Get user statistics (with proper error handling for 406/RLS issues)
      let stats = null
      let statsError = null

      try {
        const result = await supabase
          .from('user_statistics')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle() // Use maybeSingle() instead of single() to handle missing records gracefully

        stats = result.data
        statsError = result.error
      } catch (error) {
        console.log('User statistics query failed, will create default:', error)
        statsError = { code: 'PGRST116' } // Simulate not found error
      }

      // If no stats exist or null result, create default ones
      if (!stats || (statsError && statsError.code === 'PGRST116')) {
        const { data: newStats, error: createError } = await supabase
          .from('user_statistics')
          .insert([{
            user_id: userId,
            current_streak: 0,
            longest_streak: 0,
            total_earnings: 0,
            monthly_earnings: 0,
            weekly_earnings: 0,
            daily_earnings: 0,
            rank_points: 0,
            achievements: []
          }])
          .select()
          .single()

        if (createError) throw createError
        return { data: { user, stats: newStats }, error: null }
      }

      if (statsError) throw statsError

      return { data: { user, stats }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get live matches for dashboard
  async getLiveMatches(limit = 5) {
    try {
      const { data, error } = await supabase
        .from('matches')
        .select(`
          *,
          host:users!host_id(username, first_name, last_name),
          participants:match_participants(count)
        `)
        .in('status', ['open', 'ongoing'])
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get leaderboard data
  async getLeaderboard(type: 'diamonds' | 'wins' | 'referrals' = 'diamonds', limit = 10) {
    try {
      let orderBy = 'diamond_balance'
      if (type === 'wins') orderBy = 'wins'
      if (type === 'referrals') {
        // For referrals, we need to count from referrals table
        const { data, error } = await supabase
          .from('users')
          .select(`
            id,
            username,
            first_name,
            last_name,
            diamond_balance,
            wins,
            total_matches,
            referrals:referrals!referrer_id(count)
          `)
          .order('diamond_balance', { ascending: false })
          .limit(limit)

        if (error) throw error
        return { data, error: null }
      }

      const { data, error } = await supabase
        .from('users')
        .select('id, username, first_name, last_name, diamond_balance, wins, total_matches')
        .order(orderBy, { ascending: false })
        .limit(limit)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's recent match history
  async getRecentMatches(userId: string, limit = 5) {
    try {
      const { data, error } = await supabase
        .from('match_history')
        .select(`
          *,
          match:matches(game, mode, pot_amount),
          user:users(username)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's active match participations
  async getActiveMatches(userId: string) {
    try {
      const { data, error } = await supabase
        .from('match_participants')
        .select(`
          *,
          match:matches(
            id,
            game,
            mode,
            pot_amount,
            entry_fee,
            status,
            current_players,
            max_players
          )
        `)
        .eq('user_id', userId)
        .in('match.status', ['open', 'ongoing'])

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's referral count
  async getUserReferrals(userId: string) {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .select('id')
        .eq('referrer_id', userId)

      if (error) throw error
      return { data: data?.length || 0, error: null }
    } catch (error) {
      return { data: 0, error }
    }
  },

  // Get user achievements
  async getUserAchievements(userId: string) {
    try {
      const { data, error } = await supabase
        .from('user_achievements')
        .select(`
          *,
          achievement:achievements(
            id,
            name,
            description,
            icon,
            type,
            requirement
          )
        `)
        .eq('user_id', userId)
        .eq('is_unlocked', true)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's group information
  async getUserGroup(userId: string) {
    try {
      const { data, error } = await supabase
        .from('group_members')
        .select(`
          *,
          group:community_groups(
            id,
            name,
            description,
            member_count,
            rank,
            is_private
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .single()

      if (error && error.code !== 'PGRST116') throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get detailed user profile for community features
  async getUserProfile(userId: string) {
    try {
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (userError) throw userError

      // Get user statistics
      const { data: stats } = await supabase
        .from('user_statistics')
        .select('*')
        .eq('user_id', userId)
        .maybeSingle()

      // Get user achievements
      const { data: achievements } = await supabase
        .from('user_achievements')
        .select(`
          *,
          achievement:achievements(name, description, icon)
        `)
        .eq('user_id', userId)
        .eq('is_unlocked', true)

      // Get user's group membership
      const { data: groupMembership } = await supabase
        .from('group_members')
        .select(`
          role,
          group:community_groups(name)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')

      // Get recent match history
      const { data: recentMatches } = await supabase
        .from('match_participants')
        .select(`
          result,
          match:matches(game, created_at, pot_amount)
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(5)

      return {
        data: {
          user: {
            id: user.id,
            username: user.username,
            avatar: user.username.substring(0, 2).toUpperCase(),
            level: calculateUserLevel(user.wins || 0, user.total_matches || 0),
            diamonds: user.diamond_balance || 0,
            wins: user.wins || 0,
            winRate: user.total_matches > 0 ? ((user.wins / user.total_matches) * 100) : 0,
            isOnline: true, // This would come from real-time presence
            role: user.is_referee ? 'moderator' : 'member'
          },
          stats: {
            totalMatches: user.total_matches || 0,
            monthlyWins: stats?.monthly_wins || 0,
            streak: stats?.current_streak || 0,
            achievements: (achievements || []).map(a => a.achievement?.name).filter(Boolean),
            recentMatches: recentMatches || []
          },
          social: {
            friends: [], // This would come from friends table
            groups: (groupMembership || []).map(g => (g as any).group?.name).filter(Boolean),
            reputation: stats?.reputation || 85
          }
        },
        error: null
      }
    } catch (error) {
      return { data: null, error }
    }
  }
}
