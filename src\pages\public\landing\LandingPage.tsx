import { Link } from "react-router-dom"
import {
  Trophy,
  Play,
  Users,
  Gamepad2,
  Shield,
  Wallet,
  Zap,
  CheckCircle,
  ArrowRight,
  Target,
  Star,
  TrendingUp,
  Crown,
  Gem,
  ShoppingCart,
  MessageSquare,
  Eye,
  Award,
  Sparkles,
  Rocket,
  Globe,
  Lock,
  Smartphone,
  Monitor,
  Headphones,
  Gift,
  BarChart3,
  DollarSign
} from "lucide-react"

export default function LandingPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200 sticky top-0 z-50">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                <Trophy className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Gambets
              </span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
                Home
              </Link>
              <Link to="/browse" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200 flex items-center space-x-1">
                
                Marketplace
              </Link>
              <Link to="/leaderboards" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
                Leaderboards
              </Link>
              <Link to="/rules" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
                Rules
              </Link>
              <Link to="/support" className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
                Support
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <button className="text-gray-700 hover:text-blue-600 font-medium transition-colors duration-200">
                  Login
                </button>
              </Link>
              <Link to="/signup">
                <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                  Sign Up
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-hidden flex items-center">
        {/* Animated Background */}
        <div className="absolute inset-0">
          {/* Geometric Shapes */}
          <div className="absolute top-20 left-20 w-32 h-32 border border-blue-400/30 rounded-lg rotate-45 animate-spin"></div>
          <div className="absolute bottom-32 right-20 w-24 h-24 border border-purple-400/30 rounded-full animate-pulse"></div>
          <div className="absolute top-1/2 left-10 w-16 h-16 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-lg rotate-12 animate-bounce"></div>
          <div className="absolute top-1/4 right-1/3 w-20 h-20 border-2 border-yellow-400/20 rounded-full animate-ping"></div>

          {/* Grid Pattern */}
          <div className="absolute inset-0 opacity-40">
            <div className="w-full h-full" style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
              backgroundRepeat: 'repeat'
            }}></div>
          </div>

          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 lg:px-6 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen py-20">
            {/* Left Content */}
            <div className="text-center lg:text-left">
              {/* Badge */}
              <div className="inline-flex items-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-400/30 rounded-full px-6 py-3 mb-8">
                <Sparkles className="w-5 h-5 mr-2 text-yellow-400" />
                <span className="text-blue-200 font-medium">🎮 Philippines' #1 Gaming Platform</span>
              </div>

              {/* Main Heading */}
              <h1 className="text-5xl md:text-6xl lg:text-7xl font-black mb-8 leading-tight">
                <span className="block text-white">Turn Your</span>
                <span className="block bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent">
                  Gaming Skills
                </span>
                <span className="block text-white">Into Real Money</span>
              </h1>

              {/* Subtitle */}
              <p className="text-xl md:text-2xl text-blue-100 mb-10 leading-relaxed max-w-2xl">
                Compete in <span className="text-yellow-400 font-semibold">Mobile Legends</span>,
                <span className="text-red-400 font-semibold"> Valorant</span>,
                <span className="text-green-400 font-semibold"> Dota 2</span>,
                <span className="text-orange-400 font-semibold"> Roblox</span> and more.
                Win diamonds, earn real cash with our secure referee system.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 mb-12">
                <Link to="/signup">
                  <button className="group relative bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-black font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 shadow-2xl overflow-hidden">
                    <div className="absolute inset-0 bg-white/20 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700 skew-x-12"></div>
                    <div className="relative flex items-center">
                      <Rocket className="w-6 h-6 mr-3 group-hover:animate-bounce" />
                      Start Earning Now
                      <ArrowRight className="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform" />
                    </div>
                  </button>
                </Link>
                <Link to="/browse">
                  <button className="group bg-blue-600/20 backdrop-blur-sm hover:bg-blue-600/30 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 border border-blue-400/30 hover:border-blue-400/50">
                    <div className="flex items-center">
                      <ShoppingCart className="w-6 h-6 mr-3" />
                      Marketplace
                      <Eye className="w-5 h-5 ml-3 group-hover:scale-110 transition-transform" />
                    </div>
                  </button>
                </Link>
                <Link to="/leaderboards">
                  <button className="group bg-white/10 backdrop-blur-sm hover:bg-white/20 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 border border-white/30 hover:border-white/50">
                    <div className="flex items-center">
                      <Trophy className="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform text-yellow-400" />
                      View Champions
                    </div>
                  </button>
                </Link>
              </div>

              {/* Trust Indicators */}
              <div className="flex flex-wrap gap-6 text-sm text-blue-200">
                <div className="flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span>100% Secure</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-yellow-400" />
                  <span>Instant Payouts</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-blue-400" />
                  <span>10K+ Active Players</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-5 h-5 text-purple-400" />
                  <span>Fair Play Guaranteed</span>
                </div>
              </div>
            </div>

            {/* Right Content - Game Cards */}
            <div className="relative">
              <div className="grid grid-cols-2 gap-6">
                {/* Mobile Legends */}
                <div className="group relative bg-gradient-to-br from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-400/30 rounded-2xl p-6 hover:scale-105 transition-all duration-300 cursor-pointer">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <Gamepad2 className="w-12 h-12 text-blue-400 mb-4 group-hover:scale-110 transition-transform" />
                    <h3 className="text-lg font-bold text-white mb-2">Mobile Legends</h3>
                    <p className="text-blue-200 text-sm">MOBA • 5v5 Battles</p>
                    <div className="mt-4 flex items-center text-yellow-400 text-sm">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>₱500-5000 per match</span>
                    </div>
                  </div>
                </div>

                {/* Valorant */}
                <div className="group relative bg-gradient-to-br from-red-600/20 to-pink-600/20 backdrop-blur-sm border border-red-400/30 rounded-2xl p-6 hover:scale-105 transition-all duration-300 cursor-pointer mt-8">
                  <div className="absolute inset-0 bg-gradient-to-br from-red-500/10 to-pink-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <Target className="w-12 h-12 text-red-400 mb-4 group-hover:scale-110 transition-transform" />
                    <h3 className="text-lg font-bold text-white mb-2">Valorant</h3>
                    <p className="text-red-200 text-sm">FPS • Tactical Shooter</p>
                    <div className="mt-4 flex items-center text-yellow-400 text-sm">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>₱1000-8000 per match</span>
                    </div>
                  </div>
                </div>

                {/* Dota 2 */}
                <div className="group relative bg-gradient-to-br from-green-600/20 to-teal-600/20 backdrop-blur-sm border border-green-400/30 rounded-2xl p-6 hover:scale-105 transition-all duration-300 cursor-pointer">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-teal-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <Shield className="w-12 h-12 text-green-400 mb-4 group-hover:scale-110 transition-transform" />
                    <h3 className="text-lg font-bold text-white mb-2">Dota 2</h3>
                    <p className="text-green-200 text-sm">MOBA • Strategy</p>
                    <div className="mt-4 flex items-center text-yellow-400 text-sm">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>₱800-6000 per match</span>
                    </div>
                  </div>
                </div>

                {/* Roblox */}
                <div className="group relative bg-gradient-to-br from-orange-600/20 to-yellow-600/20 backdrop-blur-sm border border-orange-400/30 rounded-2xl p-6 hover:scale-105 transition-all duration-300 cursor-pointer mt-8">
                  <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-yellow-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative">
                    <Zap className="w-12 h-12 text-orange-400 mb-4 group-hover:scale-110 transition-transform" />
                    <h3 className="text-lg font-bold text-white mb-2">Roblox</h3>
                    <p className="text-orange-200 text-sm">Platform • Multiple Games</p>
                    <div className="mt-4 flex items-center text-yellow-400 text-sm">
                      <DollarSign className="w-4 h-4 mr-1" />
                      <span>₱300-3000 per match</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 w-8 h-8 bg-yellow-400/20 rounded-full animate-ping"></div>
              <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-400/20 rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Stats Bar */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-20 max-w-4xl mx-auto">
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-black text-yellow-400 group-hover:scale-110 transition-transform">10K+</div>
              <div className="text-blue-200 text-sm font-medium">Active Gamers</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-black text-green-400 group-hover:scale-110 transition-transform">₱2M+</div>
              <div className="text-blue-200 text-sm font-medium">Total Winnings</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-black text-purple-400 group-hover:scale-110 transition-transform">500+</div>
              <div className="text-blue-200 text-sm font-medium">Daily Matches</div>
            </div>
            <div className="text-center group">
              <div className="text-3xl md:text-4xl font-black text-red-400 group-hover:scale-110 transition-transform">24/7</div>
              <div className="text-blue-200 text-sm font-medium">Live Support</div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get started in three simple steps and start winning diamonds today!
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Step 1 */}
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 text-center transform hover:scale-105 hover:-translate-y-2">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Users className="w-10 h-10 text-white group-hover:animate-pulse" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">1</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                Create or Join a Match
              </h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Browse available matches or create your own. Choose your game mode and stake your diamonds.
              </p>
              <div className="flex items-center justify-center">
                <span className="bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium flex items-center">
                  <Star className="w-4 h-4 mr-2" />
                  100 - 10,000 diamonds
                </span>
              </div>
            </div>

            {/* Step 2 */}
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 text-center transform hover:scale-105 hover:-translate-y-2">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Gamepad2 className="w-10 h-10 text-white group-hover:animate-pulse" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                Battle In-Game
              </h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                Play your match using your gaming skills. Submit results and proof when the game ends.
              </p>
              <div className="flex items-center justify-center">
                <span className="bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium flex items-center">
                  <Shield className="w-4 h-4 mr-2" />
                  Fair play guaranteed
                </span>
              </div>
            </div>

            {/* Step 3 */}
            <div className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 text-center transform hover:scale-105 hover:-translate-y-2">
              <div className="relative">
                <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                  <Trophy className="w-10 h-10 text-white group-hover:animate-pulse" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-600 rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-sm">3</span>
                </div>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors duration-300">
                Winner Gets the Pot
              </h3>
              <p className="text-gray-600 leading-relaxed mb-6">
                After verification, the winner receives all diamonds from the match pot directly to their wallet.
              </p>
              <div className="flex items-center justify-center">
                <span className="bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium flex items-center">
                  <Zap className="w-4 h-4 mr-2" />
                  Instant payout
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Supported Games Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Supported Games</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Battle across the most popular competitive games with
              <span className="text-blue-600 font-semibold"> millions of players worldwide</span>
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
            {/* Mobile Legends */}
            <div className="group relative bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 text-center hover:shadow-2xl transition-all duration-500 border border-gray-100 transform hover:scale-105 hover:-translate-y-2">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                <Gamepad2 className="w-12 h-12 text-white group-hover:animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                Mobile Legends
              </h3>
              <p className="text-gray-600 mb-4 text-lg">MOBA battles on mobile</p>
              <div className="space-y-3">
                <div className="bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium flex items-center justify-center">
                  <Users className="w-4 h-4 mr-2" />
                  1v1, 2v2, 5v5 supported
                </div>
                <div className="text-gray-500 text-sm flex items-center justify-center">
                  <TrendingUp className="w-4 h-4 mr-1" />
                  Most popular in Philippines
                </div>
              </div>
            </div>

            {/* Valorant */}
            <div className="group relative bg-gradient-to-br from-red-50 to-orange-50 rounded-2xl p-8 text-center hover:shadow-2xl transition-all duration-500 border border-gray-100 transform hover:scale-105 hover:-translate-y-2">
              <div className="w-24 h-24 bg-gradient-to-br from-red-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                <Target className="w-12 h-12 text-white group-hover:animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-red-600 transition-colors duration-300">
                Valorant
              </h3>
              <p className="text-gray-600 mb-4 text-lg">Tactical FPS action</p>
              <div className="space-y-3">
                <div className="bg-red-100 text-red-700 px-4 py-2 rounded-full text-sm font-medium flex items-center justify-center">
                  <Users className="w-4 h-4 mr-2" />
                  1v1, 2v2, 5v5 supported
                </div>
                <div className="text-gray-500 text-sm flex items-center justify-center">
                  <Trophy className="w-4 h-4 mr-1" />
                  Competitive esports scene
                </div>
              </div>
            </div>

            {/* Roblox */}
            <div className="group relative bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl p-8 text-center hover:shadow-2xl transition-all duration-500 border border-gray-100 transform hover:scale-105 hover:-translate-y-2">
              <div className="w-24 h-24 bg-gradient-to-br from-orange-500 to-yellow-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                <Zap className="w-12 h-12 text-white group-hover:animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-3 group-hover:text-orange-600 transition-colors duration-300">
                Roblox
              </h3>
              <p className="text-gray-600 mb-4 text-lg">Multiple game experiences</p>
              <div className="space-y-3">
                <div className="bg-orange-100 text-orange-700 px-4 py-2 rounded-full text-sm font-medium flex items-center justify-center">
                  <Users className="w-4 h-4 mr-2" />
                  Various game modes
                </div>
                <div className="text-gray-500 text-sm flex items-center justify-center">
                  <Star className="w-4 h-4 mr-1" />
                  Creative platform gaming
                </div>
              </div>
            </div>
          </div>

          {/* More Games Coming Soon */}
          <div className="text-center">
            <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-8 max-w-3xl mx-auto border border-gray-200 hover:shadow-lg transition-all duration-300">
              <div className="flex items-center justify-center mb-4">
                <Star className="w-6 h-6 text-blue-600 mr-2" />
                <h3 className="text-2xl font-bold text-gray-900">More Games Coming Soon</h3>
              </div>
              <p className="text-gray-600 mb-6">Wild Rift, Call of Duty, CS:GO and more competitive games</p>
              <div className="flex flex-wrap justify-center gap-3">
                <span className="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  Wild Rift
                </span>
                <span className="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  Call of Duty
                </span>
                <span className="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  CS:GO
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6">
              How to Start <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">Winning</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get started in 3 simple steps and start earning from your gaming skills
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Step 1 */}
            <div className="relative group">
              <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl p-8 text-center border-2 border-transparent group-hover:border-blue-200 transition-all duration-300 transform group-hover:scale-105">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <Users className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-sm">1</div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Create Account</h3>
                <p className="text-gray-600 leading-relaxed">
                  Sign up for free and verify your gaming accounts. Link your Mobile Legends, Valorant, Roblox, or Dota 2 profile.
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="relative group">
              <div className="bg-gradient-to-br from-green-50 to-teal-50 rounded-3xl p-8 text-center border-2 border-transparent group-hover:border-green-200 transition-all duration-300 transform group-hover:scale-105">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-r from-green-600 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <Gamepad2 className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-sm">2</div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Join Matches</h3>
                <p className="text-gray-600 leading-relaxed">
                  Browse available matches, choose your stake amount, and get matched with players of similar skill level.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="relative group">
              <div className="bg-gradient-to-br from-yellow-50 to-orange-50 rounded-3xl p-8 text-center border-2 border-transparent group-hover:border-yellow-200 transition-all duration-300 transform group-hover:scale-105">
                <div className="relative">
                  <div className="w-20 h-20 bg-gradient-to-r from-yellow-600 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300">
                    <Trophy className="w-10 h-10 text-white" />
                  </div>
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center text-black font-bold text-sm">3</div>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Win & Earn</h3>
                <p className="text-gray-600 leading-relaxed">
                  Play your match, submit results, and get verified by our referees. Winners receive diamonds instantly!
                </p>
              </div>
            </div>
          </div>

          {/* Process Flow */}
          <div className="mt-16 max-w-4xl mx-auto">
            <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl p-8 text-white text-center">
              <h3 className="text-2xl font-bold mb-6">🛡️ Referee Protection System</h3>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="flex items-center space-x-3">
                  <Shield className="w-8 h-8 text-yellow-400 flex-shrink-0" />
                  <div className="text-left">
                    <div className="font-semibold">Secure Escrow</div>
                    <div className="text-blue-100 text-sm">Stakes held safely</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Eye className="w-8 h-8 text-green-400 flex-shrink-0" />
                  <div className="text-left">
                    <div className="font-semibold">Match Verification</div>
                    <div className="text-blue-100 text-sm">Results reviewed</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="w-8 h-8 text-purple-400 flex-shrink-0" />
                  <div className="text-left">
                    <div className="font-semibold">Instant Payout</div>
                    <div className="text-blue-100 text-sm">Winners paid fast</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Why Choose Gambets?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              The most trusted and secure gaming platform in the Philippines
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Secure Payments */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
              <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <Wallet className="w-10 h-10 text-white group-hover:animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-green-600 transition-colors duration-300">
                Secure Payments
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Deposit diamonds safely using GCash, Maya, or bank transfer with encrypted transactions.
              </p>
              <div className="flex flex-wrap justify-center gap-2">
                <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">GCash</span>
                <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">Maya</span>
                <span className="bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-medium">Bank Transfer</span>
              </div>
            </div>

            {/* Fair Play */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <Shield className="w-10 h-10 text-white group-hover:animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors duration-300">
                Fair Play System
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                All match results are manually reviewed by our team to ensure fair play and prevent cheating.
              </p>
              <div className="flex justify-center">
                <span className="bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium flex items-center">
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Verified Results
                </span>
              </div>
            </div>

            {/* Fast Payouts */}
            <div className="group bg-white rounded-2xl p-8 text-center shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2">
              <div className="w-20 h-20 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
                <Zap className="w-10 h-10 text-white group-hover:animate-pulse" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4 group-hover:text-purple-600 transition-colors duration-300">
                Fast Payouts
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                Winners receive their diamond prizes within 24 hours of match verification and approval.
              </p>
              <div className="flex justify-center">
                <span className="bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium flex items-center">
                  <Zap className="w-4 h-4 mr-2" />
                  Under 24 hours
                </span>
              </div>
            </div>
          </div>

          {/* Additional Features Grid */}
          <div className="mt-16 grid md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {/* Marketplace */}
            <div className="bg-white rounded-xl p-6 text-center shadow-md hover:shadow-lg transition-all duration-300 group">
              <ShoppingCart className="w-8 h-8 text-orange-600 mx-auto mb-3 group-hover:scale-110 transition-transform" />
              <h4 className="font-bold text-gray-900 mb-2">Gaming Marketplace</h4>
              <p className="text-sm text-gray-600">Buy and sell gaming accounts safely with referee protection</p>
            </div>

            {/* Tournaments */}
            <div className="bg-white rounded-xl p-6 text-center shadow-md hover:shadow-lg transition-all duration-300 group">
              <Crown className="w-8 h-8 text-purple-600 mx-auto mb-3 group-hover:scale-110 transition-transform" />
              <h4 className="font-bold text-gray-900 mb-2">Tournaments</h4>
              <p className="text-sm text-gray-600">Join competitive tournaments with massive prize pools</p>
            </div>

            {/* Community */}
            <div className="bg-white rounded-xl p-6 text-center shadow-md hover:shadow-lg transition-all duration-300 group">
              <MessageSquare className="w-8 h-8 text-blue-600 mx-auto mb-3 group-hover:scale-110 transition-transform" />
              <h4 className="font-bold text-gray-900 mb-2">Gaming Community</h4>
              <p className="text-sm text-gray-600">Connect with fellow gamers and form teams</p>
            </div>

            {/* 24/7 Support */}
            <div className="bg-white rounded-xl p-6 text-center shadow-md hover:shadow-lg transition-all duration-300 group">
              <Headphones className="w-8 h-8 text-green-600 mx-auto mb-3 group-hover:scale-110 transition-transform" />
              <h4 className="font-bold text-gray-900 mb-2">24/7 Support</h4>
              <p className="text-sm text-gray-600">Round-the-clock customer support for all your needs</p>
            </div>
          </div>
        </div>
      </section>

      {/* Platform Benefits Section */}
      <section className="py-20 bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 text-white">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="bg-gradient-to-r from-yellow-400 to-orange-500 bg-clip-text text-transparent">
                Why Gamers Choose Us
              </span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Join the most trusted gaming platform in the Philippines
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Multi-Platform */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
              <Monitor className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-3">Multi-Platform Gaming</h3>
              <p className="text-gray-300">Play on mobile, PC, or console - we support all major gaming platforms</p>
            </div>

            {/* Instant Rewards */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
              <Gift className="w-12 h-12 text-green-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-3">Instant Rewards</h3>
              <p className="text-gray-300">Win diamonds instantly and withdraw to your preferred payment method</p>
            </div>

            {/* Global Community */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
              <Globe className="w-12 h-12 text-blue-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-3">Global Community</h3>
              <p className="text-gray-300">Connect with gamers worldwide and participate in international tournaments</p>
            </div>

            {/* Advanced Security */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
              <Lock className="w-12 h-12 text-red-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-3">Advanced Security</h3>
              <p className="text-gray-300">Bank-level encryption and security measures protect your account and funds</p>
            </div>

            {/* Real-time Analytics */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
              <BarChart3 className="w-12 h-12 text-purple-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-3">Performance Analytics</h3>
              <p className="text-gray-300">Track your gaming performance with detailed statistics and insights</p>
            </div>

            {/* Mobile Optimized */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
              <Smartphone className="w-12 h-12 text-orange-400 mx-auto mb-4" />
              <h3 className="text-xl font-bold mb-3">Mobile Optimized</h3>
              <p className="text-gray-300">Seamless gaming experience on any device with our responsive platform</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 relative overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-32 h-32 bg-white/10 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-white/5 rounded-full animate-bounce delay-500"></div>
          <div className="absolute top-1/3 right-1/4 w-24 h-24 bg-white/5 rounded-full animate-bounce delay-1500"></div>
        </div>

        <div className="container mx-auto px-4 lg:px-6 text-center relative z-10">
          <div className="animate-pulse">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">Ready to Start Winning?</h2>
          </div>
          <div className="animate-bounce">
            <p className="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
              Join thousands of players earning real money through skill-based gaming.
              <span className="text-white font-semibold"> Your gaming skills = Real rewards!</span>
            </p>
          </div>
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link to="/signup">
              <button className="group bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center">
                <Play className="w-5 h-5 mr-2 group-hover:animate-pulse" />
                Get Started Now
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
              </button>
            </Link>
            <Link to="/leaderboards">
              <button className="group border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 text-lg rounded-xl font-bold transition-all duration-300 transform hover:scale-105 flex items-center">
                <Trophy className="w-5 h-5 mr-2 group-hover:animate-bounce" />
                View Leaderboards
              </button>
            </Link>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">What Gamers Say</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Hear from our community of successful gamers
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">JM</span>
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-gray-900">Juan Miguel</h4>
                  <p className="text-sm text-gray-600">Mobile Legends Pro</p>
                </div>
              </div>
              <div className="flex mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 italic">
                "I've earned over ₱50,000 in just 3 months! The platform is secure and payouts are always on time. Best gaming platform in the Philippines!"
              </p>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-teal-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">AS</span>
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-gray-900">Anna Santos</h4>
                  <p className="text-sm text-gray-600">Valorant Champion</p>
                </div>
              </div>
              <div className="flex mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 italic">
                "Fair play system is amazing! No cheaters, just pure skill-based competition. I've won multiple tournaments and the community is fantastic."
              </p>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">RC</span>
                </div>
                <div className="ml-4">
                  <h4 className="font-bold text-gray-900">Rico Cruz</h4>
                  <p className="text-sm text-gray-600">Dota 2 Expert</p>
                </div>
              </div>
              <div className="flex mb-4">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>
              <p className="text-gray-700 italic">
                "The marketplace feature is incredible! I sold my old account safely and bought a better one. Referee system gives me peace of mind."
              </p>
            </div>
          </div>

          {/* Trust Badges */}
          <div className="mt-16 flex flex-wrap justify-center items-center gap-8 opacity-60">
            <div className="flex items-center space-x-2">
              <Shield className="w-6 h-6 text-green-600" />
              <span className="text-gray-600 font-medium">SSL Secured</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-6 h-6 text-blue-600" />
              <span className="text-gray-600 font-medium">Verified Platform</span>
            </div>
            <div className="flex items-center space-x-2">
              <Award className="w-6 h-6 text-purple-600" />
              <span className="text-gray-600 font-medium">Award Winning</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="w-6 h-6 text-orange-600" />
              <span className="text-gray-600 font-medium">10K+ Users</span>
            </div>
          </div>
        </div>
      </section>

      {/* Marketplace Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-pink-600 rounded-2xl mb-6 shadow-lg">
              <ShoppingCart className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Gaming <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">Marketplace</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Buy and sell gaming accounts, rare skins, and in-game items safely with our secure marketplace
            </p>
          </div>

          {/* Marketplace Preview */}
          <div className="grid lg:grid-cols-3 gap-8 mb-12">
            {/* Featured Account */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
              <div className="relative mb-4">
                <img
                  src="https://images.unsplash.com/photo-**********-adc38448a05e?w=400&h=200&fit=crop"
                  alt="Gaming Account"
                  className="w-full h-48 object-cover rounded-xl"
                />
                <div className="absolute top-3 right-3 bg-green-500 text-white px-2 py-1 rounded-lg text-xs font-bold">
                  VERIFIED
                </div>
              </div>
              <h3 className="font-bold text-lg mb-2">Mobile Legends Mythic Account</h3>
              <p className="text-gray-600 text-sm mb-4">Level 85 • 150+ Skins • All Heroes</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Gem className="w-4 h-4 text-blue-600" />
                  <span className="font-bold text-blue-600">15,000 Diamonds</span>
                </div>
                <Link
                  to="/login"
                  state={{ from: 'marketplace', message: 'Sign in to view item details and make purchases!' }}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 inline-block text-center"
                >
                  View Details
                </Link>
              </div>
            </div>

            {/* Featured Skin */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
              <div className="relative mb-4">
                <img
                  src="https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=200&fit=crop"
                  alt="Gaming Skin"
                  className="w-full h-48 object-cover rounded-xl"
                />
                <div className="absolute top-3 right-3 bg-purple-500 text-white px-2 py-1 rounded-lg text-xs font-bold">
                  LEGENDARY
                </div>
              </div>
              <h3 className="font-bold text-lg mb-2">Valorant Phantom Skin</h3>
              <p className="text-gray-600 text-sm mb-4">Rare Collection • Limited Edition</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Gem className="w-4 h-4 text-blue-600" />
                  <span className="font-bold text-blue-600">2,500 Diamonds</span>
                </div>
                <Link
                  to="/login"
                  state={{ from: 'marketplace', message: 'Sign in to view item details and make purchases!' }}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 inline-block text-center"
                >
                  View Details
                </Link>
              </div>
            </div>

            {/* Featured Service */}
            <div className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
              <div className="relative mb-4">
                <img
                  src="https://images.unsplash.com/photo-1556438064-2d7646166914?w=400&h=200&fit=crop"
                  alt="Gaming Service"
                  className="w-full h-48 object-cover rounded-xl"
                />
                <div className="absolute top-3 right-3 bg-yellow-500 text-white px-2 py-1 rounded-lg text-xs font-bold">
                  POPULAR
                </div>
              </div>
              <h3 className="font-bold text-lg mb-2">Rank Boosting Service</h3>
              <p className="text-gray-600 text-sm mb-4">Professional • Fast • Secure</p>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Gem className="w-4 h-4 text-blue-600" />
                  <span className="font-bold text-blue-600">5,000 Diamonds</span>
                </div>
                <Link
                  to="/login"
                  state={{ from: 'marketplace', message: 'Sign in to view item details and make purchases!' }}
                  className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-4 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-300 inline-block text-center"
                >
                  View Details
                </Link>
              </div>
            </div>
          </div>

          {/* Marketplace Features */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Shield className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Secure Transactions</h3>
              <p className="text-gray-600">All transactions are protected by our escrow system and verified referees</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <CheckCircle className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Verified Sellers</h3>
              <p className="text-gray-600">All sellers are verified and rated by our community for your safety</p>
            </div>
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-3">Instant Delivery</h3>
              <p className="text-gray-600">Get your purchased items and accounts delivered instantly after verification</p>
            </div>
          </div>

          {/* CTA */}
          <div className="text-center">
            <p className="text-gray-600 mb-6">Ready to explore our marketplace?</p>
            <Link
              to="/signup"
              className="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-bold text-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
            >
              <span>Sign Up to Browse Marketplace</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            {/* Company Info */}
            <div>
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                  <Trophy className="w-7 h-7 text-white" />
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                  Gambets
                </span>
              </div>
              <p className="text-gray-400 mb-6 leading-relaxed">
                The premier skill-based gaming platform for competitive players in the Philippines.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="group w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-all duration-300 transform hover:scale-110">
                  <Users className="w-5 h-5 group-hover:animate-pulse" />
                </a>
                <a href="#" className="group w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-all duration-300 transform hover:scale-110">
                  <Star className="w-5 h-5 group-hover:animate-pulse" />
                </a>
                <a href="#" className="group w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center hover:bg-blue-600 transition-all duration-300 transform hover:scale-110">
                  <Gamepad2 className="w-5 h-5 group-hover:animate-pulse" />
                </a>
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h3 className="text-lg font-bold mb-6">Quick Links</h3>
              <ul className="space-y-3">
                <li><Link to="/" className="text-gray-400 hover:text-white transition-colors">Home</Link></li>
                <li><Link to="/leaderboards" className="text-gray-400 hover:text-white transition-colors">Leaderboards</Link></li>
                <li><Link to="/rules" className="text-gray-400 hover:text-white transition-colors">Game Rules</Link></li>
                <li><Link to="/support" className="text-gray-400 hover:text-white transition-colors">Support Center</Link></li>
              </ul>
            </div>

            {/* Legal */}
            <div>
              <h3 className="text-lg font-bold mb-6">Legal & Policies</h3>
              <ul className="space-y-3">
                <li><Link to="/terms" className="text-gray-400 hover:text-white transition-colors">Terms of Service</Link></li>
                <li><Link to="/privacy" className="text-gray-400 hover:text-white transition-colors">Privacy Policy</Link></li>
                <li><Link to="/responsible-gaming" className="text-gray-400 hover:text-white transition-colors">Responsible Gaming</Link></li>
                <li><Link to="/login" className="text-gray-400 hover:text-white transition-colors">Become a Referee</Link></li>
              </ul>
            </div>

            {/* Contact & Support */}
            <div>
              <h3 className="text-lg font-bold mb-6">Contact & Support</h3>
              <ul className="space-y-3">
                <li>
                  <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-white transition-colors">
                    <EMAIL>
                  </a>
                </li>
                <li>
                  <a href="tel:+639123456789" className="text-gray-400 hover:text-white transition-colors">
                    +63 ************
                  </a>
                </li>
                <li><span className="text-gray-400">24/7 Live Chat</span></li>
                <li><span className="text-gray-400">Response time: {"< 1 hour"}</span></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center">
              <p className="text-gray-400 mb-4 md:mb-0">
                © {new Date().getFullYear()} Gambets. All rights reserved. Made in the Philippines.
              </p>
              <div className="flex flex-wrap gap-6 text-sm">
                <Link to="/privacy" className="text-gray-400 hover:text-white transition-colors">Privacy</Link>
                <Link to="/terms" className="text-gray-400 hover:text-white transition-colors">Terms</Link>
                <Link to="/responsible-gaming" className="text-gray-400 hover:text-white transition-colors">Responsible Gaming</Link>
                <Link to="/admin" className="text-gray-600 hover:text-gray-400 transition-colors opacity-50 hover:opacity-100">Admin</Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
