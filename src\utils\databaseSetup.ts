// 🗄️ DATABASE SETUP UTILITIES
// Utilities for database initialization and health checks

import { supabase } from '../services/supabaseClient'

export interface DatabaseStatus {
  isConnected: boolean
  tablesExist: boolean
  isReady: boolean
  missingTables: string[]
  setupInstructions?: string[]
  error?: string
}

export interface SystemStats {
  totalConflicts: number
  totalRecusals: number
  activeRestrictions: number
  systemHealth: string
}

/**
 * Check database connection and basic table existence
 */
export async function checkDatabaseStatus(): Promise<DatabaseStatus> {
  try {
    // Test basic connection
    const { data: _data, error } = await supabase
      .from('users')
      .select('id')
      .limit(1)

    if (error) {
      return {
        isConnected: false,
        tablesExist: false,
        isReady: false,
        missingTables: ['users'],
        setupInstructions: ['Check database connection', 'Verify table permissions'],
        error: error.message
      }
    }

    return {
      isConnected: true,
      tablesExist: true,
      isReady: true,
      missingTables: []
    }
  } catch (err) {
    return {
      isConnected: false,
      tablesExist: false,
      isReady: false,
      missingTables: ['unknown'],
      setupInstructions: ['Check database connection'],
      error: (err as Error).message
    }
  }
}

/**
 * Get system statistics for admin dashboard
 */
export async function getSystemStats(): Promise<SystemStats> {
  try {
    // Get referee conflicts count
    const { data: conflicts } = await supabase
      .from('referee_conflict_logs')
      .select('id')

    // Get referee recusals count
    const { data: recusals } = await supabase
      .from('referee_recusals')
      .select('id')

    // Get active restrictions count
    const { data: restrictions } = await supabase
      .from('referee_betting_restrictions')
      .select('id')
      .eq('is_active', true)

    return {
      totalConflicts: conflicts?.length || 0,
      totalRecusals: recusals?.length || 0,
      activeRestrictions: restrictions?.length || 0,
      systemHealth: 'healthy'
    }
  } catch (err) {
    console.error('Error getting system stats:', err)
    return {
      totalConflicts: 0,
      totalRecusals: 0,
      activeRestrictions: 0,
      systemHealth: 'error'
    }
  }
}

/**
 * Initialize database tables if they don't exist
 * Note: In production, this should be handled by migrations
 */
export async function initializeDatabase(): Promise<boolean> {
  try {
    // This is a placeholder - in production, use proper migrations
    console.log('Database initialization should be handled by migrations')
    return true
  } catch (err) {
    console.error('Database initialization error:', err)
    return false
  }
}

/**
 * Check if all required tables exist
 */
export async function checkRequiredTables(): Promise<{ [key: string]: boolean }> {
  const requiredTables = [
    'users',
    'matches',
    'transactions',
    'referee_recusals',
    'referee_conflict_logs',
    'referee_betting_restrictions',
    'referee_conflict_settings'
  ]

  const results: { [key: string]: boolean } = {}

  for (const table of requiredTables) {
    try {
      const { error } = await supabase
        .from(table)
        .select('id')
        .limit(1)
      
      results[table] = !error
    } catch {
      results[table] = false
    }
  }

  return results
}
