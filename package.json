{"name": "gambets", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "@vercel/node": "^5.3.11", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-helmet-async": "^2.0.5", "react-router-dom": "^6.20.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react-swc": "^3.10.2", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}}