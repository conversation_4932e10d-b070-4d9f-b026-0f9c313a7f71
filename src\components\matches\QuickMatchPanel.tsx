import { useState, useEffect, useCallback } from 'react'
import { Zap, Users, X, Search, Trophy } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'
import { quickMatchService, type QuickMatchPreferences, type QuickMatchQueue } from '../../services/quickMatch'
import { supabase } from '../../services/supabase'
import { GAMES } from '../../constants'
import { ProgressBar } from '../ui/GamingComponents' // TODO: Confirm GamingButton usage

interface QuickMatchPanelProps {
  isOpen: boolean
  onClose: () => void
  onMatchFound?: (matchId: string) => void
}

export default function QuickMatchPanel({ isOpen, onClose, onMatchFound }: QuickMatchPanelProps) {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  
  // State
  const [preferences, setPreferences] = useState<QuickMatchPreferences>({
    game: 'Mobile Legends: Bang Bang',
    format: '5v5',
    maxEntryFee: 100,
    preferredRoles: [],
    voiceChatRequired: false
  })
  
  const [isSearching, setIsSearching] = useState(false)
  const [currentQueue, setCurrentQueue] = useState<QuickMatchQueue | null>(null)
  const [estimatedWaitTime, setEstimatedWaitTime] = useState<number>(0)
  const [elapsedTime, setElapsedTime] = useState<number>(0)
  const [queueStats, setQueueStats] = useState<Record<string, any>>({})

  // Check for existing queue on mount
  useEffect(() => {
    if (isOpen && user?.id) {
      checkExistingQueue()
      loadQueueStats()
    }
  }, [isOpen, user?.id])

  // Keyboard support
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [isOpen, onClose])

  // Timer for elapsed time
  useEffect(() => {
    let interval: NodeJS.Timeout
    
    if (isSearching && currentQueue) {
      interval = setInterval(() => {
        const queueTime = new Date(currentQueue.queue_time).getTime()
        const elapsed = Math.floor((Date.now() - queueTime) / 1000)
        setElapsedTime(elapsed)
      }, 1000)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isSearching, currentQueue])

  // Cleanup subscription on unmount or when search stops
  useEffect(() => {
    return () => {
      if (currentQueue && (currentQueue as any).unsubscribe) {
        (currentQueue as any).unsubscribe()
      }
    }
  }, [currentQueue])

  const checkExistingQueue = async () => {
    if (!user?.id) return
    
    try {
      const queue = await quickMatchService.getUserActiveQueue(user.id)
      if (queue) {
        setCurrentQueue(queue)
        setIsSearching(true)
        setPreferences({
          game: queue.game,
          format: queue.format as any,
          maxEntryFee: queue.max_entry_fee,
          preferredRoles: queue.preferred_roles,
          voiceChatRequired: queue.voice_chat_required
        })
      }
    } catch (error) {
      console.error('Error checking existing queue:', error)
    }
  }

  const loadQueueStats = async () => {
    try {
      const stats = await quickMatchService.getQueueStats()
      setQueueStats(stats)
    } catch (error) {
      console.error('Error loading queue stats:', error)
    }
  }

  const handleStartSearch = async () => {
    if (!user) return

    setIsSearching(true)
    
    try {
      const result = await quickMatchService.joinQuickMatchQueue(user, preferences)
      
      if (result.success) {
        if (result.match_id) {
          // Instant match found!
          addNotification({
            type: 'success',
            title: 'Match Found!',
            message: result.message
          })
          onMatchFound?.(result.match_id)
          onClose()
        } else {
          // Added to queue
          setEstimatedWaitTime(result.estimated_wait_time || 300)
          addNotification({
            type: 'info',
            title: 'Searching for Match',
            message: result.message
          })
          
          // Set up real-time subscription for queue updates
          if (result.queue_id) {
            const unsubscribe = subscribeToQueueUpdates(result.queue_id)
            // Store cleanup function for later use
            setCurrentQueue({ ...currentQueue, unsubscribe } as any)
          }
        }
      } else {
        setIsSearching(false)
        addNotification({
          type: 'error',
          title: 'Matchmaking Failed',
          message: result.message
        })
      }
    } catch (error) {
      setIsSearching(false)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to start matchmaking. Please try again.'
      })
    }
  }

  const handleCancelSearch = async () => {
    if (!user?.id) return

    try {
      const success = await quickMatchService.leaveQueue(user.id)
      if (success) {
        setIsSearching(false)
        setCurrentQueue(null)
        setElapsedTime(0)
        addNotification({
          type: 'info',
          title: 'Search Cancelled',
          message: 'You have left the matchmaking queue.'
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to cancel search. Please try again.'
      })
    }
  }

  // Real-time subscription for queue updates
  const subscribeToQueueUpdates = useCallback((queueId: string) => {
    if (!user) return

    // Subscribe to quick_match_queue table changes for this user
    const channel = supabase
      .channel(`queue-${user.id}`)
      .on('postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'quick_match_queue',
          filter: `user_id=eq.${user.id}`
        },
        async (payload) => {
          const updatedQueue = payload.new as any

          if (updatedQueue.status === 'matched') {
            // Match found! Get the match details
            try {
              const matchResult = await quickMatchService.getMatchFromQueue(queueId)

              addNotification({
                type: 'success',
                title: 'Match Found!',
                message: 'Players matched! Redirecting to match lobby...'
              })

              setIsSearching(false)
              setCurrentQueue(null)
              onClose()

              // Optionally redirect to match page
              if (matchResult?.match_id) {
                // Navigate to match or refresh matches list
                window.location.reload() // Simple refresh for now
              }
            } catch (error) {
              console.error('Error getting match from queue:', error)
            }
          } else if (updatedQueue.status === 'expired') {
            // Queue expired
            addNotification({
              type: 'warning',
              title: 'Search Expired',
              message: 'No match found within time limit. Try again!'
            })
            setIsSearching(false)
            setCurrentQueue(null)
          }
        }
      )
      .subscribe()

    // Cleanup function
    return () => {
      supabase.removeChannel(channel)
    }
  }, [user, addNotification, onClose])

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getQueueCount = (game: string, format: string): number => {
    return queueStats[game]?.[format] || 0
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={(e) => {
        // Close modal when clicking backdrop
        if (e.target === e.currentTarget) {
          onClose()
        }
      }}
    >
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-md sm:max-w-lg max-h-[90vh] overflow-hidden"
           onClick={(e) => e.stopPropagation()}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <div className="flex items-center space-x-3">
            <Zap className="w-6 h-6" />
            <h2 className="text-xl font-bold">Quick Match</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            title="Close"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {!isSearching ? (
            <>
              {/* Game Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Game
                </label>
                <select
                  value={preferences.game}
                  onChange={(e) => setPreferences(prev => ({ ...prev, game: e.target.value }))}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {Object.entries(GAMES).map(([key, game]) => (
                    <option key={key} value={game.name}>
                      {game.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Format Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Format
                </label>
                <div className="grid grid-cols-3 sm:grid-cols-5 gap-2">
                  {(['1v1', '2v2', '3v3', '4v4', '5v5'] as const).map((format) => (
                    <button
                      key={format}
                      onClick={() => setPreferences(prev => ({ ...prev, format }))}
                      className={`p-2 sm:p-3 text-sm font-medium rounded-lg border transition-colors ${
                        preferences.format === format
                          ? 'bg-blue-100 text-blue-700 border-blue-300'
                          : 'bg-gray-50 text-gray-700 border-gray-300 hover:bg-gray-100'
                      }`}
                    >
                      {format}
                      {getQueueCount(preferences.game, format) > 0 && (
                        <div className="text-xs text-green-600 mt-1">
                          {getQueueCount(preferences.game, format)} waiting
                        </div>
                      )}
                    </button>
                  ))}
                </div>
              </div>

              {/* Entry Fee */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Entry Fee: {preferences.maxEntryFee} 💎
                </label>
                <input
                  type="range"
                  min="10"
                  max="500"
                  step="10"
                  value={preferences.maxEntryFee}
                  onChange={(e) => setPreferences(prev => ({ 
                    ...prev, 
                    maxEntryFee: parseInt(e.target.value) 
                  }))}
                  className="w-full"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>10 💎</span>
                  <span>500 💎</span>
                </div>
              </div>

              {/* Voice Chat */}
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Voice Chat Required</span>
                <button
                  onClick={() => setPreferences(prev => ({ 
                    ...prev, 
                    voiceChatRequired: !prev.voiceChatRequired 
                  }))}
                  className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                    preferences.voiceChatRequired ? 'bg-blue-600' : 'bg-gray-200'
                  }`}
                >
                  <span
                    className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      preferences.voiceChatRequired ? 'translate-x-6' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              {/* User Balance Display */}
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Your Balance:</span>
                  <span className="font-semibold text-gray-900">{user?.diamond_balance || 0} 💎</span>
                </div>
              </div>

              {/* Start Button */}
              <button
                onClick={handleStartSearch}
                disabled={!user || (preferences.maxEntryFee > (user.diamond_balance || 0))}
                className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 shadow-lg hover:shadow-xl disabled:cursor-not-allowed"
              >
                <Search className="w-5 h-5" />
                <span>Find Match</span>
              </button>

              {preferences.maxEntryFee > (user?.diamond_balance || 0) && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-sm text-red-700 text-center">
                    ⚠️ Insufficient balance. You need {preferences.maxEntryFee} 💎 but only have {user?.diamond_balance || 0} 💎
                  </p>
                </div>
              )}
            </>
          ) : (
            <>
              {/* Searching State */}
              <div className="text-center space-y-6">
                {/* Close button for searching state */}
                <div className="flex justify-end">
                  <button
                    onClick={onClose}
                    className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                    title="Close (search will continue in background)"
                  >
                    <X className="w-5 h-5 text-gray-600" />
                  </button>
                </div>

                <div className="w-20 h-20 mx-auto bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center">
                  <Search className="w-10 h-10 text-blue-600 animate-pulse" />
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Searching for Players</h3>
                  <p className="text-sm text-gray-600">
                    {preferences.game} • {preferences.format} • Up to {preferences.maxEntryFee} 💎
                  </p>
                </div>

                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Time Elapsed</span>
                    <span>{formatTime(elapsedTime)}</span>
                  </div>
                  <ProgressBar
                    current={elapsedTime}
                    max={estimatedWaitTime}
                    color="blue"
                    showNumbers={false}
                  />
                  <p className="text-xs text-gray-500 text-center">
                    Estimated wait: {formatTime(estimatedWaitTime)}
                  </p>
                </div>

                {/* Queue Info */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{getQueueCount(preferences.game, preferences.format)} in queue</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Trophy className="w-4 h-4" />
                      <span>Skill matched</span>
                    </div>
                  </div>
                </div>

                {/* Cancel Button */}
                <div className="space-y-2">
                  <button
                    onClick={handleCancelSearch}
                    className="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                  >
                    <X className="w-4 h-4" />
                    <span>Cancel Search</span>
                  </button>
                  <p className="text-xs text-gray-500 text-center">
                    You can close this window and the search will continue in the background
                  </p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
