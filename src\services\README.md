# 🔧 SERVICES

**Purpose:** Backend integration and API services

## 🎯 Overview

The services directory contains all backend integration code, API clients, and data management services. This is the central hub for all external communications and data operations.

## 📁 File Structure

```
services/
├── supabase.ts          # 🗄️ Supabase client and database services
├── auth.ts              # 🔐 Authentication services (Future)
├── payments.ts          # 💳 Payment processing services (Future)
├── notifications.ts     # 📧 Notification services (Future)
└── README.md           # 📖 This documentation
```

## 🗄️ supabase.ts - Database & Authentication Service

**Purpose:** Complete Supabase integration with all database operations

### 🔧 Configuration
```typescript
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY
export const supabase = createClient(supabaseUrl, supabaseAnonKey)
```

### 📊 Data Types
- **User** - User profile and account information
- **Match** - Gaming match details and status
- **Transaction** - Diamond transactions and payments

### 🔐 Authentication Services
- `authService.signUp()` - User registration
- `authService.signIn()` - User login
- `authService.signOut()` - User logout
- `authService.getCurrentUser()` - Get current user
- `authService.getSession()` - Get current session

### 👤 User Services
- `userService.getProfile()` - Get user profile
- `userService.updateProfile()` - Update user information
- `userService.updateDiamondBalance()` - Manage diamond balance

### 🎮 Match Services
- `matchService.getMatches()` - Get available matches
- `matchService.createMatch()` - Create new match
- `matchService.joinMatch()` - Join existing match

### 💎 Transaction Services
- `transactionService.createTransaction()` - Record transactions
- `transactionService.getUserTransactions()` - Get user transaction history

### 🔄 Real-time Subscriptions
- `subscriptions.subscribeToMatches()` - Live match updates
- `subscriptions.subscribeToProfile()` - Live profile updates

## 🔗 Backend Connection Status

### ✅ CONNECTED TO SUPABASE
Your Gambets platform is **already connected** to Supabase with:

#### 🗄️ Database Tables (Expected)
- **profiles** - User profile information
- **matches** - Gaming match data
- **match_participants** - Match participation records
- **transactions** - Diamond transaction history
- **referees** - Referee information and status

#### 🔐 Authentication
- Email/password authentication
- Social login support (Google, Facebook)
- Session management
- Role-based access control

#### 📡 Real-time Features
- Live match updates
- Real-time chat (if implemented)
- Live leaderboard updates
- Instant notifications

## 🚀 Setup Instructions

### 1. Environment Configuration
Create a `.env` file in the project root:
```bash
cp .env.example .env
```

### 2. Supabase Project Setup
1. Go to [supabase.com](https://supabase.com)
2. Create a new project or use existing
3. Get your project URL and anon key
4. Update `.env` file with your credentials

### 3. Database Schema
Run the following SQL in your Supabase SQL editor:

```sql
-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Create profiles table
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  email TEXT,
  username TEXT UNIQUE,
  first_name TEXT,
  last_name TEXT,
  diamond_balance INTEGER DEFAULT 0,
  wins INTEGER DEFAULT 0,
  losses INTEGER DEFAULT 0,
  mlbb_id TEXT,
  valorant_id TEXT,
  cod_id TEXT,
  role TEXT DEFAULT 'user',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (id)
);

-- Create matches table
CREATE TABLE matches (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title TEXT NOT NULL,
  game TEXT NOT NULL,
  mode TEXT NOT NULL,
  entry_fee INTEGER NOT NULL,
  prize_pool INTEGER NOT NULL,
  max_players INTEGER NOT NULL,
  current_players INTEGER DEFAULT 0,
  status TEXT DEFAULT 'open',
  start_time TIMESTAMP WITH TIME ZONE,
  creator_id UUID REFERENCES profiles(id),
  creator_username TEXT,
  room_code TEXT,
  description TEXT,
  min_rank TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create match_participants table
CREATE TABLE match_participants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  match_id UUID REFERENCES matches(id) ON DELETE CASCADE,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(match_id, user_id)
);

-- Create transactions table
CREATE TABLE transactions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  amount INTEGER NOT NULL,
  status TEXT DEFAULT 'pending',
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4. Row Level Security Policies
```sql
-- Profiles policies
CREATE POLICY "Users can view own profile" ON profiles
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON profiles
  FOR UPDATE USING (auth.uid() = id);

-- Matches policies
CREATE POLICY "Anyone can view matches" ON matches
  FOR SELECT TO authenticated USING (true);

CREATE POLICY "Users can create matches" ON matches
  FOR INSERT TO authenticated WITH CHECK (auth.uid() = creator_id);

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON transactions
  FOR SELECT USING (auth.uid() = user_id);
```

## 🔧 Development Guidelines

### Adding New Services
1. Create service file in `services/` directory
2. Export service functions and types
3. Include error handling
4. Add TypeScript types
5. Document API functions
6. Add unit tests

### Error Handling Pattern
```typescript
export const serviceFunction = async (params) => {
  try {
    const { data, error } = await supabase
      .from('table')
      .select('*')
    
    if (error) throw error
    return { data, error: null }
  } catch (error) {
    return { data: null, error }
  }
}
```

### Type Safety
- Define TypeScript interfaces for all data types
- Use proper typing for function parameters and returns
- Export types for use in components

## 🔐 Security Best Practices

### API Security
- Use Row Level Security (RLS) policies
- Validate all inputs
- Sanitize user data
- Use parameterized queries

### Authentication
- Implement proper session management
- Use secure token storage
- Handle token refresh
- Implement logout functionality

### Data Protection
- Encrypt sensitive data
- Use HTTPS for all communications
- Implement proper CORS policies
- Follow GDPR compliance

## 📊 Performance Optimization

### Caching Strategy
- Cache frequently accessed data
- Implement proper cache invalidation
- Use browser storage appropriately
- Optimize API call frequency

### Database Optimization
- Use proper indexes
- Optimize query performance
- Implement pagination
- Use database functions where appropriate

## 🧪 Testing

### Unit Testing
- Test all service functions
- Mock external dependencies
- Test error scenarios
- Validate data transformations

### Integration Testing
- Test API integrations
- Verify database operations
- Test authentication flows
- Validate real-time features

## 📈 Monitoring & Analytics

### Performance Monitoring
- Track API response times
- Monitor error rates
- Measure database performance
- Track user engagement

### Error Tracking
- Log all errors
- Implement error reporting
- Monitor system health
- Track user issues

## 🚀 Future Enhancements

### Planned Services
- **Payment Processing** - GCash, Maya, bank transfers
- **Email Notifications** - Automated email system
- **Push Notifications** - Real-time user notifications
- **File Upload** - Profile pictures and match screenshots
- **Analytics** - User behavior and platform analytics
