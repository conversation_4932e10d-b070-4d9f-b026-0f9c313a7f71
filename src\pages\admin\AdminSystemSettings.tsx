import { useState, useEffect } from 'react'
import {
  Settings, Save, Refresh<PERSON>w, Trash, Plus, Diamond, Users, Shield, Bell, Sliders
} from 'lucide-react'
import { supabase } from '../../services/supabaseClient'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'

interface SystemSetting {
  id: string
  setting_key: string
  setting_value: any
  description: string
  updated_by: string
  updated_at: string
  updater?: {
    username: string
  }
}

interface SettingCategory {
  name: string
  icon: React.ElementType
  description: string
  settings: string[]
}

export default function AdminSystemSettings() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [settings, setSettings] = useState<SystemSetting[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState<string | null>(null)
  const [editedSettings, setEditedSettings] = useState<Record<string, any>>({})
  const [activeCategory, setActiveCategory] = useState<string>('general')
  const [, setShowAddSetting] = useState(false)
  const [,] = useState({
    setting_key: '',
    setting_value: '',
    description: ''
  })

  // Define setting categories
  const categories: SettingCategory[] = [
    {
      name: 'general',
      icon: Settings,
      description: 'General platform settings',
      settings: ['platform_name', 'platform_description', 'maintenance_mode', 'maintenance_message']
    },
    {
      name: 'diamonds',
      icon: Diamond,
      description: 'Diamond economy settings',
      settings: ['diamond_exchange_rate', 'min_deposit_amount', 'max_deposit_amount', 'min_withdrawal_amount', 'withdrawal_fee_percentage']
    },
    {
      name: 'matches',
      icon: Users,
      description: 'Match settings and rules',
      settings: ['default_match_fee', 'max_match_participants', 'match_creation_cooldown', 'match_join_cooldown', 'match_auto_cancel_minutes']
    },
    {
      name: 'referees',
      icon: Shield,
      description: 'Referee system settings',
      settings: ['referee_conflict_cooldown_days', 'referee_match_cooldown_hours', 'referee_auto_assign', 'referee_commission_percentage']
    },
    {
      name: 'notifications',
      icon: Bell,
      description: 'Notification settings',
      settings: ['email_notifications_enabled', 'push_notifications_enabled', 'match_notifications_enabled', 'system_notifications_enabled']
    },
    {
      name: 'security',
      icon: Sliders,
      description: 'Security settings',
      settings: ['max_login_attempts', 'password_reset_expiry_hours', 'session_timeout_minutes', 'require_email_verification']
    }
  ]

  useEffect(() => {
    loadSettings()
  }, [])

  const loadSettings = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('system_settings')
        .select(`
          *,
          updater:users!updated_by(username)
        `)
        .order('setting_key')

      if (error) throw error
      
      // Initialize edited settings with current values
      const initialEditedSettings: Record<string, any> = {}
      data?.forEach(setting => {
        let value = setting.setting_value
        // Parse JSON values if needed
        if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
          try {
            value = JSON.parse(value)
          } catch (e) {
            console.error('Error parsing JSON setting value:', e)
          }
        }
        initialEditedSettings[setting.setting_key] = value
      })
      
      setSettings(data || [])
      setEditedSettings(initialEditedSettings)
    } catch (error) {
      console.error('Error loading settings:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load system settings'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSettingChange = (key: string, value: any) => {
    setEditedSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const saveSetting = async (key: string) => {
    try {
      setSaving(key)
      
      // Find the setting in the settings array
      const setting = settings.find(s => s.setting_key === key)
      
      // Prepare the value (convert objects/arrays to JSON strings)
      let valueToSave = editedSettings[key]
      if (typeof valueToSave === 'object') {
        valueToSave = JSON.stringify(valueToSave)
      }
      
      if (setting) {
        // Update existing setting
        const { error } = await supabase
          .from('system_settings')
          .update({
            setting_value: valueToSave,
            updated_by: user!.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', setting.id)

        if (error) throw error
      } else {
        // Create new setting if it doesn't exist
        const { error } = await supabase
          .from('system_settings')
          .insert({
            setting_key: key,
            setting_value: valueToSave,
            description: 'Added via admin interface',
            updated_by: user!.id
          })

        if (error) throw error
      }

      addNotification({
        type: 'success',
        title: 'Setting Updated',
        message: `"${key}" has been updated successfully`
      })

      // Reload settings to get the updated data
      await loadSettings()
    } catch (error) {
      console.error('Error saving setting:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to save setting'
      })
    } finally {
      setSaving(null)
    }
  }

  // const handleAddSetting = async () => { // TODO: Confirm usage
  //   try {
  //     if (!newSetting.setting_key || !newSetting.description) {
  //       addNotification({
  //         type: 'error',
  //         title: 'Validation Error',
  //         message: 'Setting key and description are required'
  //       })
  //       return
  //     }
  //
  //     // Check if setting already exists
  //     if (settings.some(s => s.setting_key === newSetting.setting_key)) {
  //       addNotification({
  //         type: 'error',
  //         title: 'Duplicate Setting',
  //         message: 'A setting with this key already exists'
  //       })
  //       return
  //     }
  //
  //     setSaving('new')
  //
  //     // Prepare the value (convert objects/arrays to JSON strings if needed)
  //     let valueToSave = newSetting.setting_value
  //     try {
  //       // Try to parse as JSON if it looks like JSON
  //       if (valueToSave.startsWith('{') || valueToSave.startsWith('[')) {
  //         JSON.parse(valueToSave)
  //       }
  //     } catch (e) {
  //       addNotification({
  //         type: 'error',
  //         title: 'Invalid JSON',
  //         message: 'The setting value is not valid JSON'
  //       })
  //       setSaving(null)
  //       return
  //     }
  //
  //     const { error } = await supabase
  //       .from('system_settings')
  //       .insert({
  //         setting_key: newSetting.setting_key,
  //         setting_value: valueToSave,
  //         description: newSetting.description,
  //         updated_by: user!.id
  //       })
  //
  //     if (error) throw error
  //
  //     addNotification({
  //       type: 'success',
  //       title: 'Setting Added',
  //       message: `"${newSetting.setting_key}" has been added successfully`
  //     })
  //
  //     // Reset form and reload settings
  //     setNewSetting({
  //       setting_key: '',
  //       setting_value: '',
  //       description: ''
  //     })
  //     setShowAddSetting(false)
  //     await loadSettings()
  //   } catch (error) {
  //     console.error('Error adding setting:', error)
  //     addNotification({
  //       type: 'error',
  //       title: 'Error',
  //       message: 'Failed to add setting'
  //     })
  //   } finally {
  //     setSaving(null)
  //   }
  // }

  const deleteSetting = async (id: string, key: string) => {
    if (!window.confirm(`Are you sure you want to delete the setting "${key}"?`)) {
      return
    }

    try {
      setSaving(key)
      
      const { error } = await supabase
        .from('system_settings')
        .delete()
        .eq('id', id)

      if (error) throw error

      addNotification({
        type: 'success',
        title: 'Setting Deleted',
        message: `"${key}" has been deleted successfully`
      })

      // Reload settings
      await loadSettings()
    } catch (error) {
      console.error('Error deleting setting:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to delete setting'
      })
    } finally {
      setSaving(null)
    }
  }

  // Get settings for the active category
  const activeCategorySettings = settings.filter(setting => {
    const category = categories.find(cat => cat.settings.includes(setting.setting_key))
    return category?.name === activeCategory
  })

  // Get uncategorized settings
  // const uncategorizedSettings = settings.filter(setting => { // TODO: Confirm usage
  //   return !categories.some(cat => cat.settings.includes(setting.setting_key))
  // })

  // Render setting input based on value type
  const renderSettingInput = (setting: SystemSetting) => {
    const value = editedSettings[setting.setting_key]
    const valueType = typeof value

    if (valueType === 'boolean' || setting.setting_key.includes('_enabled') || setting.setting_key.includes('_mode')) {
      return (
        <div className="flex items-center space-x-3">
          <button
            onClick={() => handleSettingChange(setting.setting_key, !value)}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              value ? 'bg-blue-600' : 'bg-gray-200'
            }`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                value ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
          <span className="text-sm font-medium text-gray-700">
            {value ? 'Enabled' : 'Disabled'}
          </span>
        </div>
      )
    }

    if (valueType === 'number' || !isNaN(Number(value))) {
      return (
        <input
          type="number"
          value={value}
          onChange={(e) => handleSettingChange(setting.setting_key, Number(e.target.value))}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      )
    }

    if (valueType === 'object') {
      return (
        <textarea
          value={JSON.stringify(value, null, 2)}
          onChange={(e) => {
            try {
              const parsed = JSON.parse(e.target.value)
              handleSettingChange(setting.setting_key, parsed)
            } catch (error) {
              // Don't update if JSON is invalid
              console.error('Invalid JSON:', error)
            }
          }}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-lg font-mono text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        />
      )
    }

    return (
      <input
        type="text"
        value={value}
        onChange={(e) => handleSettingChange(setting.setting_key, e.target.value)}
        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">System Settings</h1>
            <p className="text-indigo-100 mt-1">Configure platform behavior and features</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAddSetting(true)}
              className="bg-white/20 hover:bg-white/30 rounded-lg px-4 py-2 transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Setting</span>
            </button>
            <button
              onClick={loadSettings}
              disabled={loading}
              className="bg-white/20 hover:bg-white/30 rounded-lg px-4 py-2 transition-colors flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Category Navigation */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Setting Categories</h2>
        </div>
        <div className="p-4">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {categories.map((category) => {
              const Icon = category.icon
              return (
                <button
                  key={category.name}
                  onClick={() => setActiveCategory(category.name)}
                  className={`p-3 rounded-lg text-left transition-colors ${
                    activeCategory === category.name
                      ? 'bg-blue-100 border-2 border-blue-500 text-blue-900'
                      : 'bg-gray-50 border-2 border-transparent text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Icon className="w-5 h-5 mb-2" />
                  <div className="text-sm font-medium capitalize">{category.name}</div>
                  <div className="text-xs text-gray-500 mt-1">{category.description}</div>
                </button>
              )
            })}
          </div>
        </div>
      </div>

      {/* Settings List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 capitalize">
            {activeCategory} Settings
          </h2>
          <p className="text-gray-600 mt-1">
            {categories.find(cat => cat.name === activeCategory)?.description}
          </p>
        </div>

        <div className="divide-y divide-gray-200">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading settings...</p>
            </div>
          ) : activeCategorySettings.length === 0 ? (
            <div className="p-8 text-center">
              <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No settings found</h3>
              <p className="text-gray-600">No settings found for this category.</p>
            </div>
          ) : (
            activeCategorySettings.map((setting) => (
              <div key={setting.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 mr-6">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{setting.setting_key}</h3>
                      {JSON.stringify(editedSettings[setting.setting_key]) !== JSON.stringify(setting.setting_value) && (
                        <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
                          MODIFIED
                        </span>
                      )}
                    </div>

                    <p className="text-gray-600 mb-4">{setting.description}</p>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Current Value
                        </label>
                        {renderSettingInput(setting)}
                      </div>

                      <div className="text-xs text-gray-500">
                        Last updated by {setting.updater?.username || 'Unknown'} on{' '}
                        {new Date(setting.updated_at).toLocaleString()}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => saveSetting(setting.setting_key)}
                      disabled={saving === setting.setting_key || JSON.stringify(editedSettings[setting.setting_key]) === JSON.stringify(setting.setting_value)}
                      className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                    >
                      {saving === setting.setting_key ? (
                        <RefreshCw className="w-4 h-4 animate-spin" />
                      ) : (
                        <Save className="w-4 h-4" />
                      )}
                      <span>{saving === setting.setting_key ? 'Saving...' : 'Save'}</span>
                    </button>

                    <button
                      onClick={() => deleteSetting(setting.id, setting.setting_key)}
                      disabled={saving === setting.setting_key}
                      className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                    >
                      <Trash className="w-4 h-4" />
                      <span>Delete</span>
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
