// 🔐 ADMIN LOGIN PAGE
// Secure login specifically for admin access

import React, { useState } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { Crown, Shield, Lock, Eye, EyeOff, AlertTriangle, CheckCircle } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'
import { supabase } from '../../services/supabaseClient'

const AdminLogin: React.FC = () => {
  const { signIn } = useAuth()
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [isVerifyingAdmin, setIsVerifyingAdmin] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)

    try {
      // Step 1: Regular authentication using AuthContext
      await signIn(formData.email, formData.password)

      // Step 2: Get current user after successful login
      // We need to wait a moment for the auth state to update
      setTimeout(async () => {
        try {
          setIsVerifyingAdmin(true)

          // Get current user from Supabase
          const { data: { user }, error: userError } = await supabase.auth.getUser()

          if (userError || !user) {
            setError('Authentication failed. Please try again.')
            setIsLoading(false)
            setIsVerifyingAdmin(false)
            return
          }

          // Step 3: Verify admin privileges
          const isAdmin = await AdminService.isAdmin(user.id)

          if (isAdmin) {
            // Step 4: Update admin login timestamp
            await AdminService.updateAdminLogin(user.id)

            // Step 5: Log admin login
            await AdminService.logAdminAction(
              user.id,
              'ADMIN_LOGIN_SUCCESS',
              'system',
              user.id,
              { login_method: 'admin_portal', ip_address: 'unknown' }
            )

            // Step 6: Redirect to admin dashboard
            navigate('/admin-dashboard-2024')
          } else {
            // Log unauthorized access attempt
            await AdminService.logAdminAction(
              user.id,
              'UNAUTHORIZED_ADMIN_LOGIN_ATTEMPT',
              'system',
              user.id,
              { attempted_email: formData.email }
            )

            setError('Access denied. You do not have administrative privileges.')
          }
        } catch (adminError: any) {
          console.error('Admin verification error:', adminError)
          setError('Failed to verify admin privileges. Please try again.')
        } finally {
          setIsLoading(false)
          setIsVerifyingAdmin(false)
        }
      }, 1000) // Wait 1 second for auth state to update

    } catch (error: any) {
      console.error('Admin login error:', error)
      setError('Login failed. Please check your credentials and try again.')
      setIsLoading(false)
      setIsVerifyingAdmin(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="relative w-full max-w-md">
        {/* Admin Login Card */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-10 h-10 text-black font-bold" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">Admin Portal</h1>
            <p className="text-blue-200">Secure access for administrators only</p>
          </div>

          {/* Security Notice */}
          <div className="bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 text-yellow-300">
              <Shield className="w-5 h-5" />
              <span className="font-medium">High Security Zone</span>
            </div>
            <p className="text-yellow-200 text-sm mt-1">
              All admin access attempts are logged and monitored for security purposes.
            </p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-blue-200 mb-2">
                Admin Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                required
                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all"
                placeholder="<EMAIL>"
              />
            </div>

            {/* Password Field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-blue-200 mb-2">
                Admin Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all pr-12"
                  placeholder="Enter your secure password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-4">
                <div className="flex items-center space-x-2 text-red-300">
                  <AlertTriangle className="w-5 h-5" />
                  <span className="font-medium">Access Denied</span>
                </div>
                <p className="text-red-200 text-sm mt-1">{error}</p>
              </div>
            )}

            {/* Login Button */}
            <button
              type="submit"
              disabled={isLoading || isVerifyingAdmin}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent transition-all disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Authenticating...</span>
                </div>
              ) : isVerifyingAdmin ? (
                <div className="flex items-center justify-center space-x-2">
                  <Shield className="w-5 h-5" />
                  <span>Verifying Admin Access...</span>
                </div>
              ) : (
                <div className="flex items-center justify-center space-x-2">
                  <Lock className="w-5 h-5" />
                  <span>Secure Admin Login</span>
                </div>
              )}
            </button>
          </form>

          {/* Footer */}
          <div className="mt-8 pt-6 border-t border-white/10">
            <div className="text-center">
              <Link
                to="/"
                className="text-blue-300 hover:text-white transition-colors text-sm"
              >
                ← Back to Main Site
              </Link>
            </div>
            <div className="text-center mt-4">
              <p className="text-blue-300 text-xs">
                Need admin access? Contact your system administrator.
              </p>
            </div>
          </div>
        </div>

        {/* Security Features */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <Shield className="w-6 h-6 text-blue-400 mx-auto mb-2" />
            <p className="text-blue-200 text-sm font-medium">Multi-Level Security</p>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <Lock className="w-6 h-6 text-green-400 mx-auto mb-2" />
            <p className="text-blue-200 text-sm font-medium">Encrypted Sessions</p>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <CheckCircle className="w-6 h-6 text-purple-400 mx-auto mb-2" />
            <p className="text-blue-200 text-sm font-medium">Audit Logging</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminLogin
