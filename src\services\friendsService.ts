// ============================================================================
// FRIENDS & MESSAGING SERVICE
// ============================================================================

import { supabase } from './supabase'

export interface Friend {
  id: string
  username: string
  avatar?: string
  isOnline: boolean
  lastSeen?: string
  status?: 'online' | 'away' | 'busy' | 'offline'
  gameStatus?: {
    game: string
    status: string
  }
  mutualFriends: number
  joinedDate: string
  level: number
  rank?: string
  badges?: string[]
  unreadCount?: number
  lastMessage?: {
    content: string
    timestamp: string
    isFromMe: boolean
  }
}

export interface FriendRequest {
  id: string
  username: string
  avatar?: string
  sentAt: string
  mutualFriends: number
  level: number
  type: 'incoming' | 'outgoing'
  message?: string
}

export interface PrivateMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  messageType: 'text' | 'image' | 'file' | 'game_invite' | 'gift' | 'sticker'
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed'
  timestamp: string
  replyToMessageId?: string
  attachmentUrl?: string
  reactions?: MessageReaction[]
}

export interface MessageReaction {
  id: string
  messageId: string
  userId: string
  reaction: string
  timestamp: string
}

class FriendsService {
  // ============================================================================
  // FRIENDS MANAGEMENT
  // ============================================================================

  // Get user's friends list
  async getFriends(userId: string): Promise<{ data: Friend[] | null, error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_user_friends', {
        user_uuid: userId
      })

      if (error) throw error

      // Transform data to Friend interface
      const friends: Friend[] = (data || []).map((friend: any) => ({
        id: friend.friend_id,
        username: friend.username,
        isOnline: friend.is_online || false,
        lastSeen: friend.last_seen,
        status: friend.is_online ? 'online' : 'offline',
        mutualFriends: 0, // TODO: Calculate mutual friends
        joinedDate: friend.friendship_created_at,
        level: friend.level || 1,
        rank: friend.rank,
        badges: friend.badges || [],
        unreadCount: 0 // TODO: Calculate unread messages
      }))

      return { data: friends, error: null }
    } catch (error) {
      console.error('Error getting friends:', error)
      return { data: null, error }
    }
  }

  // Send friend request
  async sendFriendRequest(senderId: string, receiverUsername: string, message?: string): Promise<{ data: any, error: any }> {
    try {
      // First, find the user by username
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('username', receiverUsername)
        .single()

      if (userError || !userData) {
        return { data: null, error: 'User not found' }
      }

      // Check if friendship already exists
      const { data: existingFriendship } = await supabase
        .from('friendships')
        .select('id')
        .or(`and(user_id.eq.${senderId},friend_id.eq.${userData.id}),and(user_id.eq.${userData.id},friend_id.eq.${senderId})`)
        .single()

      if (existingFriendship) {
        return { data: null, error: 'Already friends or request pending' }
      }

      // Create friend request
      const { data, error } = await supabase
        .from('friend_requests')
        .insert([{
          sender_id: senderId,
          receiver_id: userData.id,
          message: message || null,
          status: 'pending'
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error sending friend request:', error)
      return { data: null, error }
    }
  }

  // Get friend requests
  async getFriendRequests(userId: string): Promise<{ data: FriendRequest[] | null, error: any }> {
    try {
      const { data, error } = await supabase
        .from('friend_requests')
        .select(`
          id,
          sender_id,
          receiver_id,
          message,
          created_at,
          status,
          sender:sender_id(username, level, avatar),
          receiver:receiver_id(username, level, avatar)
        `)
        .or(`sender_id.eq.${userId},receiver_id.eq.${userId}`)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })

      if (error) throw error

      const requests: FriendRequest[] = (data || []).map((request: any) => ({
        id: request.id,
        username: request.sender_id === userId ? request.receiver.username : request.sender.username,
        avatar: request.sender_id === userId ? request.receiver.avatar : request.sender.avatar,
        sentAt: request.created_at,
        mutualFriends: 0, // TODO: Calculate mutual friends
        level: request.sender_id === userId ? request.receiver.level : request.sender.level,
        type: request.sender_id === userId ? 'outgoing' : 'incoming',
        message: request.message
      }))

      return { data: requests, error: null }
    } catch (error) {
      console.error('Error getting friend requests:', error)
      return { data: null, error }
    }
  }

  // Accept friend request
  async acceptFriendRequest(requestId: string, userId: string): Promise<{ data: any, error: any }> {
    try {
      // Get the friend request
      const { data: request, error: requestError } = await supabase
        .from('friend_requests')
        .select('sender_id, receiver_id')
        .eq('id', requestId)
        .eq('receiver_id', userId)
        .single()

      if (requestError || !request) {
        return { data: null, error: 'Friend request not found' }
      }

      // Create friendship (both directions)
      const { error: friendshipError } = await supabase
        .from('friendships')
        .insert([
          {
            user_id: request.sender_id,
            friend_id: request.receiver_id,
            status: 'accepted',
            initiated_by: request.sender_id,
            accepted_at: new Date().toISOString()
          },
          {
            user_id: request.receiver_id,
            friend_id: request.sender_id,
            status: 'accepted',
            initiated_by: request.sender_id,
            accepted_at: new Date().toISOString()
          }
        ])

      if (friendshipError) throw friendshipError

      // Update friend request status
      const { data, error } = await supabase
        .from('friend_requests')
        .update({
          status: 'accepted',
          responded_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error accepting friend request:', error)
      return { data: null, error }
    }
  }

  // Decline friend request
  async declineFriendRequest(requestId: string, userId: string): Promise<{ data: any, error: any }> {
    try {
      const { data, error } = await supabase
        .from('friend_requests')
        .update({
          status: 'declined',
          responded_at: new Date().toISOString()
        })
        .eq('id', requestId)
        .eq('receiver_id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error declining friend request:', error)
      return { data: null, error }
    }
  }

  // Remove friend
  async removeFriend(userId: string, friendId: string): Promise<{ data: any, error: any }> {
    try {
      const { error } = await supabase
        .from('friendships')
        .delete()
        .or(`and(user_id.eq.${userId},friend_id.eq.${friendId}),and(user_id.eq.${friendId},friend_id.eq.${userId})`)

      if (error) throw error
      return { data: true, error: null }
    } catch (error) {
      console.error('Error removing friend:', error)
      return { data: null, error }
    }
  }

  // ============================================================================
  // MESSAGING
  // ============================================================================

  // Send private message
  async sendMessage(senderId: string, receiverId: string, content: string, messageType: string = 'text'): Promise<{ data: PrivateMessage | null, error: any }> {
    try {
      const { data, error } = await supabase
        .from('private_messages')
        .insert([{
          sender_id: senderId,
          receiver_id: receiverId,
          content,
          message_type: messageType,
          status: 'sent'
        }])
        .select()
        .single()

      if (error) throw error

      const message: PrivateMessage = {
        id: data.id,
        senderId: data.sender_id,
        receiverId: data.receiver_id,
        content: data.content,
        messageType: data.message_type,
        status: data.status,
        timestamp: data.created_at,
        replyToMessageId: data.reply_to_message_id,
        attachmentUrl: data.attachment_url
      }

      return { data: message, error: null }
    } catch (error) {
      console.error('Error sending message:', error)
      return { data: null, error }
    }
  }

  // Get conversation messages
  async getConversationMessages(userId: string, friendId: string, limit: number = 50, offset: number = 0): Promise<{ data: PrivateMessage[] | null, error: any }> {
    try {
      const { data, error } = await supabase.rpc('get_conversation_messages', {
        user1_uuid: userId,
        user2_uuid: friendId,
        limit_count: limit,
        offset_count: offset
      })

      if (error) throw error

      const messages: PrivateMessage[] = (data || []).map((msg: any) => ({
        id: msg.message_id,
        senderId: msg.sender_id,
        receiverId: msg.receiver_id,
        content: msg.content,
        messageType: msg.message_type,
        status: msg.status,
        timestamp: msg.created_at,
        replyToMessageId: msg.reply_to_message_id
      }))

      return { data: messages.reverse(), error: null } // Reverse to show oldest first
    } catch (error) {
      console.error('Error getting conversation messages:', error)
      return { data: null, error }
    }
  }

  // Mark messages as read
  async markMessagesAsRead(userId: string, friendId: string): Promise<{ data: any, error: any }> {
    try {
      const { data, error } = await supabase
        .from('private_messages')
        .update({
          status: 'read',
          read_at: new Date().toISOString()
        })
        .eq('sender_id', friendId)
        .eq('receiver_id', userId)
        .in('status', ['sent', 'delivered'])

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error marking messages as read:', error)
      return { data: null, error }
    }
  }

  // Get unread message count
  async getUnreadMessageCount(userId: string): Promise<{ data: number, error: any }> {
    try {
      const { count, error } = await supabase
        .from('private_messages')
        .select('*', { count: 'exact', head: true })
        .eq('receiver_id', userId)
        .in('status', ['sent', 'delivered'])

      if (error) throw error
      return { data: count || 0, error: null }
    } catch (error) {
      console.error('Error getting unread message count:', error)
      return { data: 0, error }
    }
  }

  // Block user
  async blockUser(blockerId: string, blockedId: string, reason?: string): Promise<{ data: any, error: any }> {
    try {
      const { data, error } = await supabase
        .from('user_blocks')
        .insert([{
          blocker_id: blockerId,
          blocked_id: blockedId,
          reason: reason || null
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Error blocking user:', error)
      return { data: null, error }
    }
  }

  // Unblock user
  async unblockUser(blockerId: string, blockedId: string): Promise<{ data: any, error: any }> {
    try {
      const { error } = await supabase
        .from('user_blocks')
        .delete()
        .eq('blocker_id', blockerId)
        .eq('blocked_id', blockedId)

      if (error) throw error
      return { data: true, error: null }
    } catch (error) {
      console.error('Error unblocking user:', error)
      return { data: null, error }
    }
  }
}

export const friendsService = new FriendsService()
export default friendsService
