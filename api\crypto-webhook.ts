import { VercelRequest, VercelResponse } from '@vercel/node'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL || ''
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY || ''

const supabase = createClient(supabaseUrl, supabaseServiceKey)

// NowPayments webhook handler
export default async function handler(req: VercelRequest, res: VercelResponse) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    console.log('🔔 Crypto webhook received:', req.body)

    const { 
      payment_id, 
      payment_status, 
      pay_amount, 
      pay_currency,
      price_amount,
      price_currency,
      order_id,
      outcome_amount,
      outcome_currency
    } = req.body

    // Validate required fields
    if (!payment_id || !payment_status) {
      console.error('❌ Missing required webhook fields')
      return res.status(400).json({ error: 'Missing required fields' })
    }

    console.log('📊 Processing payment:', {
      payment_id,
      payment_status,
      pay_amount,
      pay_currency,
      order_id
    })

    // Update payment status in database
    const { data: payment, error: updateError } = await supabase
      .from('crypto_payments')
      .update({
        payment_status,
        confirmed_at: payment_status === 'finished' ? new Date().toISOString() : null,
        pay_amount: pay_amount || null,
        pay_currency: pay_currency || null,
        outcome_amount: outcome_amount || null,
        outcome_currency: outcome_currency || null
      })
      .eq('payment_id', payment_id)
      .select()
      .single()

    if (updateError) {
      console.error('❌ Error updating payment:', updateError)
      return res.status(500).json({ error: 'Failed to update payment' })
    }

    if (!payment) {
      console.error('❌ Payment not found:', payment_id)
      return res.status(404).json({ error: 'Payment not found' })
    }

    console.log('✅ Payment updated:', payment)

    // If payment is confirmed, add diamonds to user account
    if (payment_status === 'finished') {
      console.log('💎 Processing successful payment for user:', payment.user_id)

      // Get current user balance
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', payment.user_id)
        .single()

      if (userError) {
        console.error('❌ Error fetching user:', userError)
        return res.status(500).json({ error: 'Failed to fetch user' })
      }

      // Calculate new balance (1 PHP = 1 Diamond)
      const currentBalance = userData.diamond_balance || 0
      const diamondsToAdd = payment.diamonds || Math.floor((price_amount || payment.price_usd || 0) * 55) // Convert USD to PHP then to diamonds
      const newBalance = currentBalance + diamondsToAdd

      // Update user balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({ 
          diamond_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', payment.user_id)

      if (balanceError) {
        console.error('❌ Error updating balance:', balanceError)
        return res.status(500).json({ error: 'Failed to update balance' })
      }

      // Create transaction record
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert([{
          user_id: payment.user_id,
          type: 'deposit',
          amount: diamondsToAdd,
          balance_after: newBalance,
          description: `Crypto deposit via ${pay_currency?.toUpperCase() || 'crypto'} - ${diamondsToAdd} diamonds`,
          status: 'completed',
          payment_method: 'crypto',
          payment_reference: payment_id,
          created_at: new Date().toISOString()
        }])

      if (transactionError) {
        console.error('❌ Error creating transaction:', transactionError)
        // Don't return error here as the payment was successful
      }

      console.log('✅ Successfully processed payment:', {
        user_id: payment.user_id,
        diamonds_added: diamondsToAdd,
        new_balance: newBalance,
        payment_id
      })
    }

    // Return success response
    res.status(200).json({ 
      success: true, 
      message: 'Webhook processed successfully',
      payment_id,
      status: payment_status
    })

  } catch (error) {
    console.error('💥 Webhook processing error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
