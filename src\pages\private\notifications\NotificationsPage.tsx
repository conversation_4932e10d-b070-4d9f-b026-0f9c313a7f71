import { useState, useEffect } from 'react'
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Filter,
  Search,
  Trash2,
  Set<PERSON>s,
  AlertCircle,
  Trophy,
  Gem,
  Users,
  Shield,
  ShoppingCart,
  Calendar,
  ExternalLink,
  Loader2
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'
import { notificationService } from '../../../services/notifications'
import { Notification } from '../../../types'
import { useNotifications } from '../../../components/common/NotificationSystem'

export default function NotificationsPage() {
  const { user } = useAuth()
  const { showSuccess, showError } = useNotifications()
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [unreadCount, setUnreadCount] = useState(0)
  const [filter, setFilter] = useState<'all' | 'unread' | string>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    if (user?.id) {
      loadNotifications()
      loadUnreadCount()
      
      // Subscribe to real-time notifications
      const subscription = notificationService.subscribeToNotifications(user.id, (payload) => {
        if (payload.eventType === 'INSERT') {
          setNotifications(prev => [payload.new, ...prev])
          setUnreadCount(prev => prev + 1)
        } else if (payload.eventType === 'UPDATE') {
          setNotifications(prev => 
            prev.map(n => n.id === payload.new.id ? payload.new : n)
          )
          if (payload.new.is_read && !payload.old.is_read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          }
        }
      })

      return () => {
        subscription.unsubscribe()
      }
    }
  }, [user?.id])

  const loadNotifications = async () => {
    if (!user?.id) return
    
    setIsLoading(true)
    try {
      const { data, error } = await notificationService.getUserNotifications(user.id, {
        limit: 50,
        unreadOnly: filter === 'unread',
        type: filter !== 'all' && filter !== 'unread' ? filter : undefined
      })

      if (error) {
        showError('Error loading notifications', (error as any)?.message || 'Unknown error')
      } else {
        setNotifications(data || [])
      }
    } catch (error) {
      showError('Error loading notifications')
    } finally {
      setIsLoading(false)
    }
  }

  const loadUnreadCount = async () => {
    if (!user?.id) return
    
    try {
      const { count } = await notificationService.getUnreadCount(user.id)
      setUnreadCount(count)
    } catch (error) {
      console.error('Error loading unread count:', error)
    }
  }

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      const { error } = await notificationService.markAsRead(notificationId)
      if (error) {
        showError('Error marking notification as read')
      } else {
        showSuccess('Notification marked as read')
      }
    } catch (error) {
      showError('Error marking notification as read')
    }
  }

  const handleMarkAllAsRead = async () => {
    if (!user?.id) return
    
    try {
      const { error } = await notificationService.markAllAsRead(user.id)
      if (error) {
        showError('Error marking all notifications as read')
      } else {
        showSuccess('All notifications marked as read')
        setUnreadCount(0)
      }
    } catch (error) {
      showError('Error marking all notifications as read')
    }
  }

  const handleDelete = async (notificationId: string) => {
    try {
      const { error } = await notificationService.deleteNotification(notificationId)
      if (error) {
        showError('Error deleting notification')
      } else {
        showSuccess('Notification deleted')
        setNotifications(prev => prev.filter(n => n.id !== notificationId))
      }
    } catch (error) {
      showError('Error deleting notification')
    }
  }

  const handleCreateSamples = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      const { error } = await notificationService.createSampleNotifications(user.id)
      if (error) {
        showError('Error creating sample notifications')
      } else {
        showSuccess('Sample notifications created!')
        loadNotifications() // Refresh the list
      }
    } catch (error) {
      showError('Error creating sample notifications')
    } finally {
      setIsLoading(false)
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'match_invite':
      case 'match_start':
      case 'match_result':
        return <Trophy className="w-5 h-5 text-yellow-600" />
      case 'payment':
        return <Gem className="w-5 h-5 text-blue-600" />
      case 'tournament':
        return <Calendar className="w-5 h-5 text-purple-600" />
      case 'community':
        return <Users className="w-5 h-5 text-green-600" />
      case 'referee':
        return <Shield className="w-5 h-5 text-orange-600" />
      case 'marketplace':
        return <ShoppingCart className="w-5 h-5 text-indigo-600" />
      case 'system':
      default:
        return <AlertCircle className="w-5 h-5 text-gray-600" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'match_invite':
      case 'match_start':
      case 'match_result':
        return 'border-l-yellow-500 bg-yellow-50'
      case 'payment':
        return 'border-l-blue-500 bg-blue-50'
      case 'tournament':
        return 'border-l-purple-500 bg-purple-50'
      case 'community':
        return 'border-l-green-500 bg-green-50'
      case 'referee':
        return 'border-l-orange-500 bg-orange-50'
      case 'marketplace':
        return 'border-l-indigo-500 bg-indigo-50'
      case 'system':
        return 'border-l-gray-500 bg-gray-50'
      default:
        return 'border-l-gray-500 bg-gray-50'
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    if (searchTerm) {
      return notification.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
             notification.message.toLowerCase().includes(searchTerm.toLowerCase())
    }
    return true
  })

  const filterOptions = [
    { value: 'all', label: 'All Notifications', count: notifications.length },
    { value: 'unread', label: 'Unread', count: unreadCount },
    { value: 'match_invite', label: 'Match Invites', count: notifications.filter(n => n.type === 'match_invite').length },
    { value: 'match_result', label: 'Match Results', count: notifications.filter(n => n.type === 'match_result').length },
    { value: 'payment', label: 'Payments', count: notifications.filter(n => n.type === 'payment').length },
    { value: 'tournament', label: 'Tournaments', count: notifications.filter(n => n.type === 'tournament').length },
    { value: 'system', label: 'System', count: notifications.filter(n => n.type === 'system').length }
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-3">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Bell className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Notifications</h1>
                <p className="text-sm text-gray-600">
                  {unreadCount > 0 ? `${unreadCount} unread notifications` : 'All caught up!'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handleCreateSamples}
                disabled={isLoading}
                className="flex items-center space-x-2 px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm disabled:opacity-50"
              >
                <Bell className="w-4 h-4" />
                <span>Add Samples</span>
              </button>

              {unreadCount > 0 && (
                <button
                  onClick={handleMarkAllAsRead}
                  className="flex items-center space-x-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <CheckCheck className="w-4 h-4" />
                  <span>Mark All Read</span>
                </button>
              )}

              <button className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
                <Settings className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {filterOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label} ({option.count})
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Notifications List */}
        <div className="space-y-3">
          {isLoading ? (
            <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
              <Loader2 className="w-8 h-8 text-blue-600 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading notifications...</p>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="bg-white rounded-lg shadow-sm border p-8 text-center">
              <Bell className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
              <p className="text-gray-600">
                {searchTerm ? 'Try adjusting your search terms' : 'You\'re all caught up!'}
              </p>
            </div>
          ) : (
            filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`bg-white rounded-lg shadow-sm border-l-4 p-4 transition-all hover:shadow-md ${
                  getNotificationColor(notification.type)
                } ${!notification.is_read ? 'ring-2 ring-blue-100' : ''}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3 flex-1">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className={`text-sm font-medium ${!notification.is_read ? 'text-gray-900' : 'text-gray-700'}`}>
                          {notification.title}
                        </h3>
                        {!notification.is_read && (
                          <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                        )}
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">
                        {notification.message}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500">
                          {new Date(notification.created_at).toLocaleDateString()} at{' '}
                          {new Date(notification.created_at).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                        
                        <div className="flex items-center space-x-2">
                          {notification.data?.action_url && (
                            <button className="text-xs text-blue-600 hover:text-blue-800 flex items-center space-x-1">
                              <span>View</span>
                              <ExternalLink className="w-3 h-3" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    {!notification.is_read && (
                      <button
                        onClick={() => handleMarkAsRead(notification.id)}
                        className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                        title="Mark as read"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                    )}
                    
                    <button
                      onClick={() => handleDelete(notification.id)}
                      className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      title="Delete"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  )
}
