// 🔐 SIMPLIFIED AUTHENTICATION CONTEXT
// Robust, persistent authentication with Supabase

import { createContext, useContext, useState, ReactNode, useEffect } from 'react'
import { supabase } from '../services/supabaseClient'
import type { Session } from '@supabase/supabase-js'

// User type
interface AppUser {
  id: string
  email: string
  username: string
  first_name: string
  last_name: string
  diamond_balance: number
  total_matches: number
  wins: number
  losses: number
  created_at: string
  updated_at: string
  [key: string]: any
}

// Auth state
interface AuthState {
  user: AppUser | null
  session: Session | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

// Auth context type
interface AuthContextType extends AuthState {
  signUp: (email: string, password: string, userData: any) => Promise<{ success: boolean; needsConfirmation?: boolean; error?: string }>
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  signInWithGoogle: () => Promise<{ success: boolean; error?: string }>
  signOut: () => Promise<void>
  clearError: () => void
  refreshUser: () => Promise<void>
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
  // Check for existing session data immediately to reduce loading time
  const hasStoredSession = () => {
    try {
      const stored = localStorage.getItem('sb-bajzrybikkmrcwtadmpv-auth-token')
      return !!stored
    } catch {
      return false
    }
  }

  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isAuthenticated: false,
    isLoading: !hasStoredSession(), // Don't show loading if we have stored session
    error: null
  })

  // Initialize authentication with fast timeout
  useEffect(() => {
    let mounted = true
    let timeoutId: NodeJS.Timeout

    const initAuth = async () => {
      try {
        console.log('🔍 Checking authentication...')

        // Set a very fast timeout to prevent long loading
        timeoutId = setTimeout(() => {
          if (mounted) {
            console.log('⏰ Auth timeout - setting loading to false')
            setAuthState(prev => ({ ...prev, isLoading: false }))
          }
        }, 1000) // 1 second timeout

        // Get current session
        const { data: { session }, error } = await supabase.auth.getSession()

        // Clear timeout since we got a response
        clearTimeout(timeoutId)

        if (!mounted) return

        if (error) {
          console.error('❌ Session error:', error)
          setAuthState(prev => ({ ...prev, isLoading: false, error: null }))
          return
        }

        if (session?.user) {
          console.log('✅ User authenticated:', session.user.email)
          await handleUserSession(session)
        } else {
          console.log('❌ No active session')
          setAuthState(prev => ({ ...prev, isLoading: false }))
        }
      } catch (error) {
        console.error('❌ Auth init error:', error)
        clearTimeout(timeoutId)
        if (mounted) {
          setAuthState(prev => ({ ...prev, isLoading: false, error: null }))
        }
      }
    }

    // Handle user session (optimized for speed)
    const handleUserSession = async (session: Session) => {
      if (!mounted) return

      try {
        // Create user from session data (instant)
        const user: AppUser = {
          id: session.user.id,
          email: session.user.email!,
          username: session.user.user_metadata?.username || session.user.email!.split('@')[0],
          first_name: session.user.user_metadata?.firstName || session.user.user_metadata?.first_name || 'User',
          last_name: session.user.user_metadata?.lastName || session.user.user_metadata?.last_name || '',
          diamond_balance: 1000, // Starting balance
          total_matches: 0,
          wins: 0,
          losses: 0,
          created_at: session.user.created_at || new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        // Set auth state immediately with session data
        if (mounted) {
          setAuthState({
            user,
            session,
            isAuthenticated: true,
            isLoading: false,
            error: null
          })
        }

        // Load database profile in background (non-blocking)
        setTimeout(async () => {
          try {
            const { data: profile } = await supabase // TODO: Confirm error usage
              .from('users')
              .select('*')
              .eq('id', session.user.id)
              .single()

            if (profile && mounted) {
              // Update with database data
              const updatedUser = { ...user, ...profile }
              setAuthState(prev => ({
                ...prev,
                user: updatedUser
              }))
              console.log('📊 Database profile loaded in background')
            }
          } catch (dbError) {
            console.log('📝 Database profile not available:', dbError)
          }
        }, 100) // Load database data after UI is ready

      } catch (error) {
        console.error('❌ Error handling user session:', error)
        if (mounted) {
          setAuthState(prev => ({ ...prev, isLoading: false, error: null }))
        }
      }
    }

    // Initialize
    initAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('🔄 Auth event:', event)
      
      if (!mounted) return

      if (event === 'SIGNED_OUT' || !session) {
        setAuthState({
          user: null,
          session: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
      } else if (session?.user) {
        await handleUserSession(session)
      }
    })

    // Cleanup
    return () => {
      mounted = false
      subscription.unsubscribe()
      if (timeoutId) clearTimeout(timeoutId)
    }
  }, [])



  // Sign up
  const signUp = async (email: string, password: string, userData: any) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })

      if (error) throw error

      console.log('✅ Sign up successful:', email)

      // Check if email confirmation is needed
      const needsConfirmation = !data.session && data.user && !data.user.email_confirmed_at

      if (needsConfirmation) {
        // User needs to confirm email
        setAuthState(prev => ({ ...prev, isLoading: false }))
        return { success: true, needsConfirmation: true }
      } else {
        // User is immediately signed in (auth state will be updated by the listener)
        return { success: true, needsConfirmation: false }
      }
    } catch (error: any) {
      console.error('❌ Sign up error:', error)
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Sign up failed'
      }))
      return { success: false, error: error.message || 'Sign up failed' }
    }
  }

  // Sign in
  const signIn = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      const { error } = await supabase.auth.signInWithPassword({ // TODO: Confirm data usage
        email,
        password
      })

      if (error) throw error

      console.log('✅ Sign in successful:', email)
      // Auth state will be updated by the listener
      return { success: true }
    } catch (error: any) {
      console.error('❌ Sign in error:', error)
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Sign in failed'
      }))
      return { success: false, error: error.message || 'Sign in failed' }
    }
  }

  // Sign in with Google
  const signInWithGoogle = async () => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }))

    try {
      console.log('🔍 Initiating Google OAuth...')

      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      })

      if (error) throw error

      console.log('✅ Google OAuth initiated successfully')
      // The actual sign-in will be handled by the redirect and auth state listener
      return { success: true }
    } catch (error: any) {
      console.error('❌ Google sign in error:', error)
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Google sign in failed'
      }))
      return { success: false, error: error.message || 'Google sign in failed' }
    }
  }

  // Sign out
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      console.log('✅ Sign out successful')
    } catch (error: any) {
      console.error('❌ Sign out error:', error)
    }
  }

  // Clear error
  const clearError = () => {
    setAuthState(prev => ({ ...prev, error: null }))
  }

  // Refresh user data
  const refreshUser = async () => {
    if (!authState.session?.user) {
      console.log('No session user, skipping refresh')
      return
    }

    try {
      console.log('Refreshing user data for:', authState.session.user.id)

      const { data: profile, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', authState.session.user.id)
        .single()

      console.log('User refresh result:', { profile, error })

      if (error) {
        console.error('Error fetching user profile:', error)
        return
      }

      if (profile) {
        const updatedUser = { ...authState.user, ...profile }
        console.log('Updating user state with:', updatedUser)

        setAuthState(prev => ({
          ...prev,
          user: updatedUser
        }))

        console.log('User state updated successfully')
      }
    } catch (error) {
      console.error('Error refreshing user data:', error)
    }
  }

  const value: AuthContextType = {
    ...authState,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    clearError,
    refreshUser
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
