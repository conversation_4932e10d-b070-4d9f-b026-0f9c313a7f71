# 🧩 COMPONENTS

**Purpose:** Reusable UI components and shared functionality

## 🎯 Overview

The components directory contains all reusable UI components, organized by functionality and usage patterns. This promotes code reuse, consistency, and maintainability across the platform.

## 📁 Directory Structure

```
components/
├── 📁 common/              # Shared components used across multiple pages
├── 📁 forms/               # Form-related components and inputs
├── 📁 layout/              # Layout and navigation components
├── 📁 ui/                  # UI library components (shadcn/ui)
└── README.md               # This documentation
```

## 🔧 COMMON COMPONENTS (common/)

**Purpose:** Shared functionality used across multiple pages and features

### 🛡️ ProtectedRoute.tsx
**Purpose:** Route protection and authentication enforcement

**Features:**
- Authentication state checking
- Role-based access control
- Automatic redirects for unauthorized users
- Loading states during auth verification

**Usage:**
```typescript
// Protect private pages
<Route path="/dashboard" element={
  <ProtectedRoute>
    <DashboardPage />
  </ProtectedRoute>
} />

// Protect admin pages
<Route path="/admin/*" element={
  <ProtectedRoute requireAdmin={true}>
    <AdminRoutes />
  </ProtectedRoute>
} />
```

**Props:**
- `children` - Components to render when authorized
- `requireAdmin` - Whether admin role is required
- `redirectTo` - Custom redirect path for unauthorized users

---

### 🔔 NotificationSystem.tsx
**Purpose:** Global notification and alert system

**Features:**
- Toast notifications
- Success/error/warning/info alerts
- Auto-dismiss functionality
- Queue management for multiple notifications
- Customizable positioning and styling

**Usage:**
```typescript
// In your component
import { useNotification } from '../hooks/useNotification'

const { showNotification } = useNotification()

// Show success notification
showNotification({
  type: 'success',
  title: 'Match Joined!',
  message: 'You have successfully joined the match.',
  duration: 3000
})
```

**Notification Types:**
- `success` - Green notifications for positive actions
- `error` - Red notifications for errors
- `warning` - Yellow notifications for warnings
- `info` - Blue notifications for information

## 📝 FORM COMPONENTS (forms/)

**Purpose:** Form-related components and input elements

### 📋 RefereeGuide.tsx
**Purpose:** Guidance component for referee applications

**Features:**
- Step-by-step application guide
- Requirements checklist
- Tips and best practices
- Progress tracking
- Interactive help sections

**Usage:**
```typescript
import RefereeGuide from '../components/forms/RefereeGuide'

<RefereeGuide 
  currentStep={2}
  onStepChange={handleStepChange}
  showTips={true}
/>
```

**Props:**
- `currentStep` - Current application step
- `onStepChange` - Callback for step navigation
- `showTips` - Whether to show helpful tips

## 🏗️ LAYOUT COMPONENTS (layout/)

**Purpose:** Layout structure and navigation components

### 🧭 Navbar.tsx
**Purpose:** Main navigation component

**Features:**
- Responsive navigation menu
- User authentication status
- Role-based menu items
- Mobile hamburger menu
- Search functionality (future)
- Notification indicators (future)

**Usage:**
```typescript
import Navbar from '../components/layout/Navbar'

<Navbar 
  user={currentUser}
  onLogout={handleLogout}
  showSearch={true}
/>
```

**Props:**
- `user` - Current user object
- `onLogout` - Logout handler function
- `showSearch` - Whether to show search bar

**Navigation Items:**
- **Public:** Home, Leaderboards, Rules, Support
- **Private:** Dashboard, Matches, Profile, Wallet
- **Admin:** Admin Panel (for admin users)

## 🎨 UI COMPONENTS (ui/)

**Purpose:** Base UI library components (shadcn/ui)

### Core Components
- **Button** - Various button styles and states
- **Card** - Content containers with shadows
- **Input** - Form input fields
- **Label** - Form labels
- **Badge** - Status indicators and tags
- **Dialog** - Modal dialogs and popups
- **Dropdown** - Dropdown menus and selects
- **Tabs** - Tabbed content organization

### Usage Example
```typescript
import { Button } from '../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card'
import { Input } from '../components/ui/input'

<Card>
  <CardHeader>
    <CardTitle>Login</CardTitle>
  </CardHeader>
  <CardContent>
    <Input placeholder="Email" />
    <Button>Sign In</Button>
  </CardContent>
</Card>
```

## 🎯 Component Design Principles

### 🔄 Reusability
- **Single Responsibility** - Each component has one clear purpose
- **Configurable Props** - Customizable through props
- **Composition** - Components work well together
- **No Side Effects** - Pure components when possible

### 🎨 Consistency
- **Design System** - Follow established design patterns
- **Naming Convention** - Clear, descriptive component names
- **Prop Interface** - Consistent prop naming and types
- **Documentation** - Well-documented props and usage

### 🚀 Performance
- **Lazy Loading** - Load components when needed
- **Memoization** - Prevent unnecessary re-renders
- **Bundle Size** - Keep components lightweight
- **Tree Shaking** - Export only what's needed

## 🔧 Development Guidelines

### Creating New Components

#### 1. Choose Appropriate Directory
- **common/** - Shared across multiple pages
- **forms/** - Form-related functionality
- **layout/** - Navigation and layout
- **ui/** - Base UI elements

#### 2. Component Structure
```typescript
// ComponentName.tsx
import React from 'react'
import { ComponentNameProps } from './types'

export default function ComponentName({ 
  prop1, 
  prop2, 
  ...props 
}: ComponentNameProps) {
  return (
    <div className="component-wrapper">
      {/* Component content */}
    </div>
  )
}

// Export types
export type { ComponentNameProps }
```

#### 3. TypeScript Interface
```typescript
// types.ts or inline
interface ComponentNameProps {
  prop1: string
  prop2?: number
  children?: React.ReactNode
  className?: string
  onClick?: () => void
}
```

#### 4. Documentation
- Add JSDoc comments for complex components
- Document all props and their purposes
- Include usage examples
- Note any dependencies or requirements

### Component Standards

#### Naming Convention
- **PascalCase** for component names
- **Descriptive names** that indicate purpose
- **Consistent suffixes** (Button, Card, Modal, etc.)

#### Props Interface
- **Required props first** in interface definition
- **Optional props** marked with `?`
- **Event handlers** prefixed with `on`
- **Boolean props** prefixed with `is`, `has`, `show`

#### Styling
- **Tailwind CSS** for styling
- **Responsive design** by default
- **Dark mode support** (future)
- **Accessibility** considerations

## 🧪 Testing Strategy

### Unit Testing
```typescript
// ComponentName.test.tsx
import { render, screen } from '@testing-library/react'
import ComponentName from './ComponentName'

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName prop1="test" />)
    expect(screen.getByText('test')).toBeInTheDocument()
  })
})
```

### Testing Checklist
- **Rendering** - Component renders without errors
- **Props** - All props work as expected
- **Events** - Event handlers are called correctly
- **Accessibility** - Screen reader compatibility
- **Responsive** - Works on different screen sizes

## 📚 Component Library

### Current Components
- ✅ **ProtectedRoute** - Authentication protection
- ✅ **NotificationSystem** - Global notifications
- ✅ **RefereeGuide** - Referee application guide
- ✅ **Navbar** - Main navigation
- ✅ **UI Components** - shadcn/ui library

### Planned Components
- 🔄 **LoadingSpinner** - Loading state indicator
- 🔄 **ErrorBoundary** - Error handling wrapper
- 🔄 **SearchBar** - Global search functionality
- 🔄 **UserAvatar** - User profile pictures
- 🔄 **MatchCard** - Match display component
- 🔄 **ChatWidget** - Real-time chat component
- 🔄 **PaymentForm** - Diamond purchase forms

## 🎨 Design System Integration

### Color Palette
- **Primary** - Blue/Purple gradients
- **Success** - Green tones
- **Warning** - Yellow/Orange tones
- **Error** - Red tones
- **Neutral** - Gray scale

### Typography
- **Headings** - Bold, clear hierarchy
- **Body Text** - Readable, accessible
- **Labels** - Descriptive, consistent
- **Buttons** - Action-oriented text

### Spacing
- **Consistent margins** - 4px grid system
- **Padding standards** - Comfortable touch targets
- **Component spacing** - Logical relationships
- **Responsive scaling** - Adapts to screen size

## 🚀 Performance Optimization

### Code Splitting
```typescript
// Lazy load heavy components
const HeavyComponent = React.lazy(() => import('./HeavyComponent'))

function App() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <HeavyComponent />
    </Suspense>
  )
}
```

### Memoization
```typescript
// Prevent unnecessary re-renders
const MemoizedComponent = React.memo(function Component({ data }) {
  return <div>{data}</div>
})

// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])
```

### Bundle Optimization
- **Tree shaking** - Import only used components
- **Dynamic imports** - Load components on demand
- **Component splitting** - Separate large components
- **Asset optimization** - Optimize images and icons

## 🔮 Future Enhancements

### Advanced Components
- **DataTable** - Advanced table with sorting/filtering
- **Calendar** - Date picker and event calendar
- **Charts** - Data visualization components
- **FileUpload** - Drag-and-drop file uploads
- **RichTextEditor** - Advanced text editing

### Accessibility Improvements
- **Screen reader support** - ARIA labels and roles
- **Keyboard navigation** - Full keyboard accessibility
- **High contrast mode** - Better visibility options
- **Focus management** - Logical focus flow

### Performance Features
- **Virtual scrolling** - Handle large lists efficiently
- **Image lazy loading** - Load images on demand
- **Component caching** - Cache rendered components
- **Progressive enhancement** - Work without JavaScript
