import { memo, useMemo } from 'react'
import {
  Users,
  Trophy,
  Crown,
  Medal,
  Star,
  Lock,
  Eye,
  UserPlus,
  MessageCircle,
  Gem,
  Zap,
  Hash
} from 'lucide-react'
import { GroupCardProps } from '../types'

const GroupCard = memo<GroupCardProps>(({
  group,
  onJoin,
  onView,
  isJoined
}) => {
  // 🚀 MEMOIZED: Rank icon calculation
  const getRankIcon = useMemo(() => (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-3 h-3 text-yellow-500" />
      case 2:
        return <Medal className="w-3 h-3 text-gray-500" />
      case 3:
        return <Trophy className="w-3 h-3 text-orange-500" />
      default:
        return <span className="text-xs font-bold text-blue-600">#{rank}</span>
    }
  }, [])

  // 🚀 MEMOIZED: Rank badge color calculation
  const getRankBadgeColor = useMemo(() => (rank: number) => {
    if (rank === 1) return 'bg-gradient-to-br from-yellow-400 to-yellow-600'
    if (rank === 2) return 'bg-gradient-to-br from-gray-300 to-gray-500'
    if (rank === 3) return 'bg-gradient-to-br from-orange-400 to-orange-600'
    return 'bg-gradient-to-br from-blue-500 to-purple-600'
  }, [])

  // 🚀 MEMOIZED: Membership percentage calculation
  const membershipPercentage = useMemo(() =>
    (group.memberCount / group.maxMembers) * 100,
    [group.memberCount, group.maxMembers]
  )

  return (
    <div className="group bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden">
      {/* Group Header */}
      <div className="relative p-4 bg-gradient-to-br from-blue-50 to-purple-50">
        {/* Privacy Indicator */}
        {group.isPrivate && (
          <div className="absolute top-3 right-3">
            <Lock className="w-4 h-4 text-gray-600" />
          </div>
        )}

        <div className="flex items-start gap-3 mb-3">
          {/* Group Logo/Icon */}
          <div className="relative">
            <div className={`w-12 h-12 rounded-lg flex items-center justify-center text-white ${getRankBadgeColor(group.rank)}`}>
              {group.logo ? (
                <img src={group.logo} alt={group.name} className="w-full h-full rounded-lg object-cover" />
              ) : (
                <Hash className="w-6 h-6" />
              )}
            </div>
            {/* Rank Badge */}
            <div className="absolute -top-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center border border-gray-200">
              {getRankIcon(group.rank)}
            </div>
          </div>

          {/* Group Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-sm font-semibold text-gray-900 truncate">{group.name}</h3>
              <span className="text-xs text-gray-500 font-mono">[{group.tag}]</span>
              {isJoined && (
                <span className="px-2 py-0.5 bg-green-100 text-green-700 text-xs rounded-full font-medium">
                  Joined
                </span>
              )}
            </div>
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>Rank #{group.rank}</span>
              <span>•</span>
              <span>Level {group.level}</span>
              <span>•</span>
              <span className="text-green-600">{group.onlineMembers} online</span>
            </div>
            {group.tagline && (
              <p className="text-xs text-gray-600 mt-1 italic">{group.tagline}</p>
            )}
          </div>
        </div>

        <p className="text-xs text-gray-700 mb-3 line-clamp-2">{group.description}</p>

        {/* Group Stats */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-white rounded-lg p-2">
            <div className="flex items-center gap-1.5 mb-1">
              <Users className="w-3 h-3 text-blue-600" />
              <span className="text-xs font-medium text-gray-700">Members</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-sm font-semibold text-gray-900">{group.memberCount}</span>
              <span className="text-xs text-gray-600">/{group.maxMembers}</span>
            </div>
            {/* Membership Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
              <div 
                className="bg-blue-600 h-1 rounded-full transition-all duration-300"
                style={{ width: `${membershipPercentage}%` }}
              ></div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-2">
            <div className="flex items-center gap-1.5 mb-1">
              <Trophy className="w-3 h-3 text-yellow-600" />
              <span className="text-xs font-medium text-gray-700">Win Rate</span>
            </div>
            <div className="flex items-center gap-1">
              <span className="text-sm font-semibold text-gray-900">{group.winRate}%</span>
            </div>
            <div className="text-xs text-gray-600 mt-0.5">
              Level {group.level}
            </div>
          </div>
        </div>
      </div>

      {/* Group Details */}
      <div className="p-4">
        {/* Requirements */}
        <div className="mb-3">
          <h4 className="text-xs font-medium text-gray-700 mb-1.5">Requirements</h4>
          <div className="flex flex-wrap gap-1">
            <span className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
              Level {group.requirements.minLevel}+
            </span>
            <span className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
              {group.requirements.minWinRate}% WR
            </span>
            <span className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
              {group.requirements.minDiamonds} 💎
            </span>
          </div>
        </div>

        {/* Categories */}
        {group.categories.length > 0 && (
          <div className="mb-3">
            <h4 className="text-xs font-medium text-gray-700 mb-1.5">Categories</h4>
            <div className="flex flex-wrap gap-1">
              {group.categories.slice(0, 3).map((category, index) => (
                <span key={index} className="px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full text-xs">
                  {category}
                </span>
              ))}
              {group.categories.length > 3 && (
                <span className="px-2 py-0.5 bg-gray-100 text-gray-600 rounded-full text-xs">
                  +{group.categories.length - 3}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Group Perks */}
        <div className="mb-4">
          <h4 className="text-xs font-medium text-gray-700 mb-1.5">Perks</h4>
          <div className="grid grid-cols-2 gap-1.5 text-xs">
            <div className="flex items-center gap-1">
              <Gem className="w-3 h-3 text-blue-500" />
              <span>+{group.perks.diamondBonus}% Diamonds</span>
            </div>
            <div className="flex items-center gap-1">
              <Star className="w-3 h-3 text-yellow-500" />
              <span>+{group.perks.xpBonus}% XP</span>
            </div>
            {group.perks.matchPriority && (
              <div className="flex items-center gap-1">
                <Zap className="w-3 h-3 text-orange-500" />
                <span>Match Priority</span>
              </div>
            )}
            {group.perks.customRoles && (
              <div className="flex items-center gap-1">
                <Crown className="w-3 h-3 text-purple-500" />
                <span>Custom Roles</span>
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          {isJoined ? (
            <>
              <button
                onClick={() => onView(group.id)}
                className="flex-1 flex items-center justify-center gap-1.5 px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-xs font-medium"
              >
                <MessageCircle className="w-3 h-3" />
                <span>Chat</span>
              </button>
              <button
                onClick={() => onView(group.id)}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                title="Group Settings"
              >
                <Eye className="w-3 h-3" />
              </button>
            </>
          ) : (
            <>
              <button
                onClick={() => onJoin(group.id)}
                className="flex-1 flex items-center justify-center gap-1.5 px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-xs font-medium"
              >
                <UserPlus className="w-3 h-3" />
                <span>{group.isPrivate ? 'Request' : 'Join'}</span>
              </button>
              <button
                onClick={() => onView(group.id)}
                className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                title="View Details"
              >
                <Eye className="w-3 h-3" />
              </button>
            </>
          )}
        </div>
      </div>

      {/* Hover Effect Overlay */}
      <div className="absolute inset-0 bg-blue-600 opacity-0 group-hover:opacity-5 transition-opacity duration-200 pointer-events-none"></div>
    </div>
  )
})

export default GroupCard
