"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  X,
  Shield,
  Eye,
  AlertTriangle,
  CheckCircle,
  Target,
  Gamepad2,
  Video,
  Share2,
  DollarSign,
  Star,
  BookOpen,
  Lightbulb,
  MessageSquare,
  Zap,
} from "lucide-react"

interface RefereeGuideProps {
  isOpen: boolean
  onClose: () => void
}

export function RefereeGuide({ isOpen, onClose }: RefereeGuideProps) {
  if (!isOpen) return null

  const guideSteps = [
    {
      step: 1,
      title: "Match Selection & Preparation",
      icon: <Target className="w-5 h-5" />,
      content: [
        "Select an upcoming match from your dashboard",
        "Review player information and match details",
        "Note the room code and special instructions",
        "Prepare your streaming setup if you plan to broadcast",
      ],
    },
    {
      step: 2,
      title: "Pre-Match Setup",
      icon: <Gamepad2 className="w-5 h-5" />,
      content: [
        "Join the game room using the provided room code",
        "Verify both players are present and ready",
        "Explain the rules and match format to players",
        "Start your stream and share your referral link",
      ],
    },
    {
      step: 3,
      title: "During the Match",
      icon: <Eye className="w-5 h-5" />,
      content: [
        "Monitor the match closely for any violations",
        "Take screenshots of key moments and final results",
        "Watch for cheating, exploits, or unsportsmanlike conduct",
        "Engage with your stream audience and promote betting",
      ],
    },
    {
      step: 4,
      title: "Post-Match Verification",
      icon: <CheckCircle className="w-5 h-5" />,
      content: [
        "Determine the winner based on game results",
        "Upload screenshot proof of the final result",
        "Add detailed notes about the match",
        "Submit the result through your dashboard",
      ],
    },
  ]

  const earningTips = [
    {
      icon: <Share2 className="w-5 h-5 text-blue-600" />,
      title: "Promote Your Matches",
      description: "Share your referral links on social media and streaming platforms to earn 5% from all bets.",
    },
    {
      icon: <Video className="w-5 h-5 text-purple-600" />,
      title: "Stream Live Commentary",
      description: "Provide live commentary on YouTube, Twitch, or Facebook to attract more viewers and bettors.",
    },
    {
      icon: <Star className="w-5 h-5 text-yellow-600" />,
      title: "Build Your Reputation",
      description: "Consistent, fair refereeing builds trust and attracts more players to your matches.",
    },
    {
      icon: <DollarSign className="w-5 h-5 text-green-600" />,
      title: "Maximize Referrals",
      description: "The more people bet through your link, the more you earn. Focus on high-stakes matches.",
    },
  ]

  const violations = [
    {
      violation: "Cheating/Hacking",
      action: "Immediate disqualification",
      severity: "High",
      color: "text-red-600 bg-red-50",
    },
    {
      violation: "Exploiting Bugs",
      action: "Warning or disqualification",
      severity: "Medium",
      color: "text-orange-600 bg-orange-50",
    },
    {
      violation: "Unsportsmanlike Conduct",
      action: "Warning first, then penalty",
      severity: "Medium",
      color: "text-orange-600 bg-orange-50",
    },
    {
      violation: "Connection Issues",
      action: "Pause/reschedule if severe",
      severity: "Low",
      color: "text-yellow-600 bg-yellow-50",
    },
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-2xl">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                <BookOpen className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Referee Guide</h2>
                <p className="text-blue-100">Complete guide to refereeing on Gambets</p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-white hover:bg-white hover:bg-opacity-20 rounded-lg"
            >
              <X className="w-5 h-5" />
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-8">
          {/* Introduction */}
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <div className="flex items-start space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold text-blue-900 mb-2">Welcome, Referee!</h3>
                  <p className="text-blue-800 leading-relaxed">
                    As a Gambets referee, you play a crucial role in ensuring fair play and maintaining the integrity of
                    our gaming community. This guide will walk you through your responsibilities, earning opportunities,
                    and best practices for successful match management.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Step-by-Step Process */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <Lightbulb className="w-6 h-6 text-yellow-500 mr-2" />
              Step-by-Step Referee Process
            </h3>
            <div className="grid gap-6">
              {guideSteps.map((step, index) => (
                <Card key={index} className="border-l-4 border-l-blue-500">
                  <CardHeader className="pb-3">
                    <CardTitle className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {step.step}
                      </div>
                      <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center text-blue-600">
                        {step.icon}
                      </div>
                      <span className="text-lg font-semibold text-gray-900">{step.title}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {step.content.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-start space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Earning Tips */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <DollarSign className="w-6 h-6 text-green-500 mr-2" />
              Maximize Your Earnings
            </h3>
            <div className="grid md:grid-cols-2 gap-4">
              {earningTips.map((tip, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        {tip.icon}
                      </div>
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-1">{tip.title}</h4>
                        <p className="text-sm text-gray-600">{tip.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Violation Handling */}
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
              <AlertTriangle className="w-6 h-6 text-red-500 mr-2" />
              Handling Violations
            </h3>
            <div className="space-y-3">
              {violations.map((item, index) => (
                <div key={index} className={`p-4 rounded-lg border ${item.color}`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold">{item.violation}</h4>
                      <p className="text-sm opacity-80">{item.action}</p>
                    </div>
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-white bg-opacity-50">
                      {item.severity}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Best Practices */}
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center text-green-800">
                <Zap className="w-5 h-5 mr-2" />
                Best Practices for Success
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="font-semibold text-green-800">Communication</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Be clear and professional with players</li>
                    <li>• Explain decisions when questioned</li>
                    <li>• Stay neutral and unbiased</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold text-green-800">Documentation</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Take multiple screenshots</li>
                    <li>• Write detailed match notes</li>
                    <li>• Record any unusual incidents</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold text-green-800">Streaming</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Provide engaging commentary</li>
                    <li>• Interact with your audience</li>
                    <li>• Promote betting responsibly</li>
                  </ul>
                </div>
                <div className="space-y-2">
                  <h4 className="font-semibold text-green-800">Fairness</h4>
                  <ul className="text-sm text-green-700 space-y-1">
                    <li>• Treat all players equally</li>
                    <li>• Follow rules consistently</li>
                    <li>• Report any conflicts of interest</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Support */}
          <Card className="border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <MessageSquare className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Need Help?</h4>
                  <p className="text-gray-600">
                    Contact our support team anytime for assistance with refereeing, technical issues, or questions
                    about earnings.
                  </p>
                  <div className="flex space-x-4 mt-3">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      Contact Support
                    </Button>
                    <Button size="sm" variant="outline">
                      Join Referee Discord
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
