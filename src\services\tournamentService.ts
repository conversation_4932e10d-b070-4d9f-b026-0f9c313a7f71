import { supabase } from './supabaseClient'

export const tournamentService = {
  // Get tournaments
  async getTournaments(status?: string) {
    try {
      let query = supabase
        .from('tournaments')
        .select(`
          *,
          organizer:users!organizer_id(username),
          participants:tournament_participants(count)
        `)
        .order('tournament_start', { ascending: true })

      if (status) {
        query = query.eq('status', status)
      }

      const { data, error } = await query

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create tournament
  async createTournament(tournamentData: any) {
    try {
      const { data, error } = await supabase
        .from('tournaments')
        .insert([tournamentData])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Join tournament
  async joinTournament(tournamentId: string, userId: string, teamName?: string) {
    try {
      const { data, error } = await supabase
        .from('tournament_participants')
        .insert([{
          tournament_id: tournamentId,
          user_id: userId,
          team_name: teamName || 'Solo Player',
          status: 'registered'
        }])
        .select()
        .single()

      if (error) throw error

      // Update tournament participant count
      await supabase.rpc('increment_tournament_participants', {
        tournament_id: tournamentId
      })

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Check if user joined tournament
  async getUserTournamentParticipation(tournamentId: string, userId: string) {
    try {
      const { data, error } = await supabase
        .from('tournament_participants')
        .select('*')
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Leave tournament
  async leaveTournament(tournamentId: string, userId: string) {
    try {
      const { data, error } = await supabase
        .from('tournament_participants')
        .delete()
        .eq('tournament_id', tournamentId)
        .eq('user_id', userId)

      if (error) throw error

      // Decrement tournament participant count
      await supabase.rpc('decrement_tournament_participants', {
        tournament_id: tournamentId
      })

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get tournament details with participants
  async getTournamentDetails(tournamentId: string) {
    try {
      const { data, error } = await supabase
        .from('tournaments')
        .select(`
          *,
          organizer:users!organizer_id(username, avatar_url),
          participants:tournament_participants(
            *,
            user:users(username, avatar_url)
          )
        `)
        .eq('id', tournamentId)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
