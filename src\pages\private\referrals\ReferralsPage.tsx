// 🤝 REFERRALS PAGE
// Invite friends and earn rewards through the referral program

import { useState } from 'react'
import { 
  Users, 
  Gift, 
  Copy, 
  Share2, 
  Gem, 
  Trophy, 
  Star, 
  CheckCircle,
  UserPlus,
  Calendar,
  TrendingUp,
  Award,
  ExternalLink
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'

export default function ReferralsPage() {
  const { user } = useAuth()
  const [copied, setCopied] = useState(false)
  
  // Mock referral data
  const referralCode = user?.referral_code || 'GAMBET2024'
  const referralLink = `https://gambets.com/signup?ref=${referralCode}`
  
  const referralStats = {
    totalReferrals: 12,
    activeReferrals: 8,
    totalEarnings: 2400,
    thisMonthEarnings: 450,
    pendingRewards: 150
  }

  const recentReferrals = [
    {
      id: '1',
      username: 'NewGamer123',
      joinDate: '2024-01-10',
      status: 'active',
      earnings: 200,
      level: 15,
      matches: 23
    },
    {
      id: '2',
      username: 'ProPlayer456',
      joinDate: '2024-01-08',
      status: 'active',
      earnings: 350,
      level: 22,
      matches: 41
    },
    {
      id: '3',
      username: 'GamerFriend',
      joinDate: '2024-01-05',
      status: 'inactive',
      earnings: 50,
      level: 8,
      matches: 12
    },
    {
      id: '4',
      username: 'SkillMaster',
      joinDate: '2024-01-03',
      status: 'active',
      earnings: 480,
      level: 28,
      matches: 67
    }
  ]

  const rewardTiers = [
    {
      tier: 'Bronze',
      referrals: '1-5',
      bonus: '10%',
      perReferral: 50,
      color: 'from-amber-400 to-amber-600'
    },
    {
      tier: 'Silver',
      referrals: '6-15',
      bonus: '15%',
      perReferral: 75,
      color: 'from-gray-300 to-gray-500'
    },
    {
      tier: 'Gold',
      referrals: '16-30',
      bonus: '20%',
      perReferral: 100,
      color: 'from-yellow-400 to-yellow-600'
    },
    {
      tier: 'Diamond',
      referrals: '31+',
      bonus: '25%',
      perReferral: 150,
      color: 'from-blue-400 to-blue-600'
    }
  ]

  const copyReferralLink = async () => {
    try {
      await navigator.clipboard.writeText(referralLink)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const shareReferralLink = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Join Gambets - Gaming Platform',
          text: 'Join me on Gambets and start earning diamonds by playing your favorite games!',
          url: referralLink
        })
      } catch (err) {
        console.error('Error sharing:', err)
      }
    } else {
      copyReferralLink()
    }
  }

  const getCurrentTier = () => {
    const referrals = referralStats.totalReferrals
    if (referrals >= 31) return rewardTiers[3]
    if (referrals >= 16) return rewardTiers[2]
    if (referrals >= 6) return rewardTiers[1]
    return rewardTiers[0]
  }

  const getNextTier = () => {
    const referrals = referralStats.totalReferrals
    if (referrals < 6) return rewardTiers[1]
    if (referrals < 16) return rewardTiers[2]
    if (referrals < 31) return rewardTiers[3]
    return null
  }

  const currentTier = getCurrentTier()
  const nextTier = getNextTier()

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 lg:px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
                <Users className="w-8 h-8 text-blue-600" />
                Referral Program
              </h1>
              <p className="text-gray-600 mt-1">Invite friends and earn diamonds together</p>
            </div>
            <div className="text-right">
              <p className="text-sm text-gray-600">Total Earnings</p>
              <p className="text-2xl font-bold text-green-600 flex items-center">
                <Gem className="w-6 h-6 mr-2" />
                {referralStats.totalEarnings.toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 lg:px-6 py-8">
        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Total Referrals</p>
                <p className="text-2xl font-bold text-gray-900">{referralStats.totalReferrals}</p>
              </div>
              <UserPlus className="w-8 h-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Active Referrals</p>
                <p className="text-2xl font-bold text-green-600">{referralStats.activeReferrals}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">This Month</p>
                <p className="text-2xl font-bold text-purple-600 flex items-center">
                  <Gem className="w-5 h-5 mr-1" />
                  {referralStats.thisMonthEarnings}
                </p>
              </div>
              <Calendar className="w-8 h-8 text-purple-600" />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 mb-1">Pending Rewards</p>
                <p className="text-2xl font-bold text-yellow-600 flex items-center">
                  <Gift className="w-5 h-5 mr-1" />
                  {referralStats.pendingRewards}
                </p>
              </div>
              <Award className="w-8 h-8 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Referral Link Section */}
          <div className="lg:col-span-2 space-y-8">
            {/* Share Your Link */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Share2 className="w-5 h-5 text-blue-600" />
                Share Your Referral Link
              </h2>
              
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <p className="text-sm text-gray-600 mb-2">Your Referral Code</p>
                <p className="text-2xl font-bold text-blue-600 mb-4">{referralCode}</p>
                
                <p className="text-sm text-gray-600 mb-2">Your Referral Link</p>
                <div className="flex items-center gap-2">
                  <input
                    type="text"
                    value={referralLink}
                    readOnly
                    className="flex-1 px-3 py-2 bg-white border border-gray-300 rounded-lg text-sm"
                  />
                  <button
                    onClick={copyReferralLink}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center gap-2"
                  >
                    {copied ? <CheckCircle className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                    {copied ? 'Copied!' : 'Copy'}
                  </button>
                  <button
                    onClick={shareReferralLink}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center gap-2"
                  >
                    <Share2 className="w-4 h-4" />
                    Share
                  </button>
                </div>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">How it works:</h3>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Share your referral link with friends</li>
                  <li>• They sign up and start playing</li>
                  <li>• You both earn bonus diamonds</li>
                  <li>• Get ongoing rewards from their activity</li>
                </ul>
              </div>
            </div>

            {/* Recent Referrals */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Users className="w-5 h-5 text-blue-600" />
                Recent Referrals
              </h2>
              
              <div className="space-y-4">
                {recentReferrals.map((referral) => (
                  <div key={referral.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900">{referral.username}</h3>
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <span>Joined {new Date(referral.joinDate).toLocaleDateString()}</span>
                          <span>Level {referral.level}</span>
                          <span>{referral.matches} matches</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className={`px-2 py-1 rounded-full text-xs font-medium mb-1 ${
                        referral.status === 'active' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-gray-100 text-gray-700'
                      }`}>
                        {referral.status}
                      </div>
                      <div className="text-sm font-semibold text-green-600 flex items-center">
                        <Gem className="w-3 h-3 mr-1" />
                        +{referral.earnings}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Reward Tiers */}
          <div className="space-y-8">
            {/* Current Tier */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Star className="w-5 h-5 text-yellow-500" />
                Your Tier
              </h2>
              
              <div className={`bg-gradient-to-r ${currentTier.color} rounded-lg p-4 text-white mb-4`}>
                <h3 className="text-xl font-bold mb-2">{currentTier.tier} Tier</h3>
                <p className="text-sm opacity-90 mb-2">{currentTier.referrals} referrals</p>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Bonus Rate</span>
                  <span className="font-bold">{currentTier.bonus}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Per Referral</span>
                  <span className="font-bold flex items-center">
                    <Gem className="w-4 h-4 mr-1" />
                    {currentTier.perReferral}
                  </span>
                </div>
              </div>

              {nextTier && (
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Next Tier: {nextTier.tier}</h4>
                  <p className="text-sm text-gray-600 mb-2">
                    {nextTier.referrals.split('-')[0]} referrals needed
                  </p>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ 
                        width: `${Math.min((referralStats.totalReferrals / parseInt(nextTier.referrals.split('-')[0])) * 100, 100)}%` 
                      }}
                    ></div>
                  </div>
                </div>
              )}
            </div>

            {/* All Tiers */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center gap-2">
                <Trophy className="w-5 h-5 text-yellow-500" />
                Reward Tiers
              </h2>
              
              <div className="space-y-3">
                {rewardTiers.map((tier) => (
                  <div 
                    key={tier.tier} 
                    className={`p-3 rounded-lg border-2 ${
                      tier.tier === currentTier.tier 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold text-gray-900">{tier.tier}</h3>
                      {tier.tier === currentTier.tier && (
                        <span className="text-xs bg-blue-600 text-white px-2 py-1 rounded-full">
                          Current
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Referrals: {tier.referrals}</span>
                        <span>Bonus: {tier.bonus}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Per referral:</span>
                        <span className="font-semibold flex items-center">
                          <Gem className="w-3 h-3 mr-1" />
                          {tier.perReferral}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl p-6 text-white">
              <h3 className="text-lg font-bold mb-2">Boost Your Earnings</h3>
              <p className="text-purple-100 text-sm mb-4">
                Share on social media to reach more friends!
              </p>
              <button className="w-full bg-white text-purple-600 hover:bg-gray-100 px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                <ExternalLink className="w-4 h-4" />
                Social Media Kit
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
