import React, { useState, useRef, useEffect } from 'react'
import {
  Send,
  Heart,
  Smile,
  Crown,
  Shield,
  Star,
  MoreVertical,
  Reply,
  Volume2,
  VolumeX
} from 'lucide-react'
import { GlobalChatProps } from '../types'
import { useGlobalChat } from '../../../../hooks/useRealtime'

const GlobalChat: React.FC<GlobalChatProps> = ({
  messages,
  onSendMessage,
  onLikeMessage,
  onReactToMessage,
  isLoading = false,
  onNewMessage // Add callback for new real-time messages
}) => {
  const [newMessage, setNewMessage] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [soundEnabled, setSoundEnabled] = useState(true)
  const [typingUsers, setTypingUsers] = useState<string[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // Subscribe to real-time global chat messages
  useGlobalChat((newMessageData) => {
    // Play notification sound if enabled
    if (soundEnabled) {
      // You could add a sound notification here
      console.log('New message received:', newMessageData)
    }

    // Call parent callback to update messages
    if (onNewMessage) {
      onNewMessage(newMessageData)
    }
  })

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  // Simulate typing indicator
  useEffect(() => {
    if (newMessage.length > 0) {
      const timer = setTimeout(() => {
        setTypingUsers(['You'])
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      setTypingUsers([])
    }
  }, [newMessage])

  const handleSendMessage = () => {
    if (!newMessage.trim()) return
    
    onSendMessage(newMessage.trim())
    setNewMessage('')
    setReplyingTo(null)
    setTypingUsers([])
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const getRoleIcon = (role?: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-3 h-3 text-yellow-500" />
      case 'moderator':
        return <Shield className="w-3 h-3 text-blue-500" />
      case 'vip':
        return <Star className="w-3 h-3 text-purple-500" />
      default:
        return null
    }
  }

  const getRoleBadge = (role?: string) => {
    if (!role || role === 'member') return null
    
    const colors = {
      admin: 'bg-yellow-100 text-yellow-700',
      moderator: 'bg-blue-100 text-blue-700',
      vip: 'bg-purple-100 text-purple-700'
    }

    return (
      <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${colors[role as keyof typeof colors]}`}>
        {role.toUpperCase()}
      </span>
    )
  }

  const handleReplyToMessage = (messageId: string) => {
    setReplyingTo(messageId)
    inputRef.current?.focus()
  }

  const quickReactions = ['👍', '❤️', '😂', '😮', '😢', '😡']

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col h-96">
      {/* Chat Header */}
      <div className="p-3 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <h3 className="text-sm font-semibold text-gray-900">Global Chat</h3>
          <span className="text-xs text-gray-500">• 1,247 online</span>
        </div>
        
        <div className="flex items-center gap-1">
          <button
            onClick={() => setSoundEnabled(!soundEnabled)}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
            title={soundEnabled ? 'Mute notifications' : 'Enable notifications'}
          >
            {soundEnabled ? (
              <Volume2 className="w-4 h-4 text-gray-600" />
            ) : (
              <VolumeX className="w-4 h-4 text-gray-600" />
            )}
          </button>
          <button className="p-1 hover:bg-gray-100 rounded transition-colors">
            <MoreVertical className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Messages Container */}
      <div className="flex-1 overflow-y-auto p-3 space-y-3 bg-gray-50">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {messages.map((msg) => (
              <div key={msg.id} className="group flex gap-2">
                {/* Avatar */}
                <div className="relative flex-shrink-0">
                  <div className="w-7 h-7 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-xs">
                      {msg.avatar || msg.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  {msg.role && (
                    <div className="absolute -bottom-0.5 -right-0.5">
                      {getRoleIcon(msg.role)}
                    </div>
                  )}
                </div>

                {/* Message Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1.5 mb-1">
                    <span className="text-sm font-medium text-gray-900">{msg.username}</span>
                    {getRoleBadge(msg.role)}
                    <span className="text-xs text-gray-500">{msg.timestamp}</span>
                    {msg.isEdited && <span className="text-xs text-gray-400">(edited)</span>}
                  </div>

                  {/* Message Text */}
                  <div className={`${
                    msg.type === 'announcement' ? 'bg-blue-50 border border-blue-200 rounded-lg p-2' :
                    msg.type === 'system' ? 'bg-gray-100 rounded-lg p-2' : ''
                  }`}>
                    <p className={`text-sm text-gray-900 ${
                      msg.type === 'system' ? 'italic' : ''
                    }`}>
                      {msg.message}
                    </p>
                  </div>

                  {/* Reactions and Actions */}
                  <div className="flex items-center justify-between mt-1.5">
                    <div className="flex items-center gap-2">
                      {/* Existing Reactions */}
                      {msg.reactions && msg.reactions.length > 0 && (
                        <div className="flex items-center gap-1">
                          {msg.reactions.map((reaction, index) => (
                            <button
                              key={index}
                              onClick={() => onReactToMessage(msg.id, reaction.emoji)}
                              className="flex items-center gap-1 px-1.5 py-0.5 bg-gray-100 hover:bg-gray-200 rounded-full text-xs transition-colors"
                            >
                              <span>{reaction.emoji}</span>
                              <span className="font-medium">{reaction.count}</span>
                            </button>
                          ))}
                        </div>
                      )}

                      {/* Like Button */}
                      {msg.likes > 0 && (
                        <button
                          onClick={() => onLikeMessage(msg.id)}
                          className="flex items-center gap-1 text-xs text-gray-600 hover:text-red-600 transition-colors"
                        >
                          <Heart className="w-3 h-3" />
                          <span>{msg.likes}</span>
                        </button>
                      )}
                    </div>

                    {/* Message Actions */}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity flex items-center gap-0.5">
                      <button
                        onClick={() => handleReplyToMessage(msg.id)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                        title="Reply"
                      >
                        <Reply className="w-3 h-3 text-gray-600" />
                      </button>
                      <button
                        onClick={() => onLikeMessage(msg.id)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                        title="Like"
                      >
                        <Heart className="w-3 h-3 text-gray-600" />
                      </button>
                      <button
                        onClick={() => onReactToMessage(msg.id, '👍')}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                        title="React"
                      >
                        <Smile className="w-3 h-3 text-gray-600" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Typing Indicator */}
            {typingUsers.length > 0 && (
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <div className="flex gap-1">
                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                </div>
                <span>{typingUsers.join(', ')} {typingUsers.length === 1 ? 'is' : 'are'} typing...</span>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Chat Input */}
      <div className="p-3 border-t border-gray-200 bg-white">
        {/* Reply Context */}
        {replyingTo && (
          <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-1.5">
                <Reply className="w-3 h-3 text-blue-600" />
                <span className="text-xs text-blue-700">Replying to message</span>
              </div>
              <button
                onClick={() => setReplyingTo(null)}
                className="text-blue-600 hover:text-blue-700"
              >
                ×
              </button>
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <div className="flex-1 relative">
            <input
              ref={inputRef}
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={replyingTo ? "Type your reply..." : "Type a message..."}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              maxLength={500}
            />
          </div>

          <button
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center gap-1"
          >
            <Send className="w-3 h-3" />
            <span className="hidden sm:inline text-xs">Send</span>
          </button>
        </div>

        {/* Quick Reactions */}
        <div className="flex items-center gap-1 mt-2">
          <span className="text-xs text-gray-500">Quick:</span>
          {quickReactions.map((emoji) => (
            <button
              key={emoji}
              onClick={() => setNewMessage(prev => prev + emoji)}
              className="p-1 hover:bg-gray-100 rounded transition-colors"
            >
              <span className="text-sm">{emoji}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

export default GlobalChat
