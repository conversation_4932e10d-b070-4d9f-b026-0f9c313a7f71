import { supabase } from './supabase'
// import type { User } from './supabase' // TODO: Confirm usage

export interface MatchAnalytics {
  match_id: string
  difficulty_score: number
  avg_skill_level: number
  estimated_duration: number
  peak_activity_bonus: boolean
  region: string
  user_win_probability?: number
  risk_reward_ratio?: number
}

export interface UserGameStats {
  user_id: string
  game: string
  matches_played: number
  wins: number
  losses: number
  win_rate: number
  skill_level: number
  avg_match_duration: number
  total_earnings: number
  best_streak: number
  current_streak: number
  favorite_mode?: string
  last_played?: string
}

export interface MatchOpportunity {
  match_id: string
  game: string
  format: string
  entry_fee: number
  estimated_win_rate: number
  difficulty_score: number
  risk_reward_ratio: number
  bonus_multiplier: number
  recommendation_score: number
}

class AnalyticsService {
  // Get or create user game statistics
  async getUserGameStats(userId: string, game: string): Promise<UserGameStats | null> {
    try {
      const { data, error } = await supabase
        .from('user_game_stats')
        .select('*')
        .eq('user_id', userId)
        .eq('game', game)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        console.error('Error fetching user game stats:', error)
        return null
      }

      if (!data) {
        // Create initial stats for this user-game combination
        return await this.createInitialGameStats(userId, game)
      }

      return data
    } catch (error) {
      console.error('Error in getUserGameStats:', error)
      return null
    }
  }

  // Create initial game statistics for a user
  private async createInitialGameStats(userId: string, game: string): Promise<UserGameStats> {
    // Calculate initial stats based on user's overall performance
    const { data: userMatches } = await supabase
      .from('matches')
      .select('*')
      .eq('game', game)
      .or(`host_id.eq.${userId},winner_id.eq.${userId}`)

    const matchesPlayed = userMatches?.length || 0
    const wins = userMatches?.filter(match => match.winner_id === userId).length || 0
    const losses = matchesPlayed - wins
    const winRate = matchesPlayed > 0 ? (wins / matchesPlayed) * 100 : 0

    // Calculate skill level based on win rate and matches played
    let skillLevel = 5 // Default
    if (matchesPlayed >= 10) {
      if (winRate >= 80) skillLevel = 9
      else if (winRate >= 70) skillLevel = 8
      else if (winRate >= 60) skillLevel = 7
      else if (winRate >= 50) skillLevel = 6
      else if (winRate >= 40) skillLevel = 5
      else if (winRate >= 30) skillLevel = 4
      else if (winRate >= 20) skillLevel = 3
      else skillLevel = 2
    }

    const initialStats: Omit<UserGameStats, 'id' | 'created_at' | 'updated_at'> = {
      user_id: userId,
      game,
      matches_played: matchesPlayed,
      wins,
      losses,
      win_rate: winRate,
      skill_level: skillLevel,
      avg_match_duration: 25, // Default 25 minutes
      total_earnings: 0,
      best_streak: 0,
      current_streak: 0
    }

    const { data, error } = await supabase
      .from('user_game_stats')
      .insert([initialStats])
      .select()
      .single()

    if (error) {
      console.error('Error creating initial game stats:', error)
      return initialStats as UserGameStats
    }

    return data
  }

  // Calculate match analytics
  async calculateMatchAnalytics(match: any, userId?: string): Promise<MatchAnalytics> {
    try {
      // Check if analytics already exist
      const { data: existingAnalytics } = await supabase
        .from('match_analytics')
        .select('*')
        .eq('match_id', match.id)
        .single()

      if (existingAnalytics) {
        // Add user-specific calculations
        if (userId) {
          const userWinProbability = await this.calculateUserWinProbability(userId, match)
          const riskRewardRatio = this.calculateRiskRewardRatio(match)
          
          return {
            ...existingAnalytics,
            user_win_probability: userWinProbability,
            risk_reward_ratio: riskRewardRatio
          }
        }
        return existingAnalytics
      }

      // Calculate new analytics
      const analytics = await this.generateMatchAnalytics(match)
      
      // Save to database
      const { error } = await supabase
        .from('match_analytics')
        .insert([analytics])

      if (error) {
        console.error('Error saving match analytics:', error)
      }

      // Add user-specific data if requested
      if (userId) {
        const userWinProbability = await this.calculateUserWinProbability(userId, match)
        const riskRewardRatio = this.calculateRiskRewardRatio(match)
        
        return {
          ...analytics,
          user_win_probability: userWinProbability,
          risk_reward_ratio: riskRewardRatio
        }
      }

      return analytics
    } catch (error) {
      console.error('Error calculating match analytics:', error)
      return this.getDefaultAnalytics(match.id)
    }
  }

  // Generate match analytics
  private async generateMatchAnalytics(match: any): Promise<Omit<MatchAnalytics, 'user_win_probability' | 'risk_reward_ratio'>> {
    // Get participants' skill levels
    const { data: participants } = await supabase
      .from('match_participants')
      .select(`
        user_id,
        user_game_stats!inner(skill_level)
      `)
      .eq('match_id', match.id)

    const skillLevels = participants?.map(p => (p as any).user_game_stats?.skill_level || 5) || [5]
    const avgSkillLevel = skillLevels.reduce((sum, level) => sum + level, 0) / skillLevels.length

    // Calculate difficulty score (1-10)
    const difficultyScore = Math.max(1, Math.min(10, Math.round(
      avgSkillLevel + 
      (match.entry_fee > 100 ? 1 : 0) + // Higher entry fee = harder
      (match.max_players > 4 ? 1 : 0) + // More players = harder
      (match.current_players / match.max_players > 0.8 ? 1 : 0) // Nearly full = competitive
    )))

    // Estimate duration based on game and format
    const estimatedDuration = this.estimateMatchDuration(match.game, match.mode)

    // Check if it's peak activity time (6-10 PM)
    const now = new Date()
    const hour = now.getHours()
    const peakActivityBonus = hour >= 18 && hour <= 22

    return {
      match_id: match.id,
      avg_skill_level: avgSkillLevel,
      difficulty_score: difficultyScore,
      estimated_duration: estimatedDuration,
      peak_activity_bonus: peakActivityBonus,
      region: match.region || 'Global'
    }
  }

  // Calculate user's win probability for a specific match
  private async calculateUserWinProbability(userId: string, match: any): Promise<number> {
    const userStats = await this.getUserGameStats(userId, match.game)
    if (!userStats) return 0.5 // Default 50%

    // Base win rate from user's historical performance
    let baseWinRate = userStats.win_rate / 100

    // Get match analytics
    const analytics = await this.calculateMatchAnalytics(match)
    
    // Adjust based on skill difference
    const skillDifference = userStats.skill_level - analytics.avg_skill_level
    const skillAdjustment = skillDifference * 0.05 // 5% per skill level difference

    // Adjust based on recent performance (current streak)
    const streakAdjustment = Math.min(0.1, userStats.current_streak * 0.02) // Max 10% bonus

    // Adjust based on match difficulty
    const difficultyAdjustment = (5 - analytics.difficulty_score) * 0.02 // Easier matches = higher win rate

    // Calculate final probability
    const winProbability = Math.max(0.05, Math.min(0.95, 
      baseWinRate + skillAdjustment + streakAdjustment + difficultyAdjustment
    ))

    return winProbability
  }

  // Calculate risk/reward ratio
  private calculateRiskRewardRatio(match: any): number {
    const potentialWin = match.pot_amount - match.entry_fee // Net win
    const risk = match.entry_fee
    return potentialWin / risk
  }

  // Estimate match duration based on game and mode
  private estimateMatchDuration(game: string, mode: string): number {
    const durations: Record<string, Record<string, number>> = {
      'Mobile Legends': {
        '1v1': 15,
        '2v2': 20,
        '3v3': 25,
        '5v5': 30
      },
      'Valorant': {
        '1v1': 10,
        '2v2': 15,
        '5v5': 45
      },
      'Call of Duty': {
        '1v1': 8,
        '2v2': 12,
        '4v4': 20
      },
      'Dota 2': {
        '5v5': 50
      }
    }

    return durations[game]?.[mode] || 25 // Default 25 minutes
  }

  // Get default analytics for fallback
  private getDefaultAnalytics(matchId: string): MatchAnalytics {
    return {
      match_id: matchId,
      difficulty_score: 5,
      avg_skill_level: 5,
      estimated_duration: 25,
      peak_activity_bonus: false,
      region: 'Global',
      user_win_probability: 0.5,
      risk_reward_ratio: 1.0
    }
  }

  // Find match opportunities for a user
  async findMatchOpportunities(userId: string, limit = 5): Promise<MatchOpportunity[]> {
    try {
      // Get user's favorite games
      const { data: favoriteGames } = await supabase
        .from('user_favorite_games')
        .select('game')
        .eq('user_id', userId)
        .order('priority')

      const preferredGames = favoriteGames?.map(fg => fg.game) || []

      // Get open matches
      const { data: matches } = await supabase
        .from('matches')
        .select('*')
        .eq('status', 'open')
        .filter('current_players', 'lt', 'max_players')
        .order('created_at', { ascending: false })
        .limit(20)

      if (!matches) return []

      // Calculate opportunities for each match
      const opportunities: MatchOpportunity[] = []

      for (const match of matches) {
        const analytics = await this.calculateMatchAnalytics(match, userId)
        const userStats = await this.getUserGameStats(userId, match.game)

        if (!userStats || !analytics.user_win_probability) continue

        // Calculate bonus multiplier
        const bonusMultiplier = this.calculateBonusMultiplier(match, analytics)

        // Calculate recommendation score
        const recommendationScore = this.calculateRecommendationScore(
          match,
          analytics,
          userStats,
          preferredGames.includes(match.game)
        )

        opportunities.push({
          match_id: match.id,
          game: match.game,
          format: match.mode,
          entry_fee: match.entry_fee,
          estimated_win_rate: analytics.user_win_probability,
          difficulty_score: analytics.difficulty_score,
          risk_reward_ratio: analytics.risk_reward_ratio || 1.0,
          bonus_multiplier: bonusMultiplier,
          recommendation_score: recommendationScore
        })
      }

      // Sort by recommendation score and return top opportunities
      return opportunities
        .sort((a, b) => b.recommendation_score - a.recommendation_score)
        .slice(0, limit)

    } catch (error) {
      console.error('Error finding match opportunities:', error)
      return []
    }
  }

  // Calculate bonus multiplier for a match
  private calculateBonusMultiplier(match: any, analytics: MatchAnalytics): number {
    let multiplier = 1.0

    // Peak activity bonus
    if (analytics.peak_activity_bonus) {
      multiplier += 0.2
    }

    // Weekend bonus
    const now = new Date()
    if (now.getDay() === 0 || now.getDay() === 6) {
      multiplier += 0.1
    }

    // High stakes bonus
    if (match.entry_fee >= 200) {
      multiplier += 0.15
    }

    return multiplier
  }

  // Calculate recommendation score for a match
  private calculateRecommendationScore(
    match: any,
    analytics: MatchAnalytics,
    userStats: UserGameStats,
    isPreferredGame: boolean
  ): number {
    let score = 0

    // Win probability weight (40%)
    score += (analytics.user_win_probability || 0.5) * 40

    // Preferred game bonus (20%)
    if (isPreferredGame) {
      score += 20
    }

    // Risk/reward ratio weight (20%)
    const normalizedRiskReward = Math.min(5, analytics.risk_reward_ratio || 1) / 5
    score += normalizedRiskReward * 20

    // Skill match weight (10%)
    const skillDifference = Math.abs(userStats.skill_level - analytics.avg_skill_level)
    const skillMatchScore = Math.max(0, 10 - skillDifference * 2)
    score += skillMatchScore

    // Bonus multiplier weight (10%)
    const bonusMultiplier = this.calculateBonusMultiplier(match, analytics)
    score += (bonusMultiplier - 1.0) * 100

    return Math.max(0, Math.min(100, score))
  }

  // Update user game stats after a match
  async updateUserGameStats(userId: string, match: any, won: boolean): Promise<void> {
    try {
      const stats = await this.getUserGameStats(userId, match.game)
      if (!stats) return

      const newMatchesPlayed = stats.matches_played + 1
      const newWins = won ? stats.wins + 1 : stats.wins
      const newLosses = won ? stats.losses : stats.losses + 1
      const newWinRate = (newWins / newMatchesPlayed) * 100

      // Update streak
      const newCurrentStreak = won ? stats.current_streak + 1 : 0
      const newBestStreak = Math.max(stats.best_streak, newCurrentStreak)

      // Update earnings (if won)
      const matchEarnings = won ? (match.pot_amount - match.entry_fee) : 0
      const newTotalEarnings = stats.total_earnings + matchEarnings

      const { error } = await supabase
        .from('user_game_stats')
        .update({
          matches_played: newMatchesPlayed,
          wins: newWins,
          losses: newLosses,
          win_rate: newWinRate,
          current_streak: newCurrentStreak,
          best_streak: newBestStreak,
          total_earnings: newTotalEarnings,
          last_played: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId)
        .eq('game', match.game)

      if (error) {
        console.error('Error updating user game stats:', error)
      }
    } catch (error) {
      console.error('Error in updateUserGameStats:', error)
    }
  }
}

export const analyticsService = new AnalyticsService()

// ============================================================================
// 🌟 FAVORITES SERVICE
// ============================================================================

class FavoritesService {
  // Get user's favorite matches
  async getUserFavoriteMatches(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('user_favorites')
        .select('match_id')
        .eq('user_id', userId)

      if (error) {
        console.error('Error fetching favorite matches:', error)
        return []
      }

      return data?.map(fav => fav.match_id) || []
    } catch (error) {
      console.error('Error in getUserFavoriteMatches:', error)
      return []
    }
  }

  // Add match to favorites
  async addToFavorites(userId: string, matchId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .insert([{ user_id: userId, match_id: matchId }])

      if (error) {
        console.error('Error adding to favorites:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in addToFavorites:', error)
      return false
    }
  }

  // Remove match from favorites
  async removeFromFavorites(userId: string, matchId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('user_favorites')
        .delete()
        .eq('user_id', userId)
        .eq('match_id', matchId)

      if (error) {
        console.error('Error removing from favorites:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in removeFromFavorites:', error)
      return false
    }
  }

  // Get user's favorite games
  async getUserFavoriteGames(userId: string): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('user_favorite_games')
        .select('game')
        .eq('user_id', userId)
        .order('priority')

      if (error) {
        console.error('Error fetching favorite games:', error)
        return []
      }

      return data?.map(fav => fav.game) || []
    } catch (error) {
      console.error('Error in getUserFavoriteGames:', error)
      return []
    }
  }

  // Set user's favorite games
  async setUserFavoriteGames(userId: string, games: string[]): Promise<boolean> {
    try {
      // First, remove existing favorites
      await supabase
        .from('user_favorite_games')
        .delete()
        .eq('user_id', userId)

      // Then add new favorites
      const favorites = games.map((game, index) => ({
        user_id: userId,
        game,
        priority: index + 1
      }))

      const { error } = await supabase
        .from('user_favorite_games')
        .insert(favorites)

      if (error) {
        console.error('Error setting favorite games:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in setUserFavoriteGames:', error)
      return false
    }
  }
}

export const favoritesService = new FavoritesService()
