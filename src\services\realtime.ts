import { supabase } from './supabaseClient'
import { RealtimeChannel } from '@supabase/supabase-js'

// Real-time service for handling live updates
export class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map()

  // Subscribe to global chat messages
  subscribeToGlobalChat(callback: (message: any) => void) {
    const channelName = 'global-chat'
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'global_chat'
        },
        (payload) => {
          console.log('New global chat message:', payload)
          callback(payload.new)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to match updates
  subscribeToMatchUpdates(matchId: string, callback: (match: any) => void) {
    const channelName = `match-${matchId}`
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*', // Listen to all events (INSERT, UPDATE, DELETE)
          schema: 'public',
          table: 'matches',
          filter: `id=eq.${matchId}`
        },
        (payload) => {
          console.log('Match update:', payload)
          callback(payload.new || payload.old)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to all matches updates (for dashboard)
  subscribeToAllMatches(callback: (match: any) => void) {
    const channelName = 'all-matches'
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'matches'
        },
        (payload) => {
          console.log('Match update:', payload)
          callback(payload.new || payload.old)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to user updates (for profile changes, balance updates, etc.)
  subscribeToUserUpdates(userId: string, callback: (user: any) => void) {
    const channelName = `user-${userId}`
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${userId}`
        },
        (payload) => {
          console.log('User update:', payload)
          callback(payload.new)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to tournament updates
  subscribeToTournamentUpdates(tournamentId: string, callback: (tournament: any) => void) {
    const channelName = `tournament-${tournamentId}`
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'tournaments',
          filter: `id=eq.${tournamentId}`
        },
        (payload) => {
          console.log('Tournament update:', payload)
          callback(payload.new || payload.old)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to referee match assignments
  subscribeToRefereeMatches(refereeId: string, callback: (match: any) => void) {
    const channelName = `referee-matches-${refereeId}`
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'matches',
          filter: `referee_id=eq.${refereeId}`
        },
        (payload) => {
          console.log('Referee match update:', payload)
          callback(payload.new || payload.old)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to community group updates
  subscribeToGroupUpdates(groupId: string, callback: (group: any) => void) {
    const channelName = `group-${groupId}`
    
    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'community_groups',
          filter: `id=eq.${groupId}`
        },
        (payload) => {
          console.log('Group update:', payload)
          callback(payload.new || payload.old)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to match chat messages
  subscribeToMatchChat(matchId: string, callback: (message: any) => void) {
    const channelName = `match-chat-${matchId}`

    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'match_chat',
          filter: `match_id=eq.${matchId}`
        },
        (payload) => {
          console.log('New match chat message:', payload)
          callback(payload.new)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Subscribe to group chat messages
  subscribeToGroupChat(groupId: string, callback: (message: any) => void) {
    const channelName = `group-chat-${groupId}`

    // Remove existing subscription if any
    this.unsubscribe(channelName)

    const channel = supabase
      .channel(channelName)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'group_chat',
          filter: `group_id=eq.${groupId}`
        },
        (payload) => {
          console.log('New group chat message:', payload)
          callback(payload.new)
        }
      )
      .subscribe()

    this.channels.set(channelName, channel)
    return channel
  }

  // Unsubscribe from a specific channel
  unsubscribe(channelName: string) {
    const channel = this.channels.get(channelName)
    if (channel) {
      supabase.removeChannel(channel)
      this.channels.delete(channelName)
      console.log(`Unsubscribed from ${channelName}`)
    }
  }

  // Unsubscribe from all channels
  unsubscribeAll() {
    this.channels.forEach((channel, channelName) => {
      supabase.removeChannel(channel)
      console.log(`Unsubscribed from ${channelName}`)
    })
    this.channels.clear()
  }

  // Get connection status
  getConnectionStatus() {
    return supabase.realtime.isConnected()
  }

  // Manually connect to realtime
  connect() {
    return supabase.realtime.connect()
  }

  // Manually disconnect from realtime
  disconnect() {
    this.unsubscribeAll()
    return supabase.realtime.disconnect()
  }
}

// Export singleton instance
export const realtimeService = new RealtimeService()
