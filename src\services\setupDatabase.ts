import { supabase } from './supabase'

/**
 * Setup the referee conflict prevention system database tables
 * This function creates all necessary tables and functions for the anti-cheat system
 */
export async function setupRefereeConflictSystem() {
  try {
    console.log('🔧 Setting up Referee Conflict Prevention System...')

    // Create referee_recusals table
    const { error: recusalsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS referee_recusals (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          referee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
          reason TEXT NOT NULL,
          recusal_type VARCHAR(20) NOT NULL CHECK (recusal_type IN ('automatic', 'manual', 'admin')),
          recused_by UUID REFERENCES users(id),
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(referee_id, match_id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_referee_recusals_referee_id ON referee_recusals(referee_id);
        CREATE INDEX IF NOT EXISTS idx_referee_recusals_match_id ON referee_recusals(match_id);
        CREATE INDEX IF NOT EXISTS idx_referee_recusals_created_at ON referee_recusals(created_at);
      `
    })

    if (recusalsError) {
      console.error('Error creating referee_recusals table:', recusalsError)
    } else {
      console.log('✅ referee_recusals table created')
    }

    // Create referee_conflict_logs table
    const { error: logsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS referee_conflict_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          referee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          match_id UUID REFERENCES matches(id) ON DELETE CASCADE,
          conflict_type VARCHAR(50),
          conflict_details TEXT,
          action_taken VARCHAR(100),
          checked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          checked_by UUID REFERENCES users(id)
        );
        
        CREATE INDEX IF NOT EXISTS idx_referee_conflict_logs_referee_id ON referee_conflict_logs(referee_id);
        CREATE INDEX IF NOT EXISTS idx_referee_conflict_logs_match_id ON referee_conflict_logs(match_id);
        CREATE INDEX IF NOT EXISTS idx_referee_conflict_logs_checked_at ON referee_conflict_logs(checked_at);
      `
    })

    if (logsError) {
      console.error('Error creating referee_conflict_logs table:', logsError)
    } else {
      console.log('✅ referee_conflict_logs table created')
    }

    // Create referee_betting_restrictions table
    const { error: restrictionsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS referee_betting_restrictions (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
          match_id UUID NOT NULL REFERENCES matches(id) ON DELETE CASCADE,
          restriction_type VARCHAR(50) NOT NULL,
          restriction_start TIMESTAMP WITH TIME ZONE NOT NULL,
          restriction_end TIMESTAMP WITH TIME ZONE NOT NULL,
          reason TEXT NOT NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          UNIQUE(user_id, match_id, restriction_type)
        );
        
        CREATE INDEX IF NOT EXISTS idx_referee_betting_restrictions_user_id ON referee_betting_restrictions(user_id);
        CREATE INDEX IF NOT EXISTS idx_referee_betting_restrictions_match_id ON referee_betting_restrictions(match_id);
        CREATE INDEX IF NOT EXISTS idx_referee_betting_restrictions_dates ON referee_betting_restrictions(restriction_start, restriction_end);
      `
    })

    if (restrictionsError) {
      console.error('Error creating referee_betting_restrictions table:', restrictionsError)
    } else {
      console.log('✅ referee_betting_restrictions table created')
    }

    // Create referee_conflict_settings table
    const { error: settingsError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS referee_conflict_settings (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          setting_name VARCHAR(100) NOT NULL UNIQUE,
          setting_value JSONB NOT NULL,
          description TEXT,
          updated_by UUID REFERENCES users(id),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    })

    if (settingsError) {
      console.error('Error creating referee_conflict_settings table:', settingsError)
    } else {
      console.log('✅ referee_conflict_settings table created')
    }

    // Insert default settings
    const { error: defaultSettingsError } = await supabase
      .from('referee_conflict_settings')
      .upsert([
        {
          setting_name: 'friendship_cooldown_days',
          setting_value: 7,
          description: 'Days after friendship before can referee friends matches'
        },
        {
          setting_name: 'betting_cooldown_hours',
          setting_value: 24,
          description: 'Hours before/after refereeing when betting is restricted'
        },
        {
          setting_name: 'match_cooldown_hours',
          setting_value: 12,
          description: 'Hours before/after playing when refereeing is restricted'
        },
        {
          setting_name: 'auto_recusal_enabled',
          setting_value: true,
          description: 'Whether to automatically recuse referees with conflicts'
        },
        {
          setting_name: 'manual_recusal_required',
          setting_value: true,
          description: 'Whether manual recusal option is required'
        },
        {
          setting_name: 'strict_mode_enabled',
          setting_value: false,
          description: 'Whether to enable strictest conflict prevention'
        }
      ], { 
        onConflict: 'setting_name',
        ignoreDuplicates: true 
      })

    if (defaultSettingsError) {
      console.error('Error inserting default settings:', defaultSettingsError)
    } else {
      console.log('✅ Default conflict settings inserted')
    }

    console.log('🎉 Referee Conflict Prevention System setup complete!')
    return { success: true, message: 'Database setup completed successfully' }

  } catch (error) {
    console.error('❌ Error setting up database:', error)
    return { success: false, error: error }
  }
}

/**
 * Check if the referee conflict system is properly set up
 */
export async function checkSystemSetup() {
  try {
    // Check if tables exist by trying to query them
    const tables = [
      'referee_recusals',
      'referee_conflict_logs', 
      'referee_betting_restrictions',
      'referee_conflict_settings'
    ]

    const results = []
    
    for (const table of tables) {
      try {
        const { error } = await supabase
          .from(table)
          .select('id')
          .limit(1)
        
        results.push({
          table,
          exists: !error,
          error: error?.message
        })
      } catch (err) {
        results.push({
          table,
          exists: false,
          error: (err as Error).message
        })
      }
    }

    const allTablesExist = results.every(r => r.exists)
    
    return {
      success: allTablesExist,
      tables: results,
      message: allTablesExist 
        ? 'All referee conflict system tables are properly set up'
        : 'Some tables are missing or have issues'
    }

  } catch (error) {
    return {
      success: false,
      error: error,
      message: 'Error checking system setup'
    }
  }
}

/**
 * Test the conflict detection system
 */
export async function testConflictSystem() {
  try {
    console.log('🧪 Testing conflict detection system...')

    // Test 1: Check if we can insert a test conflict log
    const { error: logError } = await supabase
      .from('referee_conflict_logs')
      .insert({
        referee_id: '00000000-0000-0000-0000-000000000000', // Test UUID
        conflict_type: 'system_test',
        conflict_details: 'System test - can be safely ignored',
        action_taken: 'test_completed',
        checked_at: new Date().toISOString()
      })

    if (logError) {
      console.error('❌ Conflict log test failed:', logError)
      return { success: false, error: logError }
    }

    // Clean up test data
    await supabase
      .from('referee_conflict_logs')
      .delete()
      .eq('conflict_type', 'system_test')

    console.log('✅ Conflict system test passed')
    return { success: true, message: 'Conflict system is working properly' }

  } catch (error) {
    console.error('❌ Error testing conflict system:', error)
    return { success: false, error: error }
  }
}

/**
 * Get system statistics
 */
export async function getSystemStats() {
  try {
    const stats = {
      totalConflicts: 0,
      totalRecusals: 0,
      activeRestrictions: 0,
      systemHealth: 'unknown'
    }

    // Get total conflicts
    const { count: conflictCount } = await supabase
      .from('referee_conflict_logs')
      .select('*', { count: 'exact', head: true })

    stats.totalConflicts = conflictCount || 0

    // Get total recusals
    const { count: recusalCount } = await supabase
      .from('referee_recusals')
      .select('*', { count: 'exact', head: true })

    stats.totalRecusals = recusalCount || 0

    // Get active restrictions
    const { count: restrictionCount } = await supabase
      .from('referee_betting_restrictions')
      .select('*', { count: 'exact', head: true })
      .gte('restriction_end', new Date().toISOString())

    stats.activeRestrictions = restrictionCount || 0

    // Determine system health
    stats.systemHealth = 'healthy'

    return { success: true, stats }

  } catch (error) {
    console.error('Error getting system stats:', error)
    return { success: false, error: error }
  }
}
