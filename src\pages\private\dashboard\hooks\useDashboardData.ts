import { useState, useEffect } from 'react'
import { useAuth } from '../../../../contexts/AuthContext'
// TODO: dashboardService not yet extracted - using old import temporarily
import { dashboardService } from '../../../../services/supabase'
// import { matchService } from '../../../../services/matchService' // TODO: Confirm usage

interface DashboardStats {
  totalMatches: number
  wins: number
  losses: number
  winRate: number
  diamondBalance: number
  currentRank: string
  todayMatches: number
  weeklyEarnings: number
}

interface LiveMatch {
  id: string
  game: string
  teams: string[]
  status: 'live' | 'upcoming'
  startTime: string
  prizePool: number
}

export const useDashboardData = () => {
  const { user } = useAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [liveMatches, setLiveMatches] = useState<LiveMatch[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return

      try {
        setIsLoading(true)
        setError(null)

        // Get real user stats from database
        const { data: userStatsData, error: statsError } = await dashboardService.getUserStats(user.id)

        if (statsError) {
          throw new Error((statsError as any)?.message || 'Failed to load user stats')
        }

        const userData = userStatsData?.user
        const userStatistics = userStatsData?.stats

        // Calculate real stats
        const totalMatches = userData?.total_matches || 0
        const wins = userData?.wins || 0
        const losses = userData?.losses || 0
        const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0

        const realStats: DashboardStats = {
          totalMatches,
          wins,
          losses,
          winRate,
          diamondBalance: userData?.diamond_balance || 0,
          currentRank: calculateRank(wins, totalMatches),
          todayMatches: userStatistics?.daily_matches || 0,
          weeklyEarnings: userStatistics?.weekly_earnings || 0
        }

        // Get real live matches
        const { data: matchesData, error: matchesError } = await dashboardService.getLiveMatches(5)

        if (matchesError) {
          console.error('Failed to load live matches:', matchesError)
        }

        const realLiveMatches: LiveMatch[] = (matchesData || []).map((match: any) => ({
          id: match.id,
          game: match.game,
          teams: [
            match.host?.username || 'Host',
            `${match.current_players}/${match.max_players} Players`
          ],
          status: match.status === 'ongoing' ? 'live' : 'upcoming',
          startTime: match.scheduled_start_time || match.created_at,
          prizePool: match.pot_amount
        }))

        setStats(realStats)
        setLiveMatches(realLiveMatches)
      } catch (err: any) {
        setError(err.message || 'Failed to load dashboard data')
        console.error('Dashboard data error:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [user])

  // Helper function to calculate rank based on wins and matches
  const calculateRank = (wins: number, totalMatches: number): string => {
    const winRate = totalMatches > 0 ? (wins / totalMatches) * 100 : 0

    if (wins >= 100 && winRate >= 80) return 'Mythic'
    if (wins >= 50 && winRate >= 70) return 'Legend'
    if (wins >= 25 && winRate >= 60) return 'Epic'
    if (wins >= 10 && winRate >= 50) return 'Grandmaster'
    if (wins >= 5) return 'Master'
    return 'Elite'
  }

  const refreshData = async () => {
    if (!user) return

    try {
      setIsLoading(true)
      setError(null)

      // Reload user stats
      const { data: userStatsData, error: statsError } = await dashboardService.getUserStats(user.id)

      if (statsError) {
        throw new Error((statsError as any)?.message || 'Failed to refresh user stats')
      }

      const userData = userStatsData?.user
      const userStatistics = userStatsData?.stats

      const totalMatches = userData?.total_matches || 0
      const wins = userData?.wins || 0
      const losses = userData?.losses || 0
      const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0

      const refreshedStats: DashboardStats = {
        totalMatches,
        wins,
        losses,
        winRate,
        diamondBalance: userData?.diamond_balance || 0,
        currentRank: calculateRank(wins, totalMatches),
        todayMatches: userStatistics?.daily_matches || 0,
        weeklyEarnings: userStatistics?.weekly_earnings || 0
      }

      // Reload live matches
      const { data: matchesData, error: matchesError } = await dashboardService.getLiveMatches(5)

      if (!matchesError) {
        const refreshedLiveMatches: LiveMatch[] = (matchesData || []).map((match: any) => ({
          id: match.id,
          game: match.game,
          teams: [
            match.host?.username || 'Host',
            `${match.current_players}/${match.max_players} Players`
          ],
          status: match.status === 'ongoing' ? 'live' : 'upcoming',
          startTime: match.scheduled_start_time || match.created_at,
          prizePool: match.pot_amount
        }))
        setLiveMatches(refreshedLiveMatches)
      }

      setStats(refreshedStats)
    } catch (err: any) {
      setError(err.message || 'Failed to refresh dashboard data')
      console.error('Dashboard refresh error:', err)
    } finally {
      setIsLoading(false)
    }
  }

  return {
    stats,
    liveMatches,
    isLoading,
    error,
    refreshData
  }
}
