
// 🏗️ ADMIN LAYOUT
// Professional admin dashboard layout

import React, { useState, useEffect } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'
import {
  Crown,
  Users,
  Shield,
  Settings,
  BarChart3,
  FileText,
  Bell,
  LogOut,
  Menu,
  X,
  Home,
  UserCheck,
  Gavel,
  Activity,
  UserPlus,
  CreditCard
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'

interface AdminLayoutProps {
  children: React.ReactNode
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { user, signOut } = useAuth()
  const location = useLocation()
  const navigate = useNavigate()
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [adminUser, setAdminUser] = useState<any>(null)
  const [notifications] = useState<any[]>([])

  useEffect(() => {
    loadAdminData()
  }, [user])

  const loadAdminData = async () => {
    if (!user) return

    try {
      const adminData = await AdminService.getAdminUser(user.id)
      setAdminUser(adminData)
    } catch (error) {
      console.error('Error loading admin data:', error)
    }
  }

  const handleSignOut = async () => {
    try {
      // Log admin logout
      if (user) {
        await AdminService.logAdminAction(
          user.id,
          'ADMIN_LOGOUT',
          'system',
          user.id
        )
      }
      
      await signOut()
      navigate('/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  const adminNavigation = [
    {
      name: 'Dashboard',
      href: '/admin-dashboard-2024',
      icon: BarChart3,
      description: 'Overview and statistics'
    },
    {
      name: 'User Management',
      href: '/admin-dashboard-2024/users',
      icon: Users,
      description: 'Manage platform users'
    },
    {
      name: 'Invite Team',
      href: '/admin-dashboard-2024/invite-team',
      icon: UserPlus,
      description: 'Invite team members',
      adminLevel: 3
    },
    {
      name: 'Referee Applications',
      href: '/admin-dashboard-2024/referees',
      icon: UserCheck,
      description: 'Review referee applications'
    },
    {
      name: 'Match Management',
      href: '/admin-dashboard-2024/matches',
      icon: Gavel,
      description: 'Oversee matches and disputes'
    },
    {
      name: 'Payment Confirmation',
      href: '/admin-dashboard-2024/payments',
      icon: CreditCard,
      description: 'Review and process payments'
    },
    {
      name: 'System Settings',
      href: '/admin-dashboard-2024/settings',
      icon: Settings,
      description: 'Platform configuration'
    },
    {
      name: 'Audit Logs',
      href: '/admin-dashboard-2024/audit',
      icon: FileText,
      description: 'Admin activity logs'
    },
    {
      name: 'Analytics',
      href: '/admin-dashboard-2024/analytics',
      icon: Activity,
      description: 'Platform analytics'
    }
  ]

  const getAdminLevelBadge = (level: number) => {
    switch (level) {
      case 3:
        return { label: 'Super Admin', color: 'bg-red-500', icon: Crown }
      case 2:
        return { label: 'Platform Admin', color: 'bg-purple-500', icon: Shield }
      case 1:
        return { label: 'Referee Coordinator', color: 'bg-blue-500', icon: UserCheck }
      default:
        return { label: 'User', color: 'bg-gray-500', icon: Users }
    }
  }

  const adminBadge = getAdminLevelBadge(adminUser?.admin_level || 0)
  const BadgeIcon = adminBadge.icon

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile sidebar backdrop */}
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`fixed inset-y-0 left-0 z-50 w-80 bg-gradient-to-b from-slate-900 via-blue-900 to-indigo-900 transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
        isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="flex flex-col h-full overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-white/10">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center">
                <Crown className="w-6 h-6 text-black font-bold" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-white">Gambets Admin</h1>
                <p className="text-blue-200 text-sm">Control Panel</p>
              </div>
            </div>
            <button
              onClick={() => setIsSidebarOpen(false)}
              className="lg:hidden text-white hover:text-gray-300 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Admin Info */}
          <div className="p-6 border-b border-white/10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-12 h-12 bg-white/10 rounded-full flex items-center justify-center">
                <BadgeIcon className="w-6 h-6 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="text-white font-medium">{user?.user_metadata?.username || user?.email}</h3>
                <p className="text-blue-200 text-sm">{user?.email}</p>
              </div>
            </div>
            <div className={`inline-flex items-center space-x-2 ${adminBadge.color} text-white px-3 py-1 rounded-full text-sm`}>
              <BadgeIcon className="w-4 h-4" />
              <span>{adminBadge.label}</span>
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-6 space-y-2 overflow-y-auto admin-sidebar-scroll">
            {adminNavigation.map((item) => {
              const isActive = location.pathname === item.href
              const Icon = item.icon

              // Hide super admin only items if user doesn't have level 3
              if (item.adminLevel === 3 && adminUser?.admin_level < 3) {
                return null
              }

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 group ${
                    isActive
                      ? 'bg-white/20 text-white border border-white/30'
                      : 'text-blue-200 hover:bg-white/10 hover:text-white'
                  }`}
                  onClick={() => setIsSidebarOpen(false)}
                >
                  <Icon className={`w-5 h-5 ${isActive ? 'text-yellow-400' : 'group-hover:text-yellow-400'} transition-colors`} />
                  <div className="flex-1">
                    <div className="font-medium">{item.name}</div>
                    <div className="text-xs opacity-75">{item.description}</div>
                  </div>
                  {item.adminLevel === 3 && (
                    <Crown className="w-4 h-4 text-yellow-400" />
                  )}
                </Link>
              )
            })}
          </nav>

          {/* Footer */}
          <div className="p-6 border-t border-white/10 space-y-3">
            <Link
              to="/dashboard"
              className="flex items-center space-x-3 text-blue-200 hover:text-white transition-colors"
            >
              <Home className="w-5 h-5" />
              <span>Back to User Dashboard</span>
            </Link>
            <button
              onClick={handleSignOut}
              className="flex items-center space-x-3 text-red-300 hover:text-red-200 transition-colors w-full"
            >
              <LogOut className="w-5 h-5" />
              <span>Sign Out</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="lg:ml-80">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="flex items-center justify-between px-6 py-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setIsSidebarOpen(true)}
                className="lg:hidden text-gray-600 hover:text-gray-900 transition-colors"
              >
                <Menu className="w-6 h-6" />
              </button>
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {adminNavigation.find(item => item.href === location.pathname)?.name || 'Admin Dashboard'}
                </h2>
                <p className="text-sm text-gray-600">
                  {adminNavigation.find(item => item.href === location.pathname)?.description || 'Administrative control panel'}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="relative p-2 text-gray-600 hover:text-gray-900 transition-colors">
                <Bell className="w-6 h-6" />
                {notifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                    {notifications.length}
                  </span>
                )}
              </button>

              {/* Security Indicator */}
              <div className="flex items-center space-x-2 bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm">
                <Shield className="w-4 h-4" />
                <span>Secure Session</span>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

export default AdminLayout
