import { useState, useEffect } from 'react'
import {
  CheckCircle, XCircle, Eye, Gem,
  DollarSign, Download, Smartphone, CreditCard, Building2
} from 'lucide-react'
import { adminDepositService } from '../../services/manualPaymentService'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'

interface DepositRequest {
  id: string
  user_id: string
  amount_php: number
  amount_usd: number
  base_diamonds: number
  bonus_diamonds: number
  total_diamonds: number
  bonus_percentage: number
  payment_method: string
  payment_proof?: string
  payment_proof_url?: string
  reference_number?: string
  user_notes?: string
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  user?: {
    id: string
    username: string
    email: string
    first_name: string
    last_name: string
    diamond_balance: number
  }
}

export default function DepositManagement() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [deposits, setDeposits] = useState<DepositRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedDeposit, setSelectedDeposit] = useState<DepositRequest | null>(null)
  const [processingId, setProcessingId] = useState<string | null>(null)
  const [adminNotes, setAdminNotes] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve')
  const [showProofModal, setShowProofModal] = useState(false)
  const [proofImageUrl, setProofImageUrl] = useState<string>('')

  useEffect(() => {
    loadPendingDeposits()
  }, [])

  const loadPendingDeposits = async () => {
    setLoading(true)
    try {
      const { data, error } = await adminDepositService.getPendingDeposits()
      if (error) {
        console.error('Error loading deposits:', error)
        addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to load deposit requests'
        })
      } else {
        setDeposits(data || [])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProcessDeposit = async () => {
    if (!selectedDeposit || !user) return

    setProcessingId(selectedDeposit.id)
    try {
      const { error } = await adminDepositService.processDeposit(
        selectedDeposit.id,
        actionType,
        user.id,
        adminNotes
      )

      if (error) {
        addNotification({
          type: 'error',
          title: 'Error',
          message: `Failed to ${actionType} deposit`
        })
      } else {
        addNotification({
          type: 'success',
          title: 'Success',
          message: `Deposit ${actionType}d successfully`
        })
        loadPendingDeposits()
        setShowModal(false)
        setSelectedDeposit(null)
        setAdminNotes('')
      }
    } catch (error) {
      console.error('Error processing deposit:', error)
    } finally {
      setProcessingId(null)
    }
  }

  const viewPaymentProof = async (deposit: DepositRequest) => {
    if (!deposit.payment_proof) return

    try {
      const { data, error } = await adminDepositService.getDepositWithProof(deposit.id)
      if (error || !data?.payment_proof_url) {
        addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to load payment proof'
        })
        return
      }

      setProofImageUrl(data.payment_proof_url)
      setShowProofModal(true)
    } catch (error) {
      console.error('Error loading proof:', error)
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'gcash':
        return <Smartphone className="w-4 h-4" />
      case 'maya':
        return <CreditCard className="w-4 h-4" />
      case 'bank_transfer':
        return <Building2 className="w-4 h-4" />
      default:
        return <DollarSign className="w-4 h-4" />
    }
  }

  const formatCurrency = (amount: number) => `₱${amount.toLocaleString()}`

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Deposit Management</h2>
            <p className="text-sm text-gray-600 mt-1">
              Review and process pending deposit requests
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
              {deposits.length} Pending
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        {deposits.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
            <p className="text-gray-600">No pending deposit requests at the moment.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {deposits.map((deposit) => (
              <div
                key={deposit.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        {getPaymentMethodIcon(deposit.payment_method)}
                        <span className="font-medium text-gray-900 capitalize">
                          {deposit.payment_method.replace('_', ' ')}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(deposit.created_at).toLocaleDateString()}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">User</p>
                        <p className="font-medium">
                          {deposit.user?.first_name} {deposit.user?.last_name}
                        </p>
                        <p className="text-sm text-gray-500">@{deposit.user?.username}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Amount</p>
                        <p className="font-medium">{formatCurrency(deposit.amount_php)}</p>
                        <p className="text-sm text-gray-500">
                          Ref: {deposit.reference_number || 'N/A'}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Diamonds</p>
                        <div className="flex items-center space-x-2">
                          <Gem className="w-4 h-4 text-blue-500" />
                          <span className="font-medium">{deposit.total_diamonds.toLocaleString()} 💎</span>
                        </div>
                        {deposit.bonus_diamonds > 0 && (
                          <p className="text-sm text-green-600">
                            +{deposit.bonus_diamonds.toLocaleString()} bonus ({deposit.bonus_percentage}%)
                          </p>
                        )}
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Current Balance</p>
                        <p className="font-medium">{deposit.user?.diamond_balance?.toLocaleString() || 0} 💎</p>
                        {deposit.user_notes && (
                          <p className="text-sm text-gray-500 mt-1">
                            Note: {deposit.user_notes}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    {deposit.payment_proof && (
                      <button
                        onClick={() => viewPaymentProof(deposit)}
                        className="bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    )}
                    <button
                      onClick={() => {
                        setSelectedDeposit(deposit)
                        setActionType('approve')
                        setShowModal(true)
                      }}
                      className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => {
                        setSelectedDeposit(deposit)
                        setActionType('reject')
                        setShowModal(true)
                      }}
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                    >
                      Reject
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Processing Modal */}
      {showModal && selectedDeposit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              {actionType === 'approve' ? 'Approve' : 'Reject'} Deposit
            </h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                User: {selectedDeposit.user?.first_name} {selectedDeposit.user?.last_name}
              </p>
              <p className="text-sm text-gray-600 mb-2">
                Amount: {formatCurrency(selectedDeposit.amount_php)}
              </p>
              <p className="text-sm text-gray-600 mb-4">
                Diamonds: {selectedDeposit.total_diamonds.toLocaleString()} 💎
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Notes {actionType === 'reject' ? '(Required)' : '(Optional)'}
              </label>
              <textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
                placeholder={
                  actionType === 'approve' 
                    ? 'Optional notes about the approval...'
                    : 'Please provide a reason for rejection...'
                }
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowModal(false)
                  setSelectedDeposit(null)
                  setAdminNotes('')
                }}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleProcessDeposit}
                disabled={processingId === selectedDeposit.id || (actionType === 'reject' && !adminNotes.trim())}
                className={`flex-1 py-2 px-4 rounded-md text-white font-medium transition-colors ${
                  actionType === 'approve'
                    ? 'bg-green-500 hover:bg-green-600 disabled:bg-green-300'
                    : 'bg-red-500 hover:bg-red-600 disabled:bg-red-300'
                }`}
              >
                {processingId === selectedDeposit.id ? 'Processing...' : 
                 actionType === 'approve' ? 'Approve' : 'Reject'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Payment Proof Modal */}
      {showProofModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-auto">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Payment Proof</h3>
              <button
                onClick={() => {
                  setShowProofModal(false)
                  setProofImageUrl('')
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
            
            <div className="text-center">
              <img
                src={proofImageUrl}
                alt="Payment Proof"
                className="max-w-full max-h-96 mx-auto rounded-lg shadow-sm"
                onError={() => {
                  addNotification({
                    type: 'error',
                    title: 'Error',
                    message: 'Failed to load payment proof image'
                  })
                }}
              />
              <div className="mt-4">
                <a
                  href={proofImageUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center gap-2 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors"
                >
                  <Download className="w-4 h-4" />
                  Download Full Size
                </a>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
