import { supabase } from './supabase'

export interface CreateNotificationData {
  user_id: string
  type: string
  title: string
  message: string
  data?: any
}

export const notificationService = {
  // Get user notifications with pagination
  async getUserNotifications(userId: string, options: {
    limit?: number
    offset?: number
    unreadOnly?: boolean
    type?: string
  } = {}) {
    try {
      const { limit = 20, offset = 0, unreadOnly = false, type } = options

      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1)

      if (unreadOnly) {
        query = query.eq('is_read', false)
      }

      if (type) {
        query = query.eq('type', type)
      }

      const { data, error } = await query

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get unread notification count
  async getUnreadCount(userId: string) {
    try {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('is_read', false)

      if (error) throw error
      return { count: count || 0, error: null }
    } catch (error) {
      return { count: 0, error }
    }
  },

  // Mark notification as read
  async markAsRead(notificationId: string) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Mark all notifications as read for a user
  async markAllAsRead(userId: string) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', userId)
        .eq('is_read', false)
        .select()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Delete notification
  async deleteNotification(notificationId: string) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create new notification
  async createNotification(notificationData: CreateNotificationData) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert([notificationData])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Bulk create notifications
  async createBulkNotifications(notifications: CreateNotificationData[]) {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert(notifications)
        .select()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },



  // Subscribe to real-time notifications
  subscribeToNotifications(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`notifications:${userId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      }, callback)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // Helper functions for common notification types
  async notifyMatchInvite(userId: string, matchId: string, inviterName: string, game: string) {
    return this.createNotification({
      user_id: userId,
      type: 'match_invite',
      title: 'Match Invitation',
      message: `${inviterName} invited you to join a ${game} match!`,
      data: { match_id: matchId, inviter: inviterName, game }
    })
  },

  async notifyMatchResult(userId: string, matchId: string, won: boolean, diamonds: number, game: string) {
    return this.createNotification({
      user_id: userId,
      type: 'match_result',
      title: won ? 'Victory! 🏆' : 'Match Complete',
      message: won
        ? `Congratulations! You won the ${game} match and earned ${diamonds} diamonds!`
        : `Your ${game} match has ended. Better luck next time!`,
      data: { match_id: matchId, won, diamonds, game }
    })
  },

  async notifyPaymentSuccess(userId: string, amount: number, diamonds: number) {
    return this.createNotification({
      user_id: userId,
      type: 'payment',
      title: 'Payment Successful! 💎',
      message: `Your payment of ₱${amount} has been processed. ${diamonds} diamonds added to your account!`,
      data: { amount, diamonds }
    })
  },

  async notifySystemMessage(userId: string, title: string, message: string) {
    return this.createNotification({
      user_id: userId,
      type: 'system',
      title,
      message
    })
  },

  // Create sample notifications for testing
  async createSampleNotifications(userId: string) {
    const sampleNotifications = [
      {
        user_id: userId,
        type: 'match_invite',
        title: 'Match Invitation',
        message: 'Player123 invited you to join a Mobile Legends match!',
        data: { match_id: 'sample-match-1', inviter: 'Player123', game: 'Mobile Legends' }
      },
      {
        user_id: userId,
        type: 'match_result',
        title: 'Victory! 🏆',
        message: 'Congratulations! You won the Valorant match and earned 50 diamonds!',
        data: { match_id: 'sample-match-2', won: true, diamonds: 50, game: 'Valorant' }
      },
      {
        user_id: userId,
        type: 'payment',
        title: 'Payment Successful! 💎',
        message: 'Your payment of ₱100 has been processed. 100 diamonds added to your account!',
        data: { amount: 100, diamonds: 100 }
      },
      {
        user_id: userId,
        type: 'tournament',
        title: 'Tournament Starting Soon',
        message: 'The Mobile Legends Championship starts in 30 minutes!',
        data: { tournament_id: 'sample-tournament-1', tournament_name: 'Mobile Legends Championship' }
      },
      {
        user_id: userId,
        type: 'system',
        title: 'Welcome to Gambets! 🎮',
        message: 'Your account has been successfully created. Start playing and earning diamonds!',
        data: {}
      }
    ]

    try {
      const results = await Promise.all(
        sampleNotifications.map(notification => this.createNotification(notification))
      )
      return { data: results, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
