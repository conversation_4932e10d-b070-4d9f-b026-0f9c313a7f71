/**
 * @deprecated This file is being phased out in favor of modular service architecture.
 *
 * NEW MODULAR SERVICES (Use these instead):
 * - authService.ts - Authentication functions
 * - userService.ts - User profile and preferences
 * - matchService.ts - Match-related functions
 * - walletService.ts - Payment and diamond transactions
 * - realtimeService.ts - Real-time subscriptions
 * - referralService.ts - Referral system functions
 * - refereeService.ts - Referee applications and oversight
 * - dashboardService.ts - Analytics and dashboard stats
 * - communityService.ts - Community groups and global chat
 * - tournamentService.ts - Tournament creation and management
 * - platformService.ts - Miscellaneous platform functions
 *
 * The supabase client is now centralized in supabaseClient.ts
 *
 * TODO: Remove this file once all imports are updated to use modular services
 */

// Import the centralized Supabase client instead of creating a new one
import { supabase } from './supabaseClient'

// Re-export for backward compatibility
export { supabase }

// Types - Updated to match your existing database schema
export interface User {
  id: string
  email: string
  username: string
  first_name: string
  last_name: string
  mlbb_id?: string
  valorant_id?: string
  dota_id?: string
  cod_id?: string
  pubg_id?: string
  lol_id?: string
  cs_id?: string
  diamond_balance: number
  total_matches: number
  wins: number
  losses: number
  referral_code?: string
  referred_by?: string
  created_at: string
  updated_at: string
}

// Use the unified Match interface from types
export interface DatabaseMatch {
  id: string
  title: string
  game: string
  mode: string
  game_type?: string
  pot_amount: number
  entry_fee: number
  diamond_pot: number
  max_players: number
  current_players: number
  host_id: string
  referee_id?: string
  winner_id?: string
  status: string
  rules?: string
  room_code?: string
  match_link?: string
  region?: string
  referee_notes?: string
  proof_required?: boolean
  scheduled_start_time?: string
  actual_start_time?: string
  actual_end_time?: string
  match_duration_minutes?: number
  dispute_count?: number
  created_at: string
  updated_at?: string

  // Joined fields from view
  host_username?: string
  host_first_name?: string
  host_last_name?: string
  referee_username?: string
  referee_first_name?: string
  referee_last_name?: string
  winner_username?: string
  current_participants?: number
}

export interface Transaction {
  id: string
  user_id: string
  type: string
  amount: number
  description?: string
  match_id?: string
  payment_method?: string
  payment_reference?: string
  status: string
  created_at: string
}

// Auth functions
export const authService = {
  // Sign up
  async signUp(email: string, password: string, userData: any) {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData
        }
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign in
  async signIn(email: string, password: string) {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })
      
      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Sign out
  async signOut() {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  // Get current user
  async getCurrentUser() {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      if (error) throw error
      return { user, error: null }
    } catch (error) {
      return { user: null, error }
    }
  },

  // Get session
  async getSession() {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()
      if (error) throw error
      return { session, error: null }
    } catch (error) {
      return { session: null, error }
    }
  }
}

// User functions - Updated to work with your 'users' table
export const userService = {
  // Get user profile
  async getProfile(userId: string) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update user profile
  async updateProfile(userId: string, updates: Partial<User>) {
    try {
      const { data, error } = await supabase
        .from('users')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update diamond balance
  async updateDiamondBalance(userId: string, amount: number, operation: 'add' | 'subtract') {
    try {
      const { data: profile } = await this.getProfile(userId)
      if (!profile) throw new Error('User not found')

      const newBalance = operation === 'add'
        ? profile.diamond_balance + amount
        : profile.diamond_balance - amount

      if (newBalance < 0) throw new Error('Insufficient balance')

      const { data, error } = await supabase
        .from('users')
        .update({
          diamond_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create user profile (for signup)
  async createProfile(userData: Omit<User, 'created_at' | 'updated_at'>) {
    try {
      const { data, error } = await supabase
        .from('users')
        .insert([{
          ...userData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

// Match functions - Updated for your schema
export const matchService = {
  // Get all matches (simplified query to avoid 400 errors)
  async getMatches(filters?: {
    game?: string;
    status?: string;
    format?: string;
    minPot?: number;
    maxPot?: number;
    search?: string;
    sortBy?: 'newest' | 'highest-bet' | 'soonest';
  }) {
    try {
      // Start with basic matches query to avoid join issues
      let query = supabase
        .from('matches')
        .select('*')

      // Apply filters
      if (filters?.game) {
        query = query.eq('game', filters.game)
      }
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }
      if (filters?.format) {
        query = query.eq('mode', filters.format)
      }
      if (filters?.minPot) {
        query = query.gte('pot_amount', filters.minPot)
      }
      if (filters?.maxPot) {
        query = query.lte('pot_amount', filters.maxPot)
      }
      if (filters?.search) {
        query = query.or(`game.ilike.%${filters.search}%,mode.ilike.%${filters.search}%,rules.ilike.%${filters.search}%`)
      }

      // Apply sorting
      switch (filters?.sortBy) {
        case 'highest-bet':
          query = query.order('pot_amount', { ascending: false })
          break
        case 'soonest':
          query = query.order('scheduled_start_time', { ascending: true })
          break
        case 'newest':
        default:
          query = query.order('created_at', { ascending: false })
          break
      }

      const { data: matches, error } = await query
      if (error) throw error

      // If we have matches, get user details separately to avoid join issues
      if (matches && matches.length > 0) {
        const userIds = new Set()
        matches.forEach(match => {
          if (match.host_id) userIds.add(match.host_id)
          if (match.referee_id) userIds.add(match.referee_id)
          if (match.winner_id) userIds.add(match.winner_id)
        })

        // Get user details for all involved users
        const { data: users } = await supabase
          .from('users')
          .select('id, username, first_name, last_name')
          .in('id', Array.from(userIds))

        // Create user lookup map
        const userMap = {}
        if (users) {
          users.forEach(user => {
            (userMap as any)[user.id] = user
          })
        }

        // Enhance matches with user data
        const enhancedMatches = matches.map(match => ({
          ...match,
          host: (userMap as any)[match.host_id] || null,
          referee: (userMap as any)[match.referee_id] || null,
          winner: (userMap as any)[match.winner_id] || null
        }))

        return { data: enhancedMatches, error: null }
      }

      return { data: matches, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create match with host entry fee deduction and auto-assign referee
  async createMatch(matchData: Omit<DatabaseMatch, 'id' | 'created_at' | 'updated_at' | 'host_username' | 'referee_username' | 'winner_username' | 'current_participants'>) {
    try {
      // BUSINESS RULE: Check if user already has an active match
      console.log('🔍 Checking for existing active matches for user:', matchData.host_id)
      const { data: existingMatches } = await supabase
        .from('matches')
        .select('id, title, game, status')
        .eq('host_id', matchData.host_id)
        .in('status', ['open', 'waiting', 'full', 'ongoing', 'in_progress', 'active', 'battling', 'fighting'])

      console.log('🔍 Found existing matches:', existingMatches?.length || 0, existingMatches)

      if (existingMatches && existingMatches.length > 0) {
        const activeMatch = existingMatches[0]
        console.log('❌ Blocking match creation due to existing active match:', activeMatch)
        throw new Error(`You already have an active match: "${activeMatch.title || activeMatch.game}". Please cancel it before creating a new match.`)
      }

      console.log('✅ No active matches found, proceeding with creation')

      // Check if host has enough diamonds
      const { data: hostData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', matchData.host_id)
        .single()

      if (!hostData || (hostData.diamond_balance || 0) < matchData.entry_fee) {
        throw new Error('Insufficient diamond balance')
      }

      // Create match first to get match ID for conflict checking
      const { data: newMatch, error: matchError } = await supabase
        .from('matches')
        .insert([{
          ...matchData,
          // Remove referee_id since column doesn't exist
          created_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (matchError) throw matchError

      // Add host as a participant (needed for conflict checking)
      try {
        await supabase
          .from('match_participants')
          .insert([{
            match_id: newMatch.id,
            user_id: matchData.host_id,
            team_number: 1, // Host is team 1
            joined_at: new Date().toISOString()
          }])
      } catch (participantError) {
        console.warn('Could not add host as participant:', participantError)
        // Continue anyway - this is for conflict checking
      }

      // Try to get a conflict-free referee for this specific match
      const availableReferee = await this.getAvailableReferee(newMatch.id)

      // Deduct entry fee from host's balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          diamond_balance: (hostData.diamond_balance || 0) - matchData.entry_fee
        })
        .eq('id', matchData.host_id)

      if (balanceError) throw balanceError

      // Skip referee assignment since referee_id column doesn't exist
      // TODO: Add referee_id column to database if referee system is needed
      if (availableReferee) {
        console.log('Referee available but referee_id column missing:', availableReferee.id)
      }

      // Create transaction record for host entry
      await supabase
        .from('transactions')
        .insert([{
          user_id: matchData.host_id,
          type: 'match_entry',
          amount: -matchData.entry_fee,
          status: 'completed',
          description: `Created ${matchData.game} ${matchData.mode} match`,
          match_id: newMatch.id,
          created_at: new Date().toISOString()
        }])

      // Return the match with updated referee info
      const { data: finalMatch } = await supabase
        .from('matches')
        .select('*')
        .eq('id', newMatch.id)
        .single()

      return { data: finalMatch || newMatch, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get count of available referees (ultra-simplified)
  async getAvailableRefereeCount() {
    try {
      // Just get basic user count and return a reasonable estimate
      const { count, error } = await supabase
        .from('users')
        .select('id', { count: 'exact', head: true })

      if (error) {
        console.error('Error getting user count for referee estimate:', error)
        return 3 // Fallback number
      }

      // Estimate that 20% of users could be referees (reasonable assumption)
      const estimatedReferees = Math.max(1, Math.floor((count || 0) * 0.2))
      return Math.min(estimatedReferees, 10) // Cap at 10 for display purposes
    } catch (error) {
      console.error('Error getting referee count:', error)
      return 3 // Fallback number
    }
  },

  // Get an available referee for match assignment (with conflict checking)
  async getAvailableReferee(matchId?: string) {
    try {
      // Get users who are approved referees and currently available
      // Exclude level referees (admin_level >= 1) as they shouldn't referee regular matches
      const { data: referees, error } = await supabase
        .from('users')
        .select('id, username')
        .eq('referee_status', 'approved')
        .is('admin_level', null) // Exclude level referees
        .order('created_at', { ascending: true }) // FIFO assignment

      if (error) throw error
      if (!referees || referees.length === 0) return null

      // If no specific match, return first available referee
      if (!matchId) {
        return referees[0]
      }

      // Check each referee for conflicts with the specific match
      const { refereeConflictService } = await import('./refereeConflictService')

      for (const referee of referees) {
        const conflictResult = await refereeConflictService.checkRefereeConflict(referee.id, matchId)

        if (!conflictResult.hasConflict) {
          return referee
        }
      }

      console.log('No conflict-free referees available for match:', matchId)
      return null
    } catch (error) {
      console.log('Error finding available referee:', error)
      return null
    }
  },

  // Join match - complete transaction with diamond deduction
  async joinMatch(matchId: string, userId: string) {
    try {
      // First check if match is available
      const { data: match } = await supabase
        .from('matches')
        .select('*')
        .eq('id', matchId)
        .single()

      if (!match) throw new Error('Match not found')

      // Prevent creator from joining their own match
      if (match.host_id === userId) {
        throw new Error('You cannot join your own match')
      }

      // Check if user is assigned as referee for this match
      if (match.referee_id === userId) {
        throw new Error('You cannot participate in matches you are refereeing')
      }

      // BUSINESS RULE: Check if user has created any active matches
      const { data: userCreatedMatches } = await supabase
        .from('matches')
        .select('id, title, game, status')
        .eq('host_id', userId)
        .in('status', ['open', 'waiting', 'full', 'ongoing', 'in_progress', 'active', 'battling', 'fighting'])

      if (userCreatedMatches && userCreatedMatches.length > 0) {
        const activeMatch = userCreatedMatches[0]
        throw new Error(`You cannot join other matches while you have an active match: "${activeMatch.title || activeMatch.game}". Please cancel your match first.`)
      }

      if (match.current_players >= match.max_players) {
        throw new Error('Match is full')
      }

      if (match.status !== 'open') {
        throw new Error('Match is no longer available')
      }

      // Check if user is already in this match
      const { data: existingParticipant } = await supabase
        .from('match_participants')
        .select('id')
        .eq('match_id', matchId)
        .eq('user_id', userId)
        .single()

      if (existingParticipant) {
        throw new Error('You have already joined this match')
      }

      // Check if user has any active matches (prevent joining multiple)
      const { data: activeMatches } = await supabase
        .from('match_participants')
        .select('match_id, matches!inner(status)')
        .eq('user_id', userId)
        .in('matches.status', ['open', 'ongoing'])

      if (activeMatches && activeMatches.length > 0) {
        throw new Error('You must complete or cancel your current match before joining another')
      }

      // Check if user has enough diamonds
      const { data: userData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (!userData || (userData.diamond_balance || 0) < match.entry_fee) {
        throw new Error('Insufficient diamond balance')
      }

      // Deduct entry fee from user's balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          diamond_balance: (userData.diamond_balance || 0) - match.entry_fee
        })
        .eq('id', userId)

      if (balanceError) throw balanceError

      // Create transaction record
      await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'match_entry',
          amount: -match.entry_fee,
          status: 'completed',
          description: `Joined ${match.game} ${match.mode} match`,
          match_id: matchId,
          created_at: new Date().toISOString()
        }])

      // Try to add user to match participants (may fail due to permissions)
      try {
        const { error: participantError } = await supabase
          .from('match_participants')
          .insert([{ match_id: matchId, user_id: userId }])

        if (participantError) {
          console.log('Could not add to match_participants (permission issue):', participantError)
          // Continue anyway - we'll track via match status
        }
      } catch (participantError) {
        console.log('Participant insertion failed, continuing:', participantError)
      }

      // Update match current players count and status
      const newPlayerCount = match.current_players + 1
      const willBeFull = newPlayerCount >= match.max_players

      const updateData = {
        current_players: newPlayerCount,
        status: willBeFull ? 'full' : 'open'
      }

      const { data, error } = await supabase
        .from('matches')
        .update(updateData)
        .eq('id', matchId)
        .select()

      if (error) throw error

      // Return the first match if multiple returned, or null if none
      const matchData = Array.isArray(data) ? data[0] : data
      return { data: matchData, error: null }
    } catch (error) {
      console.error('Join match error:', error)
      return { data: null, error }
    }
  },

  // Cancel match (only for match creator)
  async cancelMatch(matchId: string, userId: string) {
    try {
      // First check if match exists and user is the host
      const { data: match } = await supabase
        .from('matches')
        .select('*')
        .eq('id', matchId)
        .eq('host_id', userId) // Only host can cancel
        .single()

      if (!match) {
        throw new Error('Match not found or you are not the host')
      }

      // Check if match is already cancelled (prevent duplicate cancellations)
      if (match.status.toLowerCase() === 'cancelled') {
        throw new Error('Match is already cancelled')
      }

      // Check if match cannot be cancelled (only prevent cancelling completed matches)
      const normalizedStatus = match.status.toLowerCase()
      if (normalizedStatus === 'completed') {
        throw new Error('Cannot cancel a completed match')
      }

      // Allow cancelling matches in any other status (open, full, in_progress, etc.)
      // Host should be able to cancel their match even if it's in progress
      console.log('✅ Match can be cancelled, current status:', match.status)

      // Update match status to cancelled (simplest possible update)
      console.log('🔧 Updating match status to cancelled...')
      const { error: matchError } = await supabase
        .from('matches')
        .update({ status: 'cancelled' })
        .eq('id', matchId)
        .eq('host_id', userId) // Double-check ownership

      if (matchError) {
        console.error('❌ Match update error:', matchError)
        throw matchError
      }

      console.log('✅ Match status updated successfully')

      // Refund entry fee to host
      const { data: hostData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (hostData) {
        await supabase
          .from('users')
          .update({
            diamond_balance: (hostData.diamond_balance || 0) + match.entry_fee
          })
          .eq('id', userId)
      }

      // Create refund transaction record
      await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'match_refund',
          amount: match.entry_fee,
          status: 'completed',
          description: `Match cancelled - refund for ${match.game} ${match.mode}`,
          match_id: matchId,
          created_at: new Date().toISOString()
        }])

      // Try to refund other participants (if any)
      try {
        const { data: participants } = await supabase
          .from('match_participants')
          .select('user_id')
          .eq('match_id', matchId)

        if (participants && participants.length > 0) {
          for (const participant of participants) {
            if (participant.user_id !== userId) { // Don't refund host twice
              // Get participant's current balance
              const { data: participantData } = await supabase
                .from('users')
                .select('diamond_balance')
                .eq('id', participant.user_id)
                .single()

              if (participantData) {
                // Refund participant
                await supabase
                  .from('users')
                  .update({
                    diamond_balance: (participantData.diamond_balance || 0) + match.entry_fee
                  })
                  .eq('id', participant.user_id)

                // Create refund transaction
                await supabase
                  .from('transactions')
                  .insert([{
                    user_id: participant.user_id,
                    type: 'match_refund',
                    amount: match.entry_fee,
                    status: 'completed',
                    description: `Match cancelled by host - refund for ${match.game} ${match.mode}`,
                    match_id: matchId,
                    created_at: new Date().toISOString()
                  }])
              }
            }
          }
        }
      } catch (participantError) {
        console.log('Could not refund participants due to permission issues:', participantError)
        // Continue anyway - at least host gets refunded
      }

      return { data: { success: true }, error: null }
    } catch (error) {
      console.error('Cancel match error:', error)
      return { data: null, error }
    }
  },

  // Real-time match subscriptions
  subscribeToMatches(callback: (matches: any[]) => void) {
    const matchesChannel = supabase
      .channel('matches')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches' },
        async () => {
          // Reload matches when any change occurs
          const { data } = await this.getMatches()
          if (data) callback(data)
        }
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_participants' },
        async () => {
          // Reload matches when participants change
          const { data } = await this.getMatches()
          if (data) callback(data)
        }
      )
      .subscribe()

    return matchesChannel
  },

  // Subscribe to specific match updates
  subscribeToMatch(matchId: string, callback: (match: any) => void) {
    return supabase
      .channel(`match-${matchId}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches', filter: `id=eq.${matchId}` },
        (payload) => {
          callback(payload.new || payload.old)
        }
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_participants', filter: `match_id=eq.${matchId}` },
        async () => {
          // Reload full match data when participants change
          const { data } = await this.getMatchDetails(matchId)
          if (data) callback(data)
        }
      )
      .subscribe()
  },

  subscribeToMatchParticipants(matchId: string, callback: (participants: any[]) => void) {
    return supabase
      .channel(`match-participants-${matchId}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_participants', filter: `match_id=eq.${matchId}` },
        async () => {
          // Reload participants when any change occurs
          try {
            const { data } = await supabase
              .from('match_participants')
              .select('*')
              .eq('match_id', matchId)
            if (data) callback(data)
          } catch (error) {
            console.log('Could not load participants:', error)
            callback([])
          }
        }
      )
      .subscribe()
  },

  // Get user's match history
  async getUserMatchHistory(userId: string, limit: number = 10) {
    try {
      const { data, error } = await supabase
        .from('matches')
        .select('*')
        .or(`host_id.eq.${userId},winner_id.eq.${userId}`)
        .in('status', ['completed', 'cancelled'])
        .order('updated_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.log('Error getting match history:', error)
      return { data: [], error: null }
    }
  },

  // Get user statistics
  async getUserStats(userId: string) {
    try {
      // Get match statistics
      const { data: matches } = await supabase
        .from('matches')
        .select('*')
        .or(`host_id.eq.${userId},winner_id.eq.${userId}`)
        .eq('status', 'completed')

      const totalMatches = matches?.length || 0
      const wins = matches?.filter(match => match.winner_id === userId).length || 0
      const losses = totalMatches - wins
      const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0

      // Get earnings from transactions
      const { data: transactions } = await supabase
        .from('transactions')
        .select('amount, type')
        .eq('user_id', userId)
        .in('type', ['match_win', 'match_refund'])

      const totalEarnings = transactions?.reduce((sum, tx) => {
        return tx.type === 'match_win' ? sum + tx.amount : sum
      }, 0) || 0

      return {
        data: {
          totalMatches,
          wins,
          losses,
          winRate,
          totalEarnings
        },
        error: null
      }
    } catch (error) {
      console.log('Error getting user stats:', error)
      return {
        data: {
          totalMatches: 0,
          wins: 0,
          losses: 0,
          winRate: 0,
          totalEarnings: 0
        },
        error: null
      }
    }
  },

  // Leave match
  async leaveMatch(matchId: string, userId: string) {
    try {
      // Check if user is in the match
      const { data: _participant, error: participantError } = await supabase
        .from('match_participants')
        .select('id')
        .eq('match_id', matchId)
        .eq('user_id', userId)
        .single()

      if (participantError) {
        throw new Error('Not joined in this match')
      }

      // Get match details
      const { data: match, error: matchError } = await supabase
        .from('matches')
        .select('current_players, status')
        .eq('id', matchId)
        .single()

      if (matchError) throw matchError

      if (match.status !== 'open') {
        throw new Error('Cannot leave match that has already started')
      }

      // Remove from match
      const { error } = await supabase
        .from('match_participants')
        .delete()
        .eq('match_id', matchId)
        .eq('user_id', userId)

      if (error) throw error

      // Update match current_players count
      await supabase
        .from('matches')
        .update({
          current_players: Math.max(0, match.current_players - 1),
          status: 'open',
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)

      return { data: { success: true }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's matches - simplified to avoid permission issues
  async getUserMatches(userId: string) {
    try {
      const { data, error } = await supabase
        .from('match_participants')
        .select('match_id')
        .eq('user_id', userId)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.log('Error getting user matches, returning empty array:', error)
      return { data: [], error: null } // Return empty array instead of failing
    }
  },

  // Get match details with all participants
  async getMatchDetails(matchId: string) {
    try {
      const { data, error } = await supabase
        .from('matches')
        .select(`
          *,
          host:users!host_id(id, username, first_name, last_name),
          winner:users!winner_id(id, username, first_name, last_name),
          participants:match_participants(
            id,
            user_id,
            joined_at,
            is_ready,
            user:users(id, username, first_name, last_name)
          )
        `)
        .eq('id', matchId)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Start match when both players are ready
  async startMatch(matchId: string, _refereeId?: string) {
    try {
      console.log('🎮 Starting match:', matchId)

      // Update match status to in_progress
      const { error: matchError } = await supabase
        .from('matches')
        .update({
          status: 'in_progress',
          actual_start_time: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)

      if (matchError) {
        console.error('Failed to start match:', matchError)
        throw matchError
      }

      console.log('✅ Match started successfully')
      return { data: { success: true }, error: null }

    } catch (error) {
      console.error('Start match error:', error)
      return { data: null, error }
    }
  },

  // Update match status
  async updateMatchStatus(matchId: string, status: string, winnerId?: string) {
    try {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      }

      if (winnerId) {
        updateData.winner_id = winnerId
      }

      if (status === 'in_progress') {
        updateData.actual_start_time = new Date().toISOString()
      } else if (status === 'completed') {
        updateData.actual_end_time = new Date().toISOString()
      }

      const { data, error } = await supabase
        .from('matches')
        .update(updateData)
        .eq('id', matchId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update participant ready status
  async updateParticipantReady(matchId: string, userId: string, isReady: boolean) {
    try {
      const { data, error } = await supabase
        .from('match_participants')
        .update({ is_ready: isReady })
        .eq('match_id', matchId)
        .eq('user_id', userId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

// Transaction functions
export const transactionService = {
  // Create transaction
  async createTransaction(transactionData: Omit<Transaction, 'id' | 'created_at'>) {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .insert([transactionData])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user transactions
  async getUserTransactions(userId: string) {
    try {
      const { data, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

// Wallet functions - New service for wallet operations
export const walletService = {
  // Get user's current diamond balance
  async getBalance(userId: string) {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (error) throw error
      return { data: data.diamond_balance, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Deduct balance from user account
  async deductBalance(userId: string, amount: number) {
    try {
      // First get current balance
      const { data: currentUser, error: getUserError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (getUserError) throw getUserError

      const newBalance = (currentUser.diamond_balance || 0) - amount

      // Update with new balance
      const { data, error } = await supabase
        .from('users')
        .update({ diamond_balance: newBalance })
        .eq('id', userId)
        .select('diamond_balance')
        .single()

      if (error) throw error
      return { data: data.diamond_balance, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create diamond purchase transaction
  async createPurchase(userId: string, amount: number, diamonds: number, paymentMethod: string) {
    try {
      // Create transaction record
      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'diamond_purchase',
          amount: amount,
          description: `Diamond purchase - ${diamonds} diamonds`,
          payment_method: paymentMethod,
          status: 'pending'
        }])
        .select()
        .single()

      if (transactionError) throw transactionError

      return { data: transaction, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Complete diamond purchase (called after payment confirmation)
  async completePurchase(transactionId: string, paymentReference: string) {
    try {
      // Get transaction details
      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .select('user_id, amount')
        .eq('id', transactionId)
        .single()

      if (transactionError) throw transactionError

      // Update transaction status
      await supabase
        .from('transactions')
        .update({
          status: 'completed',
          payment_reference: paymentReference
        })
        .eq('id', transactionId)

      // Add diamonds to user balance
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', transaction.user_id)
        .single()

      if (userError) throw userError

      await supabase
        .from('users')
        .update({
          diamond_balance: user.diamond_balance + transaction.amount
        })
        .eq('id', transaction.user_id)

      return { data: { success: true }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get top-up offers
  async getTopUpOffers() {
    try {
      // Updated offers with minimum ₱20 and better promotions
      const offers = [
        { id: '1', amount: 20, diamonds: 20, is_popular: false, bonus: 0 },
        { id: '2', amount: 50, diamonds: 50, is_popular: false, bonus: 0 },
        { id: '3', amount: 100, diamonds: 105, is_popular: false, bonus: 5 },
        { id: '4', amount: 250, diamonds: 265, is_popular: false, bonus: 15 },
        { id: '5', amount: 500, diamonds: 550, is_popular: true, bonus: 50 },
        { id: '6', amount: 1000, diamonds: 1100, is_popular: false, bonus: 100 },
        { id: '7', amount: 2500, diamonds: 2750, is_popular: false, bonus: 250 },
        { id: '8', amount: 5000, diamonds: 5750, is_popular: true, bonus: 750 },
        { id: '9', amount: 10000, diamonds: 11500, is_popular: false, bonus: 1500 },
        { id: '10', amount: 25000, diamonds: 30000, is_popular: false, bonus: 5000 },
        { id: '11', amount: 50000, diamonds: 62500, is_popular: false, bonus: 12500 }
      ]

      return { data: offers, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get payment methods
  async getPaymentMethods() {
    try {
      // For now, return static payment methods. In production, this would come from database
      const methods = [
        {
          id: 'gcash',
          name: 'GCash',
          description: 'Pay with GCash wallet',
          processing_time: 'Instant',
          is_active: true
        },
        {
          id: 'maya',
          name: 'Maya',
          description: 'Pay with Maya wallet',
          processing_time: 'Instant',
          is_active: true
        },
        {
          id: 'bank',
          name: 'Bank Transfer',
          description: 'Direct bank transfer',
          processing_time: '1-3 hours',
          is_active: true
        }
      ]

      return { data: methods, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create withdrawal request
  async createWithdrawal(userId: string, amount: number, diamonds: number, withdrawalMethod: string, accountDetails: any) {
    try {
      // Check if user has enough diamonds
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (userError) throw userError

      if (user.diamond_balance < diamonds) {
        throw new Error('Insufficient diamond balance')
      }

      // Create withdrawal transaction record
      const { data: transaction, error: transactionError } = await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'diamond_withdrawal',
          amount: -amount, // Negative for withdrawal
          description: `Diamond withdrawal - ${diamonds} diamonds to ${withdrawalMethod}`,
          payment_method: withdrawalMethod,
          status: 'pending',
          withdrawal_details: accountDetails
        }])
        .select()
        .single()

      if (transactionError) throw transactionError

      // Deduct diamonds from user balance (hold them until withdrawal is processed)
      await supabase
        .from('users')
        .update({
          diamond_balance: user.diamond_balance - diamonds,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      return { data: transaction, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get withdrawal limits and fees
  async getWithdrawalInfo() {
    try {
      // In production, this would come from database/config
      const withdrawalInfo = {
        minimum_amount: 100, // Minimum ₱100 withdrawal
        maximum_amount: 50000, // Maximum ₱50,000 per day
        processing_time: {
          gcash: 'Instant',
          maya: 'Instant',
          bank: '1-3 business days'
        },
        fees: {
          gcash: 0, // No fees for GCash
          maya: 0, // No fees for Maya
          bank: 15 // ₱15 fee for bank transfers
        }
      }

      return { data: withdrawalInfo, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

// Real-time subscriptions - Updated for your table structure
export const subscriptions = {
  // Subscribe to match updates
  subscribeToMatches(callback: (payload: any) => void) {
    return supabase
      .channel('matches')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches' },
        callback
      )
      .subscribe()
  },

  // Subscribe to user profile updates
  subscribeToProfile(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`user:${userId}`)
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'users', filter: `id=eq.${userId}` },
        callback
      )
      .subscribe()
  },

  // Subscribe to transactions
  subscribeToTransactions(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`transactions:${userId}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'transactions', filter: `user_id=eq.${userId}` },
        callback
      )
      .subscribe()
  }
}

// TODO: DEPRECATED - Use individual service modules instead
// Import referral service from separate module
export { referralService } from './referralService'

// TODO: DEPRECATED - Use individual service modules instead
// Import referee service from separate module
export { refereeService } from './refereeService'

// TODO: DEPRECATED - Use individual service modules instead
// Import dashboard service from separate module
export { dashboardService } from './dashboardService'

// TODO: DEPRECATED - Use individual service modules instead
// Import community service from separate module
export { communityService } from './communityService'

// TODO: DEPRECATED - Use individual service modules instead
// Import tournament service from separate module
export { tournamentService } from './tournamentService'

// TODO: DEPRECATED - Use individual service modules instead
// Import platform service from separate module
export { platformService } from './platformService'

// Marketplace service - Import from separate file
export { marketplaceService } from './marketplace'

// Admin service - Import from separate file
export { adminService } from './admin'

// Export notification service
export { notificationService } from './notifications'

export default supabase
