import { Link } from "react-router-dom"
import { useState, useEffect, useMemo } from "react"
import {
  Trophy, Plus, Users, Crown, Gamepad2, Gem, ChevronLeft, ChevronRight, Zap,
  Flame, Shield, Bell, RefreshCw, ArrowUp, ArrowDown, Loader2, BarChart3
} from "lucide-react"
import { useAuth } from "../../../contexts/AuthContext"
// TODO: dashboardService not yet extracted - using old import temporarily
import { dashboardService } from "../../../services/supabase"
import { useAllMatches, useUserUpdates } from "../../../hooks/useRealtime"
import FriendsSystem from "../../../components/social/FriendsSystem"
import Leaderboard from "../../../components/leaderboard/Leaderboard"
import AnalyticsDashboard from "../../../components/analytics/AnalyticsDashboard"
import { isMobileDevice, PerformanceMonitor } from "../../../utils/mobilePerformance"

export default function DashboardPage() {
  const { user } = useAuth()
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedMatch, setSelectedMatch] = useState<string | null>(null)
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [liveMatches, setLiveMatches] = useState<any[]>([])
  const [leaderboard, setLeaderboard] = useState<any[]>([])
  const [activeMatches, setActiveMatches] = useState<any[]>([])
  const [referralCount, setReferralCount] = useState(0)
  const [liveStats, setLiveStats] = useState({
    onlinePlayers: 1247,
    activeMatches: 23,
    totalPrizePool: 125000
  })

  // 🚀 MOBILE OPTIMIZATION: Device detection and performance monitoring
  const isMobile = useMemo(() => isMobileDevice(), [])

  // Performance monitoring for mobile optimization
  useEffect(() => {
    if (isMobile) {
      PerformanceMonitor.startTiming('dashboard-mobile-load')
    }
    return () => {
      if (isMobile) {
        const loadTime = PerformanceMonitor.endTiming('dashboard-mobile-load')
        if (loadTime > 2000) {
          console.warn('🐌 Dashboard mobile load time:', loadTime + 'ms')
        }
      }
    }
  }, [isMobile])

  // Enhanced carousel data for esports betting hub
  const carouselSlides = [
    {
      id: 1,
      title: "🔥 Live Matches - Bet on ongoing tournaments",
      subtitle: "Real-time betting with instant payouts",
      icon: Flame,
      color: "from-red-500 via-pink-500 to-red-600",
      action: "Watch Live",
      link: "/matches"
    },
    {
      id: 2,
      title: "💎 Diamond Rewards - Earn while you play",
      subtitle: "Win matches, climb ranks, earn diamonds",
      icon: Gem,
      color: "from-blue-500 via-purple-500 to-blue-600",
      action: "Start Playing",
      link: "/tournaments"
    },
    {
      id: 3,
      title: "🏆 Tournament Hub - Join premium events",
      subtitle: "Compete for massive prize pools",
      icon: Trophy,
      color: "from-yellow-500 via-orange-500 to-yellow-600",
      action: "Join Now",
      link: "/tournaments"
    },
    {
      id: 4,
      title: "👥 Referral Program - Invite friends & earn",
      subtitle: "Get 10% of friends' winnings forever",
      icon: Users,
      color: "from-green-500 via-emerald-500 to-green-600",
      action: "Invite Friends",
      link: "/referrals"
    }
  ]

  // Load dashboard data on mount
  useEffect(() => {
    const loadDashboardData = async () => {
      if (!user?.id) return

      setIsLoading(true)
      try {
        // Load user stats with fallback
        const { data: userStats, error: statsError } = await dashboardService.getUserStats(user.id)
        if (statsError) {
          console.error('Error loading user stats:', statsError)
          // Set fallback data if stats fail to load
          setDashboardData({
            user: user,
            stats: {
              current_streak: 0,
              longest_streak: 0,
              total_earnings: 0,
              monthly_earnings: 0,
              weekly_earnings: 0,
              daily_earnings: 0,
              rank_points: 0,
              achievements: []
            }
          })
        } else {
          setDashboardData(userStats)
        }

        // Load live matches with fallback
        const { data: matches, error: matchesError } = await dashboardService.getLiveMatches(5)
        if (matchesError) {
          console.error('Error loading live matches:', matchesError)
          // Set empty array as fallback
          setLiveMatches([])
        } else {
          setLiveMatches(matches || [])
        }

        // Load leaderboard with fallback
        const { data: leaderboardData, error: leaderboardError } = await dashboardService.getLeaderboard('diamonds', 10)
        if (leaderboardError) {
          console.error('Error loading leaderboard:', leaderboardError)
          // Set empty array as fallback
          setLeaderboard([])
        } else {
          setLeaderboard(leaderboardData || [])
        }

        // Load user's active matches with fallback
        const { data: activeMatchesData, error: activeMatchesError } = await dashboardService.getActiveMatches(user.id)
        if (activeMatchesError) {
          console.error('Error loading active matches:', activeMatchesError)
          // Set empty array as fallback
          setActiveMatches([])
        } else {
          setActiveMatches(activeMatchesData || [])
        }

        // Load user's referral count with fallback
        const { data: referralData, error: referralError } = await dashboardService.getUserReferrals(user.id)
        if (referralError) {
          console.error('Error loading referrals:', referralError)
          // Set 0 as fallback
          setReferralCount(0)
        } else {
          setReferralCount(referralData || 0)
        }

      } catch (error) {
        console.error('Error loading dashboard data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadDashboardData()
  }, [user?.id])

  // Subscribe to real-time match updates
  useAllMatches((updatedMatch) => {
    setLiveMatches(prev => {
      const existingIndex = prev.findIndex(match => match.id === updatedMatch.id)
      if (existingIndex >= 0) {
        // Update existing match
        const updated = [...prev]
        updated[existingIndex] = {
          ...updated[existingIndex],
          status: updatedMatch.status,
          current_players: updatedMatch.current_players,
          pot_amount: updatedMatch.pot_amount
        }
        return updated
      } else if (updatedMatch.status === 'open' || updatedMatch.status === 'ongoing') {
        // Add new match if it's open or ongoing
        return [...prev, updatedMatch]
      }
      return prev
    })
  })

  // Subscribe to user updates (for balance changes, etc.)
  useUserUpdates(user?.id || null, (updatedUser) => {
    // Update dashboard data when user data changes
    setDashboardData((prev: any) => prev ? {
      ...prev,
      user: updatedUser
    } : null)
  })

  // Auto-scroll carousel every 5 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselSlides.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [carouselSlides.length])

  // Simulate live stats updates
  useEffect(() => {
    const interval = setInterval(() => {
      setLiveStats(prev => ({
        onlinePlayers: prev.onlinePlayers + Math.floor(Math.random() * 10 - 5),
        activeMatches: liveMatches.length || prev.activeMatches,
        totalPrizePool: prev.totalPrizePool + Math.floor(Math.random() * 1000 - 500)
      }))
    }, 3000)
    return () => clearInterval(interval)
  }, [liveMatches.length])

  // 🚀 MEMOIZED: Calculate user stats from real data
  const userStats = useMemo(() => dashboardData?.user || user, [dashboardData?.user, user])
  const userStatistics = useMemo(() => dashboardData?.stats, [dashboardData?.stats])

  const calculatedStats = useMemo(() => {
    const wins = userStats?.wins || 0
    const losses = userStats?.losses || 0
    const totalMatches = userStats?.total_matches || 0
    const winRate = totalMatches > 0 ? ((wins / totalMatches) * 100).toFixed(1) : '0.0'
    const currentStreak = userStatistics?.current_streak || 0
    const monthlyRank = 3 // This would come from a ranking calculation

    return { wins, losses, totalMatches, winRate, currentStreak, monthlyRank }
  }, [userStats, userStatistics])

  const { wins, losses, winRate, currentStreak, monthlyRank } = calculatedStats

  // Transform live matches data for display
  const displayMatches = liveMatches.map((match) => ({
    id: match.id,
    game: match.game,
    tournament: 'Live Match',
    teams: [match.host?.username || 'Host', `${match.current_players}/${match.max_players} Players`],
    odds: calculateMatchOdds(match),
    betAmount: match.pot_amount || 0,
    viewers: calculateViewers(match),
    time: match.status === 'ongoing' ? 'LIVE' : 'OPEN',
    status: match.status as 'live' | 'open',
    gameIcon: getGameIcon(match.game)
  }))

  // Transform leaderboard data for display
  const displayLeaderboard = leaderboard.map((player, index) => ({
    rank: index + 1,
    username: player.username,
    diamonds: player.diamond_balance || 0,
    change: calculateRankChange(player, index),
    isUser: player.id === user?.id,
    badge: getRankBadge(index)
  }))

  // Helper functions for data transformation
  function calculateMatchOdds(match: any): string[] {
    // Calculate odds based on pot amount and players
    const basePot = match.pot_amount || 100
    const playerCount = match.current_players || 1
    const odds1 = (1.5 + (basePot / 1000)).toFixed(2)
    const odds2 = (1.6 + (playerCount / 10)).toFixed(2)
    return [odds1, odds2]
  }

  function calculateViewers(match: any): number {
    // Calculate viewers based on pot amount and game popularity
    const basePot = match.pot_amount || 100
    const gameMultiplier = match.game === 'Mobile Legends' ? 2 :
                          match.game === 'Valorant' ? 1.5 : 1
    return Math.floor((basePot / 10) * gameMultiplier) + 50
  }

  function getGameIcon(game: string): string {
    switch (game?.toLowerCase()) {
      case 'mobile legends': return '🏆'
      case 'valorant': return '🎯'
      case 'wild rift': return '⚔️'
      case 'cs:go': case 'cs2': return '🔫'
      case 'dota 2': return '🛡️'
      default: return '🎮'
    }
  }

  function calculateRankChange(player: any, index: number): string {
    // Calculate rank change based on recent performance
    const winRate = player.total_matches > 0 ? (player.wins / player.total_matches) * 100 : 0
    if (winRate > 70) return `+${Math.floor(Math.random() * 3) + 1}`
    if (winRate < 40) return `-${Math.floor(Math.random() * 2) + 1}`
    return index % 2 === 0 ? '+1' : '0'
  }

  function getRankBadge(index: number): string {
    if (index === 0) return '👑'
    if (index === 1) return '🥈'
    if (index === 2) return '🥉'
    if (index === 3) return '⭐'
    return '🎮'
  }

  // 🚀 MEMOIZED: Quick stats for dashboard
  const quickStats = useMemo(() => ({
    todayEarnings: userStatistics?.daily_earnings || 0,
    activeBets: activeMatches.length,
    winStreak: currentStreak,
    nextTournament: getNextTournamentTime()
  }), [userStatistics?.daily_earnings, activeMatches.length, currentStreak])

  // Additional calculated values
  const referralEarnings = userStatistics?.total_earnings || 0
  const totalReferrals = referralCount

  // Helper function to calculate next tournament time
  function getNextTournamentTime() {
    const upcomingMatches = liveMatches.filter(match =>
      match.status === 'open' &&
      new Date(match.scheduled_start_time || match.created_at) > new Date()
    )

    if (upcomingMatches.length === 0) return 'No upcoming'

    const nextMatch = upcomingMatches[0]
    const startTime = new Date(nextMatch.scheduled_start_time || nextMatch.created_at)
    const now = new Date()
    const diffMs = startTime.getTime() - now.getTime()

    if (diffMs <= 0) return 'Starting soon'

    const hours = Math.floor(diffMs / (1000 * 60 * 60))
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60))

    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  // Utility functions
  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`
    return num.toString()
  }

  const getChangeColor = (change: string) => {
    if (change.startsWith('+')) return 'text-green-600'
    if (change.startsWith('-')) return 'text-red-600'
    return 'text-gray-600'
  }

  const getChangeIcon = (change: string) => {
    if (change.startsWith('+')) return <ArrowUp className="w-3 h-3" />
    if (change.startsWith('-')) return <ArrowDown className="w-3 h-3" />
    return null
  }

  const handleQuickAction = (_action: string) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      // In a real app, this would perform the actual action
    }, 1000)
  }

  // Loading state
  if (isLoading || !user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    )
  }

  // Error state
  if (!dashboardData && !isLoading) {
    return (
      <div className="h-screen bg-gradient-to-br from-blue-50 to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mb-4 animate-pulse">
            <Gamepad2 className="w-8 h-8 text-white" />
          </div>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 font-medium">Loading your gaming dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="p-2 sm:p-4 lg:p-6 pb-8">
        {/* Responsive Hero Carousel */}
        <div className="relative h-12 sm:h-16 lg:h-20 bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 mb-2 sm:mb-4">
          <div
            className="flex transition-transform duration-700 ease-in-out h-full"
            style={{ transform: `translateX(-${currentSlide * 100}%)` }}
          >
            {carouselSlides.map((slide) => {
              const IconComponent = slide.icon
              return (
                <div
                  key={slide.id}
                  className={`min-w-full h-full bg-gradient-to-r ${slide.color} flex items-center justify-between px-2 sm:px-4 lg:px-6 relative overflow-hidden`}
                >
                  <div className="flex items-center space-x-2 sm:space-x-3 text-white z-10">
                    <div className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 bg-white/20 rounded-lg flex items-center justify-center backdrop-blur-sm">
                      <IconComponent className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5" />
                    </div>
                    <div>
                      <h3 className="font-bold text-xs sm:text-sm lg:text-base">{slide.title}</h3>
                      <p className="text-white/90 text-xs hidden sm:block lg:text-sm">{slide.subtitle}</p>
                    </div>
                  </div>

                  <Link
                    to={slide.link}
                    className="bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white px-2 sm:px-3 lg:px-4 py-1 sm:py-1.5 rounded-md font-medium transition-all duration-200 text-xs sm:text-sm lg:text-base z-10"
                  >
                    <span className="hidden sm:inline">{slide.action}</span>
                    <span className="sm:hidden">Go</span>
                  </Link>
                </div>
              )
            })}
          </div>

          {/* Responsive Carousel Controls */}
          <button
            onClick={() => setCurrentSlide((prev) => (prev - 1 + carouselSlides.length) % carouselSlides.length)}
            className="absolute left-1 sm:left-2 lg:left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
          >
            <ChevronLeft className="w-2 h-2 sm:w-3 sm:h-3 lg:w-4 lg:h-4" />
          </button>
          <button
            onClick={() => setCurrentSlide((prev) => (prev + 1) % carouselSlides.length)}
            className="absolute right-1 sm:right-2 lg:right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
          >
            <ChevronRight className="w-2 h-2 sm:w-3 sm:h-3 lg:w-4 lg:h-4" />
          </button>

          {/* Responsive Carousel Indicators */}
          <div className="absolute bottom-0.5 sm:bottom-1 lg:bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-0.5 sm:space-x-1 lg:space-x-2">
            {carouselSlides.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-1 h-1 sm:w-1.5 sm:h-1.5 lg:w-2 lg:h-2 rounded-full transition-all duration-200 ${
                  index === currentSlide ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Responsive Welcome Header */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4 mb-2 sm:mb-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <div className="flex items-center space-x-2 sm:space-x-3">
              <div className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-xs sm:text-sm lg:text-base font-bold text-white">
                  {user.first_name?.charAt(0) || user.username?.charAt(0) || 'U'}
                </span>
              </div>
              <div>
                <h1 className="text-sm sm:text-lg lg:text-xl font-bold text-gray-900 flex items-center space-x-1 sm:space-x-2">
                  <span>Welcome, {user.first_name || user.username}!</span>
                  {currentStreak >= 3 && <Flame className="w-3 h-3 sm:w-4 sm:h-4 text-orange-500" />}
                </h1>
                <div className="flex items-center space-x-2 sm:space-x-3 text-xs sm:text-sm text-gray-600">
                  <span className="flex items-center space-x-1">
                    <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Online</span>
                  </span>
                  <span className="hidden sm:inline">Rank #{monthlyRank}</span>
                  <span className="flex items-center space-x-1">
                    <Zap className="w-2 h-2 sm:w-3 sm:h-3 text-yellow-500" />
                    <span>{currentStreak} streak</span>
                  </span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-1 sm:space-x-2">
              {/* Responsive Live Stats */}
              <div className="hidden md:flex items-center space-x-2 lg:space-x-3 bg-gray-50 rounded-lg px-2 py-1 lg:px-3 lg:py-2">
                <div className="text-center">
                  <div className="text-xs lg:text-sm font-bold text-gray-900">{formatNumber(liveStats.onlinePlayers)}</div>
                  <div className="text-xs text-gray-600">Online</div>
                </div>
                <div className="text-center">
                  <div className="text-xs lg:text-sm font-bold text-gray-900">{liveStats.activeMatches}</div>
                  <div className="text-xs text-gray-600">Live</div>
                </div>
              </div>

              <button
                onClick={() => handleQuickAction('refresh')}
                className="p-1 sm:p-1.5 lg:p-2 hover:bg-gray-100 rounded-md transition-colors"
              >
                <RefreshCw className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" />
              </button>
              <button className="p-1 sm:p-1.5 lg:p-2 hover:bg-gray-100 rounded-md transition-colors">
                <Bell className="w-3 h-3 sm:w-4 sm:h-4 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Responsive Stats Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 lg:gap-4 mb-2 sm:mb-4">
          <Link to="/wallet" className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4 hover:shadow-lg transition-all duration-200 group">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-1 mb-1">
                  <div className="w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-md flex items-center justify-center">
                    <Gem className="w-2 h-2 sm:w-3 sm:h-3 lg:w-4 lg:h-4 text-white" />
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-gray-600">Diamonds</span>
                </div>
                <div className="text-sm sm:text-lg lg:text-xl font-bold text-gray-900">{formatNumber(user.diamond_balance || 15450)}</div>
                <div className="text-xs text-green-600 flex items-center space-x-1">
                  <ArrowUp className="w-2 h-2" />
                  <span>+{quickStats.todayEarnings}</span>
                </div>
              </div>
              <div className="text-sm sm:text-lg lg:text-xl">💎</div>
            </div>
          </Link>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-1 mb-1">
                  <div className="w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-md flex items-center justify-center">
                    <Trophy className="w-2 h-2 sm:w-3 sm:h-3 lg:w-4 lg:h-4 text-white" />
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-gray-600">Win Rate</span>
                </div>
                <div className="text-sm sm:text-lg lg:text-xl font-bold text-gray-900">{winRate}%</div>
                <div className="text-xs text-gray-600">{wins}W/{losses}L</div>
              </div>
              <div className="text-sm sm:text-lg lg:text-xl">🏆</div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-1 mb-1">
                  <div className="w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-gradient-to-br from-orange-500 to-red-600 rounded-md flex items-center justify-center">
                    <Flame className="w-2 h-2 sm:w-3 sm:h-3 lg:w-4 lg:h-4 text-white" />
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-gray-600">Streak</span>
                </div>
                <div className="text-sm sm:text-lg lg:text-xl font-bold text-gray-900">{currentStreak}</div>
                <div className="text-xs text-orange-600">Best: 12</div>
              </div>
              <div className="text-sm sm:text-lg lg:text-xl">🔥</div>
            </div>
          </div>

          <Link to="/referrals" className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4 hover:shadow-lg transition-all duration-200 group">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-1 mb-1">
                  <div className="w-4 h-4 sm:w-6 sm:h-6 lg:w-8 lg:h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-md flex items-center justify-center">
                    <Users className="w-2 h-2 sm:w-3 sm:h-3 lg:w-4 lg:h-4 text-white" />
                  </div>
                  <span className="text-xs sm:text-sm font-medium text-gray-600">Referrals</span>
                </div>
                <div className="text-sm sm:text-lg lg:text-xl font-bold text-gray-900">{totalReferrals}</div>
                <div className="text-xs text-purple-600">+{formatNumber(referralEarnings)} 💎</div>
              </div>
              <div className="text-sm sm:text-lg lg:text-xl">👥</div>
            </div>
          </Link>
        </div>

        {/* Responsive Main Content Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-2 sm:gap-3 lg:gap-4 mb-4">
          {/* Live Matches - Responsive Section */}
          <div className="md:col-span-1 lg:col-span-5 bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4">
            <div className="flex items-center justify-between mb-2 sm:mb-3">
              <h3 className="text-xs sm:text-sm lg:text-base font-bold text-gray-900 flex items-center space-x-1">
                <div className="w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span>Live Matches</span>
              </h3>
              <Link to="/matches" className="text-blue-600 hover:text-blue-700 font-medium text-xs sm:text-sm flex items-center space-x-1">
                <span className="hidden sm:inline">View All</span>
                <span className="sm:hidden">All</span>
                <ChevronRight className="w-2 h-2 sm:w-3 sm:h-3" />
              </Link>
            </div>

            <div className="space-y-1 sm:space-y-2 max-h-32 sm:max-h-40 lg:max-h-48 overflow-y-auto">
              {displayMatches.length > 0 ? displayMatches.slice(0, 2).map((match) => (
                <div
                  key={match.id}
                  className={`border rounded-md p-1.5 sm:p-2 lg:p-3 hover:shadow-sm transition-all duration-200 cursor-pointer ${
                    selectedMatch === match.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}
                  onClick={() => setSelectedMatch(match.id)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-1 sm:space-x-2">
                      <div className="text-xs sm:text-sm lg:text-base">{match.gameIcon}</div>
                      <div>
                        <h4 className="font-medium text-gray-900 text-xs sm:text-sm">{match.game}</h4>
                        <p className="text-xs text-gray-600 hidden sm:block">{match.tournament}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="flex items-center space-x-1 text-red-600">
                        <div className="w-1 h-1 sm:w-1.5 sm:h-1.5 bg-red-500 rounded-full animate-pulse"></div>
                        <span className="text-xs font-medium">{match.time}</span>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-1 text-xs">
                    <div className="bg-blue-50 rounded p-1 sm:p-1.5 text-center">
                      <div className="font-medium text-gray-900 text-xs sm:text-sm">{match.teams[0]}</div>
                      <div className="text-blue-600 font-bold text-xs sm:text-sm">{match.odds[0]}</div>
                    </div>
                    <div className="bg-red-50 rounded p-1 sm:p-1.5 text-center">
                      <div className="font-medium text-gray-900 text-xs sm:text-sm">{match.teams[1]}</div>
                      <div className="text-red-600 font-bold text-xs sm:text-sm">{match.odds[1]}</div>
                    </div>
                  </div>

                  <div className="mt-1 pt-1 border-t border-gray-200 flex items-center justify-between">
                    <span className="text-xs sm:text-sm text-gray-600">{formatNumber(match.betAmount)} 💎</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation()
                        handleQuickAction('Place Bet')
                      }}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-2 sm:px-3 py-0.5 sm:py-1 rounded text-xs sm:text-sm font-medium transition-colors"
                    >
                      Bet
                    </button>
                  </div>
                </div>
              )) : (
                <div className="text-center py-4">
                  <Gamepad2 className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">No live matches right now</p>
                  <Link to="/matches" className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                    Create a match
                  </Link>
                </div>
              )}
            </div>
          </div>

          {/* Quick Actions & Progress - Responsive */}
          <div className="md:col-span-1 lg:col-span-3 space-y-2 sm:space-y-3">
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4">
              <h3 className="text-xs sm:text-sm lg:text-base font-bold text-gray-900 mb-2 sm:mb-3">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-1 sm:gap-2">
                <Link to="/matches" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-md p-1.5 sm:p-2 lg:p-3 flex flex-col items-center space-y-0.5 sm:space-y-1 transition-all duration-200">
                  <Plus className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5" />
                  <span className="text-xs sm:text-sm font-medium">Create</span>
                </Link>
                <Link to="/tournaments" className="bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white rounded-md p-1.5 sm:p-2 lg:p-3 flex flex-col items-center space-y-0.5 sm:space-y-1 transition-all duration-200">
                  <Trophy className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5" />
                  <span className="text-xs sm:text-sm font-medium">Tournaments</span>
                </Link>
                <Link to="/wallet" className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white rounded-md p-1.5 sm:p-2 lg:p-3 flex flex-col items-center space-y-0.5 sm:space-y-1 transition-all duration-200">
                  <Gem className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5" />
                  <span className="text-xs sm:text-sm font-medium">Diamonds</span>
                </Link>
                <Link to="/apply-referee" className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-md p-1.5 sm:p-2 lg:p-3 flex flex-col items-center space-y-0.5 sm:space-y-1 transition-all duration-200">
                  <Shield className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5" />
                  <span className="text-xs sm:text-sm font-medium">Referee</span>
                </Link>
              </div>
            </div>

            {/* Responsive Progress Summary */}
            <div className="bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4">
              <h3 className="text-xs sm:text-sm lg:text-base font-bold text-gray-900 mb-2 sm:mb-3 flex items-center space-x-1">
                <BarChart3 className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-blue-600" />
                <span>Today's Progress</span>
              </h3>
              <div className="space-y-1.5 sm:space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">Earnings</span>
                  <span className="font-bold text-green-600 text-xs sm:text-sm">+{quickStats.todayEarnings} 💎</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">Active Bets</span>
                  <span className="font-bold text-blue-600 text-xs sm:text-sm">{quickStats.activeBets}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">Win Streak</span>
                  <span className="font-bold text-orange-600 flex items-center space-x-1 text-xs sm:text-sm">
                    <Flame className="w-2 h-2 sm:w-3 sm:h-3" />
                    <span>{quickStats.winStreak}</span>
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs sm:text-sm text-gray-600">Next Tournament</span>
                  <span className="font-bold text-purple-600 text-xs sm:text-sm">{quickStats.nextTournament}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Responsive Leaderboard */}
          <div className="md:col-span-2 lg:col-span-4 bg-white rounded-lg shadow-md border border-gray-200 p-2 sm:p-3 lg:p-4">
            <div className="flex items-center justify-between mb-2 sm:mb-3">
              <h3 className="text-xs sm:text-sm lg:text-base font-bold text-gray-900 flex items-center space-x-1">
                <Crown className="w-3 h-3 sm:w-4 sm:h-4 lg:w-5 lg:h-5 text-yellow-600" />
                <span>Leaderboard</span>
              </h3>
              <Link to="/community" className="text-blue-600 hover:text-blue-700 font-medium text-xs sm:text-sm flex items-center space-x-1">
                <span className="hidden sm:inline">View All</span>
                <span className="sm:hidden">All</span>
                <ChevronRight className="w-2 h-2 sm:w-3 sm:h-3" />
              </Link>
            </div>

            <div className="space-y-1 max-h-32 sm:max-h-40 lg:max-h-48 overflow-y-auto">
              {displayLeaderboard.length > 0 ? displayLeaderboard.slice(0, 4).map((player) => (
                <div
                  key={player.rank}
                  className={`flex items-center justify-between p-1.5 sm:p-2 lg:p-3 rounded-md transition-all duration-200 ${
                    player.isUser
                      ? "bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200"
                      : "bg-gray-50 hover:bg-gray-100"
                  }`}
                >
                  <div className="flex items-center space-x-1.5 sm:space-x-2">
                    <div className={`w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 rounded-full flex items-center justify-center font-bold text-xs sm:text-sm ${
                      player.rank === 1 ? "bg-gradient-to-r from-yellow-400 to-yellow-600 text-white" :
                      player.rank === 2 ? "bg-gradient-to-r from-gray-300 to-gray-500 text-white" :
                      player.rank === 3 ? "bg-gradient-to-r from-orange-400 to-orange-600 text-white" :
                      player.isUser ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white" :
                      "bg-gray-200 text-gray-700"
                    }`}>
                      {player.rank <= 3 ? player.badge : player.rank}
                    </div>

                    <div>
                      <div className="flex items-center space-x-1">
                        <span className={`font-medium text-xs sm:text-sm ${player.isUser ? 'text-blue-900' : 'text-gray-900'}`}>
                          {player.isUser ? "You" : player.username}
                        </span>
                        {player.isUser && <span className="text-xs bg-blue-100 text-blue-700 px-1 py-0.5 rounded-full hidden sm:inline">You</span>}
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-gray-600">
                        <span className={`flex items-center space-x-1 ${getChangeColor(player.change)}`}>
                          {getChangeIcon(player.change)}
                          <span className="hidden sm:inline">{player.change}</span>
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="flex items-center space-x-1 font-bold text-gray-900 text-xs sm:text-sm">
                      <span>{formatNumber(player.diamonds)}</span>
                      <Gem className="w-2 h-2 sm:w-3 sm:h-3 text-blue-600" />
                    </div>
                  </div>
                </div>
              )) : (
                <div className="text-center py-4">
                  <Crown className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">No leaderboard data yet</p>
                  <p className="text-xs text-gray-500">Play matches to appear on the leaderboard</p>
                </div>
              )}
            </div>

            {/* Quick Leaderboard Actions */}
            <div className="mt-2 pt-2 border-t border-gray-200">
              <div className="grid grid-cols-2 gap-1">
                <Link to="/community" className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded-md text-xs font-medium text-center transition-colors">
                  Full Rankings
                </Link>
                <button
                  onClick={() => handleQuickAction('challenge')}
                  className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-2 py-1 rounded-md text-xs font-medium transition-colors"
                >
                  Challenge
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
          {/* Friends System */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
            <FriendsSystem />
          </div>

          {/* Enhanced Leaderboard */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
            <Leaderboard />
          </div>
        </div>

        {/* Analytics Dashboard */}
        <div className="mt-4">
          <AnalyticsDashboard />
        </div>
      </div>
    </div>
  )
}
