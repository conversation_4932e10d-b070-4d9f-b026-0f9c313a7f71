import React from 'react'
import {
  Trophy,
  Crown,
  Medal,
  Star,
  TrendingUp,
  TrendingDown,
  Minus,
  Gem,
  Users,
  RefreshCw,
  ChevronRight
} from 'lucide-react'
import { LeaderboardPanelProps } from '../types'

const LeaderboardPanel: React.FC<LeaderboardPanelProps> = ({
  type,
  entries,
  isLoading = false
}) => {
  const getTypeConfig = () => {
    switch (type) {
      case 'wins':
        return {
          title: 'Top Wins',
          icon: <Trophy className="w-4 h-4 text-yellow-500" />,
          color: 'yellow',
          suffix: ' wins'
        }
      case 'diamonds':
        return {
          title: 'Top Diamonds',
          icon: <Gem className="w-4 h-4 text-blue-500" />,
          color: 'blue',
          suffix: ' 💎'
        }
      case 'referrals':
        return {
          title: 'Top Referrals',
          icon: <Users className="w-4 h-4 text-purple-500" />,
          color: 'purple',
          suffix: ' refs'
        }
      default:
        return {
          title: 'Leaderboard',
          icon: <Trophy className="w-4 h-4 text-gray-500" />,
          color: 'gray',
          suffix: ''
        }
    }
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-4 h-4 text-yellow-500" />
      case 2:
        return <Medal className="w-4 h-4 text-gray-500" />
      case 3:
        return <Trophy className="w-4 h-4 text-orange-500" />
      default:
        return (
          <div className="w-4 h-4 rounded-full bg-blue-100 flex items-center justify-center">
            <span className="text-xs font-semibold text-blue-600">{rank}</span>
          </div>
        )
    }
  }

  const getRankBadgeColor = (rank: number) => {
    if (rank === 1) return 'bg-gradient-to-br from-yellow-400 to-yellow-600 text-white'
    if (rank === 2) return 'bg-gradient-to-br from-gray-300 to-gray-500 text-white'
    if (rank === 3) return 'bg-gradient-to-br from-orange-400 to-orange-600 text-white'
    return 'bg-blue-100 text-blue-700'
  }

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="w-3 h-3 text-green-500" />
    if (change < 0) return <TrendingDown className="w-3 h-3 text-red-500" />
    return <Minus className="w-3 h-3 text-gray-400" />
  }

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600'
    if (change < 0) return 'text-red-600'
    return 'text-gray-500'
  }

  const formatValue = (value: number) => {
    if (type === 'diamonds' && value >= 1000) {
      return `${(value / 1000).toFixed(1)}k`
    }
    return value.toLocaleString()
  }

  const config = getTypeConfig()

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-3 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {config.icon}
            <h3 className="text-sm font-semibold text-gray-900">{config.title}</h3>
          </div>
          <div className="flex items-center gap-1">
            <button
              className="p-1 hover:bg-gray-100 rounded transition-colors"
              title="Refresh"
            >
              <RefreshCw className="w-3 h-3 text-gray-600" />
            </button>
            <button
              className="p-1 hover:bg-gray-100 rounded transition-colors"
              title="View All"
            >
              <ChevronRight className="w-3 h-3 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-3">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex items-center gap-3 animate-pulse">
                <div className="w-6 h-6 bg-gray-200 rounded-full"></div>
                <div className="w-5 h-5 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-3 bg-gray-200 rounded w-20 mb-1"></div>
                  <div className="h-2 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-12"></div>
              </div>
            ))}
          </div>
        ) : entries.length === 0 ? (
          <div className="text-center py-6">
            <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
              {config.icon}
            </div>
            <p className="text-sm text-gray-500">No data available</p>
          </div>
        ) : (
          <div className="space-y-2">
            {entries.slice(0, 10).map((entry) => (
              <div
                key={entry.userId}
                className="group flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer"
              >
                {/* Rank Badge */}
                <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-semibold ${getRankBadgeColor(entry.rank)}`}>
                  {entry.rank <= 3 ? getRankIcon(entry.rank) : entry.rank}
                </div>

                {/* Avatar */}
                <div className="relative">
                  <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-medium text-xs">
                      {entry.avatar || entry.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  {entry.isOnline && (
                    <div className="absolute -bottom-0.5 -right-0.5 w-2 h-2 bg-green-500 rounded-full border border-white"></div>
                  )}
                </div>

                {/* User Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium text-gray-900 truncate">
                      {entry.username}
                    </span>
                    {entry.rank <= 3 && (
                      <Star className="w-3 h-3 text-yellow-500" />
                    )}
                  </div>
                  {entry.guild && (
                    <p className="text-xs text-gray-500 truncate">{entry.guild}</p>
                  )}
                </div>

                {/* Value and Change */}
                <div className="text-right">
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-semibold text-gray-900">
                      {formatValue(entry.value)}{config.suffix}
                    </span>
                  </div>
                  {entry.change !== 0 && (
                    <div className={`flex items-center gap-0.5 text-xs ${getChangeColor(entry.change)}`}>
                      {getChangeIcon(entry.change)}
                      <span>{Math.abs(entry.change)}</span>
                    </div>
                  )}
                </div>

                {/* Hover Action */}
                <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                  <ChevronRight className="w-3 h-3 text-gray-400" />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {!isLoading && entries.length > 0 && (
        <div className="p-3 border-t border-gray-200">
          <button className="w-full text-xs text-blue-600 hover:text-blue-700 font-medium transition-colors">
            View Full Leaderboard
          </button>
        </div>
      )}
    </div>
  )
}

export default LeaderboardPanel
