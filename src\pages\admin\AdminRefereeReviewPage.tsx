"use client"
import { useState, useMemo } from "react"
import { He<PERSON><PERSON> } from "react-helmet-async"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import {
  Shield,
  Search,
  Filter,
  Eye,
  Check,
  X,
  Clock,
  User,
  FileText,
  Calendar,
  ChevronDown,
  ChevronUp,
  Gamepad2,
  CheckCircle,
  AlertCircle,
  Download,
  RefreshCw,
} from "lucide-react"

interface RefereeApplication {
  id: string
  submittedAt: string
  status: "pending" | "approved" | "rejected" | "under_review"
  personalInfo: {
    firstName: string
    lastName: string
    email: string
    phone: string
    dateOfBirth: string
    nationality: string
  }
  gamingInfo: {
    primaryGame: string
    rank: string
    yearsPlaying: number
    ign: string
    refereeExperience: string
  }
  documents: {
    governmentId: string
    selfieWithId?: string
  }
  payoutMethod: {
    type: "gcash" | "maya" | "bank"
    gcashNumber?: string
    mayaNumber?: string
    bankName?: string
    accountNumber?: string
  }
  billingAddress: {
    street: string
    city: string
    province: string
    zipCode: string
  }
  applicationAnswers: {
    motivation: string
    availability: string
    conflictResolution: string
  }
  adminNotes?: string
  reviewedBy?: string
  reviewedAt?: string
  priority: "low" | "medium" | "high"
}

export default function AdminRefereeReviewPage() {
  const [applications, setApplications] = useState<RefereeApplication[]>(mockApplications)
  const [selectedApplication, setSelectedApplication] = useState<RefereeApplication | null>(null)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState<string>("all")
  const [priorityFilter, setPriorityFilter] = useState<string>("all")
  const [sortBy, setSortBy] = useState<string>("newest")
  const [showFilters, setShowFilters] = useState(false)
  const [adminNote, setAdminNote] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState<string | null>(null)

  // Filter and sort applications
  const filteredApplications = useMemo(() => {
    const filtered = applications.filter((app) => {
      const matchesSearch =
        app.personalInfo.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.personalInfo.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.personalInfo.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        app.id.toLowerCase().includes(searchTerm.toLowerCase())

      const matchesStatus = statusFilter === "all" || app.status === statusFilter
      const matchesPriority = priorityFilter === "all" || app.priority === priorityFilter

      return matchesSearch && matchesStatus && matchesPriority
    })

    // Sort applications
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "newest":
          return new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime()
        case "oldest":
          return new Date(a.submittedAt).getTime() - new Date(b.submittedAt).getTime()
        case "priority":
          const priorityOrder = { high: 3, medium: 2, low: 1 }
          return priorityOrder[b.priority] - priorityOrder[a.priority]
        case "name":
          return `${a.personalInfo.firstName} ${a.personalInfo.lastName}`.localeCompare(
            `${b.personalInfo.firstName} ${b.personalInfo.lastName}`,
          )
        default:
          return 0
      }
    })

    return filtered
  }, [applications, searchTerm, statusFilter, priorityFilter, sortBy])

  const handleApplicationAction = async (applicationId: string, action: "approve" | "reject" | "review") => {
    setIsProcessing(true)
    setError(null)
    setSuccessMessage(null)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500))

      setApplications((prev) =>
        prev.map((app) =>
          app.id === applicationId
            ? {
                ...app,
                status: action === "approve" ? "approved" : action === "reject" ? "rejected" : "under_review",
                adminNotes: adminNote || app.adminNotes,
                reviewedBy: "Admin User",
                reviewedAt: new Date().toISOString(),
              }
            : app,
        ),
      )

      setAdminNote("")
      setSuccessMessage(`Application ${action}d successfully!`)

      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(null), 3000)
    } catch (err) {
      setError(`Failed to ${action} application. Please try again.`)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleDocumentView = (documentType: string, fileName: string) => {
    // In a real app, this would open the document in a modal or new tab
    alert(`Opening ${documentType}: ${fileName}`)
  }

  const handleExportData = () => {
    // In a real app, this would export application data
    const dataStr = JSON.stringify(filteredApplications, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement("a")
    link.href = url
    link.download = "referee-applications.json"
    link.click()
    URL.revokeObjectURL(url)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-700 border-yellow-200"
      case "approved":
        return "bg-green-100 text-green-700 border-green-200"
      case "rejected":
        return "bg-red-100 text-red-700 border-red-200"
      case "under_review":
        return "bg-blue-100 text-blue-700 border-blue-200"
      default:
        return "bg-gray-100 text-gray-700 border-gray-200"
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "bg-red-100 text-red-700"
      case "medium":
        return "bg-yellow-100 text-yellow-700"
      case "low":
        return "bg-green-100 text-green-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  const getApplicationStats = () => {
    const total = applications.length
    const pending = applications.filter((app) => app.status === "pending").length
    const approved = applications.filter((app) => app.status === "approved").length
    const rejected = applications.filter((app) => app.status === "rejected").length
    const underReview = applications.filter((app) => app.status === "under_review").length

    return { total, pending, approved, rejected, underReview }
  }

  const stats = getApplicationStats()

  return (
    <>
      <Helmet>
        <title>Referee Applications Review - Admin Dashboard | Gambets</title>
        <meta name="description" content="Review and manage referee applications for Gambets platform" />
      </Helmet>

      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Shield className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Referee Applications</h1>
                  <p className="text-gray-600">Review and manage referee applications</p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-3">
                <Button onClick={handleExportData} variant="outline" className="flex items-center gap-2">
                  <Download className="w-4 h-4" />
                  Export Data
                </Button>
                <Button onClick={() => window.location.reload()} variant="outline" className="flex items-center gap-2">
                  <RefreshCw className="w-4 h-4" />
                  Refresh
                </Button>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-2 sm:grid-cols-5 gap-4 mt-6">
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-700">{stats.total}</div>
                <div className="text-sm text-blue-600">Total</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-yellow-700">{stats.pending}</div>
                <div className="text-sm text-yellow-600">Pending</div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-blue-700">{stats.underReview}</div>
                <div className="text-sm text-blue-600">Under Review</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-green-700">{stats.approved}</div>
                <div className="text-sm text-green-600">Approved</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-red-700">{stats.rejected}</div>
                <div className="text-sm text-red-600">Rejected</div>
              </div>
            </div>
          </div>
        </div>

        {/* Alert Messages */}
        {error && (
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center gap-3">
              <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
              <p className="text-red-700">{error}</p>
              <button onClick={() => setError(null)} className="ml-auto text-red-600 hover:text-red-800">
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {successMessage && (
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 pt-4">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center gap-3">
              <CheckCircle className="w-5 h-5 text-green-600 flex-shrink-0" />
              <p className="text-green-700">{successMessage}</p>
              <button onClick={() => setSuccessMessage(null)} className="ml-auto text-green-600 hover:text-green-800">
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="grid lg:grid-cols-3 gap-6">
            {/* Left Panel - Applications List */}
            <div className="lg:col-span-2 space-y-6">
              {/* Search and Filters */}
              <Card>
                <CardContent className="p-4 sm:p-6">
                  <div className="space-y-4">
                    {/* Search Bar */}
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <input
                        type="text"
                        placeholder="Search by name, email, or ID..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                      />
                    </div>

                    {/* Filter Toggle */}
                    <div className="flex items-center justify-between">
                      <Button
                        onClick={() => setShowFilters(!showFilters)}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        <Filter className="w-4 h-4" />
                        Filters
                        {showFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                      </Button>
                      <div className="text-sm text-gray-600">
                        {filteredApplications.length} of {applications.length} applications
                      </div>
                    </div>

                    {/* Filters */}
                    {showFilters && (
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Status</Label>
                          <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                            className="w-full mt-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          >
                            <option value="all">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="under_review">Under Review</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                          </select>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Priority</Label>
                          <select
                            value={priorityFilter}
                            onChange={(e) => setPriorityFilter(e.target.value)}
                            className="w-full mt-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          >
                            <option value="all">All Priority</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                          </select>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Sort By</Label>
                          <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value)}
                            className="w-full mt-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-colors"
                          >
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                            <option value="priority">Priority</option>
                            <option value="name">Name A-Z</option>
                          </select>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Applications List */}
              <div className="space-y-4">
                {filteredApplications.map((application) => (
                  <div
                    key={application.id}
                    className="cursor-pointer"
                    onClick={() => setSelectedApplication(application)}
                  >
                    <Card
                      className={`transition-all hover:shadow-md ${
                        selectedApplication?.id === application.id ? "ring-2 ring-blue-500 shadow-md" : ""
                      }`}
                    >
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-start gap-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                              <User className="w-5 h-5 text-blue-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="font-semibold text-gray-900 truncate">
                                {application.personalInfo.firstName} {application.personalInfo.lastName}
                              </h3>
                              <p className="text-sm text-gray-600 truncate">{application.personalInfo.email}</p>
                              <div className="flex items-center gap-2 mt-1">
                                <Gamepad2 className="w-4 h-4 text-gray-400" />
                                <span className="text-sm text-gray-600">{application.gamingInfo.primaryGame}</span>
                                <span className="text-gray-400">•</span>
                                <span className="text-sm text-gray-600">{application.gamingInfo.rank}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col sm:items-end gap-2">
                          <div className="flex items-center gap-2">
                            <Badge className={`${getStatusColor(application.status)} border`}>
                              {application.status.replace("_", " ")}
                            </Badge>
                            <Badge className={getPriorityColor(application.priority)}>{application.priority}</Badge>
                          </div>
                          <div className="flex items-center gap-1 text-xs text-gray-500">
                            <Calendar className="w-3 h-3" />
                            {formatDate(application.submittedAt)}
                          </div>
                          <div className="text-xs text-gray-500">ID: {application.id}</div>
                        </div>
                      </div>
                    </CardContent>
                    </Card>
                  </div>
                ))}

                {filteredApplications.length === 0 && (
                  <Card>
                    <CardContent className="p-8 text-center">
                      <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No applications found</h3>
                      <p className="text-gray-600">Try adjusting your search or filter criteria.</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Right Panel - Application Details */}
            <div className="space-y-6">
              {selectedApplication ? (
                <>
                  {/* Application Header */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Eye className="w-5 h-5 text-blue-600" />
                        Application Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold text-gray-900">
                            {selectedApplication.personalInfo.firstName} {selectedApplication.personalInfo.lastName}
                          </h3>
                          <p className="text-sm text-gray-600">ID: {selectedApplication.id}</p>
                        </div>
                        <Badge className={`${getStatusColor(selectedApplication.status)} border`}>
                          {selectedApplication.status.replace("_", " ")}
                        </Badge>
                      </div>

                      <div className="text-sm text-gray-600">
                        <div className="flex items-center gap-1 mb-1">
                          <Calendar className="w-4 h-4" />
                          Submitted: {formatDate(selectedApplication.submittedAt)}
                        </div>
                        {selectedApplication.reviewedAt && (
                          <div className="flex items-center gap-1">
                            <User className="w-4 h-4" />
                            Reviewed by {selectedApplication.reviewedBy} on {formatDate(selectedApplication.reviewedAt)}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Personal Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Personal Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 gap-3 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Email:</span>
                          <p className="text-gray-600">{selectedApplication.personalInfo.email}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Phone:</span>
                          <p className="text-gray-600">{selectedApplication.personalInfo.phone}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Date of Birth:</span>
                          <p className="text-gray-600">{selectedApplication.personalInfo.dateOfBirth}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Nationality:</span>
                          <p className="text-gray-600">{selectedApplication.personalInfo.nationality}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Gaming Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Gaming Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="grid grid-cols-1 gap-3 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Primary Game:</span>
                          <p className="text-gray-600">{selectedApplication.gamingInfo.primaryGame}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Rank:</span>
                          <p className="text-gray-600">{selectedApplication.gamingInfo.rank}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Years Playing:</span>
                          <p className="text-gray-600">{selectedApplication.gamingInfo.yearsPlaying} years</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">IGN:</span>
                          <p className="text-gray-600">{selectedApplication.gamingInfo.ign}</p>
                        </div>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700">Referee Experience:</span>
                        <p className="text-gray-600 mt-1">{selectedApplication.gamingInfo.refereeExperience}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Documents */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Documents</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center gap-2">
                            <FileText className="w-4 h-4 text-gray-600" />
                            <span className="text-sm font-medium">Government ID</span>
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            className="flex items-center gap-1"
                            onClick={() =>
                              handleDocumentView("Government ID", selectedApplication.documents.governmentId)
                            }
                          >
                            <Eye className="w-4 h-4" />
                            View
                          </Button>
                        </div>
                        {selectedApplication.documents.selfieWithId && (
                          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center gap-2">
                              <FileText className="w-4 h-4 text-gray-600" />
                              <span className="text-sm font-medium">Selfie with ID</span>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex items-center gap-1"
                              onClick={() =>
                                handleDocumentView("Selfie with ID", selectedApplication.documents.selfieWithId!)
                              }
                            >
                              <Eye className="w-4 h-4" />
                              View
                            </Button>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Payout Method */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Payout Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="text-sm">
                        <span className="font-medium text-gray-700">Method:</span>
                        <p className="text-gray-600 capitalize">{selectedApplication.payoutMethod.type}</p>
                      </div>
                      {selectedApplication.payoutMethod.gcashNumber && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">GCash Number:</span>
                          <p className="text-gray-600">{selectedApplication.payoutMethod.gcashNumber}</p>
                        </div>
                      )}
                      {selectedApplication.payoutMethod.mayaNumber && (
                        <div className="text-sm">
                          <span className="font-medium text-gray-700">Maya Number:</span>
                          <p className="text-gray-600">{selectedApplication.payoutMethod.mayaNumber}</p>
                        </div>
                      )}
                      {selectedApplication.payoutMethod.bankName && (
                        <div className="grid grid-cols-1 gap-2 text-sm">
                          <div>
                            <span className="font-medium text-gray-700">Bank Name:</span>
                            <p className="text-gray-600">{selectedApplication.payoutMethod.bankName}</p>
                          </div>
                          <div>
                            <span className="font-medium text-gray-700">Account Number:</span>
                            <p className="text-gray-600">{selectedApplication.payoutMethod.accountNumber}</p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Billing Address */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Billing Address</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-sm text-gray-600">
                        <p>{selectedApplication.billingAddress.street}</p>
                        <p>
                          {selectedApplication.billingAddress.city}, {selectedApplication.billingAddress.province}
                        </p>
                        <p>{selectedApplication.billingAddress.zipCode}</p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Application Answers */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Application Answers</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <span className="font-medium text-gray-700 block mb-1">Motivation:</span>
                        <p className="text-sm text-gray-600">{selectedApplication.applicationAnswers.motivation}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 block mb-1">Availability:</span>
                        <p className="text-sm text-gray-600">{selectedApplication.applicationAnswers.availability}</p>
                      </div>
                      <div>
                        <span className="font-medium text-gray-700 block mb-1">Conflict Resolution:</span>
                        <p className="text-sm text-gray-600">
                          {selectedApplication.applicationAnswers.conflictResolution}
                        </p>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Admin Notes */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Admin Notes</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {selectedApplication.adminNotes && (
                        <div className="p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600">{selectedApplication.adminNotes}</p>
                        </div>
                      )}
                      <div>
                        <Label htmlFor="adminNote" className="text-sm font-medium text-gray-700">
                          Add Note
                        </Label>
                        <textarea
                          id="adminNote"
                          value={adminNote}
                          onChange={(e) => setAdminNote(e.target.value)}
                          className="w-full mt-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                          rows={3}
                          placeholder="Add internal notes about this application..."
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Action Buttons */}
                  {selectedApplication.status === "pending" || selectedApplication.status === "under_review" ? (
                    <Card>
                      <CardContent className="p-4">
                        <div className="grid grid-cols-1 gap-3">
                          <Button
                            onClick={() => handleApplicationAction(selectedApplication.id, "approve")}
                            disabled={isProcessing}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            {isProcessing ? (
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                            ) : (
                              <Check className="w-4 h-4 mr-2" />
                            )}
                            Approve Application
                          </Button>
                          <Button
                            onClick={() => handleApplicationAction(selectedApplication.id, "reject")}
                            disabled={isProcessing}
                            className="bg-red-600 hover:bg-red-700 text-white"
                          >
                            {isProcessing ? (
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                            ) : (
                              <X className="w-4 h-4 mr-2" />
                            )}
                            Reject Application
                          </Button>
                          <Button
                            onClick={() => handleApplicationAction(selectedApplication.id, "review")}
                            disabled={isProcessing}
                            variant="outline"
                            className="border-blue-500 text-blue-600 hover:bg-blue-50"
                          >
                            {isProcessing ? (
                              <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mr-2" />
                            ) : (
                              <Clock className="w-4 h-4 mr-2" />
                            )}
                            Mark Under Review
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card>
                      <CardContent className="p-4 text-center">
                        <div className="flex items-center justify-center gap-2 text-gray-600">
                          <CheckCircle className="w-5 h-5" />
                          <span>Application has been {selectedApplication.status}</span>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </>
              ) : (
                <Card>
                  <CardContent className="p-8 text-center">
                    <Eye className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Application</h3>
                    <p className="text-gray-600">
                      Choose an application from the list to view details and take action.
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

// Mock data for demonstration
const mockApplications: RefereeApplication[] = [
  {
    id: "REF-2024-001",
    submittedAt: "2024-01-15T10:30:00Z",
    status: "pending",
    priority: "high",
    personalInfo: {
      firstName: "Juan",
      lastName: "Dela Cruz",
      email: "<EMAIL>",
      phone: "+63 ************",
      dateOfBirth: "1995-03-15",
      nationality: "Filipino",
    },
    gamingInfo: {
      primaryGame: "Mobile Legends",
      rank: "Mythical Glory",
      yearsPlaying: 5,
      ign: "JuanMLPro",
      refereeExperience:
        "I have been refereeing local tournaments for 2 years and have experience with competitive gaming rules and regulations.",
    },
    documents: {
      governmentId: "id_001.jpg",
      selfieWithId: "selfie_001.jpg",
    },
    payoutMethod: {
      type: "gcash",
      gcashNumber: "+63 ************",
    },
    billingAddress: {
      street: "123 Rizal Street, Barangay San Antonio",
      city: "Quezon City",
      province: "Metro Manila",
      zipCode: "1100",
    },
    applicationAnswers: {
      motivation:
        "I want to contribute to the gaming community by ensuring fair play and helping resolve disputes in competitive matches.",
      availability: "Available weekdays 6PM-11PM and weekends 2PM-11PM (GMT+8)",
      conflictResolution:
        "I would listen to both parties, review evidence carefully, and make decisions based on game rules and fair play principles.",
    },
  },
  {
    id: "REF-2024-002",
    submittedAt: "2024-01-14T15:45:00Z",
    status: "under_review",
    priority: "medium",
    personalInfo: {
      firstName: "Maria",
      lastName: "Santos",
      email: "<EMAIL>",
      phone: "+63 ************",
      dateOfBirth: "1992-07-22",
      nationality: "Filipino",
    },
    gamingInfo: {
      primaryGame: "Valorant",
      rank: "Immortal 2",
      yearsPlaying: 4,
      ign: "MariaShoots",
      refereeExperience:
        "New to refereeing but have extensive competitive gaming experience and strong understanding of game mechanics.",
    },
    documents: {
      governmentId: "id_002.jpg",
    },
    payoutMethod: {
      type: "bank",
      bankName: "BPI",
      accountNumber: "**********",
    },
    billingAddress: {
      street: "456 Bonifacio Avenue",
      city: "Manila",
      province: "Metro Manila",
      zipCode: "1000",
    },
    applicationAnswers: {
      motivation: "I love competitive gaming and want to help maintain integrity in esports matches.",
      availability: "Available most evenings and weekends",
      conflictResolution: "I believe in thorough investigation and clear communication with all parties involved.",
    },
    adminNotes: "Strong gaming background, reviewing referee training materials.",
    reviewedBy: "Admin User",
    reviewedAt: "2024-01-15T09:00:00Z",
  },
  {
    id: "REF-2024-003",
    submittedAt: "2024-01-13T20:15:00Z",
    status: "approved",
    priority: "low",
    personalInfo: {
      firstName: "Carlos",
      lastName: "Reyes",
      email: "<EMAIL>",
      phone: "+63 ************",
      dateOfBirth: "1988-11-08",
      nationality: "Filipino",
    },
    gamingInfo: {
      primaryGame: "Mobile Legends",
      rank: "Mythic",
      yearsPlaying: 6,
      ign: "CarlosML",
      refereeExperience:
        "Experienced referee with 3+ years in local and online tournaments. Certified by several gaming organizations.",
    },
    documents: {
      governmentId: "id_003.jpg",
      selfieWithId: "selfie_003.jpg",
    },
    payoutMethod: {
      type: "maya",
      mayaNumber: "+63 ************",
    },
    billingAddress: {
      street: "789 Magsaysay Boulevard",
      city: "Cebu City",
      province: "Cebu",
      zipCode: "6000",
    },
    applicationAnswers: {
      motivation: "Passionate about esports and want to contribute to its growth by ensuring fair competition.",
      availability: "Very flexible schedule, can accommodate most time zones",
      conflictResolution: "Use established protocols, document everything, and maintain neutrality at all times.",
    },
    adminNotes: "Excellent candidate with strong background. Approved for immediate onboarding.",
    reviewedBy: "Senior Admin",
    reviewedAt: "2024-01-14T11:30:00Z",
  },
  {
    id: "REF-2024-004",
    submittedAt: "2024-01-12T14:20:00Z",
    status: "rejected",
    priority: "low",
    personalInfo: {
      firstName: "Pedro",
      lastName: "Garcia",
      email: "<EMAIL>",
      phone: "+63 ************",
      dateOfBirth: "2005-05-12",
      nationality: "Filipino",
    },
    gamingInfo: {
      primaryGame: "Mobile Legends",
      rank: "Epic",
      yearsPlaying: 2,
      ign: "PedroGamer",
      refereeExperience: "No formal experience but willing to learn.",
    },
    documents: {
      governmentId: "id_004.jpg",
    },
    payoutMethod: {
      type: "gcash",
      gcashNumber: "+63 ************",
    },
    billingAddress: {
      street: "321 Luna Street",
      city: "Davao City",
      province: "Davao del Sur",
      zipCode: "8000",
    },
    applicationAnswers: {
      motivation: "I want to earn money and help with games.",
      availability: "After school hours",
      conflictResolution: "I would try to be fair to everyone.",
    },
    adminNotes:
      "Applicant is too young and lacks sufficient experience. Recommend reapplying in the future with more gaming experience.",
    reviewedBy: "Admin User",
    reviewedAt: "2024-01-13T16:45:00Z",
  },
]
