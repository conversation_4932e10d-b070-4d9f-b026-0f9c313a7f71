import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  Gamepad2,
  User,
  Wallet,
  FileText,
  Shield,
  History,
  Users,
  Settings,
  Trophy,
  X,
  LogOut,
  ShoppingCart,
  Bell
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
}

export default function Sidebar({ isOpen, onClose }: SidebarProps) {
  const location = useLocation()
  const { user, signOut } = useAuth()

  // Check if user has any referee access (regular referees OR level referees)
  const hasRefereeAccess = user?.admin_level >= 1 ||
                          user?.referee_status === 'approved' ||
                          user?.role === 'referee' ||
                          user?.role === 'admin' ||
                          user?.role === 'super_admin'

  // Check if user should see referee application (those WITHOUT referee access)
  const shouldShowRefereeApplication = !hasRefereeAccess

  const handleSignOut = async () => {
    await signOut()
    window.location.href = '/'
  }

  const topNavigationItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: Home,
      description: 'Overview and quick actions'
    },
    {
      name: 'Matches',
      href: '/matches',
      icon: Gamepad2,
      description: 'Create and join matches'
    },
    {
      name: 'Tournaments',
      href: '/tournaments',
      icon: Trophy,
      description: 'Join tournaments and competitions'
    },
    {
      name: 'Marketplace',
      href: '/marketplace',
      icon: ShoppingCart,
      description: 'Buy and sell gaming accounts'
    },
    // Show referee application only for users WITHOUT referee access
    ...(shouldShowRefereeApplication ? [{
      name: 'Referee Application',
      href: '/apply-referee',
      icon: FileText,
      description: 'Apply to become a referee'
    }] : []),
    // Show referee panel for ALL users WITH referee access (regular + level referees)
    ...(hasRefereeAccess ? [{
      name: 'Referee Panel',
      href: '/referee',
      icon: Shield,
      description: 'Process assigned matches'
    }] : []),
    {
      name: 'History',
      href: '/history',
      icon: History,
      description: 'View match and transaction history'
    },
    {
      name: 'Community',
      href: '/community',
      icon: Users,
      description: 'Chat and leaderboards'
    }
  ]

  const bottomNavigationItems = [
    {
      name: 'Wallet',
      href: '/wallet',
      icon: Wallet,
      description: 'Buy diamonds with crypto, withdraw cash, manage balance'
    },
    {
      name: 'Notifications',
      href: '/notifications',
      icon: Bell,
      description: 'View your notifications and alerts'
    },
    {
      name: 'Profile',
      href: '/profile',
      icon: User,
      description: 'Manage your profile'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Settings,
      description: 'Account and app settings'
    }
  ]

  const isActive = (href: string) => {
    if (href === '/dashboard' && location.pathname === '/dashboard') return true
    if (href !== '/dashboard' && location.pathname.startsWith(href)) return true
    return false
  }

  return (
    <>
      {/* Mobile Overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 h-full w-64 bg-white border-r border-gray-200 z-50 transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        md:translate-x-0 md:static md:z-auto
      `}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Trophy className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">Gambets</span>
          </Link>
          
          {/* Close button for mobile */}
          <button
            onClick={onClose}
            className="p-1 rounded-lg hover:bg-gray-100 md:hidden"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* User Info */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-blue-600" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-gray-900 truncate">
                {user?.first_name || user?.username || 'User'}
              </p>
              <div className="flex items-center space-x-1">
                <Wallet className="w-3 h-3 text-blue-600" />
                <p className="text-xs text-gray-500">
                  {user?.diamond_balance || 0} Diamonds
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Top Navigation */}
        <nav className="flex-1 p-4 space-y-1 overflow-y-auto">
          {topNavigationItems.map((item) => {
            const Icon = item.icon
            const active = isActive(item.href)

            return (
              <Link
                key={item.name}
                to={item.href}
                onClick={onClose}
                className={`
                  group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                  ${active
                    ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
                title={item.description}
              >
                <Icon className={`
                  mr-3 h-5 w-5 flex-shrink-0
                  ${active ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'}
                `} />
                <span className="truncate">{item.name}</span>
              </Link>
            )
          })}
        </nav>

        {/* Bottom Navigation */}
        <div className="p-4 border-t border-gray-200">
          <div className="space-y-1 mb-4">
            {bottomNavigationItems.map((item) => {
              const Icon = item.icon
              const active = isActive(item.href)

              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={onClose}
                  className={`
                    group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors
                    ${active
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }
                  `}
                  title={item.description}
                >
                  <Icon className={`
                    mr-3 h-5 w-5 flex-shrink-0
                    ${active ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-500'}
                  `} />
                  <span className="truncate">{item.name}</span>
                </Link>
              )
            })}
          </div>

          {/* Sign Out */}
          <button
            onClick={handleSignOut}
            className="w-full flex items-center px-3 py-2 text-sm font-medium text-gray-700 rounded-lg hover:bg-red-50 hover:text-red-700 transition-colors"
          >
            <LogOut className="mr-3 h-5 w-5 text-gray-400" />
            Sign Out
          </button>
        </div>
      </div>
    </>
  )
}
