# 🔒 PRIVATE PAGES

**Access Level:** Authentication required - Only logged-in users can access

## 🎯 Purpose

Private pages provide the core functionality of the Gambets platform for authenticated users. They handle:
- User account management and personalization
- Gaming and match participation
- Financial transactions and diamond management
- Community interaction and social features
- Referee system participation

## 📄 Page Inventory

### 🏠 DashboardPage.tsx
**Route:** `/dashboard`
**Purpose:** Main user hub and activity center

**Features:**
- Personal match overview
- Quick action buttons
- Recent activity feed
- Diamond balance display
- Performance statistics
- Upcoming matches

**Key Components:**
- Welcome header with user info
- Match cards with status indicators
- Quick action grid
- Statistics dashboard
- Activity timeline

**Target Audience:** All authenticated users

---

### 👤 ProfilePage.tsx
**Route:** `/profile`
**Purpose:** User profile management and settings

**Features:**
- Personal information editing
- Gaming ID management (MLBB, Valorant, COD)
- Profile picture upload
- Account preferences
- Privacy settings
- Account security options

**Key Components:**
- Profile form with validation
- Gaming ID input fields
- Image upload component
- Settings toggles
- Security options

**Target Audience:** Users managing their accounts

---

### 💎 WalletPage.tsx
**Route:** `/wallet`
**Purpose:** Diamond balance and transaction management

**Features:**
- Current diamond balance
- Transaction history
- Deposit diamonds (GCash, Maya, Bank)
- Withdraw diamonds
- Transaction filtering
- Payment method management

**Key Components:**
- Balance display card
- Transaction table
- Deposit/withdraw forms
- Payment method selector
- Transaction filters

**Target Audience:** Users managing finances

---

### 🎮 MatchesPage.tsx
**Route:** `/matches`
**Purpose:** Browse and join available matches

**Features:**
- Match browser with filters
- Game type filtering
- Entry fee ranges
- Match status indicators
- Quick join functionality
- Create new match option

**Key Components:**
- Match grid/list view
- Filter sidebar
- Match cards with details
- Join/create buttons
- Search functionality

**Target Audience:** Users looking for games

---

### 🎯 MatchDetailPage.tsx
**Route:** `/match/:id`
**Purpose:** Individual match details and management

**Features:**
- Match information display
- Participant list
- Match rules and requirements
- Join/leave functionality
- Chat system
- Result submission

**Key Components:**
- Match info header
- Participant cards
- Rules section
- Action buttons
- Chat interface
- Result forms

**Target Audience:** Match participants

---

### 🌟 CommunityPage.tsx
**Route:** `/community`
**Purpose:** Social features and community interaction

**Features:**
- Global chat system
- Guild/team features
- Player search
- Friend system
- Community events
- Leaderboards

**Key Components:**
- Chat interface
- Guild management
- Player directory
- Event calendar
- Social features

**Target Audience:** Users seeking social interaction

---

### ⚖️ RefereeDashboardPage.tsx
**Route:** `/referee-dashboard`
**Purpose:** Referee management and match oversight

**Features:**
- Assigned match reviews
- Decision making tools
- Referee statistics
- Earnings tracking
- Performance metrics
- Match dispute resolution

**Key Components:**
- Match review queue
- Decision forms
- Statistics dashboard
- Earnings display
- Performance charts

**Target Audience:** Approved referees

---

### 📋 RefereeApplicationPage.tsx
**Route:** `/apply-referee`
**Purpose:** Referee application and onboarding

**Features:**
- Application form
- Requirements checklist
- Experience documentation
- Background verification
- Terms acceptance
- Application status tracking

**Key Components:**
- Multi-step application form
- Requirements display
- Document upload
- Status tracker
- Terms agreement

**Target Audience:** Users applying to be referees

## 🛡️ Authentication & Authorization

### Route Protection
All private pages are protected by the `ProtectedRoute` component:

```typescript
<Route path="/dashboard" element={
  <ProtectedRoute>
    <DashboardPage />
  </ProtectedRoute>
} />
```

### User State Management
- Authentication status checking
- User profile data loading
- Session management
- Automatic logout on token expiry

### Role-Based Access
- **Standard Users** - Access to all private pages except referee dashboard
- **Referees** - Additional access to referee dashboard
- **Admins** - Access to all private pages plus admin pages

## 🔄 User Flow Patterns

### New User Onboarding
1. **DashboardPage** - Welcome and overview
2. **ProfilePage** - Complete profile setup
3. **WalletPage** - Add initial diamonds
4. **MatchesPage** - Find first match

### Regular Gaming Session
1. **DashboardPage** - Check activity
2. **MatchesPage** - Browse matches
3. **MatchDetailPage** - Join specific match
4. **WalletPage** - Check winnings

### Referee Workflow
1. **RefereeApplicationPage** - Apply for referee role
2. **RefereeDashboardPage** - Review assigned matches
3. **MatchDetailPage** - Make decisions
4. **WalletPage** - Track referee earnings

## 🎨 Design Principles

### User-Centric Design
- **Personalization** - Content tailored to user preferences
- **Quick Access** - Frequently used features prominently displayed
- **Status Indicators** - Clear visual feedback on all actions
- **Contextual Help** - Assistance where needed

### Data Visualization
- **Statistics** - Charts and graphs for performance data
- **Progress Tracking** - Visual progress indicators
- **Real-time Updates** - Live data where applicable
- **Historical Data** - Trends and historical performance

### Interactive Elements
- **Real-time Chat** - Live communication features
- **Dynamic Updates** - Content updates without page refresh
- **Drag & Drop** - Intuitive interaction patterns
- **Modal Dialogs** - Non-disruptive action flows

## 📊 Data Management

### State Management
- User profile data
- Match information
- Transaction history
- Real-time updates
- Cached data optimization

### API Integration
- Supabase real-time subscriptions
- Optimistic updates
- Error handling and retry logic
- Loading states management

### Data Security
- Sensitive data encryption
- Secure API communications
- Input validation and sanitization
- XSS and CSRF protection

## 🚀 Performance Optimization

### Loading Strategies
- **Lazy Loading** - Components loaded on demand
- **Data Prefetching** - Anticipate user needs
- **Caching** - Reduce redundant API calls
- **Pagination** - Handle large datasets efficiently

### Real-time Features
- **WebSocket Connections** - Live updates
- **Optimistic Updates** - Immediate UI feedback
- **Conflict Resolution** - Handle concurrent updates
- **Connection Management** - Robust connection handling

## 📱 Responsive Considerations

### Mobile Optimization
- **Touch-First Design** - Large touch targets
- **Simplified Navigation** - Mobile-friendly menus
- **Gesture Support** - Swipe and touch gestures
- **Offline Capabilities** - Basic functionality offline

### Cross-Platform Consistency
- **Consistent Experience** - Same features across devices
- **Adaptive Layouts** - Optimized for each screen size
- **Performance Parity** - Similar performance across platforms

## 🔧 Development Guidelines

### Adding New Private Pages
1. Create component in `pages/private/`
2. Implement authentication checks
3. Add route protection
4. Include loading states
5. Handle error scenarios
6. Add responsive design
7. Update navigation

### Security Best Practices
- **Input Validation** - Validate all user inputs
- **Authorization Checks** - Verify user permissions
- **Secure Communications** - HTTPS and secure headers
- **Data Sanitization** - Clean all user-generated content

### Testing Requirements
- **Authentication Testing** - Verify access controls
- **Functionality Testing** - Test all user interactions
- **Performance Testing** - Ensure responsive performance
- **Security Testing** - Validate security measures

## 🎯 Success Metrics

### User Engagement
- **Daily Active Users** - Regular platform usage
- **Session Duration** - Time spent in private areas
- **Feature Adoption** - Usage of different features
- **Return Rate** - User retention metrics

### Business Metrics
- **Match Participation** - Gaming activity levels
- **Transaction Volume** - Diamond usage and deposits
- **Referee Activity** - Referee system engagement
- **Community Growth** - Social feature usage
