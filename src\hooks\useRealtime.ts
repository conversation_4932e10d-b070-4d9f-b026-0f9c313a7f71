import { useEffect, useRef } from 'react'
import { RealtimeService } from '../services/realtime'

const realtimeService = new RealtimeService()

// Hook for subscribing to global chat messages
export const useGlobalChat = (callback: (message: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    realtimeService.subscribeToGlobalChat((message) => {
      callbackRef.current(message)
    })

    return () => {
      realtimeService.unsubscribe('global-chat')
    }
  }, [])
}

// Hook for subscribing to match updates
export const useMatchUpdates = (matchId: string | null, callback: (match: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    if (!matchId) return

    realtimeService.subscribeToMatchUpdates(matchId, (match) => {
      callbackRef.current(match)
    })

    return () => {
      realtimeService.unsubscribe(`match-${matchId}`)
    }
  }, [matchId])
}

// Hook for subscribing to all matches (for dashboard)
export const useAllMatches = (callback: (match: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    realtimeService.subscribeToAllMatches((match) => {
      callbackRef.current(match)
    })

    return () => {
      realtimeService.unsubscribe('all-matches')
    }
  }, [])
}

// Hook for subscribing to user updates
export const useUserUpdates = (userId: string | null, callback: (user: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    if (!userId) return

    realtimeService.subscribeToUserUpdates(userId, (user) => {
      callbackRef.current(user)
    })

    return () => {
      realtimeService.unsubscribe(`user-${userId}`)
    }
  }, [userId])
}

// Hook for subscribing to tournament updates
export const useTournamentUpdates = (tournamentId: string | null, callback: (tournament: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    if (!tournamentId) return

    realtimeService.subscribeToTournamentUpdates(tournamentId, (tournament) => {
      callbackRef.current(tournament)
    })

    return () => {
      realtimeService.unsubscribe(`tournament-${tournamentId}`)
    }
  }, [tournamentId])
}

// Hook for subscribing to referee matches
export const useRefereeMatches = (refereeId: string | null, callback: (match: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    if (!refereeId) return

    realtimeService.subscribeToRefereeMatches(refereeId, (match) => {
      callbackRef.current(match)
    })

    return () => {
      realtimeService.unsubscribe(`referee-matches-${refereeId}`)
    }
  }, [refereeId])
}

// Hook for subscribing to group updates
export const useGroupUpdates = (groupId: string | null, callback: (group: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    if (!groupId) return

    realtimeService.subscribeToGroupUpdates(groupId, (group) => {
      callbackRef.current(group)
    })

    return () => {
      realtimeService.unsubscribe(`group-${groupId}`)
    }
  }, [groupId])
}

// Hook for subscribing to group chat
export const useGroupChat = (groupId: string | null, callback: (message: any) => void) => {
  const callbackRef = useRef(callback)
  callbackRef.current = callback

  useEffect(() => {
    if (!groupId) return

    realtimeService.subscribeToGroupChat(groupId, (message) => {
      callbackRef.current(message)
    })

    return () => {
      realtimeService.unsubscribe(`group-chat-${groupId}`)
    }
  }, [groupId])
}

// Hook for managing realtime connection status
export const useRealtimeConnection = () => {
  useEffect(() => {
    // Connect to realtime when component mounts
    realtimeService.connect()

    // Cleanup on unmount
    return () => {
      realtimeService.disconnect()
    }
  }, [])

  return {
    isConnected: realtimeService.getConnectionStatus(),
    connect: realtimeService.connect.bind(realtimeService),
    disconnect: realtimeService.disconnect.bind(realtimeService)
  }
}
