import React, { useState } from 'react'
import {
  X,
  Megaphone,
  Calendar,
  Wrench,
  Trophy,
  ExternalLink,
  ChevronRight,
  Star,
  Zap
} from 'lucide-react'
import { Announcement } from '../types'

interface AnnouncementBannerProps {
  announcements: Announcement[]
  onDismiss: (id: string) => void
  onReadMore?: (announcement: Announcement) => void
}

const AnnouncementBanner: React.FC<AnnouncementBannerProps> = ({
  announcements,
  onDismiss,
  onReadMore
}) => {
  const [currentIndex, setCurrentIndex] = useState(0)

  // Filter active announcements
  const activeAnnouncements = announcements.filter(a => a.isActive)

  if (activeAnnouncements.length === 0) return null

  const currentAnnouncement = activeAnnouncements[currentIndex]

  const getAnnouncementConfig = (type: string) => {
    switch (type) {
      case 'update':
        return {
          icon: <Zap className="w-4 h-4" />,
          gradient: 'from-blue-500 to-purple-600',
          bgColor: 'bg-gradient-to-r from-blue-50 to-purple-50',
          borderColor: 'border-blue-200',
          textColor: 'text-blue-900'
        }
      case 'event':
        return {
          icon: <Calendar className="w-4 h-4" />,
          gradient: 'from-green-500 to-emerald-600',
          bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',
          borderColor: 'border-green-200',
          textColor: 'text-green-900'
        }
      case 'maintenance':
        return {
          icon: <Wrench className="w-4 h-4" />,
          gradient: 'from-orange-500 to-red-600',
          bgColor: 'bg-gradient-to-r from-orange-50 to-red-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-900'
        }
      case 'tournament':
        return {
          icon: <Trophy className="w-4 h-4" />,
          gradient: 'from-yellow-500 to-orange-600',
          bgColor: 'bg-gradient-to-r from-yellow-50 to-orange-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-900'
        }
      default:
        return {
          icon: <Megaphone className="w-4 h-4" />,
          gradient: 'from-gray-500 to-gray-600',
          bgColor: 'bg-gradient-to-r from-gray-50 to-gray-100',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-900'
        }
    }
  }

  const config = getAnnouncementConfig(currentAnnouncement.type)

  const handleNext = () => {
    if (activeAnnouncements.length > 1) {
      setCurrentIndex((prev) => (prev + 1) % activeAnnouncements.length)
    }
  }

  const handlePrevious = () => {
    if (activeAnnouncements.length > 1) {
      setCurrentIndex((prev) => (prev - 1 + activeAnnouncements.length) % activeAnnouncements.length)
    }
  }

  const formatTimeRemaining = (expiresAt?: string) => {
    if (!expiresAt) return null
    
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diff = expiry.getTime() - now.getTime()
    
    if (diff <= 0) return 'Expired'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 24) {
      const days = Math.floor(hours / 24)
      return `${days}d remaining`
    } else if (hours > 0) {
      return `${hours}h ${minutes}m remaining`
    } else {
      return `${minutes}m remaining`
    }
  }

  return (
    <div className={`relative ${config.bgColor} border ${config.borderColor} rounded-lg p-3 mb-4 shadow-sm`}>
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className={`w-full h-full bg-gradient-to-r ${config.gradient} rounded-lg`}></div>
      </div>

      <div className="relative flex items-center gap-3">
        {/* Icon */}
        <div className={`flex-shrink-0 w-8 h-8 bg-gradient-to-r ${config.gradient} rounded-lg flex items-center justify-center text-white`}>
          {config.icon}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h3 className={`text-sm font-semibold ${config.textColor} truncate`}>
              {currentAnnouncement.title}
            </h3>
            {currentAnnouncement.type === 'tournament' && (
              <Star className="w-3 h-3 text-yellow-500" />
            )}
            {currentAnnouncement.expiresAt && (
              <span className="px-2 py-0.5 bg-white bg-opacity-50 rounded-full text-xs font-medium">
                {formatTimeRemaining(currentAnnouncement.expiresAt)}
              </span>
            )}
          </div>
          <p className={`text-xs ${config.textColor} opacity-90 line-clamp-2`}>
            {currentAnnouncement.content}
          </p>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-1">
          {/* Navigation for multiple announcements */}
          {activeAnnouncements.length > 1 && (
            <div className="flex items-center gap-1 mr-2">
              <button
                onClick={handlePrevious}
                className="p-1 hover:bg-white hover:bg-opacity-50 rounded transition-colors"
                title="Previous announcement"
              >
                <ChevronRight className="w-3 h-3 rotate-180 text-gray-600" />
              </button>
              <span className="text-xs text-gray-600 px-1">
                {currentIndex + 1}/{activeAnnouncements.length}
              </span>
              <button
                onClick={handleNext}
                className="p-1 hover:bg-white hover:bg-opacity-50 rounded transition-colors"
                title="Next announcement"
              >
                <ChevronRight className="w-3 h-3 text-gray-600" />
              </button>
            </div>
          )}

          {/* Read More Button */}
          {onReadMore && (
            <button
              onClick={() => onReadMore(currentAnnouncement)}
              className="flex items-center gap-1 px-2 py-1 bg-white bg-opacity-50 hover:bg-opacity-75 rounded text-xs font-medium transition-colors"
            >
              <span>Read More</span>
              <ExternalLink className="w-3 h-3" />
            </button>
          )}

          {/* Dismiss Button */}
          <button
            onClick={() => onDismiss(currentAnnouncement.id)}
            className="p-1 hover:bg-white hover:bg-opacity-50 rounded transition-colors"
            title="Dismiss announcement"
          >
            <X className="w-4 h-4 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Progress Indicator for Multiple Announcements */}
      {activeAnnouncements.length > 1 && (
        <div className="flex gap-1 mt-2 justify-center">
          {activeAnnouncements.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentIndex
                  ? 'bg-white bg-opacity-80'
                  : 'bg-white bg-opacity-30 hover:bg-opacity-50'
              }`}
            />
          ))}
        </div>
      )}

      {/* Auto-advance timer for multiple announcements */}
      {activeAnnouncements.length > 1 && (
        <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-white bg-opacity-20 rounded-b-lg overflow-hidden">
          <div 
            className="h-full bg-white bg-opacity-60 transition-all duration-[5000ms] ease-linear"
            style={{
              width: '100%',
              animation: 'progress 5s linear infinite'
            }}
          />
        </div>
      )}

      <style>{`
        @keyframes progress {
          from { width: 0%; }
          to { width: 100%; }
        }
      `}</style>
    </div>
  )
}

export default AnnouncementBanner
