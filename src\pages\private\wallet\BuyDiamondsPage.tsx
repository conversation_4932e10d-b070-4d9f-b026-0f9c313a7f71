import { useState, useEffect } from 'react'
import {
  ArrowLeft, Gem, Upload, AlertCircle, CheckCircle, Info,
  Calculator, DollarSign
} from 'lucide-react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '../../../contexts/AuthContext'
import { useNotifications } from '../../../contexts/NotificationContext'
import { manualPaymentService } from '../../../services/manualPaymentService'
import {
  PAYMENT_METHODS,
  TRADITIONAL_PAYMENT_METHODS,
  CRYPTO_PAYMENT_METHODS
} from '../../../services/cryptoPayments'
import { cryptoPaymentsService } from '../../../services/cryptoPayments'

export default function BuyDiamondsPage() {
  const navigate = useNavigate()
  const { user } = useAuth()
  const { addNotification } = useNotifications()

  // State for custom amount system
  const [customAmount, setCustomAmount] = useState<string>('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('')
  const [paymentType, setPaymentType] = useState<'traditional' | 'crypto'>('traditional')
  const [isProcessing, setIsProcessing] = useState(false)
  const [diamondPreview, setDiamondPreview] = useState<any>(null)
  const [paymentProof, setPaymentProof] = useState<File | null>(null)
  const [referenceNumber, setReferenceNumber] = useState<string>('')
  const [userNotes, setUserNotes] = useState<string>('')
  const [validationError, setValidationError] = useState<string>('')

  // Get available payment methods based on type
  const availablePaymentMethods = paymentType === 'traditional'
    ? TRADITIONAL_PAYMENT_METHODS
    : CRYPTO_PAYMENT_METHODS

  // Update diamond preview when amount changes
  useEffect(() => {
    if (customAmount && !isNaN(Number(customAmount))) {
      const amount = Number(customAmount)
      const preview = manualPaymentService.calculateDiamondsPreview(amount)
      setDiamondPreview(preview)

      // Validate amount for selected payment method
      if (selectedPaymentMethod) {
        const validation = manualPaymentService.validateAmount(amount, selectedPaymentMethod)
        setValidationError(validation.valid ? '' : validation.error || '')
      }
    } else {
      setDiamondPreview(null)
      setValidationError('')
    }
  }, [customAmount, selectedPaymentMethod])

  // Handle payment method change
  const handlePaymentMethodChange = (methodId: string) => {
    setSelectedPaymentMethod(methodId)
    const method = PAYMENT_METHODS.find(m => m.id === methodId)
    if (method) {
      setPaymentType(method.type as 'traditional' | 'crypto')

      // Validate current amount with new method
      if (customAmount && !isNaN(Number(customAmount))) {
        const validation = manualPaymentService.validateAmount(Number(customAmount), methodId)
        setValidationError(validation.valid ? '' : validation.error || '')
      }
    }
  }

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'application/pdf']
      const maxSize = 5 * 1024 * 1024 // 5MB

      if (!allowedTypes.includes(file.type)) {
        addNotification({
          type: 'error',
          title: 'Invalid File Type',
          message: 'Please upload a JPG, PNG, or PDF file'
        })
        return
      }

      if (file.size > maxSize) {
        addNotification({
          type: 'error',
          title: 'File Too Large',
          message: 'Please upload a file smaller than 5MB'
        })
        return
      }

      setPaymentProof(file)
    }
  }

  const handleSubmitDeposit = async () => {
    if (!user || !customAmount || !selectedPaymentMethod) {
      addNotification({
        type: 'error',
        title: 'Missing Information',
        message: 'Please fill in all required fields'
      })
      return
    }

    const amount = Number(customAmount)
    if (isNaN(amount) || amount <= 0) {
      addNotification({
        type: 'error',
        title: 'Invalid Amount',
        message: 'Please enter a valid amount'
      })
      return
    }

    // Validate amount
    const validation = manualPaymentService.validateAmount(amount, selectedPaymentMethod)
    if (!validation.valid) {
      addNotification({
        type: 'error',
        title: 'Invalid Amount',
        message: validation.error || 'Invalid amount'
      })
      return
    }

    // For traditional payments, require payment proof and reference
    const method = PAYMENT_METHODS.find(m => m.id === selectedPaymentMethod)
    if (method?.type === 'traditional') {
      if (!paymentProof) {
        addNotification({
          type: 'error',
          title: 'Payment Proof Required',
          message: 'Please upload your payment receipt'
        })
        return
      }

      if (!referenceNumber.trim()) {
        addNotification({
          type: 'error',
          title: 'Reference Number Required',
          message: 'Please enter the transaction reference number'
        })
        return
      }
    }

    setIsProcessing(true)
    try {
      const method = PAYMENT_METHODS.find(m => m.id === selectedPaymentMethod)

      if (method?.type === 'traditional') {
        // Handle traditional payments (manual processing)
        const paymentProofData = paymentProof ? {
          file: paymentProof,
          reference_number: referenceNumber,
          notes: userNotes
        } : undefined

        const result = await manualPaymentService.submitDepositRequest(
          user.id,
          amount,
          selectedPaymentMethod,
          paymentProofData
        )

        if (result.error) {
          throw new Error((result.error as any)?.message || 'Failed to submit deposit request')
        }

        addNotification({
          type: 'success',
          title: 'Deposit Request Submitted',
          message: 'Your deposit request has been submitted for review. You will receive diamonds once approved.'
        })

      } else if (method?.type === 'crypto') {
        // Handle crypto payments (automated processing via NowPayments.io gateway)

        // Create crypto payment with correct API format
        const result = await cryptoPaymentsService.createPayment(
          user.id,
          'custom', // package ID for custom amounts
          (method as any).symbol, // cryptocurrency symbol (e.g., 'usdttrc20')
          amount // custom amount in PHP
        )

        if (result.error) {
          throw new Error(result.error.message || 'Failed to create crypto payment')
        }

        addNotification({
          type: 'success',
          title: 'Payment Created',
          message: 'Redirecting to NowPayments.io gateway...'
        })

        // Redirect to payment gateway
        if ((result.data as any)?.payment_url) {
          window.location.href = (result.data as any).payment_url
        } else {
          throw new Error('No payment URL received from gateway')
        }
      }

      // Reset form
      setCustomAmount('')
      setSelectedPaymentMethod('')
      setPaymentProof(null)
      setReferenceNumber('')
      setUserNotes('')
      setDiamondPreview(null)

      // Navigate back to wallet
      setTimeout(() => {
        navigate('/wallet')
      }, 2000)
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Submission Failed',
        message: error instanceof Error ? error.message : 'An error occurred'
      })
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-3">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="flex items-center gap-3 mb-6">
          <button
            onClick={() => navigate('/wallet')}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <div>
            <h1 className="text-xl font-semibold text-gray-900">Buy Diamonds</h1>
            <p className="text-sm text-gray-600">Enter custom amount and choose payment method</p>
          </div>
        </div>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Custom Amount Input */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                <Calculator className="w-5 h-5 text-blue-500" />
                Enter Amount
              </h2>

              {/* Payment Type Selector */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Payment Type
                </label>
                <div className="grid grid-cols-2 gap-3">
                  <button
                    onClick={() => {
                      setPaymentType('traditional')
                      setSelectedPaymentMethod('')
                    }}
                    className={`p-3 border-2 rounded-lg text-center transition-all ${
                      paymentType === 'traditional'
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">Traditional</div>
                    <div className="text-xs text-gray-500">₱10,000 - ₱40,000</div>
                    <div className="text-xs text-gray-500">GCash, Maya, Bank</div>
                  </button>
                  <button
                    onClick={() => {
                      setPaymentType('crypto')
                      setSelectedPaymentMethod('')
                    }}
                    className={`p-3 border-2 rounded-lg text-center transition-all ${
                      paymentType === 'crypto'
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="font-medium">Cryptocurrency</div>
                    <div className="text-xs text-gray-500">₱50 - Unlimited</div>
                    <div className="text-xs text-gray-500">USDT, USDC</div>
                  </button>
                </div>
              </div>

              {/* Amount Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Amount (PHP)
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">₱</span>
                  <input
                    type="number"
                    value={customAmount}
                    onChange={(e) => setCustomAmount(e.target.value)}
                    placeholder={paymentType === 'traditional' ? '10,000' : '50'}
                    className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                {validationError && (
                  <p className="mt-1 text-sm text-red-600 flex items-center gap-1">
                    <AlertCircle className="w-4 h-4" />
                    {validationError}
                  </p>
                )}
              </div>

              {/* Diamond Preview */}
              {diamondPreview && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                    <Gem className="w-4 h-4" />
                    You will receive:
                  </h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-blue-700">Base Diamonds:</span>
                      <span className="font-medium">{diamondPreview.base_diamonds.toLocaleString()} 💎</span>
                    </div>
                    {diamondPreview.bonus_diamonds > 0 && (
                      <div className="flex justify-between">
                        <span className="text-green-700">Bonus ({diamondPreview.bonus_percentage}%):</span>
                        <span className="font-medium text-green-700">+{diamondPreview.bonus_diamonds.toLocaleString()} 💎</span>
                      </div>
                    )}
                    <hr className="border-blue-200" />
                    <div className="flex justify-between text-base font-semibold">
                      <span className="text-blue-900">Total Diamonds:</span>
                      <span className="text-blue-900">{diamondPreview.total_diamonds.toLocaleString()} 💎</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Payment Methods & Details */}
          <div className="space-y-6">
            {/* Payment Methods */}
            <div className="bg-white rounded-xl shadow-sm border p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                {paymentType === 'traditional' ? 'Traditional Payment' : 'Cryptocurrency Payment'}
              </h3>

              <div className="space-y-3">
                {availablePaymentMethods.map((method) => (
                  <div
                    key={method.id}
                    onClick={() => handlePaymentMethodChange(method.id)}
                    className={`p-3 border-2 rounded-lg cursor-pointer transition-all ${
                      selectedPaymentMethod === method.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{method.icon}</div>
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{method.name}</div>
                        <div className="text-xs text-gray-500">{method.description}</div>
                        <div className="text-xs text-gray-500">
                          {method.processing_time} • Min: ₱{method.min_amount_php.toLocaleString()}
                          {method.max_amount_php !== Infinity && ` • Max: ₱${method.max_amount_php.toLocaleString()}`}
                        </div>
                      </div>
                      {selectedPaymentMethod === method.id && (
                        <CheckCircle className="w-5 h-5 text-blue-500" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Payment Instructions */}
            {selectedPaymentMethod && (
              <div className="bg-white rounded-xl shadow-sm border p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Info className="w-5 h-5 text-blue-500" />
                  Payment Instructions
                </h3>

                {(() => {
                  const method = PAYMENT_METHODS.find(m => m.id === selectedPaymentMethod)
                  if (!method) return null

                  if (method.type === 'traditional') {
                    return (
                      <div className="space-y-4">
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <p className="text-sm text-yellow-800">
                            <strong>Manual Processing:</strong> Send payment to our account and upload receipt.
                            Diamonds will be added after verification (1-24 hours).
                          </p>
                        </div>

                        {(method as any).account_info && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <h4 className="font-medium text-gray-900 mb-2">Account Details:</h4>
                            <div className="text-sm space-y-1">
                              <div><strong>Name:</strong> {(method as any).account_info.account_name}</div>
                              <div><strong>Number:</strong> {(method as any).account_info.account_number}</div>
                              {(method as any).account_info.bank_name && (
                                <div><strong>Bank:</strong> {(method as any).account_info.bank_name}</div>
                              )}
                            </div>
                            <p className="text-xs text-gray-600 mt-2">
                              {(method as any).account_info.instructions}
                            </p>
                          </div>
                        )}

                        {/* File Upload */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Payment Receipt <span className="text-red-500">*</span>
                          </label>
                          <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                            <input
                              type="file"
                              accept="image/*,.pdf"
                              onChange={handleFileUpload}
                              className="hidden"
                              id="payment-proof"
                            />
                            <label htmlFor="payment-proof" className="cursor-pointer">
                              <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                              <p className="text-sm text-gray-600">
                                {paymentProof ? paymentProof.name : 'Click to upload receipt (JPG, PNG, PDF)'}
                              </p>
                            </label>
                          </div>
                        </div>

                        {/* Reference Number */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Reference Number <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="text"
                            value={referenceNumber}
                            onChange={(e) => setReferenceNumber(e.target.value)}
                            placeholder="Enter transaction reference number"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>

                        {/* Notes */}
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Additional Notes (Optional)
                          </label>
                          <textarea
                            value={userNotes}
                            onChange={(e) => setUserNotes(e.target.value)}
                            placeholder="Any additional information..."
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    )
                  } else {
                    return (
                      <div className="space-y-4">
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p className="text-sm text-green-800">
                            <strong>Automated Processing:</strong> Diamonds will be added automatically after payment confirmation (5-15 minutes).
                          </p>
                        </div>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <p className="text-sm text-blue-800">
                            <strong>Crypto Gateway:</strong> You will be redirected to our secure cryptocurrency payment gateway to complete the transaction.
                          </p>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="text-sm space-y-1">
                            <div><strong>Network:</strong> {(method as any).network}</div>
                            <div><strong>Currency:</strong> {method.name}</div>
                            <div><strong>Fee:</strong> {method.fee_percentage}%</div>
                          </div>
                        </div>
                      </div>
                    )
                  }
                })()}
              </div>
            )}

            {/* Submit Button */}
            {customAmount && selectedPaymentMethod && diamondPreview && (
              <div className="bg-white rounded-xl shadow-sm border p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Summary</h3>

                <div className="space-y-3 mb-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Amount</span>
                    <span className="font-medium">₱{Number(customAmount).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base Diamonds</span>
                    <span className="font-medium">{diamondPreview.base_diamonds.toLocaleString()} 💎</span>
                  </div>
                  {diamondPreview.bonus_diamonds > 0 && (
                    <div className="flex justify-between">
                      <span className="text-green-600">Bonus ({diamondPreview.bonus_percentage}%)</span>
                      <span className="font-medium text-green-600">+{diamondPreview.bonus_diamonds.toLocaleString()} 💎</span>
                    </div>
                  )}
                  <hr />
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Total Diamonds</span>
                    <span className="text-blue-600">
                      {diamondPreview.total_diamonds.toLocaleString()} 💎
                    </span>
                  </div>
                </div>

                <button
                  onClick={handleSubmitDeposit}
                  disabled={isProcessing || !!validationError}
                  className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white font-semibold py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <DollarSign className="w-4 h-4" />
                      Submit Deposit Request
                    </>
                  )}
                </button>

                <p className="text-xs text-gray-500 text-center mt-2">
                  Your request will be reviewed and diamonds added within 1-24 hours
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
