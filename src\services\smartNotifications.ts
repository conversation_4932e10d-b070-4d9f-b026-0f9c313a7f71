import { supabase } from './supabase'
import type { User } from './supabase'

export interface SmartNotification {
  id: string
  user_id: string
  type: 'perfect_match' | 'friend_activity' | 'price_alert' | 'win_streak' | 'bonus_opportunity'
  title: string
  message: string
  action_url?: string
  action_label?: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  data: Record<string, any>
  read: boolean
  created_at: string
}

export interface NotificationPreferences {
  user_id: string
  perfect_match_enabled: boolean
  friend_activity_enabled: boolean
  price_alerts_enabled: boolean
  win_streak_enabled: boolean
  bonus_opportunities_enabled: boolean
  quiet_hours_start?: string
  quiet_hours_end?: string
  max_daily_notifications: number
}

export interface MatchOpportunity {
  match_id: string
  game: string
  format: string
  entry_fee: number
  estimated_win_rate: number
  difficulty_score: number
  bonus_multiplier?: number
}

class SmartNotificationService {
  // Analyze if a match is perfect for a user
  async analyzeMatchForUser(user: User, match: any): Promise<MatchOpportunity | null> {
    try {
      // Get user's game performance history
      const { data: userStats } = await supabase
        .from('user_game_stats')
        .select('*')
        .eq('user_id', user.id)
        .eq('game', match.game)
        .single()

      // Calculate win probability based on historical performance
      const baseWinRate = userStats?.win_rate || 0.5
      const skillDifference = ((user as any).skill_level || 5) - (match.avg_skill_level || 5)
      const estimatedWinRate = Math.min(0.95, Math.max(0.05, baseWinRate + (skillDifference * 0.1)))

      // Calculate difficulty score (1-10)
      const difficultyScore = Math.max(1, Math.min(10, 
        5 + (match.avg_skill_level - ((user as any).skill_level || 5)) +
        (match.entry_fee > user.diamond_balance * 0.1 ? 2 : 0)
      ))

      // Check if this is a good opportunity
      const isAffordable = match.entry_fee <= user.diamond_balance * 0.2 // Max 20% of balance
      const isWinnable = estimatedWinRate >= 0.6 // At least 60% win chance
      const isPreferredGame = (user as any).favorite_games?.includes(match.game)

      if (isAffordable && (isWinnable || isPreferredGame)) {
        return {
          match_id: match.id,
          game: match.game,
          format: match.mode,
          entry_fee: match.entry_fee,
          estimated_win_rate: estimatedWinRate,
          difficulty_score: difficultyScore,
          bonus_multiplier: this.getBonusMultiplier(match)
        }
      }

      return null
    } catch (error) {
      console.error('Error analyzing match for user:', error)
      return null
    }
  }

  // Check for bonus opportunities (happy hour, events, etc.)
  private getBonusMultiplier(_match: any): number {
    const now = new Date()
    const hour = now.getHours()
    
    // Happy hour bonus (6-8 PM)
    if (hour >= 18 && hour <= 20) {
      return 1.5
    }
    
    // Weekend bonus
    if (now.getDay() === 0 || now.getDay() === 6) {
      return 1.2
    }
    
    return 1.0
  }

  // Generate perfect match notification
  async createPerfectMatchNotification(user: User, opportunity: MatchOpportunity): Promise<SmartNotification> {
    const bonusText = (opportunity as any).bonus_multiplier > 1
      ? ` (${opportunity.bonus_multiplier}x bonus active!)` 
      : ''

    return {
      id: crypto.randomUUID(),
      user_id: user.id,
      type: 'perfect_match',
      title: '🎯 Perfect Match Found!',
      message: `${opportunity.game} ${opportunity.format} - ${Math.round(opportunity.estimated_win_rate * 100)}% win probability${bonusText}`,
      action_url: `/matches?highlight=${opportunity.match_id}`,
      action_label: 'Join Match',
      priority: opportunity.estimated_win_rate > 0.8 ? 'high' : 'medium',
      data: {
        match_id: opportunity.match_id,
        estimated_win_rate: opportunity.estimated_win_rate,
        entry_fee: opportunity.entry_fee,
        bonus_multiplier: opportunity.bonus_multiplier
      },
      read: false,
      created_at: new Date().toISOString()
    }
  }

  // Create friend activity notification
  async createFriendActivityNotification(user: User, friend: any, activity: any): Promise<SmartNotification> {
    const activityText = activity.type === 'match_won' 
      ? `won ${activity.prize_amount}💎 in ${activity.game}!`
      : `joined a ${activity.game} match`

    return {
      id: crypto.randomUUID(),
      user_id: user.id,
      type: 'friend_activity',
      title: '👥 Friend Activity',
      message: `${friend.username} just ${activityText} Challenge them?`,
      action_url: `/matches?game=${activity.game}&challenge=${friend.id}`,
      action_label: 'Challenge',
      priority: 'medium',
      data: {
        friend_id: friend.id,
        friend_username: friend.username,
        activity_type: activity.type,
        game: activity.game
      },
      read: false,
      created_at: new Date().toISOString()
    }
  }

  // Create price alert notification
  async createPriceAlertNotification(user: User, match: any, targetPrice: number): Promise<SmartNotification> {
    return {
      id: crypto.randomUUID(),
      user_id: user.id,
      type: 'price_alert',
      title: '💎 Price Alert!',
      message: `${match.game} match at your target price (${targetPrice}💎) just opened!`,
      action_url: `/matches?highlight=${match.id}`,
      action_label: 'View Match',
      priority: 'high',
      data: {
        match_id: match.id,
        target_price: targetPrice,
        actual_price: match.entry_fee,
        game: match.game
      },
      read: false,
      created_at: new Date().toISOString()
    }
  }

  // Check user's notification preferences
  async getUserNotificationPreferences(userId: string): Promise<NotificationPreferences> {
    const { data } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()

    // Default preferences if none exist
    return data || {
      user_id: userId,
      perfect_match_enabled: true,
      friend_activity_enabled: true,
      price_alerts_enabled: true,
      win_streak_enabled: true,
      bonus_opportunities_enabled: true,
      max_daily_notifications: 10
    }
  }

  // Check if user is in quiet hours
  private isInQuietHours(preferences: NotificationPreferences): boolean {
    if (!preferences.quiet_hours_start || !preferences.quiet_hours_end) {
      return false
    }

    const now = new Date()
    const currentTime = now.getHours() * 60 + now.getMinutes()
    
    const startTime = this.parseTime(preferences.quiet_hours_start)
    const endTime = this.parseTime(preferences.quiet_hours_end)

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime
    } else {
      // Quiet hours span midnight
      return currentTime >= startTime || currentTime <= endTime
    }
  }

  private parseTime(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }

  // Send notification to user
  async sendNotification(notification: SmartNotification): Promise<void> {
    try {
      // Check user preferences
      const preferences = await this.getUserNotificationPreferences(notification.user_id)
      
      // Check if notification type is enabled
      const typeEnabled = this.isNotificationTypeEnabled(notification.type, preferences)
      if (!typeEnabled) {
        return
      }

      // Check quiet hours
      if (this.isInQuietHours(preferences)) {
        // Queue for later or skip non-urgent notifications
        if (notification.priority !== 'urgent') {
          return
        }
      }

      // Check daily limit
      const todayCount = await this.getTodayNotificationCount(notification.user_id)
      if (todayCount >= preferences.max_daily_notifications) {
        return
      }

      // Save to database
      await supabase
        .from('smart_notifications')
        .insert([notification])

      // Send push notification if supported
      if ('Notification' in window && Notification.permission === 'granted') {
        new Notification(notification.title, {
          body: notification.message,
          icon: '/favicon.ico',
          badge: '/badge-icon.png'
        })
      }

    } catch (error) {
      console.error('Error sending notification:', error)
    }
  }

  private isNotificationTypeEnabled(type: string, preferences: NotificationPreferences): boolean {
    switch (type) {
      case 'perfect_match': return preferences.perfect_match_enabled
      case 'friend_activity': return preferences.friend_activity_enabled
      case 'price_alert': return preferences.price_alerts_enabled
      case 'win_streak': return preferences.win_streak_enabled
      case 'bonus_opportunity': return preferences.bonus_opportunities_enabled
      default: return true
    }
  }

  private async getTodayNotificationCount(userId: string): Promise<number> {
    const today = new Date().toISOString().split('T')[0]
    const { count } = await supabase
      .from('smart_notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .gte('created_at', `${today}T00:00:00.000Z`)
      .lt('created_at', `${today}T23:59:59.999Z`)

    return count || 0
  }

  // Get user's notifications
  async getUserNotifications(userId: string, limit = 20): Promise<SmartNotification[]> {
    const { data } = await supabase
      .from('smart_notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit)

    return data || []
  }

  // Mark notification as read
  async markAsRead(notificationId: string): Promise<void> {
    await supabase
      .from('smart_notifications')
      .update({ read: true })
      .eq('id', notificationId)
  }

  // Update user notification preferences
  async updateNotificationPreferences(preferences: NotificationPreferences): Promise<void> {
    await supabase
      .from('notification_preferences')
      .upsert([preferences])
  }
}

export const smartNotificationService = new SmartNotificationService()
