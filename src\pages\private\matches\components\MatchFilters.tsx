import { useState } from 'react'
import { Filter, X, Search, DollarSign, Users, Trophy, Clock } from 'lucide-react'
import { TouchButton } from '../../../../components/mobile/TouchOptimized'
import { GAMES } from '../../../../constants'

export interface MatchFilters {
  game?: string
  status?: string
  format?: string
  minPot?: number
  maxPot?: number
  search?: string
  sortBy?: 'newest' | 'highest-bet' | 'soonest'
}

interface MatchFiltersProps {
  filters: MatchFilters
  onFiltersChange: (filters: MatchFilters) => void
  onClearFilters: () => void
}

export default function MatchFilters({ filters, onFiltersChange, onClearFilters }: MatchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const gameOptions = Object.entries(GAMES).map(([key, game]) => ({
    value: key,
    label: game.name
  }))

  const statusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'full', label: 'Full' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'completed', label: 'Completed' },
    { value: 'cancelled', label: 'Cancelled' }
  ]

  const formatOptions = [
    { value: '1v1', label: '1v1' },
    { value: '2v2', label: '2v2' },
    { value: '3v3', label: '3v3' },
    { value: '4v4', label: '4v4' },
    { value: '5v5', label: '5v5' }
  ]

  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'highest-bet', label: 'Highest Prize' },
    { value: 'soonest', label: 'Starting Soon' }
  ]

  const updateFilter = (key: keyof MatchFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value
    })
  }

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== null
  )

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700">
      {/* Filter Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-gray-400" />
          <span className="text-white font-medium">Filters</span>
          {hasActiveFilters && (
            <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
              Active
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          {hasActiveFilters && (
            <TouchButton
              onClick={onClearFilters}
              className="text-gray-400 hover:text-white text-sm"
            >
              Clear All
            </TouchButton>
          )}
          <TouchButton
            onClick={() => setIsExpanded(!isExpanded)}
            className="text-gray-400 hover:text-white"
          >
            {isExpanded ? <X className="w-4 h-4" /> : <Filter className="w-4 h-4" />}
          </TouchButton>
        </div>
      </div>

      {/* Quick Filters (Always Visible) */}
      <div className="p-4 space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search matches..."
            value={filters.search || ''}
            onChange={(e) => updateFilter('search', e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
          />
        </div>

        {/* Quick Status Filters */}
        <div className="flex flex-wrap gap-2">
          {statusOptions.slice(0, 3).map((status) => (
            <TouchButton
              key={status.value}
              onClick={() => updateFilter('status', filters.status === status.value ? '' : status.value)}
              className={`px-3 py-1 rounded-full text-sm transition-colors ${
                filters.status === status.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              }`}
            >
              {status.label}
            </TouchButton>
          ))}
        </div>
      </div>

      {/* Expanded Filters */}
      {isExpanded && (
        <div className="p-4 border-t border-gray-700 space-y-4">
          {/* Game Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              <Trophy className="w-4 h-4 inline mr-1" />
              Game
            </label>
            <select
              value={filters.game || ''}
              onChange={(e) => updateFilter('game', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
            >
              <option value="">All Games</option>
              {gameOptions.map((game) => (
                <option key={game.value} value={game.value}>
                  {game.label}
                </option>
              ))}
            </select>
          </div>

          {/* Format Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              <Users className="w-4 h-4 inline mr-1" />
              Format
            </label>
            <select
              value={filters.format || ''}
              onChange={(e) => updateFilter('format', e.target.value)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
            >
              <option value="">All Formats</option>
              {formatOptions.map((format) => (
                <option key={format.value} value={format.value}>
                  {format.label}
                </option>
              ))}
            </select>
          </div>

          {/* Prize Pool Range */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              <DollarSign className="w-4 h-4 inline mr-1" />
              Prize Pool Range
            </label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="number"
                placeholder="Min"
                value={filters.minPot || ''}
                onChange={(e) => updateFilter('minPot', e.target.value ? parseInt(e.target.value) : undefined)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
              <input
                type="number"
                placeholder="Max"
                value={filters.maxPot || ''}
                onChange={(e) => updateFilter('maxPot', e.target.value ? parseInt(e.target.value) : undefined)}
                className="bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none"
              />
            </div>
          </div>

          {/* Sort By */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">
              <Clock className="w-4 h-4 inline mr-1" />
              Sort By
            </label>
            <select
              value={filters.sortBy || 'newest'}
              onChange={(e) => updateFilter('sortBy', e.target.value as any)}
              className="w-full bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white focus:border-blue-500 focus:outline-none"
            >
              {sortOptions.map((sort) => (
                <option key={sort.value} value={sort.value}>
                  {sort.label}
                </option>
              ))}
            </select>
          </div>

          {/* All Status Options */}
          <div>
            <label className="block text-sm font-medium text-gray-400 mb-2">Status</label>
            <div className="grid grid-cols-2 gap-2">
              {statusOptions.map((status) => (
                <TouchButton
                  key={status.value}
                  onClick={() => updateFilter('status', filters.status === status.value ? '' : status.value)}
                  className={`px-3 py-2 rounded-lg text-sm transition-colors ${
                    filters.status === status.value
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  }`}
                >
                  {status.label}
                </TouchButton>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
