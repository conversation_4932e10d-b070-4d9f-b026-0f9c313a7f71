import React, { useState, useEffect } from 'react'
import {
  ShoppingCart,
  TrendingUp,
  Eye,
  DollarSign,
  Activity
} from 'lucide-react'
import { marketplaceService } from '../../services/marketplace'

interface MarketplaceStats {
  totalListings: number
  activeListings: number
  totalSales: number
  averagePrice: number
  topGames: {
    game: string
    count: number
  }[]
  recentActivity: {
    type: 'listing' | 'sale' | 'view'
    title: string
    time: string
  }[]
}

export default function MarketplaceStats() {
  const [stats, setStats] = useState<MarketplaceStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadStats()
  }, [])

  const loadStats = async () => {
    setLoading(true)
    try {
      // Get real live stats from database
      const { data: listings } = await marketplaceService.getListings()
      const { data: orders } = await marketplaceService.getOrders?.() || { data: [] }

      if (listings) {
        const activeListings = listings.filter(l => l.status === 'active').length
        const totalListings = listings.length
        const completedOrders = orders?.filter(o => o.status === 'completed') || []
        const totalSales = completedOrders.length

        // Calculate average price from active listings
        const activePrices = listings
          .filter(l => l.status === 'active')
          .map(l => l.price_diamonds)
        const averagePrice = activePrices.length > 0
          ? Math.round(activePrices.reduce((a, b) => a + b, 0) / activePrices.length)
          : 0

        const realStats: MarketplaceStats = {
          totalListings,
          activeListings,
          totalSales,
          averagePrice,
          topGames: [],
          recentActivity: []
        }
        setStats(realStats)
      } else {
        // If no data, show zeros instead of fake numbers
        setStats({
          totalListings: 0,
          activeListings: 0,
          totalSales: 0,
          averagePrice: 0,
          topGames: [],
          recentActivity: []
        })
      }
    } catch (error) {
      console.error('Error loading marketplace stats:', error)
      // Show zeros on error instead of fake data
      setStats({
        totalListings: 0,
        activeListings: 0,
        totalSales: 0,
        averagePrice: 0,
        topGames: [],
        recentActivity: []
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!stats) return null

  const StatCard = ({ 
    icon, 
    label, 
    value, 
    color 
  }: { 
    icon: React.ReactNode
    label: string
    value: string | number
    color: string
  }) => (
    <div className="flex items-center space-x-3">
      <div className={`${color} p-2 rounded-lg`}>
        {icon}
      </div>
      <div>
        <p className="text-sm text-gray-600">{label}</p>
        <p className="text-lg font-semibold text-gray-900">{value}</p>
      </div>
    </div>
  )

  return (
    <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6 flex items-center space-x-2">
        <Activity className="w-5 h-5 text-blue-600" />
        <span>Marketplace Overview</span>
      </h2>

      {/* Key Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
        <StatCard
          icon={<ShoppingCart className="w-5 h-5 text-white" />}
          label="Total Listings"
          value={stats.totalListings.toLocaleString()}
          color="bg-blue-500"
        />
        <StatCard
          icon={<Eye className="w-5 h-5 text-white" />}
          label="Active Listings"
          value={stats.activeListings.toLocaleString()}
          color="bg-green-500"
        />
        <StatCard
          icon={<TrendingUp className="w-5 h-5 text-white" />}
          label="Total Sales"
          value={stats.totalSales.toLocaleString()}
          color="bg-purple-500"
        />
        <StatCard
          icon={<DollarSign className="w-5 h-5 text-white" />}
          label="Avg Price"
          value={`${stats.averagePrice.toLocaleString()} 💎`}
          color="bg-amber-500"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Games */}
        <div>
          <h3 className="text-sm font-semibold text-gray-900 mb-4">Popular Games</h3>
          <div className="space-y-3">
            {stats.topGames.map((game, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-2 h-2 rounded-full ${
                    index === 0 ? 'bg-blue-500' :
                    index === 1 ? 'bg-green-500' :
                    index === 2 ? 'bg-purple-500' : 'bg-gray-400'
                  }`}></div>
                  <span className="text-sm text-gray-700">{game.game}</span>
                </div>
                <span className="text-sm font-medium text-gray-900">{game.count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <h3 className="text-sm font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-3">
            {stats.recentActivity.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className={`w-2 h-2 rounded-full mt-2 ${
                  activity.type === 'sale' ? 'bg-green-500' :
                  activity.type === 'listing' ? 'bg-blue-500' : 'bg-gray-400'
                }`}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm text-gray-700 truncate">{activity.title}</p>
                  <p className="text-xs text-gray-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
