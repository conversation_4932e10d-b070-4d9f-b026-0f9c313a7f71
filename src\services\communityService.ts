import { supabase } from './supabaseClient'

export const communityService = {
  // Get global chat messages
  async getGlobalMessages(limit = 50) {
    try {
      const { data, error } = await supabase
        .from('global_chat')
        .select(`
          *,
          user:users(username, first_name, last_name)
        `)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error
      return { data: data?.reverse(), error: null } // Reverse to show oldest first
    } catch (error) {
      return { data: null, error }
    }
  },

  // Send global chat message
  async sendGlobalMessage(userId: string, message: string) {
    try {
      const { data, error } = await supabase
        .from('global_chat')
        .insert([{
          user_id: userId,
          message: message,
          message_type: 'text'
        }])
        .select(`
          *,
          user:users(username, first_name, last_name)
        `)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get community groups
  async getCommunityGroups(limit = 20) {
    try {
      const { data, error } = await supabase
        .from('community_groups')
        .select(`
          *,
          owner:users!owner_id(username),
          members:group_members(count)
        `)
        .eq('is_private', false)
        .order('rank', { ascending: true })
        .limit(limit)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create community group
  async createGroup(userId: string, groupData: any) {
    try {
      const { data, error } = await supabase
        .from('community_groups')
        .insert([{
          name: groupData.name,
          tag: groupData.tag,
          description: groupData.description,
          tagline: groupData.tagline,
          owner_id: userId,
          is_private: groupData.isPrivate || false,
          max_members: groupData.maxMembers || 50,
          current_members: 1,
          level: 1,
          win_rate: 0,
          requirements: groupData.requirements || {},
          categories: groupData.categories || [],
          perks: groupData.perks || {}
        }])
        .select()
        .single()

      if (error) throw error

      // Add creator as group member
      await supabase
        .from('group_members')
        .insert([{
          group_id: data.id,
          user_id: userId,
          role: 'leader'
        }])

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
