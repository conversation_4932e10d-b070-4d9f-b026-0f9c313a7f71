import React, { useState, useEffect } from 'react'
import { 
  TrendingUp, Trophy, Target, Clock, Users, Star, 
  Zap, Calendar, Award, ArrowUp, ArrowDown, Eye 
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { analyticsService, type MatchOpportunity } from '../../services/analytics'
import { GamingMatchCard, StatChip, ProgressBar, DiamondCounter } from '../ui/GamingComponents'

interface DashboardInsight {
  type: 'performance' | 'opportunity' | 'achievement' | 'recommendation'
  title: string
  description: string
  action?: string
  actionUrl?: string
  priority: 'high' | 'medium' | 'low'
  icon: React.ReactNode
}

interface PerformanceMetrics {
  totalMatches: number
  winRate: number
  currentStreak: number
  bestStreak: number
  totalEarnings: number
  averageMatchDuration: number
  favoriteGame: string
  skillTrend: 'up' | 'down' | 'stable'
}

export default function PersonalizedDashboard() {
  const { user } = useAuth()
  const [opportunities, setOpportunities] = useState<MatchOpportunity[]>([])
  const [insights, setInsights] = useState<DashboardInsight[]>([])
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.id) {
      loadDashboardData()
    }
  }, [user?.id])

  const loadDashboardData = async () => {
    if (!user?.id) return

    setLoading(true)
    try {
      // Load match opportunities
      const matchOpportunities = await analyticsService.findMatchOpportunities(user.id, 3)
      setOpportunities(matchOpportunities)

      // Load performance metrics
      const performanceData = await loadPerformanceMetrics()
      setMetrics(performanceData)

      // Generate insights
      const dashboardInsights = await generateInsights(performanceData, matchOpportunities)
      setInsights(dashboardInsights)

    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadPerformanceMetrics = async (): Promise<PerformanceMetrics> => {
    // This would typically aggregate data from multiple sources
    // For now, we'll use sample data with some real calculations
    
    const games = ['Mobile Legends', 'Valorant', 'Call of Duty']
    let totalMatches = 0
    let totalWins = 0
    let totalEarnings = 0
    let bestStreak = 0
    let currentStreak = 0
    let favoriteGame = 'Mobile Legends'
    let maxMatches = 0

    for (const game of games) {
      const stats = await analyticsService.getUserGameStats(user!.id, game)
      if (stats) {
        totalMatches += stats.matches_played
        totalWins += stats.wins
        totalEarnings += stats.total_earnings
        bestStreak = Math.max(bestStreak, stats.best_streak)
        
        if (stats.matches_played > maxMatches) {
          maxMatches = stats.matches_played
          favoriteGame = game
          currentStreak = stats.current_streak
        }
      }
    }

    const winRate = totalMatches > 0 ? (totalWins / totalMatches) * 100 : 0

    return {
      totalMatches,
      winRate,
      currentStreak,
      bestStreak,
      totalEarnings,
      averageMatchDuration: 25, // Would calculate from actual data
      favoriteGame,
      skillTrend: winRate > 60 ? 'up' : winRate < 40 ? 'down' : 'stable'
    }
  }

  const generateInsights = async (
    metrics: PerformanceMetrics, 
    opportunities: MatchOpportunity[]
  ): Promise<DashboardInsight[]> => {
    const insights: DashboardInsight[] = []

    // Performance insights
    if (metrics.winRate > 70) {
      insights.push({
        type: 'performance',
        title: 'Excellent Performance!',
        description: `You're on fire with a ${metrics.winRate.toFixed(1)}% win rate. Consider higher stakes matches.`,
        action: 'View High Stakes Matches',
        priority: 'high',
        icon: <Trophy className="w-5 h-5 text-yellow-500" />
      })
    } else if (metrics.winRate < 40) {
      insights.push({
        type: 'recommendation',
        title: 'Practice Recommended',
        description: `Your win rate is ${metrics.winRate.toFixed(1)}%. Try lower stakes matches to improve.`,
        action: 'Find Practice Matches',
        priority: 'high',
        icon: <Target className="w-5 h-5 text-blue-500" />
      })
    }

    // Streak insights
    if (metrics.currentStreak >= 3) {
      insights.push({
        type: 'opportunity',
        title: `${metrics.currentStreak} Win Streak!`,
        description: 'You\'re hot! This might be a good time for a high-value match.',
        action: 'Find Premium Matches',
        priority: 'high',
        icon: <Zap className="w-5 h-5 text-orange-500" />
      })
    }

    // Time-based insights
    const hour = new Date().getHours()
    if (hour >= 18 && hour <= 22) {
      insights.push({
        type: 'opportunity',
        title: 'Peak Gaming Hours',
        description: 'More players are online now. Great time for quick matches!',
        action: 'Quick Match',
        priority: 'medium',
        icon: <Clock className="w-5 h-5 text-green-500" />
      })
    }

    // Opportunity insights
    if (opportunities.length > 0) {
      const bestOpp = opportunities[0]
      insights.push({
        type: 'opportunity',
        title: 'Perfect Match Available',
        description: `${bestOpp.game} ${bestOpp.format} with ${(bestOpp.estimated_win_rate * 100).toFixed(0)}% win probability`,
        action: 'View Match',
        priority: 'high',
        icon: <Star className="w-5 h-5 text-purple-500" />
      })
    }

    return insights.slice(0, 4) // Limit to 4 insights
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="w-4 h-4 text-green-500" />
      case 'down': return <ArrowDown className="w-4 h-4 text-red-500" />
      default: return <div className="w-4 h-4" />
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    )
  }

  if (!metrics) {
    return (
      <div className="text-center py-12">
        <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Data Available</h3>
        <p className="text-gray-600">Play some matches to see your personalized insights!</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Your Gaming Dashboard</h2>
          <p className="text-gray-600">Personalized insights and opportunities</p>
        </div>
        <button
          onClick={loadDashboardData}
          className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
        >
          <Eye className="w-5 h-5" />
        </button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <GamingMatchCard variant="glass" className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Win Rate</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.winRate.toFixed(1)}%</p>
            </div>
            <div className="flex items-center space-x-1">
              {getTrendIcon(metrics.skillTrend)}
              <Trophy className="w-8 h-8 text-yellow-500" />
            </div>
          </div>
        </GamingMatchCard>

        <GamingMatchCard variant="glass" className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Current Streak</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.currentStreak}</p>
            </div>
            <Zap className="w-8 h-8 text-orange-500" />
          </div>
        </GamingMatchCard>

        <GamingMatchCard variant="glass" className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Earnings</p>
              <DiamondCounter amount={metrics.totalEarnings} size="large" />
            </div>
            <Award className="w-8 h-8 text-green-500" />
          </div>
        </GamingMatchCard>

        <GamingMatchCard variant="glass" className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Matches Played</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalMatches}</p>
            </div>
            <Users className="w-8 h-8 text-blue-500" />
          </div>
        </GamingMatchCard>
      </div>

      {/* Insights & Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Insights */}
        <GamingMatchCard variant="default" className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
            Today's Insights
          </h3>
          <div className="space-y-4">
            {insights.map((insight, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                <div className="flex-shrink-0 mt-1">
                  {insight.icon}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{insight.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{insight.description}</p>
                  {insight.action && (
                    <button className="text-sm text-blue-600 hover:text-blue-800 font-medium mt-2">
                      {insight.action} →
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </GamingMatchCard>

        {/* Match Opportunities */}
        <GamingMatchCard variant="default" className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Star className="w-5 h-5 mr-2 text-purple-600" />
            Perfect Matches for You
          </h3>
          <div className="space-y-4">
            {opportunities.length > 0 ? (
              opportunities.map((opportunity, index) => (
                <div key={index} className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900">
                      {opportunity.game} {opportunity.format}
                    </h4>
                    <StatChip
                      icon={<Trophy className="w-3 h-3" />}
                      value={`${(opportunity.estimated_win_rate * 100).toFixed(0)}%`}
                      color="green"
                    />
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>Entry: {opportunity.entry_fee} 💎</span>
                    <span>Risk/Reward: {opportunity.risk_reward_ratio.toFixed(1)}x</span>
                  </div>
                  <ProgressBar
                    current={opportunity.recommendation_score}
                    max={100}
                    label="Match Score"
                    color="blue"
                    showNumbers={false}
                  />
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Target className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                <p>No perfect matches found right now.</p>
                <p className="text-sm">Check back later for new opportunities!</p>
              </div>
            )}
          </div>
        </GamingMatchCard>
      </div>

      {/* Performance Chart Placeholder */}
      <GamingMatchCard variant="default" className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Calendar className="w-5 h-5 mr-2 text-green-600" />
          Performance Overview
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <p className="text-sm text-gray-600">Favorite Game</p>
            <p className="text-xl font-bold text-gray-900">{metrics.favoriteGame}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Best Streak</p>
            <p className="text-xl font-bold text-gray-900">{metrics.bestStreak} wins</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Avg. Match Duration</p>
            <p className="text-xl font-bold text-gray-900">{metrics.averageMatchDuration}m</p>
          </div>
        </div>
      </GamingMatchCard>
    </div>
  )
}
