// 🛒 MARKETPLACE PAGE
// Main marketplace with listings grid, filters, and post listing modal

import React, { useState, useEffect } from 'react'
import {
  ShoppingCart,
  Plus,
  Grid,
  List
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'
import { useNotifications } from '../../../contexts/NotificationContext'
import { marketplaceService, MarketplaceListing, MarketplaceCategory } from '../../../services/marketplace'
import ListingCard from '../../../components/marketplace/ListingCard'
import ListingFormModal from '../../../components/marketplace/ListingFormModal'
import FilterBar from '../../../components/marketplace/FilterBar'
import MarketplaceStats from '../../../components/marketplace/MarketplaceStats'

const MarketplacePage: React.FC = () => {
  const { } = useAuth() // TODO: Confirm user usage
  const { addNotification } = useNotifications()
  const [listings, setListings] = useState<MarketplaceListing[]>([])
  const [categories, setCategories] = useState<MarketplaceCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [showListingModal, setShowListingModal] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filters, setFilters] = useState({
    game: '',
    category: '',
    item_type: '',
    minPrice: '',
    maxPrice: '',
    search: '',
    sortBy: 'newest' as 'price_asc' | 'price_desc' | 'newest' | 'popular'
  })
  const [searchQuery] = useState('') // TODO: Confirm setSearchQuery usage

  // Load initial data
  useEffect(() => {
    loadMarketplaceData()
  }, [])

  // Load data when filters change
  useEffect(() => {
    loadListings()
  }, [filters])

  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setFilters(prev => ({ ...prev, search: searchQuery }))
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [searchQuery])

  const loadMarketplaceData = async () => {
    setIsLoading(true)
    try {
      // Load categories
      const { data: categoriesData } = await marketplaceService.getCategories()
      if (categoriesData && categoriesData.length > 0) {
        setCategories(categoriesData)
      } else {
        // Create default categories if none exist
        await createDefaultCategories()
      }

      // Load listings
      await loadListings()
    } catch (error) {
      console.error('Error loading marketplace data:', error)
      addNotification({
        type: 'error',
        title: 'Loading Failed',
        message: 'Failed to load marketplace data. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }



  const createDefaultCategories = async () => {
    try {
      const defaultCategories = [
        {
          name: 'Accounts',
          description: 'Gaming accounts for various games',
          icon: '👤',
          is_active: true,
          sort_order: 1
        },
        {
          name: 'Items',
          description: 'In-game items and collectibles',
          icon: '🎒',
          is_active: true,
          sort_order: 2
        },
        {
          name: 'Currency',
          description: 'Game currency and diamonds',
          icon: '💎',
          is_active: true,
          sort_order: 3
        },
        {
          name: 'Services',
          description: 'Boosting and coaching services',
          icon: '🚀',
          is_active: true,
          sort_order: 4
        }
      ]

      for (const category of defaultCategories) {
        await marketplaceService.createCategory(category)
      }

      // Reload categories
      const { data: categoriesData } = await marketplaceService.getCategories()
      if (categoriesData) {
        setCategories(categoriesData)
      }
    } catch (error) {
      console.error('Error creating default categories:', error)
    }
  }

  const loadListings = async () => {
    try {
      const filterParams = {
        game: filters.game || undefined,
        category: filters.category || undefined,
        minPrice: filters.minPrice ? parseInt(filters.minPrice) : undefined,
        maxPrice: filters.maxPrice ? parseInt(filters.maxPrice) : undefined,
        search: filters.search || undefined,
        sortBy: filters.sortBy,
        limit: 50
      }

      const { data, error } = await marketplaceService.getListings(filterParams)
      
      if (error) {
        console.error('Error loading listings:', error)
        return
      }

      setListings(data || [])
    } catch (error) {
      console.error('Error loading listings:', error)
    }
  }

  // Real-time subscription for new listings
  useEffect(() => {
    const subscription = marketplaceService.subscribeToListings((payload) => {
      if (payload.eventType === 'INSERT') {
        setListings(prev => [payload.new, ...prev])
      }
    })

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  const handleListingCreated = (newListing: MarketplaceListing) => {
    setListings(prev => [newListing, ...prev])
    setShowListingModal(false)
    addNotification({
      type: 'success',
      title: 'Listing Created!',
      message: `Your listing "${newListing.title}" has been posted successfully.`
    })
  }

  // const gameIcons = { // TODO: Confirm usage
  //   'Mobile Legends': Gamepad2,
  //   'Dota 2': Shield,
  //   'CS:GO': Zap,
  //   'Wild Rift': Crown,
  //   'Valorant': Star,
  //   'Honor of Kings': Crown
  // }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-7xl mx-auto">
          {/* Header Skeleton */}
          <div className="mb-8">
            <div className="h-8 bg-gray-200 rounded w-64 mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 animate-pulse"></div>
          </div>

          {/* Filter Bar Skeleton */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div className="flex flex-wrap gap-4">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-10 bg-gray-200 rounded w-32 animate-pulse"></div>
              ))}
            </div>
          </div>

          {/* Listings Grid Skeleton */}
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
              <div key={i} className="bg-white rounded-xl shadow-lg p-4">
                <div className="h-48 bg-gray-200 rounded-lg mb-4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Compact Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">🛒 Marketplace</h1>
            </div>
            <button
              onClick={() => setShowListingModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Post</span>
            </button>
          </div>

          {/* Compact Statistics */}
          <MarketplaceStats />
        </div>

        {/* Filter Bar */}
        <FilterBar
          filters={filters}
          categories={categories}
          onFilterChange={handleFilterChange}
        />

        {/* View Mode Toggle */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {listings.length} listing{listings.length !== 1 ? 's' : ''} found
            </span>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
              }`}
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-600 hover:bg-gray-300'
              }`}
            >
              <List className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Loading marketplace...</h3>
            <p className="text-gray-600">Please wait while we fetch the latest listings</p>
          </div>
        )}

        {/* Listings Grid */}
        {!isLoading && listings.length === 0 ? (
          <div className="text-center py-12">
            <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No listings found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your filters or be the first to post a listing!</p>
            <button
              onClick={() => setShowListingModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              Post First Listing
            </button>
          </div>
        ) : !isLoading ? (
          <div className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {listings.map((listing) => (
              <ListingCard
                key={listing.id}
                listing={listing}
                viewMode={viewMode}
              />
            ))}
          </div>
        ) : null}

        {/* Post Listing Modal */}
        <ListingFormModal
          isOpen={showListingModal}
          onClose={() => setShowListingModal(false)}
          onListingCreated={handleListingCreated}
          categories={categories}
        />
      </div>
    </div>
  )
}

export default MarketplacePage
