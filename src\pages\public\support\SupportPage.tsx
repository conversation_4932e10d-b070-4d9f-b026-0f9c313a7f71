import { Link } from "react-router-dom"

export default function SupportPage() {



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold">🏆</span>
              </div>
              <span className="text-xl font-bold text-gray-900">Gambets</span>
            </Link>
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-gray-700 hover:text-blue-600 font-medium">Home</Link>
              <Link to="/browse" className="text-gray-700 hover:text-blue-600 font-medium">Marketplace</Link>
              <Link to="/leaderboards" className="text-gray-700 hover:text-blue-600 font-medium">Leaderboards</Link>
              <Link to="/rules" className="text-gray-700 hover:text-blue-600 font-medium">Rules</Link>
              <Link to="/support" className="text-blue-600 font-medium">Support</Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <button className="text-gray-700 hover:text-blue-600 font-medium">Login</button>
              </Link>
              <Link to="/signup">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                  Sign Up
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-16">
        <div className="container mx-auto px-4 lg:px-6 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">🆘 Support Center</h1>
          <p className="text-xl text-blue-100 max-w-3xl mx-auto">
            Need help? We're here for you 24/7. Get quick answers or contact our support team.
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 lg:px-6 py-12">
        <div className="max-w-6xl mx-auto">

          {/* Quick Help */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">💬</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Live Chat</h3>
              <p className="text-gray-600 mb-6">Get instant help from our support team</p>
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold w-full">
                Start Chat
              </button>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">📧</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Email Support</h3>
              <p className="text-gray-600 mb-6">Send us a detailed message</p>
              <a href="mailto:<EMAIL>" className="bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold w-full inline-block">
                Email Us
              </a>
            </div>

            <div className="bg-white rounded-2xl shadow-lg p-8 text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-6">
                <span className="text-3xl">📞</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Phone Support</h3>
              <p className="text-gray-600 mb-6">Call us directly for urgent issues</p>
              <a href="tel:+************" className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg font-semibold w-full inline-block">
                Call Now
              </a>
            </div>
          </div>

          {/* FAQ */}
          <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">❓ Frequently Asked Questions</h2>
            <div className="space-y-6">
              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">How do I deposit diamonds?</h3>
                <p className="text-gray-600">You can deposit diamonds using GCash, Maya, or bank transfer. Go to your wallet and select "Buy Diamonds" to see all available payment methods.</p>
              </div>

              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">How long does it take to receive winnings?</h3>
                <p className="text-gray-600">Winnings are typically processed within 24 hours after match verification. During peak times, it may take up to 48 hours.</p>
              </div>

              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">What if my opponent doesn't show up?</h3>
                <p className="text-gray-600">If your opponent doesn't join within 10 minutes of the scheduled match time, you'll automatically win and receive the full pot.</p>
              </div>

              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">How do I report a cheater?</h3>
                <p className="text-gray-600">Use the "Report Player" button in your match history or contact support with evidence (screenshots/videos). We take cheating very seriously.</p>
              </div>

              <div className="border-b border-gray-200 pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-3">Can I get a refund?</h3>
                <p className="text-gray-600">Refunds are available for technical issues, proven cheating, or platform errors. Normal match losses are not eligible for refunds.</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">What games are supported?</h3>
                <p className="text-gray-600">Currently we support Mobile Legends, Valorant, and Call of Duty. More games like Wild Rift, Dota 2, and CS:GO are coming soon!</p>
              </div>
            </div>
          </div>

          {/* Contact Info */}
          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-blue-50 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-blue-900 mb-6">📍 Contact Information</h3>
              <div className="space-y-4 text-blue-800">
                <div className="flex items-center">
                  <span className="mr-3">📧</span>
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <span className="mr-3">📞</span>
                  <span>+63 ************</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-3">⏰</span>
                  <span>24/7 Support Available</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-3">⚡</span>
                  <span>Average Response: Under 1 hour</span>
                </div>
              </div>
            </div>

            <div className="bg-green-50 rounded-2xl p-8">
              <h3 className="text-xl font-bold text-green-900 mb-6">🚀 Quick Links</h3>
              <div className="space-y-3">
                <Link to="/rules" className="block text-green-700 hover:text-green-900 font-medium">
                  📋 Game Rules & Guidelines
                </Link>
                <Link to="/terms" className="block text-green-700 hover:text-green-900 font-medium">
                  📄 Terms of Service
                </Link>
                <Link to="/privacy" className="block text-green-700 hover:text-green-900 font-medium">
                  🔒 Privacy Policy
                </Link>
                <Link to="/apply-referee" className="block text-green-700 hover:text-green-900 font-medium">
                  👨‍⚖️ Become a Referee
                </Link>
                <Link to="/responsible-gaming" className="block text-green-700 hover:text-green-900 font-medium">
                  🎯 Responsible Gaming
                </Link>
              </div>
            </div>
          </div>

          {/* Emergency Contact */}
          <div className="bg-red-50 rounded-2xl border border-red-200 p-8 mt-8 text-center">
            <h3 className="text-xl font-bold text-red-900 mb-4">🚨 Emergency Issues</h3>
            <p className="text-red-700 mb-6">
              For urgent account security issues, suspected fraud, or payment problems, contact us immediately:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a href="tel:+************" className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold">
                📞 Emergency Hotline
              </a>
              <a href="mailto:<EMAIL>" className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-semibold">
                📧 Emergency Email
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
