# 📄 Pages Directory Documentation

## 🎯 Overview

This directory contains all page components for the Gambets platform, organized by access level and functionality.

## 📁 Folder Structure

```
pages/
├── 📁 public/     # 🌐 PUBLIC PAGES - No authentication required
├── 📁 private/    # 🔒 PRIVATE PAGES - Authentication required  
└── 📁 admin/      # 👑 ADMIN PAGES - Admin role required
```

## 🌐 PUBLIC PAGES (public/)

**Access Level:** Open to everyone - No login required

### Purpose
- User acquisition and conversion
- Platform information and trust building
- Authentication and registration flows
- Public information and support

### Pages

| File | Route | Description | Features |
|------|-------|-------------|----------|
| **LandingPage.tsx** | `/` | Main homepage | Hero section, features, games, CTA |
| **LeaderboardsPage.tsx** | `/leaderboards` | Public rankings | Top players, statistics, game rankings |
| **LoginPage.tsx** | `/login` | User login | Email/password, social login, security |
| **SignUpPage.tsx** | `/signup` | User registration | Account creation, MLBB ID, terms |
| **RulesPage.tsx** | `/rules` | Game rules | General rules, game-specific guidelines |
| **SupportPage.tsx** | `/support` | Help center | FAQ, contact info, live chat |
| **AuthPage.tsx** | `/auth` | Alternative auth | Legacy authentication page |

### Design Principles
- **No authentication barriers** - Accessible to all visitors
- **Conversion focused** - Encourage user registration
- **Trust building** - Professional design and security messaging
- **SEO optimized** - Public content for search engines

## 🔒 PRIVATE PAGES (private/)

**Access Level:** Authenticated users only - Login required

### Purpose
- Core platform functionality
- User account management
- Gaming and match participation
- Community features

### Pages

| File | Route | Description | Features |
|------|-------|-------------|----------|
| **DashboardPage.tsx** | `/dashboard` | Main user hub | Match overview, quick actions, stats |
| **ProfilePage.tsx** | `/profile` | User profile | Edit info, gaming IDs, preferences |
| **WalletPage.tsx** | `/wallet` | Diamond management | Balance, transactions, deposit/withdraw |
| **MatchesPage.tsx** | `/matches` | Match browser | Browse, filter, join matches |
| **MatchDetailPage.tsx** | `/match/:id` | Match details | Participants, rules, join/leave |
| **CommunityPage.tsx** | `/community` | Social features | Chat, guilds, team matching |
| **RefereeDashboardPage.tsx** | `/referee-dashboard` | Referee panel | Match reviews, decisions, earnings |
| **RefereeApplicationPage.tsx** | `/apply-referee` | Referee signup | Application form, requirements |

### Design Principles
- **Authentication required** - Protected by ProtectedRoute component
- **User-centric** - Personalized content and functionality
- **Interactive** - Real-time updates and dynamic content
- **Secure** - Sensitive operations with proper validation

## 👑 ADMIN PAGES (admin/)

**Access Level:** Admin role required - Highest privilege level

### Purpose
- Platform administration
- User and content moderation
- System management
- Referee approval process

### Pages

| File | Route | Description | Features |
|------|-------|-------------|----------|
| **AdminRefereeReviewPage.tsx** | `/admin/referee-review` | Referee approval | Review applications, approve/reject |

### Design Principles
- **Role-based access** - Admin role verification required
- **Administrative tools** - Bulk operations and management features
- **Audit trails** - Logging of administrative actions
- **Security focused** - Extra validation and confirmation steps

## 🛡️ Route Protection

### Public Routes
```typescript
// No protection needed - accessible to all
<Route path="/" element={<LandingPage />} />
<Route path="/login" element={<LoginPage />} />
<Route path="/signup" element={<SignUpPage />} />
```

### Private Routes
```typescript
// Protected by authentication
<Route path="/dashboard" element={
  <ProtectedRoute>
    <DashboardPage />
  </ProtectedRoute>
} />
```

### Admin Routes
```typescript
// Protected by admin role
<Route path="/admin/*" element={
  <ProtectedRoute requireAdmin={true}>
    <AdminRoutes />
  </ProtectedRoute>
} />
```

## 🔄 Navigation Flow

### New User Journey
1. **LandingPage** → Learn about platform
2. **SignUpPage** → Create account
3. **DashboardPage** → Access private features

### Returning User Journey
1. **LoginPage** → Authenticate
2. **DashboardPage** → Main hub
3. **MatchesPage** → Find games
4. **WalletPage** → Manage diamonds

### Admin Journey
1. **LoginPage** → Admin login
2. **DashboardPage** → User dashboard
3. **AdminRefereeReviewPage** → Admin tasks

## 📱 Responsive Design

All pages are designed to work seamlessly across:
- **Desktop** (1200px+) - Full feature set
- **Tablet** (768px-1199px) - Optimized layout
- **Mobile** (320px-767px) - Touch-friendly interface

## 🎨 Design System

### Common Elements
- **Navigation** - Consistent header across all pages
- **Footer** - Standard footer for public pages
- **Loading States** - Skeleton screens and spinners
- **Error Handling** - User-friendly error messages

### Color Coding
- **Blue/Purple Gradients** - Primary brand colors
- **Green** - Success states and positive actions
- **Red** - Errors and warnings
- **Gray** - Neutral content and backgrounds

## 🔧 Development Guidelines

### Adding New Pages

1. **Determine Access Level**
   - Public: No authentication needed
   - Private: User authentication required
   - Admin: Admin role required

2. **Choose Appropriate Folder**
   - Place in `public/`, `private/`, or `admin/`

3. **Follow Naming Convention**
   - Use PascalCase with "Page" suffix
   - Example: `NewFeaturePage.tsx`

4. **Implement Route Protection**
   - Public: Direct routing
   - Private: Wrap with `<ProtectedRoute>`
   - Admin: Use `<ProtectedRoute requireAdmin={true}>`

5. **Update App.tsx**
   - Add route configuration
   - Import from correct folder

### Code Standards
- Use TypeScript for all components
- Implement proper error boundaries
- Add loading states for async operations
- Follow accessibility guidelines
- Include responsive design considerations

## 🚀 Performance Optimization

- **Code Splitting** - Lazy load pages with React.lazy()
- **Image Optimization** - Use appropriate formats and sizes
- **Bundle Analysis** - Monitor page bundle sizes
- **Caching** - Implement proper caching strategies

## 🧪 Testing Strategy

- **Unit Tests** - Test individual page components
- **Integration Tests** - Test page interactions
- **E2E Tests** - Test complete user flows
- **Accessibility Tests** - Ensure WCAG compliance
