import React from 'react'
import { Gem, <PERSON>, Clock, Trophy, Zap } from 'lucide-react'

// Enhanced Status Badge with animations
interface StatusBadgeProps {
  status: 'open' | 'full' | 'ongoing' | 'completed' | 'cancelled'
  size?: 'small' | 'medium' | 'large'
}

export function StatusBadge({ status, size = 'medium' }: StatusBadgeProps) {
  const sizeClasses = {
    small: 'px-2 py-1 text-xs',
    medium: 'px-3 py-1.5 text-sm',
    large: 'px-4 py-2 text-base'
  }

  const statusClasses = {
    open: 'status-open text-white',
    full: 'status-full text-white',
    ongoing: 'status-ongoing text-white',
    completed: 'status-completed text-white',
    cancelled: 'status-cancelled text-white'
  }

  return (
    <span className={`
      inline-flex items-center font-medium rounded-full
      ${sizeClasses[size]}
      ${statusClasses[status]}
    `}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  )
}

// Enhanced Diamond Counter with shine effect
interface DiamondCounterProps {
  amount: number
  size?: 'small' | 'medium' | 'large'
  showIcon?: boolean
}

export function DiamondCounter({ amount, size = 'medium', showIcon = true }: DiamondCounterProps) {
  const sizeClasses = {
    small: 'text-sm',
    medium: 'text-base',
    large: 'text-xl'
  }

  const iconSizes = {
    small: 'w-3 h-3',
    medium: 'w-4 h-4',
    large: 'w-5 h-5'
  }

  return (
    <div className={`flex items-center space-x-1 ${sizeClasses[size]}`}>
      {showIcon && <Gem className={`${iconSizes[size]} text-yellow-500`} />}
      <span className="diamond-count font-bold">
        {amount.toLocaleString()}
      </span>
    </div>
  )
}

// Enhanced Progress Bar with glow
interface ProgressBarProps {
  current: number
  max: number
  label?: string
  showNumbers?: boolean
  color?: 'blue' | 'green' | 'orange' | 'red'
}

export function ProgressBar({ 
  current, 
  max, 
  label, 
  showNumbers = true, 
  color = 'blue' 
}: ProgressBarProps) {
  const percentage = Math.min(100, (current / max) * 100)
  
  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    orange: 'from-orange-500 to-orange-600',
    red: 'from-red-500 to-red-600'
  }

  return (
    <div className="space-y-1">
      {(label || showNumbers) && (
        <div className="flex justify-between text-xs text-gray-600">
          {label && <span>{label}</span>}
          {showNumbers && <span>{current}/{max}</span>}
        </div>
      )}
      <div className="progress-bar h-2">
        <div 
          className={`progress-fill bg-gradient-to-r ${colorClasses[color]}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  )
}

// Gaming Button with hover effects
interface GamingButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
  size?: 'small' | 'medium' | 'large'
  disabled?: boolean
  loading?: boolean
  className?: string
}

export function GamingButton({
  children,
  onClick,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  className = ''
}: GamingButtonProps) {
  const baseClasses = 'gaming-button rounded-lg font-semibold transition-all duration-300 relative overflow-hidden'
  
  const variantClasses = {
    primary: 'gaming-gradient-primary',
    secondary: 'bg-gray-600 hover:bg-gray-700',
    success: 'gaming-gradient-success',
    warning: 'gaming-gradient-warning',
    danger: 'gaming-gradient-danger'
  }

  const sizeClasses = {
    small: 'px-3 py-2 text-sm',
    medium: 'px-4 py-3 text-base',
    large: 'px-6 py-4 text-lg'
  }

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
    >
      {loading ? (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
          Loading...
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// Enhanced Match Card with glass morphism
interface GamingMatchCardProps {
  children: React.ReactNode
  onClick?: () => void
  className?: string
  variant?: 'default' | 'glass' | 'dark'
}

export function GamingMatchCard({ 
  children, 
  onClick, 
  className = '', 
  variant = 'default' 
}: GamingMatchCardProps) {
  const variantClasses = {
    default: 'match-card',
    glass: 'glass-card',
    dark: 'glass-card-dark'
  }

  return (
    <div
      onClick={onClick}
      className={`
        ${variantClasses[variant]}
        rounded-xl p-4 cursor-pointer
        ${onClick ? 'hover:scale-105' : ''}
        ${className}
      `}
    >
      {children}
    </div>
  )
}

// Stat Chip Component
interface StatChipProps {
  icon: React.ReactNode
  value: string | number
  label?: string
  color?: 'blue' | 'green' | 'orange' | 'purple' | 'gray'
}

export function StatChip({ icon, value, label, color = 'blue' }: StatChipProps) {
  const colorClasses = {
    blue: 'bg-blue-100 text-blue-700 border-blue-200',
    green: 'bg-green-100 text-green-700 border-green-200',
    orange: 'bg-orange-100 text-orange-700 border-orange-200',
    purple: 'bg-purple-100 text-purple-700 border-purple-200',
    gray: 'bg-gray-100 text-gray-700 border-gray-200'
  }

  return (
    <div className={`
      inline-flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium border
      ${colorClasses[color]}
    `}>
      {icon}
      <span>{value}</span>
      {label && <span className="text-xs opacity-75">({label})</span>}
    </div>
  )
}

// Floating Action Button
interface FloatingActionButtonProps {
  icon: React.ReactNode
  onClick: () => void
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  color?: 'blue' | 'green' | 'orange' | 'red'
}

export function FloatingActionButton({ 
  icon, 
  onClick, 
  position = 'bottom-right',
  color = 'blue'
}: FloatingActionButtonProps) {
  const positionClasses = {
    'bottom-right': 'bottom-6 right-6',
    'bottom-left': 'bottom-6 left-6',
    'top-right': 'top-6 right-6',
    'top-left': 'top-6 left-6'
  }

  const colorClasses = {
    blue: 'gaming-gradient-primary',
    green: 'gaming-gradient-success',
    orange: 'gaming-gradient-warning',
    red: 'gaming-gradient-danger'
  }

  return (
    <button
      onClick={onClick}
      className={`
        fixed ${positionClasses[position]} z-50
        w-14 h-14 rounded-full ${colorClasses[color]}
        flex items-center justify-center text-white
        shadow-lg hover:shadow-xl transform hover:scale-110
        transition-all duration-300 floating
      `}
    >
      {icon}
    </button>
  )
}

// Loading Skeleton with shimmer
interface LoadingSkeletonProps {
  width?: string
  height?: string
  className?: string
}

export function LoadingSkeleton({ 
  width = '100%', 
  height = '20px', 
  className = '' 
}: LoadingSkeletonProps) {
  return (
    <div
      className={`loading-shimmer rounded ${className}`}
      style={{ width, height }}
    />
  )
}

// Enhanced Notification Toast
interface NotificationToastProps {
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  onClose: () => void
}

export function NotificationToast({ type, title, message, onClose }: NotificationToastProps) {
  const typeClasses = {
    success: 'gaming-gradient-success',
    error: 'gaming-gradient-danger',
    warning: 'gaming-gradient-warning',
    info: 'gaming-gradient-primary'
  }

  const icons = {
    success: <Trophy className="w-5 h-5" />,
    error: <Zap className="w-5 h-5" />,
    warning: <Clock className="w-5 h-5" />,
    info: <Users className="w-5 h-5" />
  }

  return (
    <div className={`
      ${typeClasses[type]} text-white p-4 rounded-lg shadow-lg
      transform transition-all duration-300 notification-enter-active
    `}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {icons[type]}
        </div>
        <div className="flex-1">
          <h4 className="font-semibold text-sm">{title}</h4>
          <p className="text-sm opacity-90 mt-1">{message}</p>
        </div>
        <button
          onClick={onClose}
          className="flex-shrink-0 text-white hover:text-gray-200 transition-colors"
        >
          ×
        </button>
      </div>
    </div>
  )
}
