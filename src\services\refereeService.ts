import { supabase } from './supabaseClient'

export const refereeService = {
  // Submit referee application
  async submitApplication(userId: string, applicationData: any) {
    try {
      const { data, error } = await supabase
        .from('referee_applications')
        .insert([{
          user_id: userId,
          ...applicationData,
          status: 'pending'
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's referee application
  async getUserApplication(userId: string) {
    try {
      const { data, error } = await supabase
        .from('referee_applications')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get matches assigned to referee
  async getRefereeMatches(refereeId: string) {
    try {
      const { data, error } = await supabase
        .from('matches')
        .select('*')
        .eq('referee_id', refereeId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get all matches that need referee attention (for any referee to pick up)
  async getMatchesNeedingReferee() {
    try {
      const { data, error } = await supabase
        .from('matches')
        .select('*')
        .in('status', ['full', 'in_progress'])
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Submit match result and distribute diamonds (with conflict checking)
  async submitMatchResult(matchId: string, _refereeId: string, resultData: any) {
    try {
      // TODO: Import conflict service for validation when implemented
      // const { refereeConflictService } = await import('./refereeConflictService')

      // TODO: Check if referee has conflicts with this match
      // const conflictCheck = await refereeConflictService.checkRefereeConflict(refereeId, matchId)
      // if (conflictCheck.hasConflict) {
      //   // Log the conflict attempt and throw error
      // }

      // Get match details with participants
      const { data: match, error: matchError } = await supabase
        .from('matches')
        .select(`
          *,
          host:users!host_id(id, username, diamond_balance),
          participants:match_participants(
            user_id,
            user:users(id, username, diamond_balance)
          )
        `)
        .eq('id', matchId)
        .single()

      if (matchError) throw matchError
      if (!match) throw new Error('Match not found')

      // Determine winner based on result
      let winnerId: string | null = null
      let winnerTeam: 'host' | 'participants' | null = null

      if (resultData.winner === 'playerA') {
        winnerId = match.host_id
        winnerTeam = 'host'
      } else if (resultData.winner === 'playerB' && match.participants.length > 0) {
        // For 1v1, take first participant. For team matches, this logic would be more complex
        winnerId = match.participants[0]?.user_id
        winnerTeam = 'participants'
      }

      // Update match with result
      const { error: updateError } = await supabase
        .from('matches')
        .update({
          winner_id: winnerId,
          status: 'completed',
          referee_notes: resultData.notes,
          actual_end_time: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)

      if (updateError) throw updateError

      // Distribute diamonds if there's a winner (not a draw)
      if (winnerId && match.pot_amount > 0 && winnerTeam) {
        await this.distributeDiamonds(matchId, winnerId, match.pot_amount, winnerTeam)
      }

      // Create transaction records for all participants
      await this.createMatchTransactions(matchId, match, winnerId)

      return { data: { success: true, winnerId }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Distribute diamonds to winners
  async distributeDiamonds(matchId: string, winnerId: string, potAmount: number, _winnerTeam: 'host' | 'participants') {
    try {
      // Calculate platform fee (5% for referees and platform)
      const platformFee = Math.floor(potAmount * 0.05)
      const winnerAmount = potAmount - platformFee

      // Get winner's current balance
      const { data: winner, error: winnerError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', winnerId)
        .single()

      if (winnerError) throw winnerError

      // Update winner's balance
      await supabase
        .from('users')
        .update({
          diamond_balance: winner.diamond_balance + winnerAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', winnerId)

      // Update wins count separately
      await supabase.rpc('increment_user_wins', { user_id: winnerId })

      // Create transaction record for winner
      await supabase
        .from('transactions')
        .insert({
          user_id: winnerId,
          type: 'match_win',
          amount: winnerAmount,
          status: 'completed',
          description: `Match victory - ${winnerAmount} diamonds won`,
          match_id: matchId,
          created_at: new Date().toISOString()
        })

      return { success: true, winnerAmount, platformFee }
    } catch (error) {
      console.error('Error distributing diamonds:', error)
      throw error
    }
  },

  // Simplified match result submission for new referee dashboard
  async submitMatchResultSimple(matchId: string, winnerId: string, notes?: string) {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error('Not authenticated')

      // Map simple winner IDs to the expected format
      let resultData = { notes: notes || '' }

      if (winnerId === 'player1') {
        resultData = { ...resultData, winner: 'playerA' } as any
      } else if (winnerId === 'player2') {
        resultData = { ...resultData, winner: 'playerB' } as any
      } else if (winnerId === 'draw') {
        resultData = { ...resultData, winner: 'draw' } as any
      } else {
        // Direct winner ID provided
        resultData = { ...resultData, winner: winnerId } as any
      }

      // Use the existing comprehensive submitMatchResult function
      return await this.submitMatchResult(matchId, user.id, resultData)
    } catch (error) {
      console.error('Error submitting match result:', error)
      return { data: null, error }
    }
  },

  // Create transaction records for match completion
  async createMatchTransactions(matchId: string, match: any, winnerId: string | null) {
    try {
      const transactions = []

      // Transaction for host
      transactions.push({
        user_id: match.host_id,
        type: winnerId === match.host_id ? 'match_win' : 'match_loss',
        amount: winnerId === match.host_id ? match.pot_amount - Math.floor(match.pot_amount * 0.05) : -match.entry_fee,
        status: 'completed',
        description: winnerId === match.host_id ? 'Match victory' : 'Match entry fee',
        match_id: matchId,
        created_at: new Date().toISOString()
      })

      // Transactions for participants
      for (const participant of match.participants) {
        transactions.push({
          user_id: participant.user_id,
          type: winnerId === participant.user_id ? 'match_win' : 'match_loss',
          amount: winnerId === participant.user_id ? match.pot_amount - Math.floor(match.pot_amount * 0.05) : -match.entry_fee,
          status: 'completed',
          description: winnerId === participant.user_id ? 'Match victory' : 'Match entry fee',
          match_id: matchId,
          created_at: new Date().toISOString()
        })
      }

      // Insert all transactions
      await supabase
        .from('transactions')
        .insert(transactions)

      return { success: true }
    } catch (error) {
      console.error('Error creating match transactions:', error)
      throw error
    }
  }
}
