import React from 'react'

interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export default function ResponsiveContainer({ 
  children, 
  className = '',
  maxWidth = 'xl',
  padding = 'md'
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-7xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  const paddingClasses = {
    none: '',
    sm: 'px-2 sm:px-4',
    md: 'px-4 sm:px-6 lg:px-8',
    lg: 'px-6 sm:px-8 lg:px-12'
  }

  return (
    <div className={`mx-auto ${maxWidthClasses[maxWidth]} ${paddingClasses[padding]} ${className}`}>
      {children}
    </div>
  )
}

// Responsive Grid Component
export function ResponsiveGrid({
  children,
  cols = { sm: 1, md: 2, lg: 3 },
  gap = 'md',
  className = ''
}: {
  children: React.ReactNode
  cols?: { sm?: number; md?: number; lg?: number; xl?: number }
  gap?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const gapClasses = {
    sm: 'gap-3',
    md: 'gap-4 md:gap-6',
    lg: 'gap-6 md:gap-8'
  }

  const colClasses = `grid grid-cols-${cols.sm || 1} ${
    cols.md ? `md:grid-cols-${cols.md}` : ''
  } ${
    cols.lg ? `lg:grid-cols-${cols.lg}` : ''
  } ${
    cols.xl ? `xl:grid-cols-${cols.xl}` : ''
  }`

  return (
    <div className={`${colClasses} ${gapClasses[gap]} ${className}`}>
      {children}
    </div>
  )
}

// Mobile-First Card Component
export function ResponsiveCard({
  children,
  hover = true,
  padding = 'md',
  className = ''
}: {
  children: React.ReactNode
  hover?: boolean
  padding?: 'sm' | 'md' | 'lg'
  className?: string
}) {
  const paddingClasses = {
    sm: 'p-3 sm:p-4',
    md: 'p-4 sm:p-6',
    lg: 'p-6 sm:p-8'
  }

  const hoverClass = hover ? 'hover:shadow-md transition-shadow duration-200' : ''

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${paddingClasses[padding]} ${hoverClass} ${className}`}>
      {children}
    </div>
  )
}

// Responsive Stack Component
export function ResponsiveStack({
  children,
  direction = { sm: 'col', md: 'row' },
  spacing = 'md',
  align = 'start',
  justify = 'start',
  className = ''
}: {
  children: React.ReactNode
  direction?: { sm: 'row' | 'col'; md?: 'row' | 'col'; lg?: 'row' | 'col' }
  spacing?: 'sm' | 'md' | 'lg'
  align?: 'start' | 'center' | 'end' | 'stretch'
  justify?: 'start' | 'center' | 'end' | 'between' | 'around'
  className?: string
}) {
  const spacingClasses = {
    sm: direction.sm === 'row' ? 'space-x-2' : 'space-y-2',
    md: direction.sm === 'row' ? 'space-x-4' : 'space-y-4',
    lg: direction.sm === 'row' ? 'space-x-6' : 'space-y-6'
  }

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  }

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around'
  }

  const directionClasses = `flex flex-${direction.sm} ${
    direction.md ? `md:flex-${direction.md}` : ''
  } ${
    direction.lg ? `lg:flex-${direction.lg}` : ''
  }`

  return (
    <div className={`${directionClasses} ${spacingClasses[spacing]} ${alignClasses[align]} ${justifyClasses[justify]} ${className}`}>
      {children}
    </div>
  )
}

// Responsive Text Component
export function ResponsiveText({
  children,
  size = { sm: 'base', md: 'lg' },
  weight = 'normal',
  color = 'gray-900',
  align = 'left',
  className = ''
}: {
  children: React.ReactNode
  size?: { sm: string; md?: string; lg?: string }
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold'
  color?: string
  align?: 'left' | 'center' | 'right'
  className?: string
}) {
  const sizeClasses = `text-${size.sm} ${
    size.md ? `md:text-${size.md}` : ''
  } ${
    size.lg ? `lg:text-${size.lg}` : ''
  }`

  const weightClasses = {
    light: 'font-light',
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  }

  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }

  return (
    <div className={`${sizeClasses} ${weightClasses[weight]} text-${color} ${alignClasses[align]} ${className}`}>
      {children}
    </div>
  )
}

// Mobile-Optimized Button
export function ResponsiveButton({
  children,
  variant = 'primary',
  size = { sm: 'md', md: 'lg' },
  fullWidth = { sm: true, md: false },
  disabled = false,
  loading = false,
  onClick,
  className = ''
}: {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: { sm: string; md?: string }
  fullWidth?: { sm: boolean; md?: boolean }
  disabled?: boolean
  loading?: boolean
  onClick?: () => void
  className?: string
}) {
  const baseClasses = 'font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:bg-blue-300',
    secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 disabled:bg-gray-300',
    outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-blue-500 disabled:bg-gray-50 disabled:text-gray-400',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500 disabled:text-gray-400'
  }

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  }

  const responsiveSizeClasses = `${sizeClasses[size.sm as keyof typeof sizeClasses]} ${
    size.md ? `md:${sizeClasses[size.md as keyof typeof sizeClasses]}` : ''
  }`

  const widthClasses = `${fullWidth.sm ? 'w-full' : ''} ${
    fullWidth.md !== undefined ? (fullWidth.md ? 'md:w-full' : 'md:w-auto') : ''
  }`

  return (
    <button
      onClick={onClick}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses[variant]} ${responsiveSizeClasses} ${widthClasses} ${className}`}
    >
      {loading ? (
        <div className="flex items-center justify-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// Responsive Modal
export function ResponsiveModal({
  isOpen,
  onClose,
  title,
  children,
  size = 'md'
}: {
  isOpen: boolean
  onClose: () => void
  title: string
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg' | 'xl'
}) {
  if (!isOpen) return null

  const sizeClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl'
  }

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />
      
      {/* Modal - Mobile: Full screen bottom sheet, Desktop: Centered modal */}
      <div className="fixed inset-x-0 bottom-0 md:inset-0 md:flex md:items-center md:justify-center">
        <div className={`bg-white rounded-t-xl md:rounded-xl max-h-[90vh] md:max-h-[80vh] w-full md:${sizeClasses[size]} md:mx-4 overflow-hidden`}>
          {/* Header */}
          <div className="flex items-center justify-between p-4 md:p-6 border-b border-gray-200">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900">{title}</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(90vh-64px)] md:max-h-[calc(80vh-80px)] p-4 md:p-6">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}
