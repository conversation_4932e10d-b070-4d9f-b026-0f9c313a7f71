// Community Types for Gambets Social Hub

export interface User {
  id: string
  username: string
  avatar?: string
  mlRank?: string
  guild?: string
  level: number
  diamonds: number
  wins: number
  winRate: number
  isOnline: boolean
  lastActive?: string
  role?: 'admin' | 'moderator' | 'vip' | 'member'
}

export interface Message {
  id: string
  userId: string
  username: string
  avatar?: string
  message: string
  timestamp: string
  type: 'text' | 'system' | 'announcement'
  role?: 'admin' | 'moderator' | 'vip' | 'member'
  likes: number
  reactions: Reaction[]
  isEdited?: boolean
  replyTo?: string
}

export interface Reaction {
  emoji: string
  count: number
  users: string[]
}

export interface Group {
  id: string
  name: string
  tag: string
  description: string
  banner?: string
  logo?: string
  tagline?: string
  memberCount: number
  maxMembers: number
  isPrivate: boolean
  level: number
  winRate: number
  rank: number
  onlineMembers: number
  createdAt: string
  ownerId: string
  requirements: {
    minLevel: number
    minWinRate: number
    minDiamonds: number
  }
  categories: string[]
  perks: {
    diamondBonus: number
    xpBonus: number
    matchPriority: boolean
    customRoles: boolean
  }
}

export interface GroupMember {
  id: string
  userId: string
  username: string
  avatar?: string
  role: 'leader' | 'officer' | 'member'
  joinedAt: string
  isOnline: boolean
  level: number
  contribution: number
}

export interface GroupChat {
  id: string
  groupId: string
  messages: Message[]
}

export interface LeaderboardEntry {
  rank: number
  userId: string
  username: string
  avatar?: string
  value: number
  change: number
  guild?: string
  isOnline: boolean
}

export interface Announcement {
  id: string
  title: string
  content: string
  type: 'update' | 'event' | 'maintenance' | 'tournament'
  isActive: boolean
  createdAt: string
  expiresAt?: string
  icon?: string
}

export interface UserProfile {
  user: User
  stats: {
    totalMatches: number
    monthlyWins: number
    streak: number
    achievements: string[]
    recentMatches: any[]
  }
  social: {
    friends: string[]
    groups: string[]
    reputation: number
  }
}

// Component Props Types
export interface GlobalChatProps {
  messages: Message[]
  onSendMessage: (message: string) => void
  onLikeMessage: (messageId: string) => void
  onReactToMessage: (messageId: string, emoji: string) => void
  isLoading?: boolean
  onNewMessage?: (message: Message) => void
}

export interface GroupCardProps {
  group: Group
  onJoin: (groupId: string) => void
  onView: (groupId: string) => void
  isJoined: boolean
}

export interface GroupProfileModalProps {
  group: Group | null
  members: GroupMember[]
  isOpen: boolean
  onClose: () => void
  onJoin: () => void
  onLeave: () => void
  onInvite: () => void
  userRole?: 'leader' | 'officer' | 'member'
}

export interface UserPopoverProps {
  user: UserProfile | null
  isOpen: boolean
  onClose: () => void
  onAddFriend: () => void
  onMessage: () => void
  onReport: () => void
  position: { x: number; y: number }
}

export interface LeaderboardPanelProps {
  type: 'wins' | 'diamonds' | 'referrals'
  entries: LeaderboardEntry[]
  isLoading?: boolean
}

export interface CreateGroupModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (groupData: Partial<Group>) => void
  isLoading?: boolean
}

// API Response Types
export interface ApiResponse<T> {
  data: T
  success: boolean
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasMore: boolean
}

// Filter and Sort Types
export type GroupFilter = 'all' | 'joined' | 'available' | 'recommended'
export type GroupSort = 'rank' | 'members' | 'activity' | 'level'
export type LeaderboardType = 'wins' | 'diamonds' | 'referrals'
export type ChatChannel = 'global' | 'group'

// State Types
export interface CommunityState {
  activeTab: 'chat' | 'groups' | 'leaderboard' | 'announcements'
  selectedGroup: Group | null
  selectedUser: UserProfile | null
  chatChannel: ChatChannel
  groupFilter: GroupFilter
  groupSort: GroupSort
  leaderboardType: LeaderboardType
  searchTerm: string
  isLoading: boolean
  error: string | null
}
