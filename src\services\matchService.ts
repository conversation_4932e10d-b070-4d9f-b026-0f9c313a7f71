import { supabase } from './supabaseClient'

// Database match interface for internal operations
interface DatabaseMatch {
  id: string
  title: string
  game: string
  mode: string
  max_players: number // Changed from max_participants to max_players
  entry_fee: number
  pot_amount: number
  diamond_pot: number
  status: 'open' | 'waiting' | 'full' | 'in_progress' | 'ongoing' | 'completed' | 'cancelled' | 'disputed'
  scheduled_start_time: string
  actual_start_time?: string
  actual_end_time?: string
  host_id: string
  host_username?: string
  referee_id?: string
  referee_username?: string
  winner_id?: string
  winner_username?: string
  rules?: string
  region?: string
  proof_required?: boolean
  current_players: number // Changed from current_participants to current_players
  created_at: string
  updated_at?: string
  // Removed deprecated fields: max_participants, requirements, tournament_id, bracket_position, round, stream_url, replay_url
}

export const matchService = {
  // Get all matches (simplified query to avoid 400 errors)
  async getMatches(filters?: {
    game?: string;
    status?: string;
    format?: string;
    minPot?: number;
    maxPot?: number;
    search?: string;
    sortBy?: 'newest' | 'highest-bet' | 'soonest';
  }) {
    try {
      // Start with basic matches query to avoid join issues
      let query = supabase
        .from('matches')
        .select('*')
        .not('status', 'in', '(cancelled,canceled)') // Exclude cancelled matches at database level

      // Apply filters
      if (filters?.game) {
        query = query.eq('game', filters.game)
      }
      if (filters?.status) {
        query = query.eq('status', filters.status)
      }
      if (filters?.format) {
        query = query.eq('mode', filters.format)
      }
      if (filters?.minPot) {
        query = query.gte('pot_amount', filters.minPot)
      }
      if (filters?.maxPot) {
        query = query.lte('pot_amount', filters.maxPot)
      }
      if (filters?.search) {
        query = query.or(`game.ilike.%${filters.search}%,mode.ilike.%${filters.search}%,rules.ilike.%${filters.search}%`)
      }

      // Apply sorting
      switch (filters?.sortBy) {
        case 'highest-bet':
          query = query.order('pot_amount', { ascending: false })
          break
        case 'soonest':
          query = query.order('scheduled_start_time', { ascending: true })
          break
        case 'newest':
        default:
          query = query.order('created_at', { ascending: false })
          break
      }

      const { data: matches, error } = await query
      if (error) throw error

      // If we have matches, get user details separately to avoid join issues
      if (matches && matches.length > 0) {
        const userIds = new Set()
        matches.forEach(match => {
          if (match.host_id) userIds.add(match.host_id)
          if (match.referee_id) userIds.add(match.referee_id)
          if (match.winner_id) userIds.add(match.winner_id)
        })

        // Get user details for all involved users
        const { data: users } = await supabase
          .from('users')
          .select('id, username, first_name, last_name')
          .in('id', Array.from(userIds))

        // Create user lookup map
        const userMap = {}
        if (users) {
          users.forEach(user => {
            (userMap as any)[user.id] = user
          })
        }

        // Enhance matches with user data
        const enhancedMatches = matches.map(match => ({
          ...match,
          host: (userMap as any)[match.host_id] || null,
          referee: (userMap as any)[match.referee_id] || null,
          winner: (userMap as any)[match.winner_id] || null
        }))

        return { data: enhancedMatches, error: null }
      }

      return { data: matches, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create match with host entry fee deduction and auto-assign referee
  async createMatch(matchData: Omit<DatabaseMatch, 'id' | 'created_at' | 'updated_at' | 'host_username' | 'referee_username' | 'winner_username' | 'current_participants'>) {
    try {
      // BUSINESS RULE: Check if user already has an active match
      console.log('🔍 Checking for existing active matches for user:', matchData.host_id)
      const { data: existingMatches } = await supabase
        .from('matches')
        .select('id, title, game, status')
        .eq('host_id', matchData.host_id)
        .in('status', ['open', 'waiting', 'full', 'ongoing', 'in_progress', 'active', 'battling', 'fighting'])

      console.log('🔍 Found existing matches:', existingMatches?.length || 0, existingMatches)

      if (existingMatches && existingMatches.length > 0) {
        const activeMatch = existingMatches[0]
        console.log('❌ Blocking match creation due to existing active match:', activeMatch)
        throw new Error(`You already have an active match: "${activeMatch.title || activeMatch.game}". Please cancel it before creating a new match.`)
      }

      console.log('✅ No active matches found, proceeding with creation')

      // Check if host has enough diamonds
      const { data: hostData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', matchData.host_id)
        .single()

      if (!hostData || (hostData.diamond_balance || 0) < matchData.entry_fee) {
        throw new Error('Insufficient diamond balance')
      }

      // Create match first to get match ID for conflict checking
      const { data: newMatch, error: matchError } = await supabase
        .from('matches')
        .insert([{
          ...matchData,
          // Remove referee_id since column doesn't exist
          created_at: new Date().toISOString()
        }])
        .select()
        .single()

      if (matchError) throw matchError

      // Add host as a participant (needed for conflict checking)
      try {
        await supabase
          .from('match_participants')
          .insert([{
            match_id: newMatch.id,
            user_id: matchData.host_id,
            team_number: 1, // Host is team 1
            joined_at: new Date().toISOString()
          }])
      } catch (participantError) {
        console.warn('Could not add host as participant:', participantError)
        // Continue anyway - this is for conflict checking
      }

      // Try to get a conflict-free referee for this specific match
      const availableReferee = await this.getAvailableReferee(newMatch.id)

      // Deduct entry fee from host's balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          diamond_balance: (hostData.diamond_balance || 0) - matchData.entry_fee
        })
        .eq('id', matchData.host_id)

      if (balanceError) throw balanceError

      // Skip referee assignment since referee_id column doesn't exist
      // TODO: Add referee_id column to database if referee system is needed
      if (availableReferee) {
        console.log('Referee available but referee_id column missing:', availableReferee.id)
      }

      // Create transaction record for host entry
      await supabase
        .from('transactions')
        .insert([{
          user_id: matchData.host_id,
          type: 'match_entry',
          amount: -matchData.entry_fee,
          status: 'completed',
          description: `Created ${matchData.game} ${matchData.mode} match`,
          match_id: newMatch.id,
          created_at: new Date().toISOString()
        }])

      // Return the match with updated referee info
      const { data: finalMatch } = await supabase
        .from('matches')
        .select('*')
        .eq('id', newMatch.id)
        .single()

      return { data: finalMatch || newMatch, error: null }
    } catch (error) {
      console.error('🔴 Match creation error in service:', error)

      // Enhanced error logging for debugging
      if (error && typeof error === 'object') {
        const errorObj = error as any
        console.error('🔴 Detailed error info:', {
          code: errorObj.code,
          message: errorObj.message,
          details: errorObj.details,
          hint: errorObj.hint,
          stack: errorObj.stack
        })
      }

      return { data: null, error }
    }
  },

  // Get count of available referees (ultra-simplified)
  async getAvailableRefereeCount() {
    try {
      // Just get basic user count and return a reasonable estimate
      const { count, error } = await supabase
        .from('users')
        .select('id', { count: 'exact', head: true })

      if (error) {
        console.error('Error getting user count for referee estimate:', error)
        return 3 // Fallback number
      }

      // Estimate that 20% of users could be referees (reasonable assumption)
      const estimatedReferees = Math.max(1, Math.floor((count || 0) * 0.2))
      return Math.min(estimatedReferees, 10) // Cap at 10 for display purposes
    } catch (error) {
      console.error('Error getting referee count:', error)
      return 3 // Fallback number
    }
  },

  // Get an available referee for match assignment (with conflict checking)
  async getAvailableReferee(matchId?: string) {
    try {
      // Get users who are approved referees and currently available
      // Exclude level referees (admin_level >= 1) as they shouldn't referee regular matches
      const { data: referees, error } = await supabase
        .from('users')
        .select('id, username')
        .eq('referee_status', 'approved')
        .is('admin_level', null) // Exclude level referees
        .order('created_at', { ascending: true }) // FIFO assignment

      if (error) throw error
      if (!referees || referees.length === 0) return null

      // If no specific match, return first available referee
      if (!matchId) {
        return referees[0]
      }

      // Check each referee for conflicts with the specific match
      try {
        const { refereeConflictService } = await import('./refereeConflictService')

        for (const referee of referees) {
          const conflictResult = await refereeConflictService.checkRefereeConflict(referee.id, matchId)

          if (!conflictResult.hasConflict) {
            return referee
          }
        }
      } catch (importError) {
        console.log('Could not import referee conflict service:', importError)
        // Return first referee if conflict checking fails
        return referees[0]
      }

      console.log('No conflict-free referees available for match:', matchId)
      return null
    } catch (error) {
      console.log('Error finding available referee:', error)
      return null
    }
  },

  // Join match - complete transaction with diamond deduction
  async joinMatch(matchId: string, userId: string) {
    try {
      // First check if match is available
      const { data: match } = await supabase
        .from('matches')
        .select('*')
        .eq('id', matchId)
        .single()

      if (!match) throw new Error('Match not found')

      // Prevent creator from joining their own match
      if (match.host_id === userId) {
        throw new Error('You cannot join your own match')
      }

      // Check if user is assigned as referee for this match
      if (match.referee_id === userId) {
        throw new Error('You cannot participate in matches you are refereeing')
      }

      // BUSINESS RULE: Check if user has created any active matches
      const { data: userCreatedMatches } = await supabase
        .from('matches')
        .select('id, title, game, status')
        .eq('host_id', userId)
        .in('status', ['open', 'waiting', 'full', 'ongoing', 'in_progress', 'active', 'battling', 'fighting'])

      if (userCreatedMatches && userCreatedMatches.length > 0) {
        const activeMatch = userCreatedMatches[0]
        throw new Error(`You cannot join other matches while you have an active match: "${activeMatch.title || activeMatch.game}". Please cancel your match first.`)
      }

      if (match.current_players >= match.max_players) {
        throw new Error('Match is full')
      }

      if (match.status !== 'open') {
        throw new Error('Match is no longer available')
      }

      // Check if user is already in this match
      const { data: existingParticipant } = await supabase
        .from('match_participants')
        .select('id')
        .eq('match_id', matchId)
        .eq('user_id', userId)
        .single()

      if (existingParticipant) {
        throw new Error('You have already joined this match')
      }

      // Check if user has any active matches (prevent joining multiple)
      const { data: activeMatches } = await supabase
        .from('match_participants')
        .select('match_id, matches!inner(status)')
        .eq('user_id', userId)
        .in('matches.status', ['open', 'ongoing'])

      if (activeMatches && activeMatches.length > 0) {
        throw new Error('You must complete or cancel your current match before joining another')
      }

      // Check if user has enough diamonds
      const { data: userData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (!userData || (userData.diamond_balance || 0) < match.entry_fee) {
        throw new Error('Insufficient diamond balance')
      }

      // Deduct entry fee from user's balance
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          diamond_balance: (userData.diamond_balance || 0) - match.entry_fee
        })
        .eq('id', userId)

      if (balanceError) throw balanceError

      // Create transaction record
      await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'match_entry',
          amount: -match.entry_fee,
          status: 'completed',
          description: `Joined ${match.game} ${match.mode} match`,
          match_id: matchId,
          created_at: new Date().toISOString()
        }])

      // Try to add user to match participants (may fail due to permissions)
      try {
        const { error: participantError } = await supabase
          .from('match_participants')
          .insert([{ match_id: matchId, user_id: userId }])

        if (participantError) {
          console.log('Could not add to match_participants (permission issue):', participantError)
          // Continue anyway - we'll track via match status
        }
      } catch (participantError) {
        console.log('Participant insertion failed, continuing:', participantError)
      }

      // Update match current players count and status
      const newPlayerCount = match.current_players + 1
      const willBeFull = newPlayerCount >= match.max_players

      const updateData = {
        current_players: newPlayerCount,
        status: willBeFull ? 'full' : 'open'
      }

      const { data, error } = await supabase
        .from('matches')
        .update(updateData)
        .eq('id', matchId)
        .select()

      if (error) throw error

      // Return the first match if multiple returned, or null if none
      const matchData = Array.isArray(data) ? data[0] : data
      return { data: matchData, error: null }
    } catch (error) {
      console.error('Join match error:', error)
      return { data: null, error }
    }
  },

  // Cancel match (only for match creator)
  async cancelMatch(matchId: string, userId: string) {
    try {
      // First check if match exists and user is the host
      const { data: match } = await supabase
        .from('matches')
        .select('*')
        .eq('id', matchId)
        .eq('host_id', userId) // Only host can cancel
        .single()

      if (!match) {
        throw new Error('Match not found or you are not the host')
      }

      // Check if match is already cancelled (prevent duplicate cancellations)
      if (match.status.toLowerCase() === 'cancelled') {
        throw new Error('Match is already cancelled')
      }

      // Check if match cannot be cancelled (only prevent cancelling completed matches)
      const normalizedStatus = match.status.toLowerCase()
      if (normalizedStatus === 'completed') {
        throw new Error('Cannot cancel a completed match')
      }

      // Allow cancelling matches in any other status (open, full, in_progress, etc.)
      // Host should be able to cancel their match even if it's in progress
      console.log('✅ Match can be cancelled, current status:', match.status)

      // Update match status to cancelled (simplest possible update)
      console.log('🔧 Updating match status to cancelled...')
      const { error: matchError } = await supabase
        .from('matches')
        .update({ status: 'cancelled' })
        .eq('id', matchId)
        .eq('host_id', userId) // Double-check ownership

      if (matchError) {
        console.error('❌ Match update error:', matchError)
        throw matchError
      }

      console.log('✅ Match status updated successfully')

      // Refund entry fee to host
      const { data: hostData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (hostData) {
        await supabase
          .from('users')
          .update({
            diamond_balance: (hostData.diamond_balance || 0) + match.entry_fee
          })
          .eq('id', userId)
      }

      // Create refund transaction record
      await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'match_refund',
          amount: match.entry_fee,
          status: 'completed',
          description: `Match cancelled - refund for ${match.game} ${match.mode}`,
          match_id: matchId,
          created_at: new Date().toISOString()
        }])

      // Try to refund other participants (if any)
      try {
        const { data: participants } = await supabase
          .from('match_participants')
          .select('user_id')
          .eq('match_id', matchId)

        if (participants && participants.length > 0) {
          for (const participant of participants) {
            if (participant.user_id !== userId) { // Don't refund host twice
              // Get participant's current balance
              const { data: participantData } = await supabase
                .from('users')
                .select('diamond_balance')
                .eq('id', participant.user_id)
                .single()

              if (participantData) {
                // Refund participant
                await supabase
                  .from('users')
                  .update({
                    diamond_balance: (participantData.diamond_balance || 0) + match.entry_fee
                  })
                  .eq('id', participant.user_id)

                // Create refund transaction
                await supabase
                  .from('transactions')
                  .insert([{
                    user_id: participant.user_id,
                    type: 'match_refund',
                    amount: match.entry_fee,
                    status: 'completed',
                    description: `Match cancelled by host - refund for ${match.game} ${match.mode}`,
                    match_id: matchId,
                    created_at: new Date().toISOString()
                  }])
              }
            }
          }
        }
      } catch (participantError) {
        console.log('Could not refund participants due to permission issues:', participantError)
        // Continue anyway - at least host gets refunded
      }

      return { data: { success: true }, error: null }
    } catch (error) {
      console.error('Cancel match error:', error)
      return { data: null, error }
    }
  },

  // Real-time match subscriptions
  subscribeToMatches(callback: (matches: any[]) => void) {
    const matchesChannel = supabase
      .channel('matches')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches' },
        async () => {
          // Reload matches when any change occurs
          const { data } = await this.getMatches()
          if (data) callback(data)
        }
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_participants' },
        async () => {
          // Reload matches when participants change
          const { data } = await this.getMatches()
          if (data) callback(data)
        }
      )
      .subscribe()

    return matchesChannel
  },

  // Subscribe to specific match updates
  subscribeToMatch(matchId: string, callback: (match: any) => void) {
    return supabase
      .channel(`match-${matchId}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches', filter: `id=eq.${matchId}` },
        (payload) => {
          callback(payload.new || payload.old)
        }
      )
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_participants', filter: `match_id=eq.${matchId}` },
        async () => {
          // Reload full match data when participants change
          const { data } = await this.getMatchDetails(matchId)
          if (data) callback(data)
        }
      )
      .subscribe()
  },

  // Get user's matches - simplified to avoid permission issues
  async getUserMatches(userId: string) {
    try {
      const { data, error } = await supabase
        .from('match_participants')
        .select('match_id')
        .eq('user_id', userId)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.log('Error getting user matches, returning empty array:', error)
      return { data: [], error: null } // Return empty array instead of failing
    }
  },

  // Get user statistics
  async getUserStats(userId: string) {
    try {
      // Get match statistics
      const { data: matches } = await supabase
        .from('matches')
        .select('*')
        .or(`host_id.eq.${userId},winner_id.eq.${userId}`)
        .eq('status', 'completed')

      const totalMatches = matches?.length || 0
      const wins = matches?.filter(match => match.winner_id === userId).length || 0
      const losses = totalMatches - wins
      const winRate = totalMatches > 0 ? Math.round((wins / totalMatches) * 100) : 0

      // Get earnings from transactions
      const { data: transactions } = await supabase
        .from('transactions')
        .select('amount, type')
        .eq('user_id', userId)
        .in('type', ['match_win', 'match_refund'])

      const totalEarnings = transactions?.reduce((sum, tx) => {
        return tx.type === 'match_win' ? sum + tx.amount : sum
      }, 0) || 0

      return {
        data: {
          totalMatches,
          wins,
          losses,
          winRate,
          totalEarnings
        },
        error: null
      }
    } catch (error) {
      console.log('Error getting user stats:', error)
      return {
        data: {
          totalMatches: 0,
          wins: 0,
          losses: 0,
          winRate: 0,
          totalEarnings: 0
        },
        error: null
      }
    }
  },

  // Get match details with all participants
  async getMatchDetails(matchId: string) {
    try {
      const { data, error } = await supabase
        .from('matches')
        .select(`
          *,
          host:users!host_id(id, username, first_name, last_name),
          winner:users!winner_id(id, username, first_name, last_name),
          participants:match_participants(
            id,
            user_id,
            joined_at,
            is_ready,
            user:users(id, username, first_name, last_name)
          )
        `)
        .eq('id', matchId)
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Start match when both players are ready
  async startMatch(matchId: string, _refereeId?: string) {
    try {
      console.log('🎮 Starting match:', matchId)

      // Update match status to in_progress
      const { error: matchError } = await supabase
        .from('matches')
        .update({
          status: 'in_progress',
          actual_start_time: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)

      if (matchError) {
        console.error('Failed to start match:', matchError)
        throw matchError
      }

      console.log('✅ Match started successfully')
      return { data: { success: true }, error: null }

    } catch (error) {
      console.error('Start match error:', error)
      return { data: null, error }
    }
  },

  // Submit match result with full payout processing
  async submitMatchResultSimple(matchId: string, winnerId: string, notes?: string) {
    try {
      // Get match details first
      const { data: match, error: matchFetchError } = await supabase
        .from('matches')
        .select('*, match_participants(*)')
        .eq('id', matchId)
        .single()

      if (matchFetchError || !match) {
        throw new Error('Match not found')
      }

      // Update match with winner and completion status
      const { error: matchError } = await supabase
        .from('matches')
        .update({
          status: 'completed',
          winner_id: winnerId,
          referee_notes: notes,
          actual_end_time: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)

      if (matchError) throw matchError

      // Process payouts if there's a winner (not a draw)
      if (winnerId && winnerId !== 'draw' && match.pot_amount > 0) {
        await this.processMatchPayouts(matchId, winnerId, match)
      }

      // Process betting results
      try {
        const { bettingService } = await import('./bettingService')
        await bettingService.processBetResults(matchId, winnerId)
      } catch (bettingError) {
        console.error('Error processing betting results:', bettingError)
        // Continue anyway - match result is more important
      }

      // Create transaction records for all participants
      await this.createMatchTransactionRecords(matchId, match, winnerId)

      return { data: { success: true, winnerId }, error: null }
    } catch (error) {
      console.error('Submit match result error:', error)
      return { data: null, error }
    }
  },

  // Process match payouts to winner
  async processMatchPayouts(_matchId: string, winnerId: string, match: any) {
    try {
      // Calculate platform fee (5% for referees and platform)
      const platformFee = Math.floor(match.pot_amount * 0.05)
      const winnerAmount = match.pot_amount - platformFee

      // Get winner's current balance
      const { data: winner, error: winnerError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', winnerId)
        .single()

      if (winnerError) throw winnerError

      // Update winner's balance
      await supabase
        .from('users')
        .update({
          diamond_balance: (winner.diamond_balance || 0) + winnerAmount,
          updated_at: new Date().toISOString()
        })
        .eq('id', winnerId)

      // If there's a referee, give them the platform fee
      if (match.referee_id && platformFee > 0) {
        const { data: referee } = await supabase
          .from('users')
          .select('diamond_balance')
          .eq('id', match.referee_id)
          .single()

        if (referee) {
          await supabase
            .from('users')
            .update({
              diamond_balance: (referee.diamond_balance || 0) + platformFee,
              updated_at: new Date().toISOString()
            })
            .eq('id', match.referee_id)
        }
      }

      console.log(`✅ Payouts processed: Winner ${winnerId} received ${winnerAmount} diamonds`)
    } catch (error) {
      console.error('Error processing match payouts:', error)
      throw error
    }
  },

  // Create transaction records for match completion
  async createMatchTransactionRecords(matchId: string, match: any, winnerId: string) {
    try {
      const transactions = []
      const platformFee = Math.floor(match.pot_amount * 0.05)
      const winnerAmount = match.pot_amount - platformFee

      // Winner transaction
      if (winnerId && winnerId !== 'draw') {
        transactions.push({
          user_id: winnerId,
          type: 'match_win',
          amount: winnerAmount,
          status: 'completed',
          description: `Victory in ${match.game} ${match.mode} match`,
          match_id: matchId,
          created_at: new Date().toISOString()
        })

        // Referee fee transaction
        if (match.referee_id && platformFee > 0) {
          transactions.push({
            user_id: match.referee_id,
            type: 'referee_fee',
            amount: platformFee,
            status: 'completed',
            description: `Referee fee for ${match.game} ${match.mode} match`,
            match_id: matchId,
            created_at: new Date().toISOString()
          })
        }
      }

      // Loss transactions for participants (entry fees already deducted)
      if (match.match_participants) {
        for (const participant of match.match_participants) {
          if (participant.user_id !== winnerId) {
            transactions.push({
              user_id: participant.user_id,
              type: 'match_loss',
              amount: -match.entry_fee,
              status: 'completed',
              description: `Entry fee for ${match.game} ${match.mode} match`,
              match_id: matchId,
              created_at: new Date().toISOString()
            })
          }
        }
      }

      // Insert all transactions
      if (transactions.length > 0) {
        await supabase
          .from('transactions')
          .insert(transactions)
      }

      console.log(`✅ Created ${transactions.length} transaction records for match ${matchId}`)
    } catch (error) {
      console.error('Error creating match transaction records:', error)
      // Don't throw - this is not critical for match completion
    }
  },

  // Access to supabase client for referee page
  get supabase() {
    return supabase
  }
}
