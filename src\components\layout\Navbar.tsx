import { useState } from "react"
import { Link, useLocation, useNavigate } from "react-router-dom"
import { Button } from "../ui/button"
import { Menu, X, Home, Trophy, Shield, User, Wallet, BarChart3, LogOut, Users, ShoppingCart } from "lucide-react"
import NotificationCenter from "../notifications/NotificationCenter"

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()

  // Mock authentication state - replace with real auth
  const isAuthenticated = localStorage.getItem("isAuthenticated") === "true"
  const userRole = localStorage.getItem("userRole") || "user"
  const userName = localStorage.getItem("userName") || "User"

  const handleLogout = () => {
    localStorage.removeItem("isAuthenticated")
    localStorage.removeItem("userRole")
    localStorage.removeItem("userName")
    navigate("/")
    setIsOpen(false)
  }

  const handleLogin = () => {
    // Mock login - replace with real auth
    localStorage.setItem("isAuthenticated", "true")
    localStorage.setItem("userRole", "admin") // Set as admin for demo
    localStorage.setItem("userName", "Admin User")
    navigate("/dashboard")
  }

  const navLinks = [
    { path: "/", label: "Home", icon: Home },
    { path: "/browse", label: "Marketplace", icon: ShoppingCart },
    { path: "/leaderboards", label: "Leaderboards", icon: Trophy },
    { path: "/rules", label: "Rules", icon: Shield },
    { path: "/support", label: "Support", icon: User },
  ]

  const userLinks = [
    { path: "/dashboard", label: "Dashboard", icon: BarChart3 },
    { path: "/matches", label: "Matches", icon: Trophy },
    { path: "/community", label: "Community", icon: Users },
    { path: "/profile", label: "Profile", icon: User },
    { path: "/wallet", label: "Wallet", icon: Wallet },
  ]

  const refereeLinks = [{ path: "/referee/dashboard", label: "Referee Dashboard", icon: Shield }]

  const adminLinks = [{ path: "/admin/referee-applications", label: "Review Applications", icon: Users }]

  return (
    <nav className="bg-white shadow-lg border-b border-gray-200 sticky top-0 z-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <Trophy className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">Gambets</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => {
              const Icon = link.icon
              return (
                <Link
                  key={link.path}
                  to={link.path}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    location.pathname === link.path
                      ? "text-blue-600 bg-blue-50"
                      : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{link.label}</span>
                </Link>
              )
            })}
          </div>

          {/* Desktop Auth Section */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              <div className="flex items-center space-x-4">
                {/* Notification Center */}
                <NotificationCenter />
                {/* User Menu */}
                <div className="relative group">
                  <button className="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50">
                    <User className="w-4 h-4" />
                    <span>{userName}</span>
                  </button>

                  {/* Dropdown Menu */}
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                    <div className="py-1">
                      {userLinks.map((link) => {
                        const Icon = link.icon
                        return (
                          <Link
                            key={link.path}
                            to={link.path}
                            className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                          >
                            <Icon className="w-4 h-4" />
                            <span>{link.label}</span>
                          </Link>
                        )
                      })}

                      {/* Referee Links */}
                      {(userRole === "referee" || userRole === "admin") && (
                        <>
                          <div className="border-t border-gray-100 my-1"></div>
                          {refereeLinks.map((link) => {
                            const Icon = link.icon
                            return (
                              <Link
                                key={link.path}
                                to={link.path}
                                className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                              >
                                <Icon className="w-4 h-4" />
                                <span>{link.label}</span>
                              </Link>
                            )
                          })}
                        </>
                      )}

                      {/* Admin Links */}
                      {userRole === "admin" && (
                        <>
                          <div className="border-t border-gray-100 my-1"></div>
                          {adminLinks.map((link) => {
                            const Icon = link.icon
                            return (
                              <Link
                                key={link.path}
                                to={link.path}
                                className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                              >
                                <Icon className="w-4 h-4" />
                                <span>{link.label}</span>
                              </Link>
                            )
                          })}
                        </>
                      )}

                      <div className="border-t border-gray-100 my-1"></div>
                      <button
                        onClick={handleLogout}
                        className="flex items-center space-x-2 w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>Logout</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3">
                <Button onClick={handleLogin} variant="outline">
                  Login
                </Button>
                <Link to="/apply-referee">
                  <Button className="bg-blue-600 hover:bg-blue-700">Apply as Referee</Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 rounded-md text-gray-700 hover:text-blue-600 hover:bg-gray-50"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="md:hidden border-t border-gray-200">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navLinks.map((link) => {
                const Icon = link.icon
                return (
                  <Link
                    key={link.path}
                    to={link.path}
                    onClick={() => setIsOpen(false)}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium ${
                      location.pathname === link.path
                        ? "text-blue-600 bg-blue-50"
                        : "text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{link.label}</span>
                  </Link>
                )
              })}

              {isAuthenticated ? (
                <>
                  <div className="border-t border-gray-200 my-2"></div>
                  {userLinks.map((link) => {
                    const Icon = link.icon
                    return (
                      <Link
                        key={link.path}
                        to={link.path}
                        onClick={() => setIsOpen(false)}
                        className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                      >
                        <Icon className="w-5 h-5" />
                        <span>{link.label}</span>
                      </Link>
                    )
                  })}

                  {(userRole === "referee" || userRole === "admin") && (
                    <>
                      {refereeLinks.map((link) => {
                        const Icon = link.icon
                        return (
                          <Link
                            key={link.path}
                            to={link.path}
                            onClick={() => setIsOpen(false)}
                            className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                          >
                            <Icon className="w-5 h-5" />
                            <span>{link.label}</span>
                          </Link>
                        )
                      })}
                    </>
                  )}

                  {userRole === "admin" && (
                    <>
                      {adminLinks.map((link) => {
                        const Icon = link.icon
                        return (
                          <Link
                            key={link.path}
                            to={link.path}
                            onClick={() => setIsOpen(false)}
                            className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                          >
                            <Icon className="w-5 h-5" />
                            <span>{link.label}</span>
                          </Link>
                        )
                      })}
                    </>
                  )}

                  <button
                    onClick={handleLogout}
                    className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-base font-medium text-red-600 hover:bg-red-50"
                  >
                    <LogOut className="w-5 h-5" />
                    <span>Logout</span>
                  </button>
                </>
              ) : (
                <>
                  <div className="border-t border-gray-200 my-2"></div>
                  <button
                    onClick={handleLogin}
                    className="flex items-center space-x-2 w-full px-3 py-2 rounded-md text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50"
                  >
                    <User className="w-5 h-5" />
                    <span>Login</span>
                  </button>
                  <Link
                    to="/apply-referee"
                    onClick={() => setIsOpen(false)}
                    className="flex items-center space-x-2 px-3 py-2 rounded-md text-base font-medium text-blue-600 hover:bg-blue-50"
                  >
                    <Shield className="w-5 h-5" />
                    <span>Apply as Referee</span>
                  </Link>
                </>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  )
}
