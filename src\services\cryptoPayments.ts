// ============================================================================
// CRYPTO PAYMENTS SERVICE - NOWPAYMENTS INTEGRATION
// ============================================================================

import { supabase } from './supabase'

// ============================================================================
// UNIFIED CUSTOM PAYMENT SYSTEM
// ============================================================================
// Flexible system where users can enter custom amounts
// Different minimums for traditional vs crypto payments
// Manual approval system for all payments

// Base conversion rates - SIMPLIFIED
export const PAYMENT_CONFIG = {
  // Base rate: 1 PHP = 1 diamond (no bonuses)
  diamonds_per_php: 1,
  diamonds_per_usd: 55, // 1 USD = 55 diamonds (same as PHP rate)

  // Exchange rates
  usd_to_php_rate: 55, // 1 USD = 55 PHP
}

// Calculate diamonds - SIMPLIFIED (no bonuses)
export function calculateDiamonds(amountPhp: number) {
  const totalDiamonds = Math.floor(amountPhp * PAYMENT_CONFIG.diamonds_per_php)

  return {
    base_diamonds: totalDiamonds,
    bonus_diamonds: 0,
    total_diamonds: totalDiamonds,
    bonus_percentage: 0
  }
}

// Calculate USD equivalent
export function phpToUsd(amountPhp: number) {
  return amountPhp / PAYMENT_CONFIG.usd_to_php_rate
}

// Calculate PHP equivalent
export function usdToPhp(amountUsd: number) {
  return amountUsd * PAYMENT_CONFIG.usd_to_php_rate
}

// ============================================================================
// LEGACY DIAMOND PACKAGES (for backward compatibility)
// ============================================================================
// Keep this for existing components that still use DIAMOND_PACKAGES

export const DIAMOND_PACKAGES = [
  {
    id: 'starter',
    diamonds: 100,
    price_usd: 2,
    bonus_diamonds: 0,
    total_diamonds: 100,
    popular: false,
    savings: 0
  },
  {
    id: 'popular',
    diamonds: 500,
    price_usd: 8,
    bonus_diamonds: 100,
    total_diamonds: 600,
    popular: true,
    savings: 25
  },
  {
    id: 'value',
    diamonds: 1000,
    price_usd: 15,
    bonus_diamonds: 250,
    total_diamonds: 1250,
    popular: false,
    savings: 33
  },
  {
    id: 'premium',
    diamonds: 2500,
    price_usd: 30,
    bonus_diamonds: 715,
    total_diamonds: 3215,
    popular: false,
    savings: 40
  },
  {
    id: 'ultimate',
    diamonds: 5000,
    price_usd: 50,
    bonus_diamonds: 1667,
    total_diamonds: 6667,
    popular: false,
    savings: 50
  }
]

// ============================================================================
// WITHDRAWAL SYSTEM CONFIGURATION
// ============================================================================

// Diamond to cash conversion rates (with platform fee) - SIMPLIFIED
export const WITHDRAWAL_CONFIG = {
  // Base conversion rate: 100 diamonds = 0.95 USD (5% platform fee)
  diamonds_per_usd: 100,
  platform_fee_percentage: 5,
  minimum_withdrawal_diamonds: 1000, // Minimum 1000 diamonds to withdraw
  minimum_withdrawal_usd: 9.50, // Minimum $9.50 withdrawal
  minimum_withdrawal_php: 522, // Minimum ₱522 withdrawal

  // Processing times (removed bank transfer)
  processing_time: {
    gcash: '1-3 business days',
    maya: '1-3 business days',
    crypto: '24-48 hours'
  }
}

// Supported cryptocurrencies (NowPayments) - Enhanced
export const SUPPORTED_CRYPTOS = [
  {
    symbol: 'usdttrc20',
    name: 'USDT (TRC20)',
    network: 'Tron',
    fee_estimate: '$0.50',
    confirmation_time: '1-3 min',
    recommended: true,
    icon: '₮',
    min_amount: 1,
    max_amount: 10000
  },
  {
    symbol: 'usdcmatic',
    name: 'USDC (Polygon)',
    network: 'Polygon',
    fee_estimate: '$0.10',
    confirmation_time: '1-2 min',
    recommended: true,
    icon: '💵',
    min_amount: 1,
    max_amount: 10000
  },
  {
    symbol: 'bnbbsc',
    name: 'BNB (BSC)',
    network: 'Binance Smart Chain',
    fee_estimate: '$0.20',
    confirmation_time: '3 min',
    recommended: false,
    icon: '🟡',
    min_amount: 5,
    max_amount: 5000
  },
  {
    symbol: 'eth',
    name: 'Ethereum (ETH)',
    network: 'Ethereum',
    fee_estimate: '$2-5',
    confirmation_time: '5-10 min',
    recommended: false,
    icon: '⟠',
    min_amount: 10,
    max_amount: 5000
  }
]

// ============================================================================
// UNIFIED PAYMENT METHODS - MANUAL PROCESSING
// ============================================================================

// ============================================================================
// TRADITIONAL PAYMENT METHODS (Manual Processing)
// ============================================================================

export const TRADITIONAL_PAYMENT_METHODS = [
  {
    id: 'gcash',
    name: 'GCash',
    type: 'traditional',
    icon: '📱',
    description: 'Send payment via GCash',
    processing_time: 'Manual verification (1-24 hours)',
    fee_percentage: 0,
    fixed_fee_php: 0,
    min_amount_php: 100, // ₱100 minimum
    max_amount_php: 20000, // ₱20,000 maximum
    requires_verification: true,
    manual_processing: true,
    account_info: {
      account_name: 'GAMBETS GAMING',
      account_number: '09XX-XXX-XXXX', // Will be provided by admin
      instructions: 'Send payment to our GCash account with reference number and phone number'
    }
  },
  {
    id: 'maya',
    name: 'Maya (PayMaya)',
    type: 'traditional',
    icon: '💳',
    description: 'Send payment via Maya',
    processing_time: 'Manual verification (1-24 hours)',
    fee_percentage: 0,
    fixed_fee_php: 0,
    min_amount_php: 100, // ₱100 minimum
    max_amount_php: 20000, // ₱20,000 maximum
    requires_verification: true,
    manual_processing: true,
    account_info: {
      account_name: 'GAMBETS GAMING',
      account_number: '09XX-XXX-XXXX', // Will be provided by admin
      instructions: 'Send payment to our Maya account with reference number and phone number'
    }
  }
]

// ============================================================================
// CRYPTO PAYMENT METHODS (Automated Processing via Gateway)
// ============================================================================

export const CRYPTO_PAYMENT_METHODS = [
  {
    id: 'crypto_usdt',
    name: 'USDT (TRC20)',
    type: 'crypto',
    icon: '₮',
    description: 'Pay with USDT on Tron network',
    processing_time: 'Automated (5-15 minutes)',
    fee_percentage: 2,
    fixed_fee_php: 0,
    min_amount_php: 2750, // $50 minimum (50 * 55 PHP/USD)
    max_amount_php: Infinity, // No maximum
    requires_verification: false,
    manual_processing: false,
    network: 'TRC20',
    symbol: 'usdttrc20'
  },
  {
    id: 'crypto_usdc',
    name: 'USDC (Polygon)',
    type: 'crypto',
    icon: '💵',
    description: 'Pay with USDC on Polygon network',
    processing_time: 'Automated (5-15 minutes)',
    fee_percentage: 2,
    fixed_fee_php: 0,
    min_amount_php: 2750, // $50 minimum (50 * 55 PHP/USD)
    max_amount_php: Infinity, // No maximum
    requires_verification: false,
    manual_processing: false,
    network: 'Polygon',
    symbol: 'usdcmatic'
  },
  {
    id: 'crypto_bnb',
    name: 'BNB (BSC)',
    type: 'crypto',
    icon: '🟡',
    description: 'Pay with BNB on Binance Smart Chain',
    processing_time: 'Automated (5-15 minutes)',
    fee_percentage: 2,
    fixed_fee_php: 0,
    min_amount_php: 2750, // $50 minimum (50 * 55 PHP/USD)
    max_amount_php: Infinity, // No maximum
    requires_verification: false,
    manual_processing: false,
    network: 'BSC',
    symbol: 'bnbbsc'
  },
  {
    id: 'crypto_eth',
    name: 'ETH (Ethereum)',
    type: 'crypto',
    icon: '⟠',
    description: 'Pay with ETH on Ethereum network',
    processing_time: 'Automated (5-15 minutes)',
    fee_percentage: 2.5,
    fixed_fee_php: 0,
    min_amount_php: 5500, // $100 minimum (100 * 55 PHP/USD, higher due to gas fees)
    max_amount_php: Infinity, // No maximum
    requires_verification: false,
    manual_processing: false,
    network: 'Ethereum',
    symbol: 'eth'
  }
]

// Combined payment methods for backward compatibility
export const PAYMENT_METHODS = [...TRADITIONAL_PAYMENT_METHODS, ...CRYPTO_PAYMENT_METHODS]

// Get payment methods by type
export function getPaymentMethodsByType(type: 'traditional' | 'crypto' | 'all' = 'all') {
  if (type === 'all') return PAYMENT_METHODS
  return PAYMENT_METHODS.filter(method => method.type === type)
}

// Get payment method by ID
export function getPaymentMethodById(id: string) {
  return PAYMENT_METHODS.find(method => method.id === id)
}

// NowPayments API configuration
const NOWPAYMENTS_API_URL = 'https://api.nowpayments.io/v1'
const NOWPAYMENTS_API_KEY = import.meta.env.VITE_NOWPAYMENTS_API_KEY || ''

// Check if API key is configured
const isApiConfigured = () => {
  const hasKey = NOWPAYMENTS_API_KEY && NOWPAYMENTS_API_KEY.length > 0 && !NOWPAYMENTS_API_KEY.includes('your_')
  console.log('🔑 NOWPayments API Key Status:', {
    configured: hasKey ? 'YES' : 'NO',
    keyLength: NOWPAYMENTS_API_KEY?.length || 0,
    keyPreview: NOWPAYMENTS_API_KEY ? `${NOWPAYMENTS_API_KEY.substring(0, 8)}...` : 'NOT_SET'
  })
  return hasKey
}

export interface CryptoPayment {
  id: string
  user_id: string
  package_id: string
  diamonds: number
  price_usd: number
  cryptocurrency: string
  payment_address: string
  payment_amount: string
  payment_status: 'waiting' | 'confirming' | 'confirmed' | 'sending' | 'partially_paid' | 'finished' | 'failed' | 'refunded' | 'expired'
  payment_id: string
  created_at: string
  expires_at: string
  transaction_hash?: string
  confirmed_at?: string
}

class CryptoPaymentsService {
  // Create a new crypto payment
  async createPayment(userId: string, packageId: string, cryptocurrency: string, customAmount?: number): Promise<{ data: CryptoPayment | null, error: any }> {
    try {
      // Check if API is configured
      if (!isApiConfigured()) {
        return {
          data: null,
          error: 'Crypto payments not configured yet. Please contact support.'
        }
      }

      let package_info

      if (packageId === 'custom' && customAmount) {
        // Handle custom amounts - simple conversion (1 PHP = 1 diamond)
        const totalDiamonds = Math.floor(customAmount * 1)
        package_info = {
          id: 'custom',
          price_usd: customAmount / 55, // Convert PHP to USD
          total_diamonds: totalDiamonds,
          base_diamonds: totalDiamonds,
          bonus_diamonds: 0
        }
      } else {
        // Find the diamond package
        package_info = DIAMOND_PACKAGES.find(p => p.id === packageId)
        if (!package_info) {
          return { data: null, error: 'Invalid package ID' }
        }
      }

      // Create payment with NowPayments
      const paymentPayload = {
        price_amount: package_info.price_usd,
        price_currency: 'usd',
        pay_currency: cryptocurrency,
        order_id: `gambets_${userId}_${Date.now()}`,
        order_description: `${package_info.total_diamonds} Diamonds for Gambets`,
        ipn_callback_url: `${window.location.origin}/api/crypto-webhook`,
        success_url: `${window.location.origin}/dashboard?payment=success`,
        cancel_url: `${window.location.origin}/dashboard?payment=cancelled`
      }

      console.log('🚀 Creating NowPayments payment:', paymentPayload)

      const nowPaymentResponse = await fetch(`${NOWPAYMENTS_API_URL}/payment`, {
        method: 'POST',
        headers: {
          'x-api-key': NOWPAYMENTS_API_KEY,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(paymentPayload)
      })

      if (!nowPaymentResponse.ok) {
        const errorText = await nowPaymentResponse.text()
        console.error('❌ NowPayments API Error:', {
          status: nowPaymentResponse.status,
          statusText: nowPaymentResponse.statusText,
          response: errorText
        })
        throw new Error(`Failed to create payment with NowPayments: ${nowPaymentResponse.status} - ${errorText}`)
      }

      const nowPaymentData = await nowPaymentResponse.json()
      console.log('✅ NowPayments response:', nowPaymentData)

      // Store payment in our database
      const payment: Omit<CryptoPayment, 'id' | 'created_at'> = {
        user_id: userId,
        package_id: packageId,
        diamonds: package_info.total_diamonds,
        price_usd: package_info.price_usd,
        cryptocurrency: cryptocurrency,
        payment_address: nowPaymentData.pay_address,
        payment_amount: nowPaymentData.pay_amount,
        payment_status: 'waiting',
        payment_id: nowPaymentData.payment_id,
        expires_at: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes
      }

      const { data, error } = await supabase
        .from('crypto_payments')
        .insert([payment])
        .select()
        .single()

      if (error) throw error

      // Return the payment URL from NOWPayments
      return {
        data: {
          ...data,
          payment_url: nowPaymentData.invoice_url || nowPaymentData.payment_url,
          pay_address: nowPaymentData.pay_address,
          pay_amount: nowPaymentData.pay_amount,
          payment_id: nowPaymentData.payment_id
        },
        error: null
      }
    } catch (error) {
      console.error('Error creating crypto payment:', error)
      return { data: null, error }
    }
  }

  // Get payment status
  async getPaymentStatus(paymentId: string): Promise<{ data: any, error: any }> {
    try {
      if (!isApiConfigured()) {
        return { data: null, error: 'API not configured' }
      }

      const response = await fetch(`${NOWPAYMENTS_API_URL}/payment/${paymentId}`, {
        headers: {
          'x-api-key': NOWPAYMENTS_API_KEY,
        }
      })

      if (!response.ok) {
        throw new Error('Failed to get payment status')
      }

      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  // Get user's payment history
  async getUserPayments(userId: string): Promise<{ data: CryptoPayment[] | null, error: any }> {
    try {
      const { data, error } = await supabase
        .from('crypto_payments')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  // Process webhook (payment confirmation)
  async processWebhook(webhookData: any): Promise<{ success: boolean, error?: any }> {
    try {
      const { payment_id, payment_status, pay_amount: _pay_amount, pay_currency: _pay_currency } = webhookData

      // Update payment status in database
      const { data: payment, error: updateError } = await supabase
        .from('crypto_payments')
        .update({
          payment_status,
          confirmed_at: payment_status === 'finished' ? new Date().toISOString() : null
        })
        .eq('payment_id', payment_id)
        .select()
        .single()

      if (updateError) throw updateError

      // If payment is confirmed, add diamonds to user account
      if (payment_status === 'finished' && payment) {
        await this.addDiamondsToUser(payment.user_id, payment.diamonds)
        
        // Create transaction record
        await supabase
          .from('transactions')
          .insert([{
            user_id: payment.user_id,
            type: 'deposit',
            amount: payment.diamonds,
            balance_after: (await this.getUserBalance(payment.user_id)) + payment.diamonds,
            description: `Crypto deposit - ${payment.diamonds} diamonds`,
            status: 'completed',
            payment_method: 'crypto',
            payment_reference: payment_id
          }])
      }

      return { success: true }
    } catch (error) {
      console.error('Error processing webhook:', error)
      return { success: false, error }
    }
  }

  // Add diamonds to user account
  private async addDiamondsToUser(userId: string, diamonds: number): Promise<void> {
    const { error } = await supabase.rpc('add_diamonds', {
      user_id: userId,
      amount: diamonds
    })

    if (error) {
      console.error('Error adding diamonds to user:', error)
      throw error
    }
  }

  // Get user balance helper
  private async getUserBalance(userId: string): Promise<number> {
    const { data: user, error } = await supabase
      .from('users')
      .select('diamond_balance')
      .eq('id', userId)
      .single()

    if (error) return 0
    return user.diamond_balance || 0
  }

  // Get available cryptocurrencies from NowPayments
  async getAvailableCurrencies(): Promise<{ data: string[] | null, error: any }> {
    try {
      if (!isApiConfigured()) {
        // Return supported cryptos as fallback
        return { data: SUPPORTED_CRYPTOS.map(c => c.symbol), error: null }
      }

      const response = await fetch(`${NOWPAYMENTS_API_URL}/currencies`, {
        headers: {
          'x-api-key': NOWPAYMENTS_API_KEY,
        }
      })

      if (!response.ok) {
        throw new Error('Failed to get available currencies')
      }

      const data = await response.json()
      return { data: data.currencies, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }

  // Estimate payment amount in crypto
  async estimatePayment(priceUsd: number, cryptocurrency: string): Promise<{ data: any, error: any }> {
    try {
      if (!isApiConfigured()) {
        // Return approximate estimate as fallback
        return {
          data: {
            estimated_amount: priceUsd.toString(),
            currency_from: 'usd',
            currency_to: cryptocurrency
          },
          error: null
        }
      }

      const response = await fetch(`${NOWPAYMENTS_API_URL}/estimate?amount=${priceUsd}&currency_from=usd&currency_to=${cryptocurrency}`, {
        headers: {
          'x-api-key': NOWPAYMENTS_API_KEY,
        }
      })

      if (!response.ok) {
        throw new Error('Failed to estimate payment')
      }

      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}

export const cryptoPaymentsService = new CryptoPaymentsService()
export default cryptoPaymentsService
