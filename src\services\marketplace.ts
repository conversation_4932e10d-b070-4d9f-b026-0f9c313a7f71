// 🛒 MARKETPLACE SERVICES
// Supabase integration for gaming marketplace

import { supabase } from './supabase'

// Types
export interface MarketplaceListing {
  id: string
  seller_id: string
  title: string
  description: string
  game: string
  item_type: 'account' | 'skin' | 'item' | 'currency' | 'service' | 'other'

  // Account-specific fields
  account_level?: number
  rank?: string
  heroes_count?: number
  skins_count?: number
  game_id?: string // In-game ID/username
  current_password?: string // For account transfers
  email?: string // Associated email

  // Item-specific fields
  item_name?: string
  item_rarity?: 'common' | 'rare' | 'epic' | 'legendary' | 'mythic'
  item_condition?: 'new' | 'excellent' | 'good' | 'fair' | 'poor'
  quantity?: number

  // Service-specific fields
  service_type?: 'rank_boost' | 'coaching' | 'account_leveling' | 'skin_unlock' | 'other'
  completion_time?: string
  service_details?: any

  // Pricing
  price_diamonds: number
  original_price_diamonds?: number
  is_negotiable?: boolean

  // Listing management
  category: string
  status: 'active' | 'sold' | 'suspended' | 'expired' | 'draft'
  is_verified: boolean
  is_featured: boolean

  // Media and metadata
  images?: string[]
  tags?: string[]

  // Analytics
  views_count: number
  favorites_count: number
  inquiries_count: number

  // Timestamps
  created_at: string
  updated_at: string
  expires_at?: string

  // Joined data
  seller?: {
    username: string
    is_verified?: boolean
    rating?: number
    total_sales?: number
    seller_rating?: number
  }
}

export interface MarketplaceOrder {
  id: string
  listing_id: string
  buyer_id: string
  seller_id: string
  referee_id?: string

  // Order details
  quantity: number
  price_diamonds: number
  total_amount: number

  // Payment and fees
  payment_method?: 'diamonds' | 'gcash' | 'maya' | 'bank_transfer'
  escrow_amount: number
  platform_fee: number
  referee_fee: number
  seller_amount: number

  // Order status
  status: 'pending' | 'paid' | 'in_escrow' | 'delivered' | 'completed' | 'cancelled' | 'disputed' | 'refunded'

  // Delivery
  delivery_method?: 'account_transfer' | 'in_game_trade' | 'code_delivery' | 'service_completion'
  delivery_details?: any
  delivery_note?: string
  delivery_proof?: string[]

  // Confirmations and ratings
  buyer_confirmation: boolean
  buyer_rating?: number
  buyer_review?: string
  seller_confirmation: boolean
  seller_rating?: number
  seller_review?: string

  // Dispute handling
  dispute_reason?: string
  dispute_details?: string
  dispute_evidence?: string[]
  referee_decision?: string
  referee_notes?: string

  // Timestamps
  created_at: string
  updated_at: string
  paid_at?: string
  delivered_at?: string
  completed_at?: string
  disputed_at?: string
  resolved_at?: string
  auto_complete_at?: string

  // Joined data
  listing?: MarketplaceListing
  buyer?: { username: string; rating?: number }
  seller?: { username: string; rating?: number }
  referee?: { username: string }
}

export interface MarketplaceCategory {
  id: string
  name: string
  description?: string
  icon?: string
  color?: string
  is_active: boolean
  sort_order: number
  created_at?: string
}

export interface MarketplaceFavorite {
  id: string
  user_id: string
  listing_id: string
  created_at: string
}

export interface MarketplaceInquiry {
  id: string
  listing_id: string
  buyer_id: string
  seller_id: string
  message: string
  is_read: boolean
  response?: string
  response_at?: string
  created_at: string
}

export interface MarketplaceReport {
  id: string
  reporter_id: string
  listing_id?: string
  order_id?: string
  reported_user_id?: string
  report_type: 'fake_listing' | 'scam' | 'inappropriate_content' | 'price_manipulation' | 'account_theft' | 'harassment' | 'spam' | 'other'
  reason: string
  evidence?: string[]
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed'
  admin_notes?: string
  resolved_by?: string
  resolved_at?: string
  created_at: string
}

export interface UserMarketplaceStats {
  id: string
  user_id: string
  // Seller stats
  total_listings: number
  active_listings: number
  sold_listings: number
  seller_rating: number
  seller_reviews_count: number
  total_sales_diamonds: number
  // Buyer stats
  total_purchases: number
  buyer_rating: number
  buyer_reviews_count: number
  total_spent_diamonds: number
  // Reputation
  is_verified_seller: boolean
  is_trusted_buyer: boolean
  reputation_score: number
  // Activity
  last_listing_at?: string
  last_purchase_at?: string
  created_at: string
  updated_at: string
}

// Marketplace Service
export const marketplaceService = {
  // Get all active listings with filters
  async getListings(filters: {
    game?: string
    category?: string
    minPrice?: number
    maxPrice?: number
    search?: string
    sortBy?: 'price_asc' | 'price_desc' | 'newest' | 'popular'
    limit?: number
    offset?: number
  } = {}) {
    try {
      let query = supabase
        .from('marketplace_listings')
        .select(`
          *,
          seller:users!seller_id(username, is_verified)
        `)
        .eq('status', 'active')

      // Apply filters
      if (filters.game) {
        query = query.eq('game', filters.game)
      }
      if (filters.category) {
        query = query.eq('category', filters.category)
      }
      if (filters.minPrice) {
        query = query.gte('price_diamonds', filters.minPrice)
      }
      if (filters.maxPrice) {
        query = query.lte('price_diamonds', filters.maxPrice)
      }
      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
      }

      // Apply sorting
      switch (filters.sortBy) {
        case 'price_asc':
          query = query.order('price_diamonds', { ascending: true })
          break
        case 'price_desc':
          query = query.order('price_diamonds', { ascending: false })
          break
        case 'popular':
          query = query.order('views_count', { ascending: false })
          break
        case 'newest':
        default:
          query = query.order('created_at', { ascending: false })
          break
      }

      // Apply pagination
      if (filters.limit) {
        query = query.limit(filters.limit)
      }
      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1)
      }

      const { data, error } = await query

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get single listing by ID
  async getListing(id: string) {
    try {
      const { data, error } = await supabase
        .from('marketplace_listings')
        .select(`
          *,
          seller:users!seller_id(username, is_verified, diamond_balance)
        `)
        .eq('id', id)
        .single()

      if (error) throw error

      // Increment view count
      await supabase
        .from('marketplace_listings')
        .update({ views_count: (data.views_count || 0) + 1 })
        .eq('id', id)

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create new listing
  async createListing(listing: Omit<MarketplaceListing, 'id' | 'created_at' | 'updated_at' | 'views_count' | 'favorites_count' | 'seller'>) {
    try {
      const { data, error } = await supabase
        .from('marketplace_listings')
        .insert([listing])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update listing
  async updateListing(id: string, updates: Partial<MarketplaceListing>) {
    try {
      const { data, error } = await supabase
        .from('marketplace_listings')
        .update(updates)
        .eq('id', id)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Delete listing
  async deleteListing(id: string) {
    try {
      const { error } = await supabase
        .from('marketplace_listings')
        .delete()
        .eq('id', id)

      if (error) throw error
      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  // Get orders for statistics
  async getOrders() {
    try {
      const { data, error } = await supabase
        .from('marketplace_orders')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get categories
  async getCategories() {
    try {
      const { data, error } = await supabase
        .from('marketplace_categories')
        .select('*')
        .eq('is_active', true)
        .order('sort_order')

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create category
  async createCategory(category: Omit<MarketplaceCategory, 'id'>) {
    try {
      const { data, error } = await supabase
        .from('marketplace_categories')
        .insert([category])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create order
  async createOrder(order: {
    listing_id: string
    buyer_id: string
    seller_id: string
    price_diamonds: number
    payment_method?: string
  }) {
    try {
      // Calculate fees
      const platformFee = Math.floor(order.price_diamonds * 0.05) // 5% platform fee
      const refereeFee = Math.floor(order.price_diamonds * 0.02) // 2% referee fee
      const escrowAmount = order.price_diamonds

      const orderData = {
        ...order,
        escrow_amount: escrowAmount,
        platform_fee: platformFee,
        referee_fee: refereeFee,
        status: 'pending' as const
      }

      const { data, error } = await supabase
        .from('marketplace_orders')
        .insert([orderData])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user orders
  async getUserOrders(userId: string, type: 'buyer' | 'seller' | 'referee' = 'buyer') {
    try {
      const column = type === 'buyer' ? 'buyer_id' : type === 'seller' ? 'seller_id' : 'referee_id'
      
      const { data, error } = await supabase
        .from('marketplace_orders')
        .select(`
          *,
          listing:marketplace_listings(*),
          buyer:users!buyer_id(username),
          seller:users!seller_id(username),
          referee:users!referee_id(username)
        `)
        .eq(column, userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Update order status
  async updateOrderStatus(orderId: string, status: MarketplaceOrder['status'], updates: Partial<MarketplaceOrder> = {}) {
    try {
      const updateData = {
        status,
        ...updates,
        ...(status === 'completed' && { completed_at: new Date().toISOString() })
      }

      const { data, error } = await supabase
        .from('marketplace_orders')
        .update(updateData)
        .eq('id', orderId)
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Subscribe to new listings
  subscribeToListings(callback: (payload: any) => void) {
    return supabase
      .channel('marketplace_listings')
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'marketplace_listings'
      }, callback)
      .subscribe()
  },

  // Subscribe to order updates
  subscribeToOrders(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel('marketplace_orders')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'marketplace_orders',
        filter: `buyer_id=eq.${userId},seller_id=eq.${userId},referee_id=eq.${userId}`
      }, callback)
      .subscribe()
  }
}
