import { useState, useEffect } from 'react'
import { Shield, AlertTriangle, CheckCircle, TrendingUp, Users, Ban, Eye, Activity } from 'lucide-react'
import { supabase } from '../../services/supabaseClient'
import { refereeConflictService } from '../../services/refereeConflictService'

interface SystemStats {
  totalConflictsDetected: number
  conflictsResolvedToday: number
  activeReferees: number
  conflictFreeMatches: number
  bettingRestrictionsActive: number
  autoRecusalsToday: number
  systemUptime: number
  lastConflictCheck: string
}

interface ConflictTrend {
  date: string
  conflicts: number
  resolutions: number
}

export default function AntiCheatSystemStatus() {
  const [stats, setStats] = useState<SystemStats>({
    totalConflictsDetected: 0,
    conflictsResolvedToday: 0,
    activeReferees: 0,
    conflictFreeMatches: 0,
    bettingRestrictionsActive: 0,
    autoRecusalsToday: 0,
    systemUptime: 99.9,
    lastConflictCheck: new Date().toISOString()
  })
  const [trends, setTrends] = useState<ConflictTrend[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSystemStats()
    loadTrends()
    
    // Update stats every minute
    const interval = setInterval(() => {
      loadSystemStats()
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  const loadSystemStats = async () => {
    try {
      const today = new Date().toISOString().split('T')[0]
      
      // Get conflict logs count
      const { count: totalConflicts } = await supabase
        .from('referee_conflict_logs')
        .select('*', { count: 'exact', head: true })

      // Get today's resolutions
      const { count: todayResolutions } = await supabase
        .from('referee_recusals')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', `${today}T00:00:00Z`)

      // Get active referees
      const { count: activeReferees } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .or('referee_status.eq.approved,role.eq.referee')

      // Get conflict-free matches today
      const { count: conflictFreeMatches } = await supabase
        .from('matches')
        .select('*', { count: 'exact', head: true })
        .gte('created_at', `${today}T00:00:00Z`)
        .not('referee_id', 'is', null)

      // Get active betting restrictions
      const { count: bettingRestrictions } = await supabase
        .from('referee_betting_restrictions')
        .select('*', { count: 'exact', head: true })
        .gte('restriction_end', new Date().toISOString())

      // Get today's auto-recusals
      const { count: autoRecusals } = await supabase
        .from('referee_recusals')
        .select('*', { count: 'exact', head: true })
        .eq('recusal_type', 'automatic')
        .gte('created_at', `${today}T00:00:00Z`)

      setStats({
        totalConflictsDetected: totalConflicts || 0,
        conflictsResolvedToday: todayResolutions || 0,
        activeReferees: activeReferees || 0,
        conflictFreeMatches: conflictFreeMatches || 0,
        bettingRestrictionsActive: bettingRestrictions || 0,
        autoRecusalsToday: autoRecusals || 0,
        systemUptime: 99.9, // Would be calculated from actual uptime monitoring
        lastConflictCheck: new Date().toISOString()
      })

    } catch (error) {
      console.error('Error loading system stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadTrends = async () => {
    try {
      // Get last 7 days of conflict data
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const { data: conflictLogs } = await supabase
        .from('referee_conflict_logs')
        .select('checked_at')
        .gte('checked_at', sevenDaysAgo.toISOString())

      const { data: recusals } = await supabase
        .from('referee_recusals')
        .select('created_at')
        .gte('created_at', sevenDaysAgo.toISOString())

      // Group by date
      const trendMap = new Map<string, { conflicts: number; resolutions: number }>()
      
      // Initialize last 7 days
      for (let i = 6; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]
        trendMap.set(dateStr, { conflicts: 0, resolutions: 0 })
      }

      // Count conflicts by date
      conflictLogs?.forEach(log => {
        const date = log.checked_at.split('T')[0]
        const existing = trendMap.get(date)
        if (existing) {
          existing.conflicts++
        }
      })

      // Count resolutions by date
      recusals?.forEach(recusal => {
        const date = recusal.created_at.split('T')[0]
        const existing = trendMap.get(date)
        if (existing) {
          existing.resolutions++
        }
      })

      const trendsArray = Array.from(trendMap.entries()).map(([date, data]) => ({
        date,
        conflicts: data.conflicts,
        resolutions: data.resolutions
      }))

      setTrends(trendsArray)

    } catch (error) {
      console.error('Error loading trends:', error)
    }
  }

  const runSystemCheck = async () => {
    try {
      setLoading(true)
      
      // Get all active matches with referees
      const { data: matches } = await supabase
        .from('matches')
        .select('id, referee_id')
        .not('referee_id', 'is', null)
        .in('status', ['open', 'waiting', 'in_progress'])

      let conflictsFound = 0
      
      if (matches) {
        for (const match of matches) {
          const conflictResult = await refereeConflictService.checkRefereeConflict(
            match.referee_id,
            match.id
          )
          
          if (conflictResult.hasConflict) {
            conflictsFound++
            
            // Auto-recuse if enabled
            await refereeConflictService.autoRecuseReferee(
              match.referee_id,
              match.id,
              `System check: ${conflictResult.conflictDetails}`
            )
          }
        }
      }

      await loadSystemStats()
      
      alert(`System check complete. ${conflictsFound} conflicts found and resolved.`)
      
    } catch (error) {
      console.error('Error running system check:', error)
      alert('Error running system check. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-4 gap-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Anti-Cheat System Status</h3>
              <p className="text-sm text-gray-600">Real-time monitoring and conflict prevention</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-1 text-green-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">System Active</span>
            </div>
            <button
              onClick={runSystemCheck}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              Run System Check
            </button>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="p-6">
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-red-50 rounded-lg p-4 border border-red-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Conflicts Detected</p>
                <p className="text-2xl font-bold text-red-900">{stats.totalConflictsDetected}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4 border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Resolved Today</p>
                <p className="text-2xl font-bold text-green-900">{stats.conflictsResolvedToday}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Active Referees</p>
                <p className="text-2xl font-bold text-blue-900">{stats.activeReferees}</p>
              </div>
              <Users className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-purple-600">System Uptime</p>
                <p className="text-2xl font-bold text-purple-900">{stats.systemUptime}%</p>
              </div>
              <Activity className="w-8 h-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-orange-600">Auto-Recusals Today</p>
                <p className="text-2xl font-bold text-orange-900">{stats.autoRecusalsToday}</p>
              </div>
              <Ban className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div className="bg-teal-50 rounded-lg p-4 border border-teal-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-teal-600">Conflict-Free Matches</p>
                <p className="text-2xl font-bold text-teal-900">{stats.conflictFreeMatches}</p>
              </div>
              <CheckCircle className="w-8 h-8 text-teal-500" />
            </div>
          </div>

          <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-600">Betting Restrictions</p>
                <p className="text-2xl font-bold text-yellow-900">{stats.bettingRestrictionsActive}</p>
              </div>
              <Eye className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Last Check</p>
                <p className="text-sm font-bold text-gray-900">
                  {new Date(stats.lastConflictCheck).toLocaleTimeString()}
                </p>
              </div>
              <TrendingUp className="w-8 h-8 text-gray-500" />
            </div>
          </div>
        </div>

        {/* Trends Chart */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">7-Day Conflict Trends</h4>
          <div className="flex items-end space-x-2 h-32">
            {trends.map((trend) => (
              <div key={trend.date} className="flex-1 flex flex-col items-center">
                <div className="flex flex-col items-center space-y-1 mb-2">
                  <div 
                    className="w-full bg-red-500 rounded-t"
                    style={{ height: `${Math.max(trend.conflicts * 10, 4)}px` }}
                    title={`${trend.conflicts} conflicts`}
                  ></div>
                  <div 
                    className="w-full bg-green-500 rounded-b"
                    style={{ height: `${Math.max(trend.resolutions * 10, 4)}px` }}
                    title={`${trend.resolutions} resolutions`}
                  ></div>
                </div>
                <span className="text-xs text-gray-600">
                  {new Date(trend.date).toLocaleDateString('en-US', { weekday: 'short' })}
                </span>
              </div>
            ))}
          </div>
          <div className="flex items-center justify-center space-x-4 mt-2 text-xs">
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-red-500 rounded"></div>
              <span>Conflicts</span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-3 h-3 bg-green-500 rounded"></div>
              <span>Resolutions</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
