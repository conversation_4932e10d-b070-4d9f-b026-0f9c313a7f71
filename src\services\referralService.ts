import { supabase } from './supabaseClient'

export const referralService = {
  // Get user's referrals
  async getUserReferrals(userId: string) {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .select(`
          *,
          referred_user:users!referred_id(username, first_name, last_name, created_at, total_matches)
        `)
        .eq('referrer_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Create referral relationship
  async createReferral(referrerId: string, referredId: string) {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .insert([{
          referrer_id: referrerId,
          referred_id: referredId,
          commission_earned: 0,
          status: 'active'
        }])
        .select()
        .single()

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get referral stats
  async getReferralStats(userId: string) {
    try {
      const { data, error } = await supabase
        .from('referrals')
        .select('commission_earned, status')
        .eq('referrer_id', userId)

      if (error) throw error

      const stats = {
        totalReferrals: data?.length || 0,
        activeReferrals: data?.filter(r => r.status === 'active').length || 0,
        totalEarnings: data?.reduce((sum, r) => sum + r.commission_earned, 0) || 0
      }

      return { data: stats, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
