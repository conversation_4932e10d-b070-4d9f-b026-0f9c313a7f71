import React, { useState, useEffect } from 'react'
import {
  ShoppingCart,
  Search,
  Grid,
  List,
  Star,
  Eye,
  Heart,
  Gamepad2,
  Crown,
  Shield,
  Zap,
  UserPlus
} from 'lucide-react'
import { Link, useNavigate } from 'react-router-dom'
import { marketplaceService, MarketplaceListing, MarketplaceCategory } from '../../../services/marketplace'
import FilterBar from '../../../components/marketplace/FilterBar'

const PublicMarketplacePage: React.FC = () => {
  const navigate = useNavigate()
  const [listings, setListings] = useState<MarketplaceListing[]>([])
  const [categories, setCategories] = useState<MarketplaceCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [filters, setFilters] = useState({
    game: '',
    category: '',
    item_type: '',
    minPrice: '',
    maxPrice: '',
    search: '',
    sortBy: 'newest' as 'price_asc' | 'price_desc' | 'newest' | 'popular'
  })
  const [searchQuery, setSearchQuery] = useState('')

  // Load marketplace data
  useEffect(() => {
    loadMarketplaceData()
  }, [])

  const loadMarketplaceData = async () => {
    setIsLoading(true)
    try {
      // Load categories
      const { data: categoriesData } = await marketplaceService.getCategories()
      if (categoriesData) {
        setCategories(categoriesData)
      }

      // Load listings
      await loadListings()
    } catch (error) {
      console.error('Error loading marketplace data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const loadListings = async () => {
    try {
      const { data } = await marketplaceService.getListings({
        ...filters,
        minPrice: filters.minPrice ? parseFloat(filters.minPrice) : undefined,
        maxPrice: filters.maxPrice ? parseFloat(filters.maxPrice) : undefined
      })
      if (data) {
        setListings(data)
      }
    } catch (error) {
      console.error('Error loading listings:', error)
    }
  }

  // Reload listings when filters change
  useEffect(() => {
    if (!isLoading) {
      loadListings()
    }
  }, [filters])

  const handleFilterChange = (newFilters: typeof filters) => {
    setFilters(newFilters)
  }

  const handlePurchaseClick = (listing: MarketplaceListing) => {
    // Show auth required modal/redirect to signup
    navigate('/signup', {
      state: {
        from: 'marketplace',
        listingId: listing.id,
        listingTitle: listing.title,
        message: 'Sign up to purchase items safely with referee protection!'
      }
    })
  }

  // const handleViewDetails = (listing: MarketplaceListing) => { // TODO: Confirm usage
  //   // Show auth required modal for viewing details
  //   navigate('/login', {
  //     state: {
  //       from: 'marketplace',
  //       listingId: listing.id,
  //       message: 'Sign in to view full item details and seller information!'
  //     }
  //   })
  // }

  // const gameIcons = { // TODO: Confirm usage
  //   'Mobile Legends': Gamepad2,
  //   'Dota 2': Shield,
  //   'CS:GO': Zap,
  //   'Wild Rift': Crown,
  //   'Valorant': Star
  // }

  const filteredListings = listings.filter(listing => {
    if (searchQuery) {
      return listing.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
             listing.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
             listing.game.toLowerCase().includes(searchQuery.toLowerCase())
    }
    return true
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            <Link to="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <ShoppingCart className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Gambets</span>
            </Link>
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-gray-700 hover:text-blue-600 font-medium">Home</Link>
              <Link to="/browse" className="text-blue-600 font-medium">Marketplace</Link>
              <Link to="/leaderboards" className="text-gray-700 hover:text-blue-600 font-medium">Leaderboards</Link>
              <Link to="/rules" className="text-gray-700 hover:text-blue-600 font-medium">Rules</Link>
              <Link to="/support" className="text-gray-700 hover:text-blue-600 font-medium">Support</Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <button className="text-gray-700 hover:text-blue-600 font-medium">Login</button>
              </Link>
              <Link to="/signup">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                  Sign Up
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto p-4">
        {/* Header */}
        <div className="mb-6">
          <div className="text-center mb-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Gaming Marketplace</h1>
            <p className="text-gray-600">Browse premium gaming accounts and items</p>
            <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
              <p className="text-blue-800 font-medium">
                🎯 Found something you like? <Link to="/signup" className="underline hover:text-blue-600">Sign up</Link> to purchase with referee protection!
              </p>
            </div>
          </div>

          {/* Search Bar */}
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search accounts, games, or items..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'grid' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'list' ? 'bg-blue-600 text-white' : 'bg-white text-gray-600 hover:bg-gray-50'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Filter Bar */}
          <FilterBar
            filters={filters}
            onFilterChange={handleFilterChange}
            categories={categories}
          />
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading marketplace...</p>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredListings.length === 0 && (
          <div className="text-center py-12">
            <ShoppingCart className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No listings found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or filters</p>
            <Link
              to="/signup"
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors inline-flex items-center space-x-2"
            >
              <UserPlus className="w-5 h-5" />
              <span>Join to Post Listings</span>
            </Link>
          </div>
        )}

        {/* Listings Grid */}
        {!isLoading && filteredListings.length > 0 && (
          <div className={`grid gap-6 ${
            viewMode === 'grid'
              ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              : 'grid-cols-1'
          }`}>
            {filteredListings.map((listing) => (
              <PublicListingCard
                key={listing.id}
                listing={listing}
                onPurchaseClick={() => handlePurchaseClick(listing)}
                viewMode={viewMode}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Public Listing Card Component
const PublicListingCard: React.FC<{
  listing: MarketplaceListing
  onPurchaseClick: () => void
  viewMode: 'grid' | 'list'
}> = ({ listing, onPurchaseClick, viewMode }) => {
  const gameIcons = {
    'Mobile Legends': Gamepad2,
    'Dota 2': Shield,
    'CS:GO': Zap,
    'Wild Rift': Crown,
    'Valorant': Star
  }

  const GameIcon = gameIcons[listing.game as keyof typeof gameIcons] || Gamepad2

  if (viewMode === 'list') {
    return (
      <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            <div className="bg-blue-100 p-3 rounded-lg">
              <GameIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-gray-900 mb-1">{listing.title}</h3>
              <p className="text-sm text-gray-600 mb-2">{listing.game}</p>
              <div className="flex items-center space-x-4 text-sm text-gray-500">
                {listing.account_level && <span>Level {listing.account_level}</span>}
                {listing.rank && <span>{listing.rank}</span>}
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{listing.views_count || 0}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600 mb-2">
              {listing.price_diamonds.toLocaleString()} 💎
            </div>
            <button
              onClick={onPurchaseClick}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              Sign Up to Buy
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow overflow-hidden">
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="bg-blue-100 p-2 rounded-lg">
            <GameIcon className="w-5 h-5 text-blue-600" />
          </div>
          {listing.is_verified && (
            <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
              ✓ Verified
            </div>
          )}
        </div>

        <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{listing.title}</h3>
        <p className="text-sm text-gray-600 mb-3">{listing.game}</p>

        {/* Account Details */}
        <div className="space-y-1 mb-4 text-sm text-gray-600">
          {listing.account_level && <div>Level: {listing.account_level}</div>}
          {listing.rank && <div>Rank: {listing.rank}</div>}
          {listing.heroes_count && <div>Heroes: {listing.heroes_count}</div>}
          {listing.skins_count && <div>Skins: {listing.skins_count}</div>}
        </div>

        <div className="flex items-center justify-between mb-4 text-sm text-gray-500">
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-1">
              <Eye className="w-4 h-4" />
              <span>{listing.views_count || 0}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Heart className="w-4 h-4" />
              <span>{listing.favorites_count || 0}</span>
            </div>
          </div>
        </div>

        <div className="border-t pt-4">
          <div className="flex items-center justify-between">
            <div className="text-2xl font-bold text-blue-600">
              {listing.price_diamonds.toLocaleString()} 💎
            </div>
            <button
              onClick={onPurchaseClick}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors text-sm"
            >
              Sign Up to Buy
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PublicMarketplacePage
