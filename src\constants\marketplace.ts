// 🛒 MARKETPLACE CONSTANTS
// Games, categories, and item types for the marketplace

import {
  Gamepad2,
  Shield,
  Crown,
  Star,
  Zap,
  Sword,
  Trophy,
  Gem,
  Users,
  Target,
  Flame,
  Gift,
  Sparkles,
  Coins
} from 'lucide-react'

export const MARKETPLACE_GAMES = [
  // MOBA Games
  {
    id: 'mobile-legends',
    name: 'Mobile Legends',
    category: 'MOBA',
    icon: Gamepad2,
    color: 'bg-blue-500',
    ranks: ['Warrior', 'Elite', 'Master', 'Grandmaster', 'Epic', 'Legend', 'Mythic', 'Mythical Glory'],
    popular: true
  },
  {
    id: 'hok',
    name: 'Honor of Kings',
    category: 'MOBA',
    icon: Crown,
    color: 'bg-yellow-500',
    ranks: ['Bronze', 'Silver', 'Gold', 'Platinum', '<PERSON>', '<PERSON>', 'Grandma<PERSON>', 'King'],
    popular: true
  },
  {
    id: 'wild-rift',
    name: 'Wild Rift',
    category: 'MOBA',
    icon: Shield,
    color: 'bg-purple-500',
    ranks: ['Iron', 'Bronze', 'Silver', 'Gold', 'Platinum', '<PERSON>', 'Master', 'Grandma<PERSON>', '<PERSON>'],
    popular: true
  },
  {
    id: 'dota2',
    name: 'Dota 2',
    category: 'MO<PERSON>',
    icon: Shield,
    color: 'bg-red-500',
    ranks: ['Herald', 'Guardian', 'Crusader', 'Archon', 'Legend', 'Ancient', 'Divine', 'Immortal'],
    popular: false
  },

  // FPS Games
  {
    id: 'valorant',
    name: 'Valorant',
    category: 'FPS',
    icon: Target,
    color: 'bg-red-600',
    ranks: ['Iron', 'Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Ascendant', 'Immortal', 'Radiant'],
    popular: true
  },
  {
    id: 'csgo',
    name: 'CS:GO',
    category: 'FPS',
    icon: Zap,
    color: 'bg-orange-500',
    ranks: ['Silver', 'Gold Nova', 'Master Guardian', 'Distinguished Master Guardian', 'Legendary Eagle', 'Supreme', 'Global Elite'],
    popular: false
  },
  {
    id: 'codm',
    name: 'Call of Duty Mobile',
    category: 'FPS',
    icon: Target,
    color: 'bg-gray-700',
    ranks: ['Rookie', 'Veteran', 'Elite', 'Pro', 'Master', 'Grandmaster', 'Legendary'],
    popular: true
  },

  // Battle Royale
  {
    id: 'pubgm',
    name: 'PUBG Mobile',
    category: 'Battle Royale',
    icon: Target,
    color: 'bg-yellow-600',
    ranks: ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Crown', 'Ace', 'Conqueror'],
    popular: true
  },
  {
    id: 'freefire',
    name: 'Free Fire',
    category: 'Battle Royale',
    icon: Flame,
    color: 'bg-orange-600',
    ranks: ['Bronze', 'Silver', 'Gold', 'Platinum', 'Diamond', 'Heroic', 'Grand Master'],
    popular: true
  },

  // Sandbox/Creative
  {
    id: 'roblox',
    name: 'Roblox',
    category: 'Sandbox',
    icon: Gem,
    color: 'bg-green-500',
    ranks: [],
    popular: true
  },
  {
    id: 'minecraft',
    name: 'Minecraft',
    category: 'Sandbox',
    icon: Gem,
    color: 'bg-green-600',
    ranks: [],
    popular: true
  },

  // RPG/Gacha
  {
    id: 'genshin',
    name: 'Genshin Impact',
    category: 'RPG',
    icon: Star,
    color: 'bg-blue-400',
    ranks: ['Adventure Rank 1-60'],
    popular: true
  },
  {
    id: 'honkai',
    name: 'Honkai Impact 3rd',
    category: 'RPG',
    icon: Sparkles,
    color: 'bg-pink-500',
    ranks: ['Captain Level 1-88'],
    popular: false
  },

  // Other Popular Games
  {
    id: 'clash-royale',
    name: 'Clash Royale',
    category: 'Strategy',
    icon: Trophy,
    color: 'bg-blue-600',
    ranks: ['Arena 1-15', 'Challenger', 'Master', 'Champion'],
    popular: true
  },
  {
    id: 'clash-clans',
    name: 'Clash of Clans',
    category: 'Strategy',
    icon: Sword,
    color: 'bg-green-700',
    ranks: ['Town Hall 1-15'],
    popular: true
  }
]

export const ITEM_TYPES = [
  {
    id: 'account',
    name: 'Game Accounts',
    description: 'Complete game accounts with progress',
    icon: Users,
    color: 'bg-blue-500',
    fields: ['account_level', 'rank', 'heroes_count', 'skins_count']
  },
  {
    id: 'skin',
    name: 'Skins & Cosmetics',
    description: 'Character skins, weapon skins, and cosmetic items',
    icon: Sparkles,
    color: 'bg-purple-500',
    fields: ['item_name', 'item_rarity', 'item_condition']
  },
  {
    id: 'item',
    name: 'In-Game Items',
    description: 'Weapons, equipment, and consumable items',
    icon: Gem,
    color: 'bg-green-500',
    fields: ['item_name', 'item_rarity', 'quantity', 'item_condition']
  },
  {
    id: 'currency',
    name: 'Game Currency',
    description: 'In-game coins, gems, and premium currency',
    icon: Coins,
    color: 'bg-yellow-500',
    fields: ['item_name', 'quantity']
  },
  {
    id: 'service',
    name: 'Gaming Services',
    description: 'Boosting, coaching, and account services',
    icon: Trophy,
    color: 'bg-orange-500',
    fields: ['service_type', 'completion_time']
  },
  {
    id: 'other',
    name: 'Other Items',
    description: 'Gift cards, codes, and miscellaneous items',
    icon: Gift,
    color: 'bg-gray-500',
    fields: ['item_name', 'item_condition']
  }
]

export const MARKETPLACE_CATEGORIES = [
  {
    id: 'moba',
    name: 'MOBA Games',
    description: 'Mobile Legends, Honor of Kings, Wild Rift',
    games: ['mobile-legends', 'hok', 'wild-rift', 'dota2']
  },
  {
    id: 'fps',
    name: 'FPS Games',
    description: 'Valorant, CS:GO, Call of Duty Mobile',
    games: ['valorant', 'csgo', 'codm']
  },
  {
    id: 'battle-royale',
    name: 'Battle Royale',
    description: 'PUBG Mobile, Free Fire',
    games: ['pubgm', 'freefire']
  },
  {
    id: 'sandbox',
    name: 'Sandbox Games',
    description: 'Roblox, Minecraft',
    games: ['roblox', 'minecraft']
  },
  {
    id: 'rpg',
    name: 'RPG Games',
    description: 'Genshin Impact, Honkai Impact',
    games: ['genshin', 'honkai']
  },
  {
    id: 'strategy',
    name: 'Strategy Games',
    description: 'Clash Royale, Clash of Clans',
    games: ['clash-royale', 'clash-clans']
  }
]

export const RARITY_LEVELS = [
  { id: 'common', name: 'Common', color: 'text-gray-600' },
  { id: 'uncommon', name: 'Uncommon', color: 'text-green-600' },
  { id: 'rare', name: 'Rare', color: 'text-blue-600' },
  { id: 'epic', name: 'Epic', color: 'text-purple-600' },
  { id: 'legendary', name: 'Legendary', color: 'text-yellow-600' },
  { id: 'mythic', name: 'Mythic', color: 'text-red-600' }
]

export const SERVICE_TYPES = [
  'Rank Boosting',
  'Account Leveling',
  'Coaching/Training',
  'Achievement Completion',
  'Event Participation',
  'Custom Services'
]

export const ITEM_CONDITIONS = [
  'New',
  'Like New',
  'Good',
  'Fair',
  'Used'
]
