// 🚀 SIMPLE ADMIN ACCESS
// Direct admin access for testing and setup

import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Crown, Shield, CheckCircle, AlertTriangle, Loader2 } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'

const SimpleAdminAccess: React.FC = () => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [isChecking, setIsChecking] = useState(true)
  const [adminStatus, setAdminStatus] = useState<any>(null)
  const [error, setError] = useState('')

  useEffect(() => {
    checkAdminAccess()
  }, [user])

  const checkAdminAccess = async () => {
    try {
      setIsChecking(true)
      setError('')

      if (!user) {
        setError('You must be logged in to access admin features.')
        setIsChecking(false)
        return
      }

      // Check admin status
      const isAdmin = await AdminService.isAdmin(user.id)
      const adminUser = await AdminService.getAdminUser(user.id)

      setAdminStatus({
        isAdmin,
        adminUser,
        user: {
          id: user.id,
          email: user.email,
          user_metadata: user.user_metadata
        }
      })

      if (isAdmin && adminUser && adminUser.admin_level >= 2) {
        // Log admin access
        await AdminService.logAdminAction(
          user.id,
          'ADMIN_ACCESS_GRANTED',
          'system',
          user.id,
          { access_method: 'simple_admin_access' }
        )
      }

    } catch (error: any) {
      console.error('Error checking admin access:', error)
      setError('Failed to verify admin access. Please try again.')
    } finally {
      setIsChecking(false)
    }
  }

  const grantAdminAccess = () => {
    if (adminStatus?.isAdmin && adminStatus?.adminUser?.admin_level >= 2) {
      navigate('/admin-dashboard-2024')
    }
  }

  if (isChecking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
          <Loader2 className="w-12 h-12 text-blue-400 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">Checking Admin Access</h2>
          <p className="text-blue-200">Verifying your administrative privileges...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Crown className="w-10 h-10 text-black font-bold" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Admin Access</h1>
          <p className="text-blue-200">Administrative privilege verification</p>
        </div>

        {/* User Info */}
        {user && (
          <div className="bg-white/10 rounded-lg p-4 mb-6">
            <h3 className="text-white font-medium mb-2">Current User</h3>
            <p className="text-blue-200 text-sm">{user.email}</p>
            <p className="text-blue-300 text-xs">{user.user_metadata?.username || 'No username'}</p>
          </div>
        )}

        {/* Admin Status */}
        {adminStatus && (
          <div className="space-y-4 mb-6">
            {/* Admin Check */}
            <div className={`flex items-center space-x-3 p-3 rounded-lg ${
              adminStatus.isAdmin ? 'bg-green-500/20 border border-green-400/30' : 'bg-red-500/20 border border-red-400/30'
            }`}>
              {adminStatus.isAdmin ? (
                <CheckCircle className="w-5 h-5 text-green-400" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-red-400" />
              )}
              <div>
                <p className={`font-medium ${adminStatus.isAdmin ? 'text-green-300' : 'text-red-300'}`}>
                  {adminStatus.isAdmin ? 'Admin Status: Verified' : 'Admin Status: Not Found'}
                </p>
                <p className={`text-xs ${adminStatus.isAdmin ? 'text-green-200' : 'text-red-200'}`}>
                  {adminStatus.isAdmin ? 'You have administrative privileges' : 'No admin privileges found'}
                </p>
              </div>
            </div>

            {/* Admin Level */}
            {adminStatus.adminUser && (
              <div className={`flex items-center space-x-3 p-3 rounded-lg ${
                adminStatus.adminUser.admin_level >= 2 ? 'bg-purple-500/20 border border-purple-400/30' : 'bg-yellow-500/20 border border-yellow-400/30'
              }`}>
                <Shield className={`w-5 h-5 ${adminStatus.adminUser.admin_level >= 2 ? 'text-purple-400' : 'text-yellow-400'}`} />
                <div>
                  <p className={`font-medium ${adminStatus.adminUser.admin_level >= 2 ? 'text-purple-300' : 'text-yellow-300'}`}>
                    Admin Level: {adminStatus.adminUser.admin_level}
                  </p>
                  <p className={`text-xs ${adminStatus.adminUser.admin_level >= 2 ? 'text-purple-200' : 'text-yellow-200'}`}>
                    {adminStatus.adminUser.admin_level >= 3 ? 'Super Admin' : 
                     adminStatus.adminUser.admin_level >= 2 ? 'Platform Admin' : 
                     adminStatus.adminUser.admin_level >= 1 ? 'Referee Coordinator' : 'Insufficient Level'}
                  </p>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 text-red-300">
              <AlertTriangle className="w-5 h-5" />
              <span className="font-medium">Error</span>
            </div>
            <p className="text-red-200 text-sm mt-1">{error}</p>
          </div>
        )}

        {/* Actions */}
        <div className="space-y-3">
          {!user ? (
            <a
              href="/login"
              className="w-full bg-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-blue-700 transition-colors text-center block"
            >
              Login to Gambets
            </a>
          ) : adminStatus?.isAdmin && adminStatus?.adminUser?.admin_level >= 2 ? (
            <button
              onClick={grantAdminAccess}
              className="w-full bg-gradient-to-r from-green-600 to-blue-600 text-white py-3 px-6 rounded-lg font-medium hover:from-green-700 hover:to-blue-700 transition-all"
            >
              Access Admin Dashboard
            </button>
          ) : (
            <div className="text-center">
              <p className="text-red-300 text-sm mb-3">
                You don't have sufficient admin privileges to access the dashboard.
              </p>
              <a
                href="/"
                className="text-blue-300 hover:text-white transition-colors text-sm"
              >
                Return to Main Site
              </a>
            </div>
          )}

          <button
            onClick={checkAdminAccess}
            className="w-full bg-white/10 text-white py-2 px-4 rounded-lg font-medium hover:bg-white/20 transition-colors"
          >
            Refresh Status
          </button>
        </div>

        {/* Debug Info */}
        {adminStatus && (
          <div className="mt-6 p-4 bg-black/20 rounded-lg">
            <h4 className="text-white text-sm font-medium mb-2">Debug Info:</h4>
            <pre className="text-xs text-blue-200 overflow-x-auto">
              {JSON.stringify({
                isAdmin: adminStatus.isAdmin,
                adminLevel: adminStatus.adminUser?.admin_level || 0,
                role: adminStatus.adminUser?.role || 'user',
                email: adminStatus.user.email
              }, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}

export default SimpleAdminAccess
