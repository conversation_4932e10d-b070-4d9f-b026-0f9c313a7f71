import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>, <PERSON>, CheckCircle, AlertCircle, Zap } from 'lucide-react'
import { DIAMOND_PACKAGES, SUPPORTED_CRYPTOS, cryptoPaymentsService, CryptoPayment } from '../../services/cryptoPayments'
import { useAuth } from '../../contexts/AuthContext'

interface CryptoPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  packageId?: string
}

const CryptoPaymentModal: React.FC<CryptoPaymentModalProps> = ({
  isOpen,
  onClose,
  packageId = 'popular'
}) => {
  const { user } = useAuth()
  const [selectedCrypto, setSelectedCrypto] = useState('usdttrc20')
  const [payment, setPayment] = useState<CryptoPayment | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [timeLeft, setTimeLeft] = useState<number>(0)
  const [copied, setCopied] = useState<{ address: boolean; amount: boolean }>({ address: false, amount: false })

  const selectedPackage = DIAMOND_PACKAGES.find(p => p.id === packageId)
  const selectedCryptoInfo = SUPPORTED_CRYPTOS.find(c => c.symbol === selectedCrypto)

  // Timer for payment expiration
  useEffect(() => {
    if (payment && payment.expires_at) {
      const interval = setInterval(() => {
        const now = new Date().getTime()
        const expiry = new Date(payment.expires_at).getTime()
        const remaining = Math.max(0, expiry - now)
        setTimeLeft(remaining)
        
        if (remaining === 0) {
          clearInterval(interval)
        }
      }, 1000)

      return () => clearInterval(interval)
    }
  }, [payment])

  // Format time remaining
  const formatTimeLeft = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  // Create payment
  const handleCreatePayment = async () => {
    if (!user || !selectedPackage) return

    setIsCreating(true)
    setError(null)

    try {
      const { data, error } = await cryptoPaymentsService.createPayment(
        user.id,
        packageId,
        selectedCrypto
      )

      if (error) {
        // If API not configured, show demo mode
        if (error.includes('not configured')) {
          setError('Demo Mode: Crypto payments will be available after NowPayments setup. Check the setup guide!')
        } else {
          throw error
        }
      } else if (data) {
        setPayment(data)
      }
    } catch (err: any) {
      setError(err.message || 'Failed to create payment')
    } finally {
      setIsCreating(false)
    }
  }

  // Copy to clipboard
  const copyToClipboard = async (text: string, type: 'address' | 'amount') => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(prev => ({ ...prev, [type]: true }))
      setTimeout(() => {
        setCopied(prev => ({ ...prev, [type]: false }))
      }, 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  // Reset modal state when closed
  useEffect(() => {
    if (!isOpen) {
      setPayment(null)
      setError(null)
      setTimeLeft(0)
      setCopied({ address: false, amount: false })
    }
  }, [isOpen])

  if (!isOpen || !selectedPackage) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-bold text-gray-900">Buy Diamonds</h2>
            <p className="text-sm text-gray-600">Pay with cryptocurrency</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        <div className="p-6">
          {/* Package Info */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-semibold text-gray-900 capitalize">{selectedPackage.id} Package</h3>
              {selectedPackage.popular && (
                <span className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              )}
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-blue-600">{selectedPackage.total_diamonds} 💎</p>
                {selectedPackage.bonus_diamonds > 0 && (
                  <p className="text-sm text-green-600">+{selectedPackage.bonus_diamonds} bonus diamonds!</p>
                )}
              </div>
              <div className="text-right">
                <p className="text-2xl font-bold text-gray-900">${selectedPackage.price_usd}</p>
                {selectedPackage.savings > 0 && (
                  <p className="text-sm text-green-600">{selectedPackage.savings}% savings</p>
                )}
              </div>
            </div>
          </div>

          {!payment ? (
            <>
              {/* Crypto Selection */}
              <div className="mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Choose Cryptocurrency</h4>
                <div className="space-y-2">
                  {SUPPORTED_CRYPTOS.map((crypto) => (
                    <button
                      key={crypto.symbol}
                      onClick={() => setSelectedCrypto(crypto.symbol)}
                      className={`w-full p-3 rounded-lg border-2 transition-all ${
                        selectedCrypto === crypto.symbol
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-xl">{crypto.icon}</span>
                          <div className="text-left">
                            <p className="font-medium text-gray-900">{crypto.name}</p>
                            <p className="text-sm text-gray-600">{crypto.network}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium text-gray-900">{crypto.fee_estimate}</p>
                          <p className="text-xs text-gray-600">{crypto.confirmation_time}</p>
                          {crypto.recommended && (
                            <span className="inline-flex items-center space-x-1 text-xs text-green-600">
                              <Zap className="w-3 h-3" />
                              <span>Recommended</span>
                            </span>
                          )}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              </div>

              {/* Create Payment Button */}
              <button
                onClick={handleCreatePayment}
                disabled={isCreating}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
              >
                {isCreating ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Creating Payment...</span>
                  </>
                ) : (
                  <>
                    <span>Create Payment</span>
                  </>
                )}
              </button>
            </>
          ) : (
            <>
              {/* Payment Instructions */}
              <div className="space-y-4">
                {/* Timer */}
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-5 h-5 text-orange-600" />
                    <span className="font-medium text-orange-800">
                      Payment expires in: {formatTimeLeft(timeLeft)}
                    </span>
                  </div>
                </div>

                {/* Payment Address */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Send {selectedCryptoInfo?.name} to this address:
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={payment.payment_address}
                      readOnly
                      className="flex-1 p-3 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm"
                    />
                    <button
                      onClick={() => copyToClipboard(payment.payment_address, 'address')}
                      className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                    >
                      {copied.address ? <CheckCircle className="w-5 h-5" /> : <Copy className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                {/* Payment Amount */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Exact amount to send:
                  </label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={`${payment.payment_amount} ${selectedCryptoInfo?.name}`}
                      readOnly
                      className="flex-1 p-3 border border-gray-300 rounded-lg bg-gray-50 font-mono text-sm"
                    />
                    <button
                      onClick={() => copyToClipboard(payment.payment_amount, 'amount')}
                      className="p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                    >
                      {copied.amount ? <CheckCircle className="w-5 h-5" /> : <Copy className="w-5 h-5" />}
                    </button>
                  </div>
                </div>

                {/* Status */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="w-5 h-5 text-blue-600" />
                    <div>
                      <p className="font-medium text-blue-800">Payment Status: {payment.payment_status}</p>
                      <p className="text-sm text-blue-600">
                        Send the exact amount to the address above. Diamonds will be added automatically after confirmation.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Important Notes */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h5 className="font-medium text-yellow-800 mb-2">Important:</h5>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Send only {selectedCryptoInfo?.name} to this address</li>
                    <li>• Send the exact amount shown above</li>
                    <li>• Payment will expire in 30 minutes</li>
                    <li>• Diamonds will be added after 1-3 confirmations</li>
                  </ul>
                </div>
              </div>
            </>
          )}

          {/* Error Message */}
          {error && (
            <div className="mt-4 bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CryptoPaymentModal
