import { useState, useEffect } from 'react'
import { Users, UserPlus, Check, X, Search, MessageCircle } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'
// TODO: platformService not yet extracted - using old import temporarily
import { platformService } from '../../services/supabase'

interface Friend {
  id: string
  username: string
  avatar_url?: string
  is_online?: boolean
  last_seen?: string
}

interface FriendRequest {
  id: string
  sender_id: string
  sender_username: string
  sender_avatar?: string
  created_at: string
}

export default function FriendsSystem() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [friends, setFriends] = useState<Friend[]>([])
  const [friendRequests, setFriendRequests] = useState<FriendRequest[]>([])
  const [searchUsername, setSearchUsername] = useState('')
  const [loading, setLoading] = useState(true)
  const [sendingRequest, setSendingRequest] = useState(false)
  const [activeTab, setActiveTab] = useState<'friends' | 'requests'>('friends')

  useEffect(() => {
    if (user?.id) {
      loadFriends()
      loadFriendRequests()
    }
  }, [user])

  const loadFriends = async () => {
    try {
      // This would need to be implemented in the backend
      // For now, we'll simulate it
      setFriends([])
    } catch (error) {
      console.error('Error loading friends:', error)
    }
  }

  const loadFriendRequests = async () => {
    try {
      // This would need to be implemented in the backend
      // For now, we'll simulate it
      setFriendRequests([])
    } catch (error) {
      console.error('Error loading friend requests:', error)
    } finally {
      setLoading(false)
    }
  }

  const sendFriendRequest = async () => {
    if (!user?.username || !searchUsername.trim()) return

    setSendingRequest(true)
    try {
      const result = await platformService.sendFriendRequest(user.username, searchUsername.trim())
      
      if (result.error) {
        addNotification({
          type: 'error',
          title: 'Request Failed',
          message: (result.error as any)?.message || 'Failed to send friend request'
        })
      } else {
        addNotification({
          type: 'success',
          title: 'Request Sent',
          message: `Friend request sent to ${searchUsername}`
        })
        setSearchUsername('')
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message || 'Failed to send friend request'
      })
    } finally {
      setSendingRequest(false)
    }
  }

  const acceptFriendRequest = async (requestId: string) => {
    if (!user?.id) return

    try {
      const result = await platformService.acceptFriendRequest(requestId, user.id)
      
      if (result.error) {
        addNotification({
          type: 'error',
          title: 'Accept Failed',
          message: (result.error as any)?.message || 'Failed to accept friend request'
        })
      } else {
        addNotification({
          type: 'success',
          title: 'Friend Added',
          message: 'Friend request accepted successfully'
        })
        loadFriends()
        loadFriendRequests()
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message || 'Failed to accept friend request'
      })
    }
  }

  const formatLastSeen = (lastSeen: string) => {
    const date = new Date(lastSeen)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    
    const diffInHours = Math.floor(diffInMinutes / 60)
    if (diffInHours < 24) return `${diffInHours}h ago`
    
    const diffInDays = Math.floor(diffInHours / 24)
    return `${diffInDays}d ago`
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <Users className="w-5 h-5 text-blue-600" />
            <span>Friends</span>
          </h2>
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('friends')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'friends'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              Friends ({friends.length})
            </button>
            <button
              onClick={() => setActiveTab('requests')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'requests'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              Requests ({friendRequests.length})
            </button>
          </div>
        </div>
      </div>

      {/* Add Friend Section */}
      <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
        <div className="flex space-x-3">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Enter username to add friend"
              value={searchUsername}
              onChange={(e) => setSearchUsername(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendFriendRequest()}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <button
            onClick={sendFriendRequest}
            disabled={!searchUsername.trim() || sendingRequest}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            <UserPlus className="w-4 h-4" />
            <span>{sendingRequest ? 'Sending...' : 'Add'}</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="max-h-96 overflow-y-auto">
        {activeTab === 'friends' ? (
          // Friends List
          friends.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Friends Yet</h3>
              <p className="text-gray-600">Add friends to see their activities and play together!</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {friends.map((friend) => (
                <div key={friend.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                          <span className="text-white font-semibold">
                            {friend.username.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        {friend.is_online && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{friend.username}</p>
                        <p className="text-xs text-gray-500">
                          {friend.is_online ? 'Online' : `Last seen ${formatLastSeen(friend.last_seen || '')}`}
                        </p>
                      </div>
                    </div>
                    <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50">
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )
        ) : (
          // Friend Requests
          friendRequests.length === 0 ? (
            <div className="text-center py-12">
              <UserPlus className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Friend Requests</h3>
              <p className="text-gray-600">Friend requests will appear here.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {friendRequests.map((request) => (
                <div key={request.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center">
                        <span className="text-white font-semibold">
                          {request.sender_username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">{request.sender_username}</p>
                        <p className="text-xs text-gray-500">
                          {formatLastSeen(request.created_at)}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button
                        onClick={() => acceptFriendRequest(request.id)}
                        className="p-2 text-green-600 hover:bg-green-50 rounded-lg"
                      >
                        <Check className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg">
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )
        )}
      </div>
    </div>
  )
}
