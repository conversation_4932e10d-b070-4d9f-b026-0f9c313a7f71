import { useState, useEffect } from 'react'
import {
  Trophy, Calendar, Filter, Search, Bell, Play, Gem, Info,
  MessageCircle, UserPlus, Loader2
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'
// TODO: tournamentService not yet extracted - using old import temporarily
import { tournamentService } from '../../../services/supabase'
// import TournamentBracket from '../../../components/tournaments/TournamentBracket' // TODO: Confirm usage

export default function TournamentsPage() {
  const { user } = useAuth()

  // Compact state management
  const [activeTab, setActiveTab] = useState<'live' | 'upcoming' | 'joined'>('live')
  const [searchTerm, setSearchTerm] = useState('')
  const [, setSelectedTournament] = useState<string | null>(null) // selectedTournament unused, keeping setter
  const [, setShowTournamentModal] = useState(false) // showTournamentModal unused, keeping setter
  const [showFilters, setShowFilters] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [tournaments, setTournaments] = useState<any[]>([])

  const [] = useState({ // filters and setter unused
    game: 'Mobile Legends',
    status: '',
    entryFee: ''
  })

  // Helper functions
  const getGameIcon = (game: string) => {
    switch (game) {
      case 'Mobile Legends': return '🏆'
      case 'Valorant': return '🎯'
      case 'Dota 2': return '⚔️'
      case 'Call of Duty': return '🔫'
      case 'Roblox': return '🎮'
      default: return '🎮'
    }
  }

  const getCurrentRound = (tournament: any) => {
    const now = new Date()
    const regStart = new Date(tournament.registration_start)
    const regEnd = new Date(tournament.registration_end)
    const tournStart = new Date(tournament.tournament_start)

    if (now < regStart) return 'Registration Opens Soon'
    if (now >= regStart && now < regEnd) return 'Registration Open'
    if (now >= regEnd && now < tournStart) return 'Registration Closed'
    if (tournament.status === 'live') return 'Tournament Live'
    if (tournament.status === 'completed') return 'Tournament Completed'
    return 'Upcoming'
  }

  const checkUserJoinedTournament = async (tournamentId: string, userId?: string) => {
    if (!userId) return false
    try {
      const { data } = await tournamentService.getUserTournamentParticipation(tournamentId, userId)
      return !!data
    } catch {
      return false
    }
  }

  const createSampleTournaments = async () => {
    try {
      const sampleTournaments = [
        {
          title: 'ML World Championship',
          game: 'Mobile Legends',
          prize_pool: 25000,
          entry_fee: 100,
          max_participants: 32,
          current_participants: 0,
          status: 'upcoming',
          format: 'BO3 Elimination',
          description: 'The ultimate Mobile Legends tournament with the biggest prize pool!',
          rules: 'Standard ML tournament rules apply. No cheating, no toxicity.',
          registration_start: new Date().toISOString(),
          registration_end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          tournament_start: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
          tournament_end: new Date(Date.now() + 12 * 24 * 60 * 60 * 1000).toISOString(),
          organizer_id: user?.id
        },
        {
          title: 'Weekly ML Cup',
          game: 'Mobile Legends',
          prize_pool: 5000,
          entry_fee: 50,
          max_participants: 16,
          current_participants: 0,
          status: 'upcoming',
          format: 'BO1 Swiss',
          description: 'Weekly tournament for casual and competitive players.',
          rules: 'Fair play required. Report any issues to referees.',
          registration_start: new Date().toISOString(),
          registration_end: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
          tournament_start: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          tournament_end: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toISOString(),
          organizer_id: user?.id
        },
        {
          title: 'Valorant Champions Series',
          game: 'Valorant',
          prize_pool: 15000,
          entry_fee: 75,
          max_participants: 24,
          current_participants: 0,
          status: 'upcoming',
          format: 'BO5 Finals',
          description: 'Competitive Valorant tournament for skilled players.',
          rules: 'Valorant competitive rules. Anti-cheat required.',
          registration_start: new Date().toISOString(),
          registration_end: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          tournament_start: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000).toISOString(),
          tournament_end: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
          organizer_id: user?.id
        }
      ]

      for (const tournament of sampleTournaments) {
        await tournamentService.createTournament(tournament)
      }

      // Reload tournaments after creating samples
      const { data } = await tournamentService.getTournaments()
      if (data) {
        const transformedTournaments = data.map((tournament: any) => ({
          id: tournament.id,
          title: tournament.title,
          game: tournament.game,
          gameIcon: getGameIcon(tournament.game),
          prizePool: tournament.prize_pool,
          entryFee: tournament.entry_fee,
          maxTeams: tournament.max_participants,
          currentTeams: tournament.current_participants || 0,
          status: tournament.status,
          currentRound: getCurrentRound(tournament),
          nextMatch: tournament.tournament_start,
          format: tournament.format,
          isJoined: false,
          myTeamStatus: 'not_joined',
          referee: tournament.organizer?.username || 'System',
          teams: [],
          description: tournament.description,
          rules: tournament.rules,
          registrationStart: tournament.registration_start,
          registrationEnd: tournament.registration_end,
          tournamentStart: tournament.tournament_start,
          tournamentEnd: tournament.tournament_end
        }))
        setTournaments(transformedTournaments)
      }
    } catch (error) {
      console.error('Error creating sample tournaments:', error)
    }
  }

  // Load tournaments data on mount
  useEffect(() => {
    const loadTournaments = async () => {
      setIsLoading(true)
      try {
        const { data, error } = await tournamentService.getTournaments()
        if (error) {
          console.error('Error loading tournaments:', error)
          // If no tournaments exist, create some sample ones
          await createSampleTournaments()
        } else if (data && data.length > 0) {
          // Transform Supabase data to match our interface
          const transformedTournaments = await Promise.all((data || []).map(async (tournament: any) => {
            // Check if user has joined this tournament
            const isUserJoined = await checkUserJoinedTournament(tournament.id, user?.id)

            return {
              id: tournament.id,
              title: tournament.title,
              game: tournament.game,
              gameIcon: getGameIcon(tournament.game),
              prizePool: tournament.prize_pool,
              entryFee: tournament.entry_fee,
              maxTeams: tournament.max_participants,
              currentTeams: tournament.current_participants || 0,
              status: tournament.status,
              currentRound: getCurrentRound(tournament),
              nextMatch: tournament.tournament_start,
              format: tournament.format,
              isJoined: isUserJoined,
              myTeamStatus: isUserJoined ? 'registered' : 'not_joined',
              referee: tournament.organizer?.username || 'System',
              teams: [], // This would come from tournament_participants
              description: tournament.description,
              rules: tournament.rules,
              registrationStart: tournament.registration_start,
              registrationEnd: tournament.registration_end,
              tournamentStart: tournament.tournament_start,
              tournamentEnd: tournament.tournament_end
            }
          }))
          setTournaments(transformedTournaments)
        } else {
          // No tournaments exist, create sample ones
          await createSampleTournaments()
        }
      } catch (error) {
        console.error('Error loading tournaments:', error)
        // Fallback to creating sample tournaments
        await createSampleTournaments()
      } finally {
        setIsLoading(false)
      }
    }

    loadTournaments()
  }, [user?.id])

  // Filter tournaments based on active tab
  const filteredTournaments = tournaments.filter(tournament => {
    if (activeTab === 'live') return tournament.status === 'ongoing'
    if (activeTab === 'upcoming') return tournament.status === 'upcoming' || tournament.status === 'registration'
    if (activeTab === 'joined') return tournament.isJoined
    return true
  }).filter(tournament => {
    if (searchTerm) {
      return tournament.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
             tournament.game.toLowerCase().includes(searchTerm.toLowerCase())
    }
    return true
  })

  // Tournament actions
  const handleJoinTournament = async (tournamentId: string) => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      const { error } = await tournamentService.joinTournament(tournamentId, user.id)

      if (error) {
        console.error('Error joining tournament:', error)
        // Use proper notification system instead of alert
        return
      }

      // Update local state
      setTournaments(prev => prev.map(tournament =>
        tournament.id === tournamentId
          ? { ...tournament, isJoined: true, currentTeams: tournament.currentTeams + 1, myTeamStatus: 'registered' }
          : tournament
      ))

      // Tournament joined successfully - could add notification here
    } catch (error) {
      console.error('Error joining tournament:', error)
      // Could add error notification here
    } finally {
      setIsLoading(false)
    }
  }

  const handleLeaveTournament = async (tournamentId: string) => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      const { error } = await tournamentService.leaveTournament(tournamentId, user.id)

      if (error) {
        console.error('Error leaving tournament:', error)
        alert('Failed to leave tournament. Please try again.')
        return
      }

      // Update local state
      setTournaments(prev => prev.map(tournament =>
        tournament.id === tournamentId
          ? { ...tournament, isJoined: false, currentTeams: Math.max(0, tournament.currentTeams - 1), myTeamStatus: 'not_joined' }
          : tournament
      ))

      alert('Successfully left tournament!')
    } catch (error) {
      console.error('Error leaving tournament:', error)
      alert('Failed to leave tournament. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Utility functions for compact display
  const formatTimeUntil = (dateString: string) => {
    const now = new Date()
    const target = new Date(dateString)
    const diff = target.getTime() - now.getTime()
    
    if (diff < 0) return 'Started'
    
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'live': return 'bg-red-100 text-red-700 border-red-200'
      case 'upcoming': return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'completed': return 'bg-gray-100 text-gray-700 border-gray-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getTeamStatusColor = (status: string) => {
    switch (status) {
      case 'qualified': return 'text-green-600'
      case 'eliminated': return 'text-red-600'
      case 'registered': return 'text-blue-600'
      default: return 'text-gray-600'
    }
  }



  const handleViewTournament = (tournamentId: string) => {
    setSelectedTournament(tournamentId)
    setShowTournamentModal(true)
  }

  // Use the filtered tournaments from database
  const displayTournaments = filteredTournaments

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading tournaments...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-2">
      <div className="max-w-6xl mx-auto space-y-3">
        {/* Compact Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Trophy className="w-4 h-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-bold text-gray-900">ML Tournaments</h1>
                <p className="text-xs text-gray-600">Competitive Mobile Legends Hub</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="p-1.5 hover:bg-gray-100 rounded-md transition-colors"
              >
                <Filter className="w-4 h-4 text-gray-600" />
              </button>
              <button className="p-1.5 hover:bg-gray-100 rounded-md transition-colors">
                <Bell className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>
        </div>

        {/* Compact Navigation Tabs */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-1">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('live')}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'live'
                  ? 'bg-red-500 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mr-2"></div>
              Live
            </button>
            <button
              onClick={() => setActiveTab('upcoming')}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'upcoming'
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <Calendar className="w-3 h-3 mr-2" />
              Upcoming
            </button>
            <button
              onClick={() => setActiveTab('joined')}
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === 'joined'
                  ? 'bg-green-500 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <UserPlus className="w-3 h-3 mr-2" />
              My Tournaments
            </button>
          </div>
        </div>

        {/* Search Bar */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search tournaments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
            />
          </div>
        </div>

        {/* Compact Tournament Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {displayTournaments.map((tournament) => (
            <div
              key={tournament.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 hover:shadow-md transition-all duration-200"
            >
              {/* Tournament Header */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="text-lg">{tournament.gameIcon}</div>
                  <div>
                    <h3 className="font-bold text-sm text-gray-900">{tournament.title}</h3>
                    <p className="text-xs text-gray-600">{tournament.format}</p>
                  </div>
                </div>
                <div className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(tournament.status)}`}>
                  {tournament.status === 'live' && <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse inline-block mr-1"></div>}
                  {tournament.status.toUpperCase()}
                </div>
              </div>

              {/* Prize Pool & Entry Fee */}
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-1">
                  <Gem className="w-3 h-3 text-purple-600" />
                  <span className="text-sm font-medium text-gray-900">{tournament.prizePool.toLocaleString()} 💎</span>
                </div>
                <div className="text-xs text-gray-600">Entry: {tournament.entryFee} 💎</div>
              </div>

              {/* Current Round & Teams */}
              <div className="flex items-center justify-between mb-3">
                <div className="text-xs text-gray-600">
                  <span className="font-medium">{tournament.currentRound}</span>
                  <span className="mx-1">•</span>
                  <span>{tournament.currentTeams}/{tournament.maxTeams} teams</span>
                </div>
                {tournament.nextMatch && (
                  <div className="text-xs text-blue-600 font-medium">
                    {formatTimeUntil(tournament.nextMatch)}
                  </div>
                )}
              </div>

              {/* Live Match Info */}
              {tournament.currentMatch && (
                <div className="bg-red-50 border border-red-200 rounded-md p-2 mb-3">
                  <div className="flex items-center justify-between">
                    <div className="text-xs">
                      <div className="font-medium text-red-900">
                        {tournament.currentMatch.team1} vs {tournament.currentMatch.team2}
                      </div>
                      <div className="text-red-700">
                        Game {tournament.currentMatch.gameNumber} • Score: {tournament.currentMatch.score}
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium text-red-700">LIVE</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Team Preview */}
              <div className="mb-3">
                <div className="flex items-center justify-between mb-1">
                  <span className="text-xs font-medium text-gray-700">Teams</span>
                  <button
                    onClick={() => handleViewTournament(tournament.id)}
                    className="text-xs text-blue-600 hover:text-blue-700"
                  >
                    View All
                  </button>
                </div>
                <div className="flex items-center space-x-1">
                  {tournament.teams.slice(0, 4).map((team: any) => (
                    <div
                      key={team.id}
                      className="flex items-center space-x-1 bg-gray-50 rounded px-2 py-1"
                    >
                      <span className="text-xs">{team.avatar}</span>
                      <span className="text-xs font-medium text-gray-900">{team.name}</span>
                      <span className={`text-xs ${getTeamStatusColor(team.status)}`}>
                        {team.status === 'qualified' && '✓'}
                        {team.status === 'eliminated' && '✗'}
                        {team.status === 'registered' && '○'}
                      </span>
                    </div>
                  ))}
                  {tournament.teams.length > 4 && (
                    <span className="text-xs text-gray-500">+{tournament.teams.length - 4}</span>
                  )}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-2">
                {tournament.isJoined ? (
                  <div className="flex items-center space-x-2 flex-1">
                    <div className="flex-1 bg-green-100 text-green-700 py-2 px-3 rounded-md text-xs font-medium text-center">
                      ✓ Joined • {tournament.myTeamStatus}
                    </div>
                    <button
                      onClick={() => handleLeaveTournament(tournament.id)}
                      disabled={isLoading}
                      className="bg-red-100 hover:bg-red-200 text-red-700 py-2 px-3 rounded-md text-xs font-medium transition-colors"
                    >
                      Leave
                    </button>
                  </div>
                ) : tournament.status === 'upcoming' ? (
                  <button
                    onClick={() => handleJoinTournament(tournament.id)}
                    disabled={isLoading}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded-md text-xs font-medium transition-colors disabled:opacity-50"
                  >
                    {isLoading ? 'Joining...' : 'Join Tournament'}
                  </button>
                ) : (
                  <button
                    onClick={() => handleViewTournament(tournament.id)}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded-md text-xs font-medium transition-colors"
                  >
                    View Details
                  </button>
                )}

                {tournament.status === 'live' && (
                  <button className="bg-red-600 hover:bg-red-700 text-white py-2 px-3 rounded-md text-xs font-medium transition-colors flex items-center space-x-1">
                    <Play className="w-3 h-3" />
                    <span>Watch</span>
                  </button>
                )}

                <button
                  onClick={() => handleViewTournament(tournament.id)}
                  className="p-2 hover:bg-gray-100 rounded-md transition-colors"
                >
                  <MessageCircle className="w-3 h-3 text-gray-600" />
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Loader2 className="w-8 h-8 text-blue-600 animate-spin" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">Loading tournaments...</h3>
            <p className="text-gray-600">Please wait while we fetch the latest tournaments</p>
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredTournaments.length === 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <Trophy className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-lg font-bold text-gray-900 mb-2">No tournaments found</h3>
            <p className="text-gray-600 mb-4">Try adjusting your search or check back later for new tournaments</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              Refresh Tournaments
            </button>
          </div>
        )}

        {/* Tournament Notice */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <Info className="w-4 h-4 text-blue-600 mt-0.5" />
            <div className="text-xs text-blue-800">
              <p className="font-medium mb-1">Tournament Guidelines</p>
              <p>• Matches are held in custom rooms</p>
              <p>• Add referee to your friends list before match starts</p>
              <p>• Follow fair play rules and respect opponents</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
