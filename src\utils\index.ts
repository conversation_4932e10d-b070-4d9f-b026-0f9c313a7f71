// 🛠️ UTILITY FUNCTIONS
// Common utility functions used across the Gambets platform

/**
 * 💎 DIAMOND FORMATTING
 * Format diamond amounts with proper comma separation and currency symbol
 */
export const formatDiamonds = (amount: number): string => {
  return `${amount.toLocaleString()} diamonds`
}

/**
 * 💰 CURRENCY FORMATTING
 * Format Philippine Peso amounts
 */
export const formatPeso = (amount: number): string => {
  return `₱${amount.toLocaleString('en-PH', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })}`
}

/**
 * 📅 DATE FORMATTING
 * Format dates for display in the platform
 */
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleDateString('en-PH', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj.toLocaleString('en-PH', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export const formatTimeAgo = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)

  if (diffInSeconds < 60) return 'Just now'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
  
  return formatDate(dateObj)
}

/**
 * 🎮 GAME UTILITIES
 * Game-related helper functions
 */
export const getGameIcon = (game: string): string => {
  const gameIcons: Record<string, string> = {
    'mobile-legends': 'Castle',
    'valorant': 'Target',
    'call-of-duty': 'Zap',
    'wild-rift': 'Swords',
    'dota-2': 'Shield',
    'cs-go': 'Bomb',
    'honor-of-kings': 'Crown'
  }
  return gameIcons[game.toLowerCase()] || 'Gamepad2'
}

export const getGameDisplayName = (game: string): string => {
  const gameNames: Record<string, string> = {
    'mobile-legends': 'Mobile Legends',
    'valorant': 'Valorant',
    'call-of-duty': 'Call of Duty',
    'wild-rift': 'Wild Rift',
    'dota-2': 'Dota 2',
    'cs-go': 'CS:GO',
    'honor-of-kings': 'Honor of Kings'
  }
  return gameNames[game.toLowerCase()] || game
}

/**
 * 🏆 RANK UTILITIES
 * Player ranking and status functions
 */
export const getRankIcon = (rank: number): string => {
  if (rank === 1) return 'Crown'
  if (rank === 2) return 'Medal'
  if (rank === 3) return 'Award'
  if (rank <= 10) return 'Trophy'
  if (rank <= 50) return 'Badge'
  return 'Star'
}

export const getRankColor = (rank: number): string => {
  if (rank === 1) return 'text-yellow-500'
  if (rank === 2) return 'text-gray-400'
  if (rank === 3) return 'text-amber-600'
  if (rank <= 10) return 'text-blue-500'
  if (rank <= 50) return 'text-purple-500'
  return 'text-gray-500'
}

/**
 * 📊 STATISTICS UTILITIES
 * Calculate win rates and performance metrics
 */
export const calculateWinRate = (wins: number, losses: number): number => {
  const total = wins + losses
  if (total === 0) return 0
  return Math.round((wins / total) * 100)
}

export const formatWinRate = (wins: number, losses: number): string => {
  const winRate = calculateWinRate(wins, losses)
  return `${winRate}%`
}

export const getPerformanceColor = (winRate: number): string => {
  if (winRate >= 80) return 'text-green-500'
  if (winRate >= 60) return 'text-blue-500'
  if (winRate >= 40) return 'text-yellow-500'
  return 'text-red-500'
}

/**
 * 🔐 VALIDATION UTILITIES
 * Input validation functions
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidUsername = (username: string): boolean => {
  // Username: 3-20 characters, alphanumeric and underscores only
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
  return usernameRegex.test(username)
}

export const isValidPassword = (password: string): boolean => {
  // Password: At least 8 characters, at least one letter and one number
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/
  return passwordRegex.test(password)
}

export const isValidMLBBId = (mlbbId: string): boolean => {
  // MLBB ID: Numbers and parentheses only, format: 123456789 (1234)
  const mlbbRegex = /^\d{8,10}\s*\(\d{4}\)$/
  return mlbbRegex.test(mlbbId.trim())
}

/**
 * 🎨 UI UTILITIES
 * User interface helper functions
 */
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ')
}

export const getStatusColor = (status: string): string => {
  const statusColors: Record<string, string> = {
    'open': 'bg-green-100 text-green-800',
    'full': 'bg-yellow-100 text-yellow-800',
    'ongoing': 'bg-blue-100 text-blue-800',
    'completed': 'bg-gray-100 text-gray-800',
    'cancelled': 'bg-red-100 text-red-800',
    'pending': 'bg-yellow-100 text-yellow-800',
    'approved': 'bg-green-100 text-green-800',
    'rejected': 'bg-red-100 text-red-800'
  }
  return statusColors[status.toLowerCase()] || 'bg-gray-100 text-gray-800'
}

export const getStatusIcon = (status: string): string => {
  const statusIcons: Record<string, string> = {
    'open': 'CircleDot',
    'full': 'AlertCircle',
    'ongoing': 'Play',
    'completed': 'CheckCircle',
    'cancelled': 'XCircle',
    'pending': 'Clock',
    'approved': 'CheckCircle',
    'rejected': 'XCircle'
  }
  return statusIcons[status.toLowerCase()] || 'Circle'
}

/**
 * 🔗 URL UTILITIES
 * URL and routing helper functions
 */
export const generateMatchUrl = (matchId: string): string => {
  return `/match/${matchId}`
}

export const generateProfileUrl = (username: string): string => {
  return `/profile/${username}`
}

export const isExternalUrl = (url: string): boolean => {
  return url.startsWith('http://') || url.startsWith('https://')
}

/**
 * 📱 DEVICE UTILITIES
 * Device and browser detection
 */
export const isMobile = (): boolean => {
  return window.innerWidth < 768
}

export const isTablet = (): boolean => {
  return window.innerWidth >= 768 && window.innerWidth < 1024
}

export const isDesktop = (): boolean => {
  return window.innerWidth >= 1024
}

/**
 * 🎲 RANDOM UTILITIES
 * Random generation functions
 */
export const generateRoomCode = (): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let result = ''
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

export const generateUserId = (): string => {
  return `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 🔄 ARRAY UTILITIES
 * Array manipulation functions
 */
export const shuffleArray = <T>(array: T[]): T[] => {
  const shuffled = [...array]
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
  }
  return shuffled
}

export const groupBy = <T, K extends keyof any>(
  array: T[],
  key: (item: T) => K
): Record<K, T[]> => {
  return array.reduce((groups, item) => {
    const group = key(item)
    groups[group] = groups[group] || []
    groups[group].push(item)
    return groups
  }, {} as Record<K, T[]>)
}

/**
 * 💾 STORAGE UTILITIES
 * Local storage helper functions
 */
export const setLocalStorage = (key: string, value: any): void => {
  try {
    localStorage.setItem(key, JSON.stringify(value))
  } catch (error) {
    console.error('Error saving to localStorage:', error)
  }
}

export const getLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : defaultValue
  } catch (error) {
    console.error('Error reading from localStorage:', error)
    return defaultValue
  }
}

export const removeLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key)
  } catch (error) {
    console.error('Error removing from localStorage:', error)
  }
}

/**
 * ⏱️ DEBOUNCE UTILITY
 * Debounce function calls
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
