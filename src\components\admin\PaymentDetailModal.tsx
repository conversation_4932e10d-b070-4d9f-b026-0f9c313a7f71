import { useState } from 'react'
import {
  XCircle, CheckCircle, AlertTriangle, User,
  Download, Eye, Smartphone, Building, Shield
} from 'lucide-react'

interface PaymentRequest {
  id: string
  user_id: string
  type: 'deposit' | 'withdrawal'
  amount: number
  currency: string
  payment_method: 'gcash' | 'maya' | 'bank_transfer' | 'crypto'
  status: 'pending' | 'approved' | 'rejected' | 'processing' | 'completed'
  reference_number?: string
  proof_image?: string
  account_details: any
  admin_notes?: string
  created_at: string
  processed_at?: string
  processed_by?: string
  user: {
    username: string
    email: string
    first_name: string
    last_name: string
  }
  processor?: {
    username: string
    email: string
  }
}

interface PaymentDetailModalProps {
  payment: PaymentRequest
  onClose: () => void
  onAction: (paymentId: string, action: 'approve' | 'reject', notes?: string) => void
  getStatusBadge: (status: string) => JSX.Element
  processingPayment: string | null
}

export default function PaymentDetailModal({ 
  payment, 
  onClose, 
  onAction, 
  getStatusBadge,
  processingPayment 
}: PaymentDetailModalProps) {
  const [adminNotes, setAdminNotes] = useState(payment.admin_notes || '')
  const [showProofImage, setShowProofImage] = useState(false)

  const getPaymentMethodIcon = (method: string) => {
    const methodConfig = {
      gcash: { icon: Smartphone, color: 'text-blue-600', name: 'GCash' },
      maya: { icon: Smartphone, color: 'text-green-600', name: 'Maya' },
      bank_transfer: { icon: Building, color: 'text-purple-600', name: 'Bank Transfer' },
      crypto: { icon: Shield, color: 'text-orange-600', name: 'Cryptocurrency' }
    }

    const config = methodConfig[method as keyof typeof methodConfig] || methodConfig.gcash
    const Icon = config.icon

    return { Icon, color: config.color, name: config.name }
  }

  const { Icon: PaymentIcon, color: paymentColor, name: paymentName } = getPaymentMethodIcon(payment.payment_method)

  const handleApprove = () => {
    if (window.confirm('Are you sure you want to approve this payment request?')) {
      onAction(payment.id, 'approve', adminNotes)
    }
  }

  const handleReject = () => {
    if (window.confirm('Are you sure you want to reject this payment request?')) {
      onAction(payment.id, 'reject', adminNotes)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900 capitalize">
                {payment.type} Request - {paymentName}
              </h2>
              <p className="text-gray-600 mt-1">Request ID: {payment.id}</p>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusBadge(payment.status)}
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Payment Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Payment Details</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-600">Amount:</span>
                  <span className="text-2xl font-bold text-green-600">₱{payment.amount.toLocaleString()}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-600">Payment Method:</span>
                  <div className="flex items-center space-x-2">
                    <PaymentIcon className={`w-4 h-4 ${paymentColor}`} />
                    <span className="font-medium">{paymentName}</span>
                  </div>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-600">Type:</span>
                  <span className="font-medium capitalize">{payment.type}</span>
                </div>
                {payment.reference_number && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600">Reference:</span>
                    <span className="font-medium">{payment.reference_number}</span>
                  </div>
                )}
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="text-gray-600">Created:</span>
                  <span className="font-medium">{new Date(payment.created_at).toLocaleString()}</span>
                </div>
                {payment.processed_at && (
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-gray-600">Processed:</span>
                    <span className="font-medium">{new Date(payment.processed_at).toLocaleString()}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">User Information</h3>
              <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-3">
                  <User className="w-5 h-5 text-blue-600" />
                  <span className="font-semibold text-blue-900">{payment.user.username}</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Name:</span>
                    <span className="font-medium text-blue-900">
                      {payment.user.first_name} {payment.user.last_name}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">Email:</span>
                    <span className="font-medium text-blue-900">{payment.user.email}</span>
                  </div>
                </div>
              </div>

              {payment.processor && (
                <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-3 mb-3">
                    <CheckCircle className="w-5 h-5 text-green-600" />
                    <span className="font-semibold text-green-900">Processed by</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-green-700">Admin:</span>
                      <span className="font-medium text-green-900">{payment.processor.username}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-700">Email:</span>
                      <span className="font-medium text-green-900">{payment.processor.email}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Account Details */}
          {payment.account_details && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Account Details</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                  {JSON.stringify(payment.account_details, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Proof of Payment */}
          {payment.proof_image && (
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Proof of Payment</h3>
              <div className="space-y-3">
                <button
                  onClick={() => setShowProofImage(!showProofImage)}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 font-medium"
                >
                  <Eye className="w-4 h-4" />
                  <span>{showProofImage ? 'Hide' : 'View'} Proof Image</span>
                </button>
                
                {showProofImage && (
                  <div className="border border-gray-200 rounded-lg p-4">
                    <img
                      src={payment.proof_image}
                      alt="Proof of payment"
                      className="max-w-full h-auto rounded-lg"
                    />
                  </div>
                )}
                
                <a
                  href={payment.proof_image}
                  download
                  className="inline-flex items-center space-x-2 text-green-600 hover:text-green-700 font-medium"
                >
                  <Download className="w-4 h-4" />
                  <span>Download Image</span>
                </a>
              </div>
            </div>
          )}

          {/* Admin Notes */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Admin Notes</h3>
            <textarea
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              placeholder="Add notes about this payment request..."
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          {/* Actions */}
          {payment.status === 'pending' && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin Actions</h3>
              <div className="flex flex-wrap gap-3">
                <button
                  onClick={handleApprove}
                  disabled={processingPayment === payment.id}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
                >
                  <CheckCircle className="w-4 h-4" />
                  <span>{processingPayment === payment.id ? 'Processing...' : 'Approve Payment'}</span>
                </button>

                <button
                  onClick={handleReject}
                  disabled={processingPayment === payment.id}
                  className="bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center space-x-2"
                >
                  <XCircle className="w-4 h-4" />
                  <span>{processingPayment === payment.id ? 'Processing...' : 'Reject Payment'}</span>
                </button>
              </div>
              
              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-start space-x-2">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-800">Important</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      {payment.type === 'deposit' 
                        ? 'Approving this deposit will add diamonds to the user\'s account balance.'
                        : 'Approving this withdrawal will process the payment to the user\'s account.'
                      }
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
