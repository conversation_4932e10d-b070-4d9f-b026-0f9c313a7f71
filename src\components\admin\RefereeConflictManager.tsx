import { useState, useEffect } from 'react'
import { Shield, AlertTriangle, CheckCircle, Clock, Users, Ban, Eye } from 'lucide-react'
import { refereeConflictService } from '../../services/refereeConflictService'
// import type { ConflictCheckResult } from '../../services/refereeConflictService' // TODO: Confirm usage
import { supabase } from '../../services/supabaseClient'

interface ConflictAlert {
  id: string
  refereeId: string
  refereeName: string
  matchId: string
  matchTitle: string
  conflictType: string
  conflictDetails: string
  severity: 'high' | 'medium' | 'low'
  status: 'active' | 'resolved' | 'ignored'
  createdAt: string
}

interface RefereeConflictManagerProps {
  className?: string
}

export default function RefereeConflictManager({ className = '' }: RefereeConflictManagerProps) {
  const [conflicts, setConflicts] = useState<ConflictAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [, setSelectedConflict] = useState<ConflictAlert | null>(null)
  const [conflictSettings, setConflictSettings] = useState({
    friendshipCooldownDays: 7,
    bettingCooldownHours: 24,
    matchCooldownHours: 12,
    autoRecusalEnabled: true,
    strictModeEnabled: false
  })

  useEffect(() => {
    loadConflicts()
    loadSettings()

    // Set up real-time monitoring
    const interval = setInterval(() => {
      loadConflicts()
    }, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const loadConflicts = async () => {
    try {
      setLoading(true)
      
      // Get all active referee assignments
      const { data: activeAssignments } = await supabase
        .from('matches')
        .select(`
          id,
          game,
          referee_id,
          users!referee_id(username),
          match_participants(
            user_id,
            users(username)
          )
        `)
        .not('referee_id', 'is', null)
        .eq('status', 'waiting')

      if (!activeAssignments) return

      const conflictAlerts: ConflictAlert[] = []

      // Check each assignment for conflicts
      for (const match of activeAssignments) {
        if (!match.referee_id) continue

        const conflictResult = await refereeConflictService.checkRefereeConflict(
          match.referee_id,
          match.id
        )

        if (conflictResult.hasConflict) {
          conflictAlerts.push({
            id: `${match.referee_id}-${match.id}`,
            refereeId: match.referee_id,
            refereeName: (match.users as any)?.username || 'Unknown',
            matchId: match.id,
            matchTitle: `${match.game} Match`,
            conflictType: conflictResult.conflictType || 'unknown',
            conflictDetails: conflictResult.conflictDetails,
            severity: getSeverity(conflictResult.conflictType),
            status: 'active',
            createdAt: new Date().toISOString()
          })
        }
      }

      setConflicts(conflictAlerts)
    } catch (error) {
      console.error('Error loading conflicts:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadSettings = async () => {
    try {
      const { data: settings } = await supabase
        .from('referee_conflict_settings')
        .select('setting_name, setting_value')

      if (settings) {
        const settingsObj = settings.reduce((acc, setting) => {
          acc[setting.setting_name] = JSON.parse(setting.setting_value)
          return acc
        }, {} as any)

        setConflictSettings({
          friendshipCooldownDays: settingsObj.friendship_cooldown_days || 7,
          bettingCooldownHours: settingsObj.betting_cooldown_hours || 24,
          matchCooldownHours: settingsObj.match_cooldown_hours || 12,
          autoRecusalEnabled: settingsObj.auto_recusal_enabled || true,
          strictModeEnabled: settingsObj.strict_mode_enabled || false
        })
      }
    } catch (error) {
      console.error('Error loading settings:', error)
    }
  }

  const getSeverity = (conflictType: string | null): 'high' | 'medium' | 'low' => {
    switch (conflictType) {
      case 'self_match':
        return 'high'
      case 'betting_conflict':
        return 'high'
      case 'friend_match':
        return 'medium'
      case 'recent_interaction':
        return 'low'
      default:
        return 'medium'
    }
  }

  const getSeverityColor = (severity: 'high' | 'medium' | 'low') => {
    switch (severity) {
      case 'high':
        return 'text-red-600 bg-red-50 border-red-200'
      case 'medium':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200'
      case 'low':
        return 'text-blue-600 bg-blue-50 border-blue-200'
    }
  }

  const getConflictIcon = (conflictType: string) => {
    switch (conflictType) {
      case 'self_match':
        return <Ban className="w-4 h-4" />
      case 'betting_conflict':
        return <AlertTriangle className="w-4 h-4" />
      case 'friend_match':
        return <Users className="w-4 h-4" />
      case 'recent_interaction':
        return <Clock className="w-4 h-4" />
      default:
        return <Shield className="w-4 h-4" />
    }
  }

  const resolveConflict = async (conflictId: string, action: 'recuse' | 'ignore') => {
    const conflict = conflicts.find(c => c.id === conflictId)
    if (!conflict) return

    try {
      if (action === 'recuse') {
        // Auto-recuse the referee
        await refereeConflictService.autoRecuseReferee(
          conflict.refereeId,
          conflict.matchId,
          `Admin recusal: ${conflict.conflictDetails}`
        )
      }

      // Update conflict status
      setConflicts(prev => 
        prev.map(c => 
          c.id === conflictId 
            ? { ...c, status: action === 'recuse' ? 'resolved' : 'ignored' }
            : c
        )
      )

      // Reload conflicts to get updated data
      await loadConflicts()
    } catch (error) {
      console.error('Error resolving conflict:', error)
    }
  }

  const updateSettings = async (newSettings: typeof conflictSettings) => {
    try {
      // Update each setting in the database
      const updates = [
        { name: 'friendship_cooldown_days', value: newSettings.friendshipCooldownDays },
        { name: 'betting_cooldown_hours', value: newSettings.bettingCooldownHours },
        { name: 'match_cooldown_hours', value: newSettings.matchCooldownHours },
        { name: 'auto_recusal_enabled', value: newSettings.autoRecusalEnabled },
        { name: 'strict_mode_enabled', value: newSettings.strictModeEnabled }
      ]

      for (const update of updates) {
        await supabase
          .from('referee_conflict_settings')
          .upsert({
            setting_name: update.name,
            setting_value: JSON.stringify(update.value),
            updated_at: new Date().toISOString()
          })
      }

      setConflictSettings(newSettings)
    } catch (error) {
      console.error('Error updating settings:', error)
    }
  }

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    )
  }

  const activeConflicts = conflicts.filter(c => c.status === 'active')
  const highSeverityConflicts = activeConflicts.filter(c => c.severity === 'high')

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Referee Conflicts</h3>
              <p className="text-sm text-gray-600">Monitor and resolve referee-player conflicts</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {highSeverityConflicts.length > 0 && (
              <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                {highSeverityConflicts.length} High Priority
              </span>
            )}
            <span className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
              {activeConflicts.length} Active
            </span>
          </div>
        </div>
      </div>

      {/* Conflict List */}
      <div className="p-6">
        {activeConflicts.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">No Active Conflicts</h4>
            <p className="text-gray-600">All referee assignments are conflict-free.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activeConflicts.map((conflict) => (
              <div
                key={conflict.id}
                className={`border rounded-lg p-4 ${getSeverityColor(conflict.severity)}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="mt-1">
                      {getConflictIcon(conflict.conflictType)}
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">
                        {conflict.refereeName} - {conflict.matchTitle}
                      </h4>
                      <p className="text-sm text-gray-600 mt-1">
                        {conflict.conflictDetails}
                      </p>
                      <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>Severity: {conflict.severity.toUpperCase()}</span>
                        <span>Type: {conflict.conflictType.replace('_', ' ')}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => setSelectedConflict(conflict)}
                      className="text-blue-600 hover:text-blue-700 p-1"
                      title="View Details"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => resolveConflict(conflict.id, 'recuse')}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs font-medium"
                    >
                      Recuse Referee
                    </button>
                    <button
                      onClick={() => resolveConflict(conflict.id, 'ignore')}
                      className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-3 py-1 rounded text-xs font-medium"
                    >
                      Ignore
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Settings Section */}
      <div className="border-t border-gray-200 p-6">
        <h4 className="text-sm font-medium text-gray-900 mb-4">Conflict Prevention Settings</h4>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Friendship Cooldown (days)
            </label>
            <input
              type="number"
              value={conflictSettings.friendshipCooldownDays}
              onChange={(e) => setConflictSettings(prev => ({
                ...prev,
                friendshipCooldownDays: parseInt(e.target.value)
              }))}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Betting Cooldown (hours)
            </label>
            <input
              type="number"
              value={conflictSettings.bettingCooldownHours}
              onChange={(e) => setConflictSettings(prev => ({
                ...prev,
                bettingCooldownHours: parseInt(e.target.value)
              }))}
              className="w-full border border-gray-300 rounded px-2 py-1 text-sm"
            />
          </div>
        </div>
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={conflictSettings.autoRecusalEnabled}
                onChange={(e) => setConflictSettings(prev => ({
                  ...prev,
                  autoRecusalEnabled: e.target.checked
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Auto-recusal enabled</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={conflictSettings.strictModeEnabled}
                onChange={(e) => setConflictSettings(prev => ({
                  ...prev,
                  strictModeEnabled: e.target.checked
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Strict mode</span>
            </label>
          </div>
          <button
            onClick={() => updateSettings(conflictSettings)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium"
          >
            Save Settings
          </button>
        </div>
      </div>
    </div>
  )
}
