import { useState, useEffect } from 'react'
import {
  CheckCircle,
  Smartphone, CreditCard, Building2, DollarSign, Gem
} from 'lucide-react'
import { walletService } from '../../services/walletService'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'

interface WithdrawalRequest {
  id: string
  user_id: string
  amount_diamonds: number
  amount_usd: number
  amount_php: number
  payment_method: string
  account_details: any
  status: 'pending' | 'processing' | 'completed' | 'rejected'
  created_at: string
  admin_notes?: string
  user?: {
    username: string
    email: string
    first_name: string
    last_name: string
  }
}

export default function WithdrawalManagement() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [withdrawals, setWithdrawals] = useState<WithdrawalRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<WithdrawalRequest | null>(null)
  const [processingId, setProcessingId] = useState<string | null>(null)
  const [adminNotes, setAdminNotes] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [actionType, setActionType] = useState<'approve' | 'reject'>('approve')

  useEffect(() => {
    loadPendingWithdrawals()
  }, [])

  const loadPendingWithdrawals = async () => {
    setLoading(true)
    try {
      const { data, error } = await walletService.getWithdrawalInfo(user?.id || '')
      if (error) {
        console.error('Error loading withdrawals:', error)
        addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to load withdrawal requests'
        })
      } else {
        setWithdrawals(Array.isArray(data) ? data : [])
      }
    } catch (error) {
      console.error('Error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProcessWithdrawal = async () => {
    if (!selectedWithdrawal || !user) return

    setProcessingId(selectedWithdrawal.id)
    try {
      // TODO: Add admin withdrawal processing function to walletService
      const error = null // Placeholder for now

      if (error) {
        addNotification({
          type: 'error',
          title: 'Error',
          message: `Failed to ${actionType} withdrawal`
        })
      } else {
        addNotification({
          type: 'success',
          title: 'Success',
          message: `Withdrawal ${actionType}d successfully`
        })
        loadPendingWithdrawals()
        setShowModal(false)
        setSelectedWithdrawal(null)
        setAdminNotes('')
      }
    } catch (error) {
      console.error('Error processing withdrawal:', error)
    } finally {
      setProcessingId(null)
    }
  }

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'gcash':
        return <Smartphone className="w-4 h-4" />
      case 'maya':
        return <CreditCard className="w-4 h-4" />
      case 'bank_transfer':
        return <Building2 className="w-4 h-4" />
      default:
        return <DollarSign className="w-4 h-4" />
    }
  }

  const formatCurrency = (amount: number, currency: 'USD' | 'PHP') => {
    return currency === 'USD' 
      ? `$${amount.toFixed(2)}`
      : `₱${amount.toFixed(2)}`
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Withdrawal Management</h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage pending withdrawal requests from users
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
              {withdrawals.length} Pending
            </span>
          </div>
        </div>
      </div>

      <div className="p-6">
        {withdrawals.length === 0 ? (
          <div className="text-center py-8">
            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">All caught up!</h3>
            <p className="text-gray-600">No pending withdrawal requests at the moment.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {withdrawals.map((withdrawal) => (
              <div
                key={withdrawal.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        {getPaymentMethodIcon(withdrawal.payment_method)}
                        <span className="font-medium text-gray-900 capitalize">
                          {withdrawal.payment_method.replace('_', ' ')}
                        </span>
                      </div>
                      <span className="text-sm text-gray-500">
                        {new Date(withdrawal.created_at).toLocaleDateString()}
                      </span>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <p className="text-sm text-gray-600">User</p>
                        <p className="font-medium">
                          {withdrawal.user?.first_name} {withdrawal.user?.last_name}
                        </p>
                        <p className="text-sm text-gray-500">@{withdrawal.user?.username}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Amount</p>
                        <div className="flex items-center space-x-2">
                          <Gem className="w-4 h-4 text-blue-500" />
                          <span className="font-medium">{withdrawal.amount_diamonds.toLocaleString()} 💎</span>
                        </div>
                        <p className="text-sm text-gray-500">
                          {formatCurrency(withdrawal.amount_php, 'PHP')} / {formatCurrency(withdrawal.amount_usd, 'USD')}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Account Details</p>
                        <p className="font-medium">{withdrawal.account_details.account_name}</p>
                        <p className="text-sm text-gray-500">{withdrawal.account_details.account_number}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => {
                        setSelectedWithdrawal(withdrawal)
                        setActionType('approve')
                        setShowModal(true)
                      }}
                      className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                    >
                      Approve
                    </button>
                    <button
                      onClick={() => {
                        setSelectedWithdrawal(withdrawal)
                        setActionType('reject')
                        setShowModal(true)
                      }}
                      className="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                    >
                      Reject
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Processing Modal */}
      {showModal && selectedWithdrawal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">
              {actionType === 'approve' ? 'Approve' : 'Reject'} Withdrawal
            </h3>
            
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-2">
                User: {selectedWithdrawal.user?.first_name} {selectedWithdrawal.user?.last_name}
              </p>
              <p className="text-sm text-gray-600 mb-2">
                Amount: {selectedWithdrawal.amount_diamonds.toLocaleString()} 💎 
                ({formatCurrency(selectedWithdrawal.amount_php, 'PHP')})
              </p>
              <p className="text-sm text-gray-600 mb-4">
                Method: {selectedWithdrawal.payment_method.replace('_', ' ')}
              </p>
            </div>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Admin Notes {actionType === 'reject' ? '(Required)' : '(Optional)'}
              </label>
              <textarea
                value={adminNotes}
                onChange={(e) => setAdminNotes(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
                rows={3}
                placeholder={
                  actionType === 'approve' 
                    ? 'Optional notes about the approval...'
                    : 'Please provide a reason for rejection...'
                }
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => {
                  setShowModal(false)
                  setSelectedWithdrawal(null)
                  setAdminNotes('')
                }}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleProcessWithdrawal}
                disabled={processingId === selectedWithdrawal.id || (actionType === 'reject' && !adminNotes.trim())}
                className={`flex-1 py-2 px-4 rounded-md text-white font-medium transition-colors ${
                  actionType === 'approve'
                    ? 'bg-green-500 hover:bg-green-600 disabled:bg-green-300'
                    : 'bg-red-500 hover:bg-red-600 disabled:bg-red-300'
                }`}
              >
                {processingId === selectedWithdrawal.id ? 'Processing...' : 
                 actionType === 'approve' ? 'Approve' : 'Reject'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
