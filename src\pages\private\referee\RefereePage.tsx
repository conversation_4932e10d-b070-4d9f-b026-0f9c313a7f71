import { useState, useEffect } from "react"
import { useAuth } from "../../../contexts/AuthContext"
import { useNotifications } from "../../../contexts/NotificationContext"
import { matchService } from "../../../services/matchService"
import { Button } from "../../../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { Badge } from "../../../components/ui/badge"
import { Textarea } from "../../../components/ui/textarea"
import {
  Shield,
  Clock,
  Trophy,
  CheckCircle2,
  Minus,
  AlertCircle,
  RefreshCw,
  Gamepad2
} from "lucide-react"

interface AssignedMatch {
  id: string
  title: string
  game: string
  mode: string
  status: string
  entry_fee: number
  pot_amount: number
  current_players: number
  max_players: number
  host_username?: string
  created_at: string
  scheduled_start_time?: string
}

interface MatchResult {
  winner: 'host' | 'challenger' | 'draw'
  notes: string
}

export default function RefereePage() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  
  const [assignedMatches, setAssignedMatches] = useState<AssignedMatch[]>([])
  const [selectedMatch, setSelectedMatch] = useState<AssignedMatch | null>(null)
  const [result, setResult] = useState<MatchResult>({ winner: 'host', notes: '' })
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Load only matches assigned to this referee
  const loadAssignedMatches = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)

      // Get ALL matches and filter for referee assignments
      const { data: matches, error } = await matchService.getMatches({})

      if (error) {
        console.error('Error loading referee matches:', error)
        addNotification({
          type: 'error',
          title: 'Loading Error',
          message: 'Failed to load assigned matches'
        })
        return
      }

      // Filter only matches assigned to this referee that need attention
      const myMatches = (matches || [])
        .filter(match => {
          // Must be assigned to this referee
          if (match.referee_id !== user.id) return false

          // Must be in a state that needs referee action
          const needsAction = match.status === 'ongoing' ||
                             match.status === 'full' ||
                             match.status === 'in_progress' ||
                             match.status === 'open' // Include open matches for referee preparation

          return needsAction
        })
        .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())

      console.log(`Found ${myMatches.length} matches assigned to referee ${user.id}`)
      setAssignedMatches(myMatches)

      // Auto-select first match if available and no match currently selected
      if (myMatches.length > 0 && !selectedMatch) {
        setSelectedMatch(myMatches[0])
      } else if (myMatches.length === 0 && selectedMatch) {
        // Clear selection if no matches available
        setSelectedMatch(null)
      }

    } catch (error) {
      console.error('Error loading matches:', error)
      addNotification({
        type: 'error',
        title: 'System Error',
        message: 'Failed to load referee assignments'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Submit match result
  const handleSubmitResult = async () => {
    if (!selectedMatch) return

    try {
      setIsSubmitting(true)

      // Map result to expected format
      let winnerId = null
      if (result.winner === 'host') {
        winnerId = 'player1' // Host is player 1
      } else if (result.winner === 'challenger') {
        winnerId = 'player2' // Challenger is player 2
      } else {
        winnerId = 'draw'
      }

      const { error } = await matchService.submitMatchResultSimple(
        selectedMatch.id,
        winnerId,
        result.notes
      )

      if (error) {
        throw error
      }

      addNotification({
        type: 'success',
        title: 'Result Submitted',
        message: `Match result processed. ${result.winner === 'draw' ? 'Refunds issued' : 'Diamonds distributed to winner'}.`
      })

      // Remove completed match and select next one
      const remainingMatches = assignedMatches.filter(m => m.id !== selectedMatch.id)
      setAssignedMatches(remainingMatches)
      setSelectedMatch(remainingMatches.length > 0 ? remainingMatches[0] : null)
      setResult({ winner: 'host', notes: '' })

    } catch (error) {
      console.error('Error submitting result:', error)
      addNotification({
        type: 'error',
        title: 'Submission Failed',
        message: 'Failed to submit match result. Please try again.'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  // Format time ago
  const timeAgo = (dateString: string) => {
    const now = new Date()
    const date = new Date(dateString)
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    
    if (diffMins < 60) return `${diffMins}m ago`
    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours}h ago`
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays}d ago`
  }

  useEffect(() => {
    if (!user?.id) return

    loadAssignedMatches()

    // Set up real-time subscription for referee match updates
    console.log('Setting up real-time referee match subscriptions')
    let isSubscriptionActive = true

    const matchesSubscription = matchService.subscribeToMatches(async (updatedMatches) => {
      if (!isSubscriptionActive) return

      try {
        console.log('Real-time referee matches update received')

        // Filter for matches assigned to this referee
        const myMatches = (updatedMatches || [])
          .filter(match => {
            if (match.referee_id !== user.id) return false
            const needsAction = match.status === 'ongoing' ||
                               match.status === 'full' ||
                               match.status === 'in_progress' ||
                               match.status === 'open'
            return needsAction
          })
          .sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime())

        if (isSubscriptionActive) {
          setAssignedMatches(myMatches)

          // Update selected match if it's been updated
          if (selectedMatch) {
            const updatedSelectedMatch = myMatches.find(m => m.id === selectedMatch.id)
            if (updatedSelectedMatch) {
              setSelectedMatch(updatedSelectedMatch)
            } else if (myMatches.length > 0) {
              // If selected match is no longer available, select first available
              setSelectedMatch(myMatches[0])
            } else {
              setSelectedMatch(null)
            }
          }
        }

      } catch (error) {
        console.error('Error processing real-time referee matches update:', error)
      }
    })

    // Fallback refresh every 30 seconds
    const interval = setInterval(() => {
      if (isSubscriptionActive) {
        loadAssignedMatches()
      }
    }, 30000)

    // Cleanup subscription on unmount
    return () => {
      console.log('Cleaning up referee real-time subscriptions')
      isSubscriptionActive = false
      clearInterval(interval)
      if (matchesSubscription) {
        try {
          matchService.supabase?.removeChannel?.(matchesSubscription)
        } catch (error) {
          console.error('Error cleaning up referee subscription:', error)
        }
      }
    }
  }, [user?.id])

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading referee assignments...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Shield className="w-6 h-6 text-blue-600" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">Referee Panel</h1>
                <p className="text-sm text-gray-600">Process assigned matches</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {assignedMatches.length} pending
              </span>
              <Button onClick={loadAssignedMatches} variant="outline" size="sm">
                <RefreshCw className="w-4 h-4 mr-2" />
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {assignedMatches.length === 0 ? (
          // No matches assigned
          <Card>
            <CardContent className="p-12 text-center">
              <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Matches Assigned</h3>
              <p className="text-gray-600 mb-4">
                You don't have any matches requiring referee attention at the moment.
              </p>
              <Button onClick={loadAssignedMatches} variant="outline">
                <RefreshCw className="w-4 h-4 mr-2" />
                Check for New Assignments
              </Button>
            </CardContent>
          </Card>
        ) : (
          // Process assigned matches
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Match Queue */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="w-5 h-5" />
                    <span>Assigned Matches ({assignedMatches.length})</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {assignedMatches.map((match) => (
                      <div
                        key={match.id}
                        onClick={() => setSelectedMatch(match)}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                          selectedMatch?.id === match.id
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <Gamepad2 className="w-4 h-4 text-gray-600" />
                            <span className="font-medium">{match.game} {match.mode}</span>
                          </div>
                          <Badge variant={match.status === 'ongoing' ? 'default' : 'secondary'}>
                            {match.status}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between text-sm text-gray-600">
                          <span>{match.current_players}/{match.max_players} players</span>
                          <span>💎 {match.entry_fee}</span>
                          <span>{timeAgo(match.created_at)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Result Submission */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Trophy className="w-5 h-5" />
                    <span>Submit Result</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {!selectedMatch ? (
                    <div className="text-center py-8">
                      <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500">Select a match to process</p>
                    </div>
                  ) : (
                    <div className="space-y-6">
                      {/* Match Info */}
                      <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-medium text-gray-900 mb-2">
                          {selectedMatch.game} {selectedMatch.mode}
                        </h3>
                        <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                          <div>Entry Fee: 💎 {selectedMatch.entry_fee}</div>
                          <div>Total Pot: 💎 {selectedMatch.pot_amount}</div>
                          <div>Players: {selectedMatch.current_players}/{selectedMatch.max_players}</div>
                          <div>Host: {selectedMatch.host_username || 'Unknown'}</div>
                        </div>
                      </div>

                      {/* Winner Selection */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                          Select Winner
                        </label>
                        <div className="space-y-2">
                          <Button
                            variant={result.winner === 'host' ? 'default' : 'outline'}
                            className="w-full justify-start"
                            onClick={() => setResult(prev => ({ ...prev, winner: 'host' }))}
                          >
                            <CheckCircle2 className="w-4 h-4 mr-2" />
                            Host Wins
                          </Button>
                          <Button
                            variant={result.winner === 'challenger' ? 'default' : 'outline'}
                            className="w-full justify-start"
                            onClick={() => setResult(prev => ({ ...prev, winner: 'challenger' }))}
                          >
                            <CheckCircle2 className="w-4 h-4 mr-2" />
                            Challenger Wins
                          </Button>
                          <Button
                            variant={result.winner === 'draw' ? 'default' : 'outline'}
                            className="w-full justify-start"
                            onClick={() => setResult(prev => ({ ...prev, winner: 'draw' }))}
                          >
                            <Minus className="w-4 h-4 mr-2" />
                            Draw / Refund
                          </Button>
                        </div>
                      </div>

                      {/* Notes */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Notes (Optional)
                        </label>
                        <Textarea
                          value={result.notes}
                          onChange={(e) => setResult(prev => ({ ...prev, notes: e.target.value }))}
                          placeholder="Add any notes about the match result..."
                          rows={3}
                        />
                      </div>

                      {/* Submit */}
                      <Button
                        onClick={handleSubmitResult}
                        disabled={isSubmitting}
                        className="w-full"
                      >
                        {isSubmitting ? (
                          <>
                            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            <Trophy className="w-4 h-4 mr-2" />
                            Submit Result
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
