import { supabase } from './supabase'

export interface ConflictCheckResult {
  hasConflict: boolean
  conflictType: 'self_match' | 'friend_match' | 'betting_conflict' | 'recent_interaction' | null
  conflictDetails: string
  suggestedAction: string
}

export interface RefereePlayerConflictRules {
  // Prevent self-refereeing
  cannotRefereeOwnMatches: boolean
  
  // Prevent refereeing friends' matches
  cannotRefereeFriendsMatches: boolean
  friendshipCooldownDays: number
  
  // Betting restrictions
  cannotBetWhileReferee: boolean
  bettingCooldownHours: number
  
  // Match participation restrictions
  cannotPlayWhileAssignedReferee: boolean
  matchCooldownHours: number
  
  // Automatic recusal
  autoRecusalEnabled: boolean
  manualRecusalRequired: boolean
}

class RefereeConflictService {
  private readonly conflictRules: RefereePlayerConflictRules = {
    cannotRefereeOwnMatches: true,
    cannotRefereeFriendsMatches: true,
    friendshipCooldownDays: 7, // Can't referee friends' matches for 7 days after friendship
    cannotBetWhileReferee: true,
    bettingCooldownHours: 24, // Can't bet 24 hours before/after refereeing
    cannotPlayWhileAssignedReferee: true,
    matchCooldownHours: 12, // Can't play 12 hours before/after refereeing
    autoRecusalEnabled: true,
    manualRecusalRequired: true
  }

  /**
   * Check if a referee has conflicts for a specific match
   */
  async checkRefereeConflict(refereeId: string, matchId: string): Promise<ConflictCheckResult> {
    try {
      // Get match details
      const { data: match } = await supabase
        .from('matches')
        .select(`
          *,
          match_participants!inner(
            user_id,
            users!inner(username)
          )
        `)
        .eq('id', matchId)
        .single()

      if (!match) {
        return {
          hasConflict: false,
          conflictType: null,
          conflictDetails: 'Match not found',
          suggestedAction: 'No action needed'
        }
      }

      const participantIds = match.match_participants.map((p: any) => p.user_id)

      // Check 1: Self-refereeing (referee is a participant)
      if (participantIds.includes(refereeId)) {
        return {
          hasConflict: true,
          conflictType: 'self_match',
          conflictDetails: 'Referee cannot officiate their own match',
          suggestedAction: 'Assign different referee or remove referee from match'
        }
      }

      // Check 2: Friend conflicts
      const friendConflict = await this.checkFriendshipConflicts(refereeId, participantIds)
      if (friendConflict.hasConflict) {
        return friendConflict
      }

      // Check 3: Recent betting activity
      const bettingConflict = await this.checkBettingConflicts(refereeId, matchId)
      if (bettingConflict.hasConflict) {
        return bettingConflict
      }

      // Check 4: Recent match participation
      const participationConflict = await this.checkRecentParticipation(refereeId)
      if (participationConflict.hasConflict) {
        return participationConflict
      }

      return {
        hasConflict: false,
        conflictType: null,
        conflictDetails: 'No conflicts detected',
        suggestedAction: 'Referee can be assigned'
      }

    } catch (error) {
      console.error('Error checking referee conflict:', error)
      return {
        hasConflict: true,
        conflictType: null,
        conflictDetails: 'Error checking conflicts - defaulting to conflict for safety',
        suggestedAction: 'Manual review required'
      }
    }
  }

  /**
   * Check friendship conflicts
   */
  private async checkFriendshipConflicts(refereeId: string, participantIds: string[]): Promise<ConflictCheckResult> {
    const { data: friendships } = await supabase
      .from('friendships')
      .select('*')
      .or(`user_id.in.(${participantIds.join(',')}),friend_id.in.(${participantIds.join(',')})`)
      .or(`user_id.eq.${refereeId},friend_id.eq.${refereeId}`)
      .eq('status', 'accepted')

    if (friendships && friendships.length > 0) {
      // Check if friendship is within cooldown period
      const recentFriendship = friendships.find(f => {
        const friendshipDate = new Date(f.created_at)
        const cooldownDate = new Date()
        cooldownDate.setDate(cooldownDate.getDate() - this.conflictRules.friendshipCooldownDays)
        return friendshipDate > cooldownDate
      })

      if (recentFriendship) {
        return {
          hasConflict: true,
          conflictType: 'friend_match',
          conflictDetails: `Referee has recent friendship with match participant (within ${this.conflictRules.friendshipCooldownDays} days)`,
          suggestedAction: 'Assign different referee'
        }
      }
    }

    return { hasConflict: false, conflictType: null, conflictDetails: '', suggestedAction: '' }
  }

  /**
   * Check betting conflicts
   */
  private async checkBettingConflicts(refereeId: string, matchId: string): Promise<ConflictCheckResult> {
    const cooldownDate = new Date()
    cooldownDate.setHours(cooldownDate.getHours() - this.conflictRules.bettingCooldownHours)

    const { data: recentBets } = await supabase
      .from('match_bets')
      .select('*')
      .eq('user_id', refereeId)
      .eq('match_id', matchId)
      .gte('created_at', cooldownDate.toISOString())

    if (recentBets && recentBets.length > 0) {
      return {
        hasConflict: true,
        conflictType: 'betting_conflict',
        conflictDetails: `Referee has placed bets on this match within ${this.conflictRules.bettingCooldownHours} hours`,
        suggestedAction: 'Assign different referee or cancel bets'
      }
    }

    return { hasConflict: false, conflictType: null, conflictDetails: '', suggestedAction: '' }
  }

  /**
   * Check recent match participation
   */
  private async checkRecentParticipation(refereeId: string): Promise<ConflictCheckResult> {
    const cooldownDate = new Date()
    cooldownDate.setHours(cooldownDate.getHours() - this.conflictRules.matchCooldownHours)

    const { data: recentMatches } = await supabase
      .from('match_participants')
      .select(`
        *,
        matches!inner(*)
      `)
      .eq('user_id', refereeId)
      .gte('matches.created_at', cooldownDate.toISOString())

    if (recentMatches && recentMatches.length > 0) {
      return {
        hasConflict: true,
        conflictType: 'recent_interaction',
        conflictDetails: `Referee has participated in matches within ${this.conflictRules.matchCooldownHours} hours`,
        suggestedAction: 'Wait for cooldown period or assign different referee'
      }
    }

    return { hasConflict: false, conflictType: null, conflictDetails: '', suggestedAction: '' }
  }

  /**
   * Automatically recuse referee from conflicted matches
   */
  async autoRecuseReferee(refereeId: string, matchId: string, reason: string): Promise<boolean> {
    try {
      // Remove referee assignment
      await supabase
        .from('matches')
        .update({ 
          referee_id: null,
          referee_status: 'unassigned',
          updated_at: new Date().toISOString()
        })
        .eq('id', matchId)
        .eq('referee_id', refereeId)

      // Log the recusal
      await supabase
        .from('referee_recusals')
        .insert({
          referee_id: refereeId,
          match_id: matchId,
          reason: reason,
          recusal_type: 'automatic',
          created_at: new Date().toISOString()
        })

      return true
    } catch (error) {
      console.error('Error auto-recusing referee:', error)
      return false
    }
  }

  /**
   * Check if user can place bet (not currently assigned as referee)
   */
  async canUserBet(userId: string, matchId: string): Promise<{ canBet: boolean; reason: string }> {
    // Check if user is assigned as referee for this match
    const { data: match } = await supabase
      .from('matches')
      .select('referee_id')
      .eq('id', matchId)
      .single()

    if (match?.referee_id === userId) {
      return {
        canBet: false,
        reason: 'Cannot place bets on matches you are refereeing'
      }
    }

    // Check if user has upcoming referee assignments within cooldown
    const cooldownDate = new Date()
    cooldownDate.setHours(cooldownDate.getHours() + this.conflictRules.bettingCooldownHours)

    const { data: upcomingRefereeMatches } = await supabase
      .from('matches')
      .select('id')
      .eq('referee_id', userId)
      .lte('scheduled_time', cooldownDate.toISOString())

    if (upcomingRefereeMatches && upcomingRefereeMatches.length > 0) {
      return {
        canBet: false,
        reason: `Cannot place bets within ${this.conflictRules.bettingCooldownHours} hours of referee assignments`
      }
    }

    return { canBet: true, reason: 'No conflicts detected' }
  }

  /**
   * Check if user can participate in match (not currently assigned as referee)
   */
  async canUserParticipate(userId: string, matchId: string): Promise<{ canParticipate: boolean; reason: string }> {
    // Check if user is assigned as referee for this match
    const { data: match } = await supabase
      .from('matches')
      .select('referee_id')
      .eq('id', matchId)
      .single()

    if (match?.referee_id === userId) {
      return {
        canParticipate: false,
        reason: 'Cannot participate in matches you are refereeing'
      }
    }

    return { canParticipate: true, reason: 'No conflicts detected' }
  }
}

export const refereeConflictService = new RefereeConflictService()
