import { supabase } from './supabase'
import { refereeConflictService } from './refereeConflictService'

export interface BetData {
  id: string
  user_id: string
  match_id: string
  amount: number
  predicted_winner: string
  odds: number
  status: 'pending' | 'won' | 'lost' | 'cancelled'
  created_at: string
}

export interface BetValidationResult {
  canBet: boolean
  reason: string
  conflictType?: 'referee_conflict' | 'insufficient_balance' | 'match_unavailable' | 'duplicate_bet'
}

class BettingService {
  /**
   * Validate if user can place a bet on a match
   */
  async validateBet(userId: string, matchId: string, betAmount: number): Promise<BetValidationResult> {
    try {
      // Check if user is a referee with conflicts
      const refereeConflict = await refereeConflictService.canUserBet(userId, matchId)
      if (!refereeConflict.canBet) {
        return {
          canBet: false,
          reason: refereeConflict.reason,
          conflictType: 'referee_conflict'
        }
      }

      // Check user's balance
      const { data: user } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (!user || (user.diamond_balance || 0) < betAmount) {
        return {
          canBet: false,
          reason: `Insufficient balance. You have ${user?.diamond_balance || 0} diamonds, but need ${betAmount}`,
          conflictType: 'insufficient_balance'
        }
      }

      // Check if match is available for betting
      const { data: match } = await supabase
        .from('matches')
        .select('status, scheduled_start_time')
        .eq('id', matchId)
        .single()

      if (!match) {
        return {
          canBet: false,
          reason: 'Match not found',
          conflictType: 'match_unavailable'
        }
      }

      if (!['open', 'waiting'].includes(match.status)) {
        return {
          canBet: false,
          reason: 'Match is no longer available for betting',
          conflictType: 'match_unavailable'
        }
      }

      // Check if user already has a bet on this match
      const { data: existingBet } = await supabase
        .from('match_bets')
        .select('id')
        .eq('user_id', userId)
        .eq('match_id', matchId)
        .eq('status', 'pending')
        .single()

      if (existingBet) {
        return {
          canBet: false,
          reason: 'You already have a bet on this match',
          conflictType: 'duplicate_bet'
        }
      }

      return {
        canBet: true,
        reason: 'Bet validation passed'
      }

    } catch (error) {
      console.error('Error validating bet:', error)
      return {
        canBet: false,
        reason: 'Error validating bet - please try again',
        conflictType: 'match_unavailable'
      }
    }
  }

  /**
   * Place a bet on a match
   */
  async placeBet(
    userId: string, 
    matchId: string, 
    betAmount: number, 
    predictedWinner: string,
    odds: number = 1.0
  ): Promise<{ success: boolean; data?: BetData; error?: string }> {
    try {
      // Validate the bet first
      const validation = await this.validateBet(userId, matchId, betAmount)
      if (!validation.canBet) {
        return {
          success: false,
          error: validation.reason
        }
      }

      // Start transaction
      const { data: bet, error: betError } = await supabase
        .from('match_bets')
        .insert({
          user_id: userId,
          match_id: matchId,
          amount: betAmount,
          predicted_winner: predictedWinner,
          odds: odds,
          status: 'pending',
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (betError) throw betError

      // Deduct amount from user's balance
      // Get current balance first
      const { data: userData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (!userData) {
        throw new Error('User not found')
      }

      const newBalance = userData.diamond_balance - betAmount
      const { error: balanceError } = await supabase
        .from('users')
        .update({
          diamond_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (balanceError) {
        // Rollback bet if balance update fails
        await supabase
          .from('match_bets')
          .delete()
          .eq('id', bet.id)
        
        throw balanceError
      }

      // Create transaction record
      await supabase
        .from('transactions')
        .insert({
          user_id: userId,
          type: 'match_bet',
          amount: -betAmount,
          status: 'completed',
          description: `Bet placed on match - ${betAmount} diamonds`,
          match_id: matchId,
          created_at: new Date().toISOString()
        })

      // Log betting restriction (prevent referee assignment)
      await supabase
        .from('referee_betting_restrictions')
        .insert({
          user_id: userId,
          match_id: matchId,
          restriction_type: 'active_bet',
          restriction_start: new Date().toISOString(),
          restriction_end: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours
          reason: 'User has active bet on this match',
          created_at: new Date().toISOString()
        })

      return {
        success: true,
        data: bet
      }

    } catch (error) {
      console.error('Error placing bet:', error)
      return {
        success: false,
        error: 'Failed to place bet. Please try again.'
      }
    }
  }

  /**
   * Get user's bets
   */
  async getUserBets(userId: string, status?: string): Promise<BetData[]> {
    try {
      let query = supabase
        .from('match_bets')
        .select(`
          *,
          matches!inner(
            game,
            status,
            scheduled_start_time
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      const { data, error } = await query

      if (error) throw error
      return data || []

    } catch (error) {
      console.error('Error getting user bets:', error)
      return []
    }
  }

  /**
   * Cancel a bet (if match hasn't started)
   */
  async cancelBet(betId: string, userId: string): Promise<{ success: boolean; error?: string }> {
    try {
      // Get bet details
      const { data: bet, error: betError } = await supabase
        .from('match_bets')
        .select(`
          *,
          matches!inner(status)
        `)
        .eq('id', betId)
        .eq('user_id', userId)
        .eq('status', 'pending')
        .single()

      if (betError || !bet) {
        return {
          success: false,
          error: 'Bet not found or cannot be cancelled'
        }
      }

      // Check if match hasn't started
      if (!['open', 'waiting'].includes(bet.matches.status)) {
        return {
          success: false,
          error: 'Cannot cancel bet - match has already started'
        }
      }

      // Cancel the bet
      const { error: cancelError } = await supabase
        .from('match_bets')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', betId)

      if (cancelError) throw cancelError

      // Get current balance and refund the amount
      const { data: userData } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (!userData) {
        throw new Error('User not found')
      }

      const newBalance = userData.diamond_balance + bet.amount
      const { error: refundError } = await supabase
        .from('users')
        .update({
          diamond_balance: newBalance,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (refundError) throw refundError

      // Create refund transaction
      await supabase
        .from('transactions')
        .insert({
          user_id: userId,
          type: 'bet_refund',
          amount: bet.amount,
          status: 'completed',
          description: `Bet cancelled - ${bet.amount} diamonds refunded`,
          match_id: bet.match_id,
          created_at: new Date().toISOString()
        })

      // Remove betting restriction
      await supabase
        .from('referee_betting_restrictions')
        .delete()
        .eq('user_id', userId)
        .eq('match_id', bet.match_id)
        .eq('restriction_type', 'active_bet')

      return { success: true }

    } catch (error) {
      console.error('Error cancelling bet:', error)
      return {
        success: false,
        error: 'Failed to cancel bet. Please try again.'
      }
    }
  }

  /**
   * Process bet results when match completes
   */
  async processBetResults(matchId: string, winnerId: string): Promise<void> {
    try {
      // Get all pending bets for this match
      const { data: bets } = await supabase
        .from('match_bets')
        .select('*')
        .eq('match_id', matchId)
        .eq('status', 'pending')

      if (!bets || bets.length === 0) return

      // Process each bet
      for (const bet of bets) {
        const isWinner = bet.predicted_winner === winnerId
        const newStatus = isWinner ? 'won' : 'lost'

        // Update bet status
        await supabase
          .from('match_bets')
          .update({
            status: newStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', bet.id)

        // If winner, calculate and award winnings
        if (isWinner) {
          const winnings = Math.floor(bet.amount * bet.odds)
          
          // Get current balance and award winnings
          const { data: winnerData } = await supabase
            .from('users')
            .select('diamond_balance')
            .eq('id', bet.user_id)
            .single()

          if (winnerData) {
            const newBalance = winnerData.diamond_balance + winnings
            await supabase
              .from('users')
              .update({
                diamond_balance: newBalance,
                updated_at: new Date().toISOString()
              })
              .eq('id', bet.user_id)
          }

          // Create winning transaction
          await supabase
            .from('transactions')
            .insert({
              user_id: bet.user_id,
              type: 'bet_win',
              amount: winnings,
              status: 'completed',
              description: `Bet won - ${winnings} diamonds awarded`,
              match_id: matchId,
              created_at: new Date().toISOString()
            })
        }

        // Remove betting restrictions
        await supabase
          .from('referee_betting_restrictions')
          .delete()
          .eq('user_id', bet.user_id)
          .eq('match_id', matchId)
          .eq('restriction_type', 'active_bet')
      }

    } catch (error) {
      console.error('Error processing bet results:', error)
    }
  }
}

export const bettingService = new BettingService()
