import React, { useState, useEffect } from 'react'
import {
  BarChart3,
  Users,
  Gamepad2,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Activity,
  Calendar,
  AlertCircle
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

interface AdminStats {
  totalUsers: number
  activeUsers: number
  totalMatches: number
  completedMatches: number
  totalRevenue: number
  totalTransactions: number
  averageMatchDuration: number
  topGames: {
    name: string
    matches: number
    revenue: number
  }[]
  userGrowth: {
    date: string
    users: number
    newUsers: number
  }[]
  revenueData: {
    date: string
    revenue: number
    transactions: number
  }[]
}

export default function AdminAnalytics() {
  const { user } = useAuth()
  const [stats, setStats] = useState<AdminStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<'7d' | '30d' | '90d'>('30d')

  useEffect(() => {
    if (user?.admin_level && user.admin_level > 0) {
      loadAdminStats()
    }
  }, [user, timeframe])

  const loadAdminStats = async () => {
    setLoading(true)
    try {
      // Mock data - in real app, fetch from admin API
      const mockStats: AdminStats = {
        totalUsers: 2847,
        activeUsers: 1234,
        totalMatches: 5678,
        completedMatches: 4892,
        totalRevenue: 125000,
        totalTransactions: 8934,
        averageMatchDuration: 22.5,
        topGames: [
          { name: 'Mobile Legends', matches: 2340, revenue: 67500 },
          { name: 'Valorant', matches: 1890, revenue: 45200 },
          { name: 'Call of Duty Mobile', matches: 1448, revenue: 12300 }
        ],
        userGrowth: [
          { date: '2024-01-01', users: 2500, newUsers: 45 },
          { date: '2024-01-02', users: 2545, newUsers: 52 },
          { date: '2024-01-03', users: 2597, newUsers: 38 },
          { date: '2024-01-04', users: 2635, newUsers: 67 },
          { date: '2024-01-05', users: 2702, newUsers: 89 }
        ],
        revenueData: [
          { date: '2024-01-01', revenue: 2340, transactions: 156 },
          { date: '2024-01-02', revenue: 3450, transactions: 234 },
          { date: '2024-01-03', revenue: 2890, transactions: 189 },
          { date: '2024-01-04', revenue: 4120, transactions: 267 },
          { date: '2024-01-05', revenue: 3780, transactions: 245 }
        ]
      }
      setStats(mockStats)
    } catch (error) {
      console.error('Error loading admin stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon, 
    color 
  }: { 
    title: string
    value: string | number
    change?: { value: number; isPositive: boolean }
    icon: React.ReactNode
    color: string
  }) => (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              change.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {change.isPositive ? (
                <TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1" />
              )}
              <span>{change.value.toFixed(1)}%</span>
            </div>
          )}
        </div>
        <div className={`${color} p-3 rounded-lg`}>
          {icon}
        </div>
      </div>
    </div>
  )

  if (!user?.admin_level || user.admin_level === 0) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 text-center">
        <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
        <p className="text-gray-600">You need admin privileges to view analytics.</p>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!stats) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 text-center">
        <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-600">Unable to load analytics data.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Platform Analytics</h2>
        <div className="flex space-x-1">
          {[
            { key: '7d', label: '7 Days' },
            { key: '30d', label: '30 Days' },
            { key: '90d', label: '90 Days' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setTimeframe(tab.key as any)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeframe === tab.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Users"
          value={stats.totalUsers.toLocaleString()}
          change={{ value: 12.5, isPositive: true }}
          icon={<Users className="w-6 h-6 text-white" />}
          color="bg-blue-500"
        />
        <StatCard
          title="Active Users"
          value={stats.activeUsers.toLocaleString()}
          change={{ value: 8.3, isPositive: true }}
          icon={<Activity className="w-6 h-6 text-white" />}
          color="bg-green-500"
        />
        <StatCard
          title="Total Matches"
          value={stats.totalMatches.toLocaleString()}
          change={{ value: 15.7, isPositive: true }}
          icon={<Gamepad2 className="w-6 h-6 text-white" />}
          color="bg-purple-500"
        />
        <StatCard
          title="Revenue"
          value={`₱${stats.totalRevenue.toLocaleString()}`}
          change={{ value: 23.1, isPositive: true }}
          icon={<DollarSign className="w-6 h-6 text-white" />}
          color="bg-amber-500"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Games */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Games</h3>
          <div className="space-y-4">
            {stats.topGames.map((game, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    index === 0 ? 'bg-blue-500' :
                    index === 1 ? 'bg-green-500' : 'bg-purple-500'
                  }`}></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{game.name}</p>
                    <p className="text-xs text-gray-600">{game.matches} matches</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900">₱{game.revenue.toLocaleString()}</p>
                  <p className="text-xs text-gray-600">Revenue</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Completion Rate</span>
              <span className="text-sm font-medium text-gray-900">
                {((stats.completedMatches / stats.totalMatches) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Avg Match Duration</span>
              <span className="text-sm font-medium text-gray-900">{stats.averageMatchDuration} min</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Transactions</span>
              <span className="text-sm font-medium text-gray-900">{stats.totalTransactions.toLocaleString()}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">User Activity Rate</span>
              <span className="text-sm font-medium text-gray-900">
                {((stats.activeUsers / stats.totalUsers) * 100).toFixed(1)}%
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trend</h3>
        <div className="space-y-3">
          {stats.revenueData.slice(-5).map((day, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-sm text-gray-600">
                  {new Date(day.date).toLocaleDateString('en-US', { 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">₱{day.revenue.toLocaleString()}</p>
                  <p className="text-xs text-gray-600">{day.transactions} transactions</p>
                </div>
                <div className="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${(day.revenue / Math.max(...stats.revenueData.map(d => d.revenue))) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
