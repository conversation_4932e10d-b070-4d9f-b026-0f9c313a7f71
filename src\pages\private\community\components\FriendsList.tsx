import { useState, memo, useMemo } from 'react'
import {
  UserPlus,
  Search,
  Users,
  MessageCircle,
  MoreVertical,
  User<PERSON><PERSON><PERSON>,
  <PERSON>,
  Star,
  Crown,
  Trophy,
  Gamepad2,
  Shield,
  X,
  Check
} from 'lucide-react'
import { useAuth } from '../../../../contexts/AuthContext'

interface Friend {
  id: string
  username: string
  avatar?: string
  isOnline: boolean
  lastSeen?: string
  status?: 'online' | 'away' | 'busy' | 'offline'
  gameStatus?: {
    game: string
    status: string
  }
  mutualFriends: number
  joinedDate: string
  level: number
  rank?: string
  badges?: string[]
}

interface FriendRequest {
  id: string
  username: string
  avatar?: string
  sentAt: string
  mutualFriends: number
  level: number
  type: 'incoming' | 'outgoing'
}

interface FriendsListProps {
  friends: Friend[]
  friendRequests: FriendRequest[]
  onSendFriendRequest: (username: string) => void
  onAcceptFriendRequest: (requestId: string) => void
  onDeclineFriendRequest: (requestId: string) => void
  onRemoveFriend: (friendId: string) => void
  onStartChat: (friendId: string) => void
}

const FriendsList = memo<FriendsListProps>(({
  friends,
  friendRequests,
  onSendFriendRequest,
  onAcceptFriendRequest,
  onDeclineFriendRequest,
  onRemoveFriend,
  onStartChat
}) => {
  const { } = useAuth() // TODO: Confirm user usage
  const [activeTab, setActiveTab] = useState<'friends' | 'requests' | 'add'>('friends')
  const [searchQuery, setSearchQuery] = useState('')
  const [newFriendUsername, setNewFriendUsername] = useState('')
  const [isSearching, setIsSearching] = useState(false)

  // 🚀 MEMOIZED: Filter friends based on search query
  const filteredFriends = useMemo(() =>
    friends.filter(friend =>
      friend.username.toLowerCase().includes(searchQuery.toLowerCase())
    ),
    [friends, searchQuery]
  )

  const incomingRequests = friendRequests.filter(req => req.type === 'incoming')
  const outgoingRequests = friendRequests.filter(req => req.type === 'outgoing')

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'online': return 'bg-green-500'
      case 'away': return 'bg-yellow-500'
      case 'busy': return 'bg-red-500'
      default: return 'bg-gray-400'
    }
  }

  const getBadgeIcon = (badge: string) => {
    switch (badge) {
      case 'vip': return <Crown className="w-3 h-3 text-yellow-500" />
      case 'champion': return <Trophy className="w-3 h-3 text-gold-500" />
      case 'verified': return <Shield className="w-3 h-3 text-blue-500" />
      default: return <Star className="w-3 h-3 text-gray-500" />
    }
  }

  const handleSendFriendRequest = () => {
    if (!newFriendUsername.trim()) return
    
    setIsSearching(true)
    onSendFriendRequest(newFriendUsername)
    setNewFriendUsername('')
    
    // Simulate search delay
    setTimeout(() => {
      setIsSearching(false)
    }, 1000)
  }

  const formatLastSeen = (lastSeen?: string) => {
    if (!lastSeen) return 'Never'
    const date = new Date(lastSeen)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Friends</h2>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-600">{friends.length} friends</span>
            {incomingRequests.length > 0 && (
              <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1">
                {incomingRequests.length}
              </span>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          {[
            { key: 'friends', label: 'Friends', count: friends.length },
            { key: 'requests', label: 'Requests', count: friendRequests.length },
            { key: 'add', label: 'Add Friends', count: 0 }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === tab.key
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label}
              {tab.count > 0 && (
                <span className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                  activeTab === tab.key ? 'bg-blue-100' : 'bg-gray-200'
                }`}>
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="p-4">
        {activeTab === 'friends' && (
          <div className="space-y-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search friends..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Friends List */}
            {filteredFriends.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No friends yet</h3>
                <p className="text-gray-600 mb-4">Start building your gaming network!</p>
                <button
                  onClick={() => setActiveTab('add')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  Add Friends
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredFriends.map((friend) => (
                  <div key={friend.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <div className="flex items-center space-x-3">
                      {/* Avatar */}
                      <div className="relative">
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                          {friend.username.charAt(0).toUpperCase()}
                        </div>
                        <div className={`absolute -bottom-0.5 -right-0.5 w-4 h-4 rounded-full border-2 border-white ${getStatusColor(friend.status)}`} />
                      </div>

                      {/* Friend Info */}
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-medium text-gray-900">{friend.username}</h3>
                          <span className="text-sm text-gray-500">Lv.{friend.level}</span>
                          {friend.badges?.map((badge, index) => (
                            <div key={index} className="flex items-center">
                              {getBadgeIcon(badge)}
                            </div>
                          ))}
                        </div>
                        
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>{friend.status === 'online' ? 'Online' : `Last seen ${formatLastSeen(friend.lastSeen)}`}</span>
                          {friend.mutualFriends > 0 && (
                            <span>{friend.mutualFriends} mutual friends</span>
                          )}
                        </div>
                        
                        {friend.gameStatus && (
                          <div className="flex items-center space-x-1 text-sm text-green-600 mt-1">
                            <Gamepad2 className="w-3 h-3" />
                            <span>Playing {friend.gameStatus.game}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => onStartChat(friend.id)}
                        className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                        title="Start chat"
                      >
                        <MessageCircle className="w-4 h-4" />
                      </button>
                      <div className="relative group">
                        <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                          <MoreVertical className="w-4 h-4" />
                        </button>
                        <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                          <button
                            onClick={() => onRemoveFriend(friend.id)}
                            className="w-full px-4 py-2 text-left text-red-600 hover:bg-red-50 text-sm"
                          >
                            Remove Friend
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'requests' && (
          <div className="space-y-4">
            {/* Incoming Requests */}
            {incomingRequests.length > 0 && (
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Incoming Requests ({incomingRequests.length})</h3>
                <div className="space-y-3">
                  {incomingRequests.map((request) => (
                    <div key={request.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                          {request.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{request.username}</h4>
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Lv.{request.level}</span>
                            {request.mutualFriends > 0 && (
                              <span>{request.mutualFriends} mutual friends</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => onAcceptFriendRequest(request.id)}
                          className="p-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                          title="Accept"
                        >
                          <Check className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => onDeclineFriendRequest(request.id)}
                          className="p-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                          title="Decline"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Outgoing Requests */}
            {outgoingRequests.length > 0 && (
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Sent Requests ({outgoingRequests.length})</h3>
                <div className="space-y-3">
                  {outgoingRequests.map((request) => (
                    <div key={request.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-yellow-500 to-orange-600 rounded-full flex items-center justify-center text-white font-medium">
                          {request.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{request.username}</h4>
                          <p className="text-sm text-gray-600">Request sent</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm text-gray-600">Pending</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {friendRequests.length === 0 && (
              <div className="text-center py-8">
                <UserCheck className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No friend requests</h3>
                <p className="text-gray-600">You're all caught up!</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'add' && (
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-3">Add New Friends</h3>
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder="Enter username..."
                  value={newFriendUsername}
                  onChange={(e) => setNewFriendUsername(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendFriendRequest()}
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={handleSendFriendRequest}
                  disabled={!newFriendUsername.trim() || isSearching}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
                >
                  {isSearching ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <UserPlus className="w-4 h-4" />
                  )}
                  <span>Add Friend</span>
                </button>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Tips for finding friends:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Search by exact username</li>
                <li>• Ask friends for their usernames</li>
                <li>• Meet players in matches and groups</li>
                <li>• Check leaderboards for top players</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  )
})

export default FriendsList
