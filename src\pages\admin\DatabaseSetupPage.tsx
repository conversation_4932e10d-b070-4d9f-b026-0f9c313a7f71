import { useState, useEffect } from 'react'
import { Database, CheckCircle, AlertTriangle, Play, RefreshCw, Shield } from 'lucide-react'
import { setupRefereeConflictSystem, checkSystemSetup, testConflictSystem, getSystemStats } from '../../services/setupDatabase'

interface SetupStatus {
  isSetup: boolean
  tables: Array<{
    table: string
    exists: boolean
    error?: string
  }>
  message: string
}

interface SystemStats {
  totalConflicts: number
  totalRecusals: number
  activeRestrictions: number
  systemHealth: string
}

export default function DatabaseSetupPage() {
  const [setupStatus, setSetupStatus] = useState<SetupStatus | null>(null)
  const [systemStats, setSystemStats] = useState<SystemStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [setupLoading, setSetupLoading] = useState(false)
  const [testLoading, setTestLoading] = useState(false)

  useEffect(() => {
    checkSetup()
    loadStats()
  }, [])

  const checkSetup = async () => {
    setLoading(true)
    try {
      const result = await checkSystemSetup()
      setSetupStatus({
        isSetup: result.success,
        tables: result.tables || [],
        message: result.message
      })
    } catch (error) {
      console.error('Error checking setup:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const result = await getSystemStats()
      if (result.success) {
        setSystemStats(result.stats || null)
      }
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const runSetup = async () => {
    setSetupLoading(true)
    try {
      const result = await setupRefereeConflictSystem()
      if (result.success) {
        alert('✅ Database setup completed successfully!')
        await checkSetup()
        await loadStats()
      } else {
        alert('❌ Database setup failed. Check console for details.')
      }
    } catch (error) {
      console.error('Setup error:', error)
      alert('❌ Database setup failed. Check console for details.')
    } finally {
      setSetupLoading(false)
    }
  }

  const runTest = async () => {
    setTestLoading(true)
    try {
      const result = await testConflictSystem()
      if (result.success) {
        alert('✅ System test passed! Conflict detection is working properly.')
      } else {
        alert('❌ System test failed. Check console for details.')
      }
    } catch (error) {
      console.error('Test error:', error)
      alert('❌ System test failed. Check console for details.')
    } finally {
      setTestLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <Database className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Database Setup</h1>
              <p className="text-gray-600">Referee Conflict Prevention System</p>
            </div>
          </div>

          {/* Setup Status */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
              <button
                onClick={checkSetup}
                disabled={loading}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 font-medium"
              >
                <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
            </div>

            {loading ? (
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            ) : setupStatus ? (
              <div>
                <div className={`flex items-center space-x-2 mb-3 ${
                  setupStatus.isSetup ? 'text-green-600' : 'text-red-600'
                }`}>
                  {setupStatus.isSetup ? (
                    <CheckCircle className="w-5 h-5" />
                  ) : (
                    <AlertTriangle className="w-5 h-5" />
                  )}
                  <span className="font-medium">{setupStatus.message}</span>
                </div>

                {/* Table Status */}
                <div className="grid grid-cols-2 gap-3">
                  {setupStatus.tables.map((table) => (
                    <div
                      key={table.table}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        table.exists
                          ? 'bg-green-50 border-green-200 text-green-800'
                          : 'bg-red-50 border-red-200 text-red-800'
                      }`}
                    >
                      <span className="font-medium">{table.table}</span>
                      {table.exists ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        <AlertTriangle className="w-4 h-4" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-gray-500">Loading system status...</div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Database className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Setup Database</h3>
                <p className="text-sm text-gray-600">Create all required tables</p>
              </div>
            </div>
            <button
              onClick={runSetup}
              disabled={setupLoading}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              {setupLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span>{setupLoading ? 'Setting up...' : 'Run Setup'}</span>
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <CheckCircle className="w-5 h-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Test System</h3>
                <p className="text-sm text-gray-600">Verify conflict detection</p>
              </div>
            </div>
            <button
              onClick={runTest}
              disabled={testLoading || !setupStatus?.isSetup}
              className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              {testLoading ? (
                <RefreshCw className="w-4 h-4 animate-spin" />
              ) : (
                <Play className="w-4 h-4" />
              )}
              <span>{testLoading ? 'Testing...' : 'Run Test'}</span>
            </button>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Shield className="w-5 h-5 text-purple-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">System Stats</h3>
                <p className="text-sm text-gray-600">Monitor system health</p>
              </div>
            </div>
            <button
              onClick={loadStats}
              className="w-full bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh Stats</span>
            </button>
          </div>
        </div>

        {/* System Statistics */}
        {systemStats && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">System Statistics</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div className="text-2xl font-bold text-blue-900">{systemStats.totalConflicts}</div>
                <div className="text-sm text-blue-600">Total Conflicts</div>
              </div>
              <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                <div className="text-2xl font-bold text-red-900">{systemStats.totalRecusals}</div>
                <div className="text-sm text-red-600">Total Recusals</div>
              </div>
              <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-200">
                <div className="text-2xl font-bold text-yellow-900">{systemStats.activeRestrictions}</div>
                <div className="text-sm text-yellow-600">Active Restrictions</div>
              </div>
              <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                <div className="text-2xl font-bold text-green-900 capitalize">{systemStats.systemHealth}</div>
                <div className="text-sm text-green-600">System Health</div>
              </div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Setup Instructions</h3>
          <div className="space-y-3 text-sm text-gray-600">
            <div className="flex items-start space-x-2">
              <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">1</span>
              <span>Click "Run Setup" to create all required database tables for the referee conflict prevention system.</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">2</span>
              <span>After setup is complete, click "Run Test" to verify that the conflict detection system is working properly.</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">3</span>
              <span>Monitor system statistics to track conflicts, recusals, and overall system health.</span>
            </div>
            <div className="flex items-start space-x-2">
              <span className="bg-blue-100 text-blue-800 rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">4</span>
              <span>The system will automatically prevent referee-player conflicts in real-time once setup is complete.</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
