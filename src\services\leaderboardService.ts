// ============================================================================
// LEADERBOARD SERVICE - IMPROVED VERSION
// ============================================================================
// Enhanced leaderboard service that works with existing database schema

import { supabase } from './supabase'

export interface LeaderboardEntry {
  rank: number
  userId: string
  username: string
  avatar?: string
  value: number
  change: number
  guild?: string
  isOnline: boolean
  level?: number
  winRate?: number
  totalMatches?: number
  badges?: string[]
}

export interface LeaderboardData {
  entries: LeaderboardEntry[]
  userRank?: number
  totalPlayers: number
  lastUpdated: string
}

class LeaderboardService {
  // ============================================================================
  // DIAMONDS LEADERBOARD
  // ============================================================================
  
  async getDiamondsLeaderboard(limit: number = 10): Promise<{ data: LeaderboardData | null, error: any }> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          first_name,
          last_name,
          diamond_balance,
          wins,
          losses,
          total_matches,
          created_at,
          updated_at
        `)
        .order('diamond_balance', { ascending: false })
        .limit(limit)

      if (error) throw error

      const entries: LeaderboardEntry[] = (data || []).map((user, index) => ({
        rank: index + 1,
        userId: user.id,
        username: user.username,
        avatar: user.username.charAt(0).toUpperCase(),
        value: user.diamond_balance,
        change: this.calculateRankChange(user.diamond_balance, index),
        guild: 'Gaming Squad', // TODO: Get from groups table when available
        isOnline: this.isRecentlyActive(user.updated_at),
        level: this.calculateLevel(user.total_matches || 0),
        winRate: this.calculateWinRate(user.wins || 0, user.total_matches || 0),
        totalMatches: user.total_matches || 0,
        badges: this.getBadges(user)
      }))

      // Get total player count
      const { count: totalPlayers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })

      const leaderboardData: LeaderboardData = {
        entries,
        totalPlayers: totalPlayers || 0,
        lastUpdated: new Date().toISOString()
      }

      return { data: leaderboardData, error: null }
    } catch (error) {
      console.error('Error getting diamonds leaderboard:', error)
      return { data: null, error }
    }
  }

  // ============================================================================
  // WINS LEADERBOARD
  // ============================================================================
  
  async getWinsLeaderboard(limit: number = 10): Promise<{ data: LeaderboardData | null, error: any }> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          first_name,
          last_name,
          diamond_balance,
          wins,
          losses,
          total_matches,
          created_at,
          updated_at
        `)
        .order('wins', { ascending: false })
        .limit(limit)

      if (error) throw error

      const entries: LeaderboardEntry[] = (data || []).map((user, index) => ({
        rank: index + 1,
        userId: user.id,
        username: user.username,
        avatar: user.username.charAt(0).toUpperCase(),
        value: user.wins || 0,
        change: this.calculateRankChange(user.wins || 0, index),
        guild: 'Pro Gamers', // TODO: Get from groups table when available
        isOnline: this.isRecentlyActive(user.updated_at),
        level: this.calculateLevel(user.total_matches || 0),
        winRate: this.calculateWinRate(user.wins || 0, user.total_matches || 0),
        totalMatches: user.total_matches || 0,
        badges: this.getBadges(user)
      }))

      // Get total player count
      const { count: totalPlayers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })

      const leaderboardData: LeaderboardData = {
        entries,
        totalPlayers: totalPlayers || 0,
        lastUpdated: new Date().toISOString()
      }

      return { data: leaderboardData, error: null }
    } catch (error) {
      console.error('Error getting wins leaderboard:', error)
      return { data: null, error }
    }
  }

  // ============================================================================
  // REFERRALS LEADERBOARD
  // ============================================================================
  
  async getReferralsLeaderboard(limit: number = 10): Promise<{ data: LeaderboardData | null, error: any }> {
    try {
      // Count referrals for each user
      const { data, error } = await supabase
        .from('users')
        .select(`
          id,
          username,
          first_name,
          last_name,
          diamond_balance,
          wins,
          losses,
          total_matches,
          referral_code,
          created_at,
          updated_at,
          referrals:users!referred_by(count)
        `)
        .not('referral_code', 'is', null)
        .order('diamond_balance', { ascending: false }) // Fallback ordering
        .limit(limit * 2) // Get more to sort by referral count

      if (error) throw error

      // Transform and sort by referral count
      const usersWithReferrals = (data || []).map(user => ({
        ...user,
        referral_count: user.referrals?.[0]?.count || 0
      })).sort((a, b) => b.referral_count - a.referral_count).slice(0, limit)

      const entries: LeaderboardEntry[] = usersWithReferrals.map((user, index) => ({
        rank: index + 1,
        userId: user.id,
        username: user.username,
        avatar: user.username.charAt(0).toUpperCase(),
        value: user.referral_count,
        change: this.calculateRankChange(user.referral_count, index),
        guild: 'Referral Masters', // TODO: Get from groups table when available
        isOnline: this.isRecentlyActive(user.updated_at),
        level: this.calculateLevel(user.total_matches || 0),
        winRate: this.calculateWinRate(user.wins || 0, user.total_matches || 0),
        totalMatches: user.total_matches || 0,
        badges: this.getBadges(user)
      }))

      // Get total player count with referrals
      const { count: totalPlayers } = await supabase
        .from('users')
        .select('*', { count: 'exact', head: true })
        .not('referral_code', 'is', null)

      const leaderboardData: LeaderboardData = {
        entries,
        totalPlayers: totalPlayers || 0,
        lastUpdated: new Date().toISOString()
      }

      return { data: leaderboardData, error: null }
    } catch (error) {
      console.error('Error getting referrals leaderboard:', error)
      return { data: null, error }
    }
  }

  // ============================================================================
  // GET USER RANK
  // ============================================================================
  
  async getUserRank(userId: string, type: 'diamonds' | 'wins' | 'referrals'): Promise<{ rank: number | null, error: any }> {
    try {
      let orderBy = 'diamond_balance'
      if (type === 'wins') orderBy = 'wins'
      
      if (type === 'referrals') {
        // For referrals, we need a more complex query
        const { data, error } = await supabase
          .from('users')
          .select('id, referral_code')
          .not('referral_code', 'is', null)
          .order('diamond_balance', { ascending: false })

        if (error) throw error
        
        const userIndex = (data || []).findIndex(user => user.id === userId)
        return { rank: userIndex >= 0 ? userIndex + 1 : null, error: null }
      }

      const { data, error } = await supabase
        .from('users')
        .select('id')
        .order(orderBy, { ascending: false })

      if (error) throw error

      const userIndex = (data || []).findIndex(user => user.id === userId)
      return { rank: userIndex >= 0 ? userIndex + 1 : null, error: null }
    } catch (error) {
      console.error('Error getting user rank:', error)
      return { rank: null, error }
    }
  }

  // ============================================================================
  // HELPER FUNCTIONS
  // ============================================================================

  private calculateRankChange(value: number, currentRank: number): number {
    // Simple algorithm to simulate rank changes
    // In a real app, you'd compare with previous rankings
    const baseChange = Math.floor(Math.random() * 5) - 2 // -2 to +2
    const valueBonus = value > 1000 ? 1 : 0
    const rankPenalty = currentRank > 5 ? -1 : 0
    
    return baseChange + valueBonus + rankPenalty
  }

  private isRecentlyActive(lastUpdate: string): boolean {
    const now = new Date()
    const lastActive = new Date(lastUpdate)
    const diffInMinutes = (now.getTime() - lastActive.getTime()) / (1000 * 60)
    
    return diffInMinutes < 30 // Active if updated within 30 minutes
  }

  private calculateLevel(totalMatches: number): number {
    // Simple level calculation based on matches played
    if (totalMatches >= 100) return Math.min(50, Math.floor(totalMatches / 10))
    if (totalMatches >= 50) return Math.floor(totalMatches / 5)
    if (totalMatches >= 10) return Math.floor(totalMatches / 2)
    return Math.max(1, totalMatches)
  }

  private calculateWinRate(wins: number, totalMatches: number): number {
    if (totalMatches === 0) return 0
    return Math.round((wins / totalMatches) * 100)
  }

  private getBadges(user: any): string[] {
    const badges: string[] = []
    
    const winRate = this.calculateWinRate(user.wins || 0, user.total_matches || 0)
    const diamonds = user.diamond_balance || 0
    
    if (winRate >= 80) badges.push('champion')
    if (diamonds >= 10000) badges.push('vip')
    if ((user.total_matches || 0) >= 100) badges.push('veteran')
    if (user.referral_code) badges.push('recruiter')
    
    return badges
  }

  // ============================================================================
  // COMBINED LEADERBOARD FUNCTION
  // ============================================================================
  
  async getLeaderboard(type: 'diamonds' | 'wins' | 'referrals', limit: number = 10): Promise<{ data: LeaderboardData | null, error: any }> {
    switch (type) {
      case 'diamonds':
        return this.getDiamondsLeaderboard(limit)
      case 'wins':
        return this.getWinsLeaderboard(limit)
      case 'referrals':
        return this.getReferralsLeaderboard(limit)
      default:
        return this.getDiamondsLeaderboard(limit)
    }
  }
}

export const leaderboardService = new LeaderboardService()
export default leaderboardService
