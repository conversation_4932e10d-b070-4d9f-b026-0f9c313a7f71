import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  User,
  Bell,
  Shield,
  Smartphone,
  Globe,
  Save,
  Eye,
  EyeOff,
  CheckCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'
import { supabase } from '../../../services/supabaseClient'
import { useNotifications } from '../../../contexts/NotificationContext'

export default function SettingsPage() {
  const { user, refreshUser } = useAuth()
  const { addNotification } = useNotifications()
  const [activeTab, setActiveTab] = useState<'profile' | 'notifications' | 'security' | 'preferences'>('profile')
  const [showPassword, setShowPassword] = useState(false)
  const [, setLoading] = useState(false) // loading unused, keeping setter
  const [saveLoading, setSaveLoading] = useState<string | null>(null)
  const [settings, setSettings] = useState({
    // Profile settings
    firstName: user?.first_name || '',
    lastName: user?.last_name || '',
    username: user?.username || '',
    email: user?.email || '',
    mlbbId: user?.mlbb_id || '',
    valorantId: user?.valorant_id || '',
    dotaId: user?.dota_id || '',
    codId: user?.cod_id || '',
    pubgId: user?.pubg_id || '',
    lolId: user?.lol_id || '',
    csId: user?.cs_id || '',
    bio: user?.bio || '',

    // Notification settings (mapped to existing database columns)
    perfectMatchEnabled: true,
    friendActivityEnabled: true,
    priceAlertsEnabled: true,
    winStreakEnabled: true,
    bonusOpportunitiesEnabled: true,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    maxDailyNotifications: 10,

    // Security settings
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorAuth: false,

    // Preference settings
    language: 'en',
    timezone: 'Asia/Manila',
    theme: 'light',
    currency: 'PHP',
    autoJoinMatches: false,
    showOnlineStatus: true,
    allowFriendRequests: true
  })

  // Load user preferences on component mount
  useEffect(() => {
    loadUserPreferences()
  }, [user])

  const loadUserPreferences = async () => {
    if (!user) return

    try {
      setLoading(true)

      // Load notification preferences
      const { data: notificationPrefs } = await supabase
        .from('notification_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single()

      // Load user preferences
      const { data: userPrefs } = await supabase
        .from('user_preferences')
        .select('*')
        .eq('user_id', user.id)
        .single()

      // Update settings with loaded data
      setSettings(prev => ({
        ...prev,
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        username: user.username || '',
        email: user.email || '',
        mlbbId: user.mlbb_id || '',
        valorantId: user.valorant_id || '',
        dotaId: user.dota_id || '',
        codId: user.cod_id || '',
        pubgId: user.pubg_id || '',
        lolId: user.lol_id || '',
        csId: user.cs_id || '',
        bio: user.bio || '',

        // Notification preferences (mapped to existing database columns)
        perfectMatchEnabled: notificationPrefs?.perfect_match_enabled ?? true,
        friendActivityEnabled: notificationPrefs?.friend_activity_enabled ?? true,
        priceAlertsEnabled: notificationPrefs?.price_alerts_enabled ?? true,
        winStreakEnabled: notificationPrefs?.win_streak_enabled ?? true,
        bonusOpportunitiesEnabled: notificationPrefs?.bonus_opportunities_enabled ?? true,
        quietHoursStart: notificationPrefs?.quiet_hours_start || '22:00',
        quietHoursEnd: notificationPrefs?.quiet_hours_end || '08:00',
        maxDailyNotifications: notificationPrefs?.max_daily_notifications ?? 10,

        // User preferences
        language: userPrefs?.language || 'en',
        timezone: userPrefs?.timezone || 'Asia/Manila',
        theme: userPrefs?.theme || 'light',
        currency: userPrefs?.currency || 'PHP',
        autoJoinMatches: userPrefs?.auto_join_matches ?? false,
        showOnlineStatus: userPrefs?.show_online_status ?? true,
        allowFriendRequests: userPrefs?.allow_friend_requests ?? true
      }))

    } catch (error) {
      console.error('Error loading preferences:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async (section: string) => {
    if (!user) return

    try {
      setSaveLoading(section)

      switch (section) {
        case 'profile':
          await saveProfileSettings()
          break
        case 'notifications':
          await saveNotificationSettings()
          break
        case 'security':
          await saveSecuritySettings()
          break
        case 'preferences':
          await savePreferenceSettings()
          break
      }

      addNotification({
        type: 'success',
        title: 'Settings Saved',
        message: `${section.charAt(0).toUpperCase() + section.slice(1)} settings have been updated successfully.`
      })

    } catch (error) {
      console.error(`Error saving ${section} settings:`, error)
      addNotification({
        type: 'error',
        title: 'Save Failed',
        message: `Failed to save ${section} settings. Please try again.`
      })
    } finally {
      setSaveLoading(null)
    }
  }

  const saveProfileSettings = async () => {
    console.log('Saving profile settings:', {
      firstName: settings.firstName,
      lastName: settings.lastName,
      username: settings.username,
      mlbbId: settings.mlbbId,
      bio: settings.bio,
      userId: user!.id
    })

    const { data, error } = await supabase
      .from('users')
      .update({
        first_name: settings.firstName,
        last_name: settings.lastName,
        username: settings.username,
        mlbb_id: settings.mlbbId,
        valorant_id: settings.valorantId,
        dota_id: settings.dotaId,
        cod_id: settings.codId,
        pubg_id: settings.pubgId,
        lol_id: settings.lolId,
        cs_id: settings.csId,
        bio: settings.bio,
        updated_at: new Date().toISOString()
      })
      .eq('id', user!.id)
      .select()

    console.log('Profile update result:', { data, error })

    if (error) {
      console.error('Profile update error:', error)
      throw error
    }

    // Refresh user data
    await refreshUser()
    console.log('User data refreshed')
  }

  const saveNotificationSettings = async () => {
    const { error } = await supabase
      .from('notification_preferences')
      .upsert({
        user_id: user!.id,
        perfect_match_enabled: settings.perfectMatchEnabled,
        friend_activity_enabled: settings.friendActivityEnabled,
        price_alerts_enabled: settings.priceAlertsEnabled,
        win_streak_enabled: settings.winStreakEnabled,
        bonus_opportunities_enabled: settings.bonusOpportunitiesEnabled,
        quiet_hours_start: settings.quietHoursStart,
        quiet_hours_end: settings.quietHoursEnd,
        max_daily_notifications: settings.maxDailyNotifications,
        updated_at: new Date().toISOString()
      })

    if (error) throw error
  }

  const saveSecuritySettings = async () => {
    console.log('Attempting to change password...')

    // Validate password fields
    if (!settings.currentPassword) {
      throw new Error('Current password is required')
    }
    if (!settings.newPassword) {
      throw new Error('New password is required')
    }
    if (settings.newPassword !== settings.confirmPassword) {
      throw new Error('New passwords do not match')
    }
    if (settings.newPassword.length < 8) {
      throw new Error('New password must be at least 8 characters long')
    }

    // Validate current password by attempting to sign in with it
    console.log('Validating current password...')
    const { error: signInError } = await supabase.auth.signInWithPassword({
      email: user!.email!,
      password: settings.currentPassword
    })

    if (signInError) {
      console.error('Current password validation failed:', signInError)
      throw new Error('Current password is incorrect')
    }

    console.log('Current password validated successfully')

    // Update password using Supabase Auth
    console.log('Updating password...')
    const { error: updateError } = await supabase.auth.updateUser({
      password: settings.newPassword
    })

    if (updateError) {
      console.error('Password update failed:', updateError)
      throw updateError
    }

    console.log('Password updated successfully')

    // Clear password fields
    setSettings(prev => ({
      ...prev,
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }))

    console.log('Password change completed successfully')
  }

  const savePreferenceSettings = async () => {
    const { error } = await supabase
      .from('user_preferences')
      .upsert({
        user_id: user!.id,
        language: settings.language,
        timezone: settings.timezone,
        theme: settings.theme,
        currency: settings.currency,
        auto_join_matches: settings.autoJoinMatches,
        show_online_status: settings.showOnlineStatus,
        allow_friend_requests: settings.allowFriendRequests,
        updated_at: new Date().toISOString()
      })

    if (error) throw error
  }

  const tabs = [
    { id: 'profile', name: 'Profile', icon: User },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'security', name: 'Security', icon: Shield },
    { id: 'preferences', name: 'Preferences', icon: Settings }
  ]

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Settings</h1>
        <p className="text-gray-600">Manage your account settings and preferences</p>
      </div>

      <div className="bg-white rounded-lg shadow-md">
        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Profile Information</h3>
                {user?.diamond_balance !== undefined && (
                  <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-purple-50 px-3 py-1 rounded-lg">
                    <span className="text-sm font-medium text-gray-700">Balance:</span>
                    <span className="text-sm font-bold text-blue-600">{user.diamond_balance.toLocaleString()} 💎</span>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name *
                  </label>
                  <input
                    type="text"
                    value={settings.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name *
                  </label>
                  <input
                    type="text"
                    value={settings.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Username *
                  </label>
                  <input
                    type="text"
                    value={settings.username}
                    onChange={(e) => handleInputChange('username', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">This is how other players will see you</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    value={settings.email}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 cursor-not-allowed"
                    disabled
                    readOnly
                  />
                  <p className="text-xs text-gray-500 mt-1">Email cannot be changed here</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mobile Legends ID
                  </label>
                  <input
                    type="text"
                    value={settings.mlbbId}
                    onChange={(e) => handleInputChange('mlbbId', e.target.value)}
                    placeholder="Enter your MLBB ID (e.g., *********)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Optional: Link your Mobile Legends account</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valorant ID
                  </label>
                  <input
                    type="text"
                    value={settings.valorantId}
                    onChange={(e) => handleInputChange('valorantId', e.target.value)}
                    placeholder="Enter your Valorant ID (e.g., PlayerName#1234)"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Optional: Link your Valorant account</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Dota 2 ID
                  </label>
                  <input
                    type="text"
                    value={settings.dotaId}
                    onChange={(e) => handleInputChange('dotaId', e.target.value)}
                    placeholder="Enter your Dota 2 Steam ID or Friend ID"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Optional: Link your Dota 2 account</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Call of Duty ID
                  </label>
                  <input
                    type="text"
                    value={settings.codId}
                    onChange={(e) => handleInputChange('codId', e.target.value)}
                    placeholder="Enter your COD Activision ID"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">Optional: Link your Call of Duty account</p>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Bio
                  </label>
                  <textarea
                    value={settings.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    placeholder="Tell other players about yourself..."
                    rows={3}
                    maxLength={200}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-500 mt-1">{settings.bio.length}/200 characters</p>
                </div>
              </div>

              <button
                onClick={() => handleSave('profile')}
                disabled={saveLoading === 'profile'}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
              >
                {saveLoading === 'profile' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saveLoading === 'profile' ? 'Saving...' : 'Save Profile'}
              </button>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Notification Preferences</h3>

              <div className="space-y-6">
                {/* Match & Gaming Notifications */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Match & Gaming Notifications</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Perfect Match Alerts</h5>
                        <p className="text-sm text-gray-500">Get notified when ideal matches are available</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.perfectMatchEnabled}
                          onChange={(e) => handleInputChange('perfectMatchEnabled', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Win Streak Notifications</h5>
                        <p className="text-sm text-gray-500">Celebrate your winning streaks</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.winStreakEnabled}
                          onChange={(e) => handleInputChange('winStreakEnabled', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Price Alerts</h5>
                        <p className="text-sm text-gray-500">Get notified about diamond price changes</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.priceAlertsEnabled}
                          onChange={(e) => handleInputChange('priceAlertsEnabled', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Bonus Opportunities</h5>
                        <p className="text-sm text-gray-500">Get notified about special bonuses and rewards</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.bonusOpportunitiesEnabled}
                          onChange={(e) => handleInputChange('bonusOpportunitiesEnabled', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Social Notifications */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Social Notifications</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Friend Activity</h5>
                        <p className="text-sm text-gray-500">Get notified about your friends' activities</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.friendActivityEnabled}
                          onChange={(e) => handleInputChange('friendActivityEnabled', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Notification Controls */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Notification Controls</h4>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quiet Hours Start
                        </label>
                        <input
                          type="time"
                          value={settings.quietHoursStart}
                          onChange={(e) => handleInputChange('quietHoursStart', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Quiet Hours End
                        </label>
                        <input
                          type="time"
                          value={settings.quietHoursEnd}
                          onChange={(e) => handleInputChange('quietHoursEnd', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Max Daily Notifications
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="50"
                        value={settings.maxDailyNotifications}
                        onChange={(e) => handleInputChange('maxDailyNotifications', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      <p className="text-xs text-gray-500 mt-1">Maximum number of notifications per day (1-50)</p>
                    </div>
                  </div>
                </div>
              </div>

              <button
                onClick={() => handleSave('notifications')}
                disabled={saveLoading === 'notifications'}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
              >
                {saveLoading === 'notifications' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saveLoading === 'notifications' ? 'Saving...' : 'Save Notifications'}
              </button>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Security Settings</h3>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5 mr-3" />
                  <div>
                    <h4 className="text-sm font-medium text-yellow-800">Password Security</h4>
                    <p className="text-sm text-yellow-700 mt-1">
                      Use a strong password with at least 8 characters, including uppercase, lowercase, numbers, and special characters.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Current Password *
                  </label>
                  <div className="relative">
                    <input
                      type={showPassword ? "text" : "password"}
                      value={settings.currentPassword}
                      onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                      className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter your current password"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4 text-gray-400" /> : <Eye className="w-4 h-4 text-gray-400" />}
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    New Password *
                  </label>
                  <input
                    type="password"
                    value={settings.newPassword}
                    onChange={(e) => handleInputChange('newPassword', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter your new password"
                  />
                  <div className="mt-2 space-y-1">
                    <div className={`text-xs flex items-center ${settings.newPassword.length >= 8 ? 'text-green-600' : 'text-gray-400'}`}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      At least 8 characters
                    </div>
                    <div className={`text-xs flex items-center ${/[A-Z]/.test(settings.newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Contains uppercase letter
                    </div>
                    <div className={`text-xs flex items-center ${/[a-z]/.test(settings.newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Contains lowercase letter
                    </div>
                    <div className={`text-xs flex items-center ${/\d/.test(settings.newPassword) ? 'text-green-600' : 'text-gray-400'}`}>
                      <CheckCircle className="w-3 h-3 mr-1" />
                      Contains number
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Confirm New Password *
                  </label>
                  <input
                    type="password"
                    value={settings.confirmPassword}
                    onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      settings.confirmPassword && settings.newPassword !== settings.confirmPassword
                        ? 'border-red-300'
                        : 'border-gray-300'
                    }`}
                    placeholder="Confirm your new password"
                  />
                  {settings.confirmPassword && settings.newPassword !== settings.confirmPassword && (
                    <p className="text-xs text-red-600 mt-1">Passwords do not match</p>
                  )}
                </div>
              </div>

              <button
                onClick={() => handleSave('security')}
                disabled={saveLoading === 'security' || !settings.currentPassword || !settings.newPassword || settings.newPassword !== settings.confirmPassword}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
              >
                {saveLoading === 'security' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saveLoading === 'security' ? 'Updating...' : 'Update Password'}
              </button>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">App Preferences</h3>

              <div className="space-y-6">
                {/* Display Settings */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Display Settings</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Globe className="w-4 h-4 inline mr-1" />
                        Language
                      </label>
                      <select
                        value={settings.language}
                        onChange={(e) => handleInputChange('language', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="en">English</option>
                        <option value="fil">Filipino</option>
                        <option value="es">Español</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        <Smartphone className="w-4 h-4 inline mr-1" />
                        Theme
                      </label>
                      <select
                        value={settings.theme}
                        onChange={(e) => handleInputChange('theme', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="light">Light Mode</option>
                        <option value="dark">Dark Mode</option>
                        <option value="auto">Auto (System)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Timezone
                      </label>
                      <select
                        value={settings.timezone}
                        onChange={(e) => handleInputChange('timezone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="Asia/Manila">Asia/Manila (GMT+8)</option>
                        <option value="Asia/Singapore">Asia/Singapore (GMT+8)</option>
                        <option value="Asia/Tokyo">Asia/Tokyo (GMT+9)</option>
                        <option value="UTC">UTC (GMT+0)</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Currency
                      </label>
                      <select
                        value={settings.currency}
                        onChange={(e) => handleInputChange('currency', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="PHP">Philippine Peso (₱)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Gaming Preferences */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Gaming Preferences</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Auto-Join Matches</h5>
                        <p className="text-sm text-gray-500">Automatically join matches when criteria are met</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.autoJoinMatches}
                          onChange={(e) => handleInputChange('autoJoinMatches', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Privacy Settings */}
                <div>
                  <h4 className="text-md font-medium text-gray-900 mb-4">Privacy Settings</h4>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Show Online Status</h5>
                        <p className="text-sm text-gray-500">Let other players see when you're online</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.showOnlineStatus}
                          onChange={(e) => handleInputChange('showOnlineStatus', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="text-sm font-medium text-gray-900">Allow Friend Requests</h5>
                        <p className="text-sm text-gray-500">Allow other players to send you friend requests</p>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.allowFriendRequests}
                          onChange={(e) => handleInputChange('allowFriendRequests', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <button
                onClick={() => handleSave('preferences')}
                disabled={saveLoading === 'preferences'}
                className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-blue-400 transition-colors"
              >
                {saveLoading === 'preferences' ? (
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                ) : (
                  <Save className="w-4 h-4 mr-2" />
                )}
                {saveLoading === 'preferences' ? 'Saving...' : 'Save Preferences'}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
