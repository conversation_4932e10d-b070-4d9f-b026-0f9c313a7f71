import { supabase } from './supabase'
import { analyticsService } from './analytics'
import type { User } from './supabase'

export interface QuickMatchPreferences {
  game: string
  format: '1v1' | '2v2' | '3v3' | '4v4' | '5v5'
  maxEntryFee: number
  preferredRoles?: string[]
  skillLevelRange?: [number, number] // [min, max]
  voiceChatRequired?: boolean
}

export interface QuickMatchQueue {
  id: string
  user_id: string
  game: string
  format: string
  max_entry_fee: number
  preferred_roles: string[]
  skill_level: number
  voice_chat_required: boolean
  queue_time: string
  status: 'waiting' | 'matched' | 'expired'
  estimated_wait_time?: number
}

export interface MatchmakingResult {
  success: boolean
  match_id?: string
  queue_id?: string
  estimated_wait_time?: number
  message: string
}

class QuickMatchService {
  private readonly QUEUE_TIMEOUT = 5 * 60 * 1000 // 5 minutes
  // private readonly _MIN_PLAYERS_TO_CREATE = 2 // TODO: Confirm usage
  private readonly MAX_SKILL_DIFFERENCE = 2

  // Join quick match queue
  async joinQuickMatchQueue(
    user: User, 
    preferences: QuickMatchPreferences
  ): Promise<MatchmakingResult> {
    try {
      // Validate user has enough diamonds
      if (preferences.maxEntryFee > (user.diamond_balance || 0)) {
        return {
          success: false,
          message: `Insufficient balance. You need at least ${preferences.maxEntryFee} diamonds.`
        }
      }

      // Get user's skill level for this game
      const userStats = await analyticsService.getUserGameStats(user.id, preferences.game)
      const skillLevel = userStats?.skill_level || 5

      // Check if user is already in queue
      const existingQueue = await this.getUserActiveQueue(user.id)
      if (existingQueue) {
        return {
          success: false,
          message: 'You are already in a matchmaking queue.'
        }
      }

      // Create queue entry
      const queueEntry: Omit<QuickMatchQueue, 'id' | 'estimated_wait_time'> = {
        user_id: user.id,
        game: preferences.game,
        format: preferences.format,
        max_entry_fee: preferences.maxEntryFee,
        preferred_roles: preferences.preferredRoles || [],
        skill_level: skillLevel,
        voice_chat_required: preferences.voiceChatRequired || false,
        queue_time: new Date().toISOString(),
        status: 'waiting'
      }

      const { data: queue, error } = await supabase
        .from('quick_match_queue')
        .insert([queueEntry])
        .select()
        .single()

      if (error) {
        console.error('Error joining queue:', error)
        return {
          success: false,
          message: 'Failed to join matchmaking queue. Please try again.'
        }
      }

      // Try to find immediate match
      const matchResult = await this.findCompatiblePlayers(queue)
      
      if (matchResult.success && matchResult.match_id) {
        return matchResult
      }

      // Estimate wait time
      const estimatedWaitTime = await this.estimateWaitTime(preferences)

      return {
        success: true,
        queue_id: queue.id,
        estimated_wait_time: estimatedWaitTime,
        message: `Joined matchmaking queue. Estimated wait time: ${Math.ceil(estimatedWaitTime / 60)} minutes.`
      }

    } catch (error) {
      console.error('Error in joinQuickMatchQueue:', error)
      return {
        success: false,
        message: 'An error occurred while joining the queue.'
      }
    }
  }

  // Find compatible players for matchmaking
  async findCompatiblePlayers(queueEntry: QuickMatchQueue): Promise<MatchmakingResult> {
    try {
      // Find other players in queue with compatible preferences
      const { data: compatiblePlayers, error } = await supabase
        .from('quick_match_queue')
        .select('*')
        .eq('game', queueEntry.game)
        .eq('format', queueEntry.format)
        .eq('status', 'waiting')
        .neq('user_id', queueEntry.user_id)
        .gte('max_entry_fee', this.calculateMinEntryFee(queueEntry))
        .lte('skill_level', queueEntry.skill_level + this.MAX_SKILL_DIFFERENCE)
        .gte('skill_level', queueEntry.skill_level - this.MAX_SKILL_DIFFERENCE)
        .order('queue_time', { ascending: true })

      if (error) {
        console.error('Error finding compatible players:', error)
        return {
          success: false,
          message: 'Error finding compatible players.'
        }
      }

      // Filter by voice chat requirements
      const filteredPlayers = compatiblePlayers?.filter(player => 
        player.voice_chat_required === queueEntry.voice_chat_required
      ) || []

      // Calculate required players for this format
      const requiredPlayers = this.getRequiredPlayers(queueEntry.format)
      
      if (filteredPlayers.length + 1 >= requiredPlayers) {
        // We have enough players, create match
        const selectedPlayers = filteredPlayers.slice(0, requiredPlayers - 1)
        const allPlayers = [queueEntry, ...selectedPlayers]
        
        const matchResult = await this.createInstantMatch(allPlayers)
        return matchResult
      }

      return {
        success: false,
        message: 'Not enough compatible players found yet.'
      }

    } catch (error) {
      console.error('Error in findCompatiblePlayers:', error)
      return {
        success: false,
        message: 'Error during matchmaking.'
      }
    }
  }

  // Create instant match from queue players
  private async createInstantMatch(players: QuickMatchQueue[]): Promise<MatchmakingResult> {
    try {
      // Calculate match parameters
      const entryFee = Math.min(...players.map(p => p.max_entry_fee))
      const potAmount = entryFee * players.length
      const game = players[0].game
      const format = players[0].format

      // Create match
      const matchData = {
        title: `Quick Match: ${game} ${format}`,
        game,
        mode: format,
        entry_fee: entryFee,
        pot_amount: potAmount,
        diamond_pot: potAmount, // Add diamond_pot field
        max_players: players.length,
        current_players: 0, // Will be updated as players join
        host_id: players[0].user_id, // First player becomes host
        status: 'open',
        rules: this.getDefaultRules(game),
        region: 'Global',
        scheduled_start_time: new Date(Date.now() + 2 * 60 * 1000).toISOString() // Start in 2 minutes
        // Removed created_at as it's auto-generated by database
      }

      const { data: match, error: matchError } = await supabase
        .from('matches')
        .insert([matchData])
        .select()
        .single()

      if (matchError) {
        console.error('Error creating instant match:', matchError)
        return {
          success: false,
          message: 'Failed to create match.'
        }
      }

      // Add all players to the match
      const participantPromises = players.map(async (player) => {
        // Add to match participants
        await supabase
          .from('match_participants')
          .insert([{
            match_id: match.id,
            user_id: player.user_id,
            joined_at: new Date().toISOString()
          }])

        // Deduct entry fee from user balance
        const { data: currentUser } = await supabase
          .from('users')
          .select('diamond_balance')
          .eq('id', player.user_id)
          .single()

        const newBalance = (currentUser?.diamond_balance || 0) - entryFee

        await supabase
          .from('users')
          .update({ diamond_balance: newBalance })
          .eq('id', player.user_id)

        // Create transaction record
        await supabase
          .from('transactions')
          .insert([{
            user_id: player.user_id,
            type: 'match_entry',
            amount: -entryFee,
            status: 'completed',
            description: `Quick Match: ${game} ${format}`,
            created_at: new Date().toISOString()
          }])

        // Remove from queue
        await supabase
          .from('quick_match_queue')
          .update({ status: 'matched' })
          .eq('id', player.id)
      })

      await Promise.all(participantPromises)

      // Update match player count
      await supabase
        .from('matches')
        .update({ current_players: players.length })
        .eq('id', match.id)

      return {
        success: true,
        match_id: match.id,
        message: `Match created! ${players.length} players matched instantly.`
      }

    } catch (error) {
      console.error('Error creating instant match:', error)
      return {
        success: false,
        message: 'Failed to create instant match.'
      }
    }
  }

  // Get user's active queue entry
  async getUserActiveQueue(userId: string): Promise<QuickMatchQueue | null> {
    try {
      const { data, error } = await supabase
        .from('quick_match_queue')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'waiting')
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error getting user queue:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getUserActiveQueue:', error)
      return null
    }
  }

  // Leave matchmaking queue
  async leaveQueue(userId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from('quick_match_queue')
        .update({ status: 'expired' })
        .eq('user_id', userId)
        .eq('status', 'waiting')

      if (error) {
        console.error('Error leaving queue:', error)
        return false
      }

      return true
    } catch (error) {
      console.error('Error in leaveQueue:', error)
      return false
    }
  }

  // Estimate wait time based on current queue and historical data
  private async estimateWaitTime(preferences: QuickMatchPreferences): Promise<number> {
    try {
      // Count current players in queue for this game/format
      const { count } = await supabase
        .from('quick_match_queue')
        .select('*', { count: 'exact', head: true })
        .eq('game', preferences.game)
        .eq('format', preferences.format)
        .eq('status', 'waiting')

      const queueSize = count || 0
      const requiredPlayers = this.getRequiredPlayers(preferences.format)
      
      // Base wait time calculation
      let estimatedMinutes = 2 // Minimum 2 minutes
      
      if (queueSize < requiredPlayers - 1) {
        // Need more players
        estimatedMinutes = (requiredPlayers - queueSize - 1) * 1.5 // 1.5 minutes per missing player
      }

      // Adjust based on game popularity and time of day
      const hour = new Date().getHours()
      const isPeakTime = hour >= 18 && hour <= 22
      
      if (!isPeakTime) {
        estimatedMinutes *= 1.5 // 50% longer during off-peak
      }

      // Popular games have shorter wait times
      const popularGames = ['Mobile Legends', 'Valorant', 'Call of Duty']
      if (!popularGames.includes(preferences.game)) {
        estimatedMinutes *= 1.3
      }

      return Math.max(120, estimatedMinutes * 60) // Return in seconds, minimum 2 minutes
    } catch (error) {
      console.error('Error estimating wait time:', error)
      return 300 // Default 5 minutes
    }
  }

  // Helper functions
  private getRequiredPlayers(format: string): number {
    const playerCounts: Record<string, number> = {
      '1v1': 2,
      '2v2': 4,
      '3v3': 6,
      '4v4': 8,
      '5v5': 10
    }
    return playerCounts[format] || 2
  }

  private calculateMinEntryFee(queueEntry: QuickMatchQueue): number {
    // Use the minimum of what this player is willing to pay
    return Math.max(10, queueEntry.max_entry_fee * 0.5) // At least 50% of their max
  }

  private getDefaultRules(game: string): string {
    const rules: Record<string, string> = {
      'Mobile Legends': 'Classic mode, no cheating, respect all players',
      'Valorant': 'Competitive rules, no exploits, good sportsmanship',
      'Call of Duty': 'Standard rules, no camping, fair play',
      'Dota 2': 'All Pick mode, no intentional feeding, team play'
    }
    return rules[game] || 'Standard competitive rules apply'
  }

  // Cleanup expired queue entries (should be run periodically)
  async cleanupExpiredQueues(): Promise<void> {
    try {
      const expiredTime = new Date(Date.now() - this.QUEUE_TIMEOUT).toISOString()
      
      await supabase
        .from('quick_match_queue')
        .update({ status: 'expired' })
        .eq('status', 'waiting')
        .lt('queue_time', expiredTime)

    } catch (error) {
      console.error('Error cleaning up expired queues:', error)
    }
  }

  // Get queue statistics
  async getQueueStats(): Promise<Record<string, any>> {
    try {
      const { data: stats } = await supabase
        .from('quick_match_queue')
        .select('game, format, status')
        .eq('status', 'waiting')

      const gameStats: Record<string, Record<string, number>> = {}
      
      stats?.forEach(entry => {
        if (!gameStats[entry.game]) {
          gameStats[entry.game] = {}
        }
        if (!gameStats[entry.game][entry.format]) {
          gameStats[entry.game][entry.format] = 0
        }
        gameStats[entry.game][entry.format]++
      })

      return gameStats
    } catch (error) {
      console.error('Error getting queue stats:', error)
      return {}
    }
  }

  // Get match details from queue when matched
  async getMatchFromQueue(queueId: string): Promise<{ match_id?: string } | null> {
    try {
      const { data: queue, error } = await supabase
        .from('quick_match_queue')
        .select('*')
        .eq('id', queueId)
        .single()

      if (error || !queue) {
        console.error('Error getting queue:', error)
        return null
      }

      // If queue is matched, try to find the associated match
      if (queue.status === 'matched') {
        // Look for recent matches that match the queue criteria
        const { data: matches } = await supabase
          .from('matches')
          .select('id')
          .eq('game', queue.game)
          .eq('mode', queue.format)
          .eq('status', 'open')
          .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
          .order('created_at', { ascending: false })
          .limit(1)

        if (matches && matches.length > 0) {
          return { match_id: matches[0].id }
        }
      }

      return null
    } catch (error) {
      console.error('Error getting match from queue:', error)
      return null
    }
  }
}

export const quickMatchService = new QuickMatchService()

// Add the missing table schema for quick match queue
export const QUICK_MATCH_QUEUE_SCHEMA = `
-- Quick match queue table
CREATE TABLE IF NOT EXISTS quick_match_queue (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  game TEXT NOT NULL,
  format TEXT NOT NULL CHECK (format IN ('1v1', '2v2', '3v3', '4v4', '5v5')),
  max_entry_fee INTEGER NOT NULL,
  preferred_roles TEXT[] DEFAULT '{}',
  skill_level INTEGER DEFAULT 5 CHECK (skill_level >= 1 AND skill_level <= 10),
  voice_chat_required BOOLEAN DEFAULT FALSE,
  queue_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  status TEXT DEFAULT 'waiting' CHECK (status IN ('waiting', 'matched', 'expired')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for quick match queue
CREATE INDEX IF NOT EXISTS idx_quick_match_queue_user_id ON quick_match_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_quick_match_queue_game_format ON quick_match_queue(game, format);
CREATE INDEX IF NOT EXISTS idx_quick_match_queue_status ON quick_match_queue(status);
CREATE INDEX IF NOT EXISTS idx_quick_match_queue_queue_time ON quick_match_queue(queue_time);

-- RLS policies for quick match queue
ALTER TABLE quick_match_queue ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Users can view their own queue entries" ON quick_match_queue FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can manage their own queue entries" ON quick_match_queue FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "System can view all queue entries for matchmaking" ON quick_match_queue FOR SELECT USING (true);
`
