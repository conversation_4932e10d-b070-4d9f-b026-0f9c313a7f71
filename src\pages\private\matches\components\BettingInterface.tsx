import { useState, useEffect } from 'react'
import { DollarSign, TrendingUp, AlertTriangle } from 'lucide-react'
import { useAuth } from '../../../../contexts/AuthContext'
import { useNotifications } from '../../../../contexts/NotificationContext'
import { bettingService } from '../../../../services/bettingService'
import { TouchButton } from '../../../../components/mobile/TouchOptimized'
import type { Match } from '../../../../types'

interface BettingInterfaceProps {
  match: Match
  onBetPlaced?: () => void
}

interface BetOption {
  id: string
  label: string
  odds: number
  description: string
}

export default function BettingInterface({ match, onBetPlaced }: BettingInterfaceProps) {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  
  const [betAmount, setBetAmount] = useState<number>(10)
  const [selectedOption, setSelectedOption] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const [userBets, setUserBets] = useState<any[]>([])
  const [canBet, setCanBet] = useState(true)
  const [conflictReason, setConflictReason] = useState<string>('')

  const betOptions: BetOption[] = [
    {
      id: 'host',
      label: 'Host Wins',
      odds: 1.8,
      description: `${match.host_username || 'Host'} wins the match`
    },
    {
      id: 'challenger',
      label: 'Challenger Wins', 
      odds: 2.2,
      description: 'Any challenger wins the match'
    },
    {
      id: 'draw',
      label: 'Draw/Tie',
      odds: 3.5,
      description: 'Match ends in a draw'
    }
  ]

  useEffect(() => {
    if (user && match) {
      loadUserBets()
      checkBettingEligibility()
    }
  }, [user, match])

  const loadUserBets = async () => {
    if (!user) return

    try {
      const bets = await bettingService.getUserBets(user.id, match.id)
      if (bets) {
        setUserBets(bets)
      }
    } catch (error) {
      console.error('Error loading user bets:', error)
    }
  }

  const checkBettingEligibility = async () => {
    if (!user) return

    try {
      const validation = await bettingService.validateBet(user.id, match.id, 10)
      setCanBet(validation.canBet)
      if (!validation.canBet) {
        setConflictReason(validation.reason || 'Cannot place bet on this match')
      }
    } catch (error) {
      console.error('Error checking betting eligibility:', error)
      setCanBet(false)
      setConflictReason('Error checking betting eligibility')
    }
  }

  const handlePlaceBet = async () => {
    if (!user || !selectedOption || betAmount <= 0) return

    try {
      setIsLoading(true)

      const selectedBetOption = betOptions.find(opt => opt.id === selectedOption)
      if (!selectedBetOption) return

      const result = await bettingService.placeBet(
        user.id,
        match.id,
        betAmount,
        selectedOption,
        selectedBetOption.odds
      )

      if (result.success) {
        addNotification({
          type: 'success',
          title: 'Bet Placed!',
          message: `Successfully bet ${betAmount} 💎 on ${selectedBetOption.label}`
        })
        
        // Reset form
        setBetAmount(10)
        setSelectedOption('')
        
        // Reload user bets
        await loadUserBets()
        
        // Notify parent component
        onBetPlaced?.()
      } else {
        addNotification({
          type: 'error',
          title: 'Bet Failed',
          message: result.error || 'Failed to place bet'
        })
      }
    } catch (error) {
      console.error('Error placing bet:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to place bet. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const calculatePotentialWinnings = () => {
    const selectedBetOption = betOptions.find(opt => opt.id === selectedOption)
    if (!selectedBetOption || betAmount <= 0) return 0
    return Math.floor(betAmount * selectedBetOption.odds)
  }

  const getBetStatusColor = (status: string) => {
    switch (status) {
      case 'won': return 'text-green-400 bg-green-900/20'
      case 'lost': return 'text-red-400 bg-red-900/20'
      case 'pending': return 'text-yellow-400 bg-yellow-900/20'
      default: return 'text-gray-400 bg-gray-900/20'
    }
  }

  // Don't show betting interface if match is not open or in progress
  if (!['open', 'full', 'in_progress'].includes(match.status)) {
    return (
      <div className="bg-gray-800 rounded-lg p-4">
        <div className="flex items-center gap-2 text-gray-400">
          <AlertTriangle className="w-4 h-4" />
          <span>Betting is closed for this match</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg p-6 space-y-6">
      <div className="flex items-center gap-2">
        <DollarSign className="w-5 h-5 text-yellow-400" />
        <h3 className="text-lg font-semibold text-white">Place Your Bet</h3>
      </div>

      {/* Betting Eligibility Warning */}
      {!canBet && (
        <div className="bg-red-900/20 border border-red-700 rounded-lg p-4">
          <div className="flex items-center gap-2 text-red-400">
            <AlertTriangle className="w-4 h-4" />
            <span className="font-medium">Cannot Place Bet</span>
          </div>
          <p className="text-red-300 text-sm mt-1">{conflictReason}</p>
        </div>
      )}

      {/* Current User Bets */}
      {userBets.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-gray-400 mb-3">Your Current Bets</h4>
          <div className="space-y-2">
            {userBets.map((bet) => (
              <div key={bet.id} className="bg-gray-700 rounded-lg p-3">
                <div className="flex justify-between items-center">
                  <div>
                    <span className="text-white font-medium">{bet.bet_amount} 💎</span>
                    <span className="text-gray-400 ml-2">on {bet.predicted_winner}</span>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded ${getBetStatusColor(bet.status)}`}>
                    {bet.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {canBet && (
        <>
          {/* Bet Options */}
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-3">Choose Winner</h4>
            <div className="space-y-2">
              {betOptions.map((option) => (
                <TouchButton
                  key={option.id}
                  onClick={() => setSelectedOption(option.id)}
                  className={`w-full p-4 rounded-lg border-2 transition-colors ${
                    selectedOption === option.id
                      ? 'border-blue-500 bg-blue-900/20'
                      : 'border-gray-600 bg-gray-700 hover:border-gray-500'
                  }`}
                >
                  <div className="flex justify-between items-center">
                    <div className="text-left">
                      <div className="text-white font-medium">{option.label}</div>
                      <div className="text-gray-400 text-sm">{option.description}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-yellow-400 font-bold">{option.odds}x</div>
                      <div className="text-gray-400 text-sm">odds</div>
                    </div>
                  </div>
                </TouchButton>
              ))}
            </div>
          </div>

          {/* Bet Amount */}
          <div>
            <h4 className="text-sm font-medium text-gray-400 mb-3">Bet Amount</h4>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <input
                  type="number"
                  value={betAmount}
                  onChange={(e) => setBetAmount(Math.max(1, parseInt(e.target.value) || 0))}
                  min="1"
                  max={user?.diamond_balance || 0}
                  className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white"
                  placeholder="Enter amount"
                />
                <span className="text-yellow-400">💎</span>
              </div>
              
              {/* Quick Amount Buttons */}
              <div className="flex gap-2">
                {[10, 25, 50, 100].map((amount) => (
                  <TouchButton
                    key={amount}
                    onClick={() => setBetAmount(amount)}
                    className="px-3 py-1 bg-gray-700 hover:bg-gray-600 text-white rounded text-sm"
                  >
                    {amount}
                  </TouchButton>
                ))}
              </div>
            </div>
          </div>

          {/* Potential Winnings */}
          {selectedOption && betAmount > 0 && (
            <div className="bg-gray-700 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="w-4 h-4 text-green-400" />
                  <span className="text-gray-400">Potential Winnings</span>
                </div>
                <span className="text-green-400 font-bold">
                  {calculatePotentialWinnings()} 💎
                </span>
              </div>
            </div>
          )}

          {/* Place Bet Button */}
          <TouchButton
            onClick={handlePlaceBet}
            disabled={!selectedOption || betAmount <= 0 || isLoading || (user?.diamond_balance || 0) < betAmount}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white py-3 rounded-lg font-medium"
          >
            {isLoading ? 'Placing Bet...' : `Place Bet (${betAmount} 💎)`}
          </TouchButton>

          {/* Balance Info */}
          <div className="text-center text-sm text-gray-400">
            Your Balance: {user?.diamond_balance || 0} 💎
          </div>
        </>
      )}
    </div>
  )
}
