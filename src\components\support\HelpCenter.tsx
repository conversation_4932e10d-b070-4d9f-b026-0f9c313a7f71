import { useState, useEffect } from 'react'
import {
  HelpCircle,
  Search,
  MessageCircle,
  Mail,
  Phone,
  ChevronRight,
  ChevronDown,
  ThumbsUp,
  ThumbsDown,
  Clock
} from 'lucide-react'

interface FAQItem {
  id: string
  question: string
  answer: string
  category: string
  helpful: number
  not_helpful: number
  tags: string[]
}

interface SupportTicket {
  id: string
  subject: string
  status: 'open' | 'in_progress' | 'resolved' | 'closed'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  created_at: string
  last_updated: string
}

export default function HelpCenter() {
  const [activeTab, setActiveTab] = useState<'faq' | 'contact' | 'tickets'>('faq')
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null)
  const [faqs, setFaqs] = useState<FAQItem[]>([])
  const [tickets, setTickets] = useState<SupportTicket[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadHelpData()
  }, [])

  const loadHelpData = async () => {
    setLoading(true)
    try {
      // Mock data - in real app, fetch from API
      const mockFAQs: FAQItem[] = [
        {
          id: '1',
          question: 'How do I create a match?',
          answer: 'To create a match, go to the Matches page and click the "Create Match" button. Fill in the match details including game, mode, entry fee, and maximum players. Once created, other players can join your match.',
          category: 'matches',
          helpful: 45,
          not_helpful: 3,
          tags: ['matches', 'create', 'gameplay']
        },
        {
          id: '2',
          question: 'How do I buy diamonds?',
          answer: 'You can purchase diamonds by going to your Wallet page and selecting "Top Up". Choose your preferred payment method (GCash, Maya, or Bank Transfer) and select the diamond package you want to buy.',
          category: 'payments',
          helpful: 67,
          not_helpful: 2,
          tags: ['diamonds', 'payment', 'wallet']
        },
        {
          id: '3',
          question: 'What happens if I win a match?',
          answer: 'When you win a match, you receive diamonds based on the match entry fee and number of participants. The winnings are automatically added to your diamond balance, and your win statistics are updated.',
          category: 'matches',
          helpful: 89,
          not_helpful: 1,
          tags: ['matches', 'winnings', 'diamonds']
        },
        {
          id: '4',
          question: 'How do I become a referee?',
          answer: 'To become a referee, you need to apply through the Referee Application page. You must have a good standing account, experience in the games you want to referee, and pass our verification process.',
          category: 'referee',
          helpful: 34,
          not_helpful: 5,
          tags: ['referee', 'application', 'verification']
        },
        {
          id: '5',
          question: 'Can I cancel a match I created?',
          answer: 'Yes, you can cancel a match you created as long as it hasn\'t started yet. Go to your match and click the "Cancel" button. All entry fees will be refunded to participants automatically.',
          category: 'matches',
          helpful: 56,
          not_helpful: 2,
          tags: ['matches', 'cancel', 'refund']
        }
      ]

      const mockTickets: SupportTicket[] = [
        {
          id: 'TICK-001',
          subject: 'Payment not processed',
          status: 'in_progress',
          priority: 'high',
          created_at: '2024-01-20T10:30:00Z',
          last_updated: '2024-01-20T14:22:00Z'
        },
        {
          id: 'TICK-002',
          subject: 'Account verification issue',
          status: 'resolved',
          priority: 'medium',
          created_at: '2024-01-19T09:15:00Z',
          last_updated: '2024-01-19T16:45:00Z'
        }
      ]

      setFaqs(mockFAQs)
      setTickets(mockTickets)
    } catch (error) {
      console.error('Error loading help data:', error)
    } finally {
      setLoading(false)
    }
  }

  const categories = [
    { id: 'all', name: 'All Categories', count: faqs.length },
    { id: 'matches', name: 'Matches & Gameplay', count: faqs.filter(f => f.category === 'matches').length },
    { id: 'payments', name: 'Payments & Diamonds', count: faqs.filter(f => f.category === 'payments').length },
    { id: 'referee', name: 'Referee System', count: faqs.filter(f => f.category === 'referee').length },
    { id: 'account', name: 'Account & Profile', count: faqs.filter(f => f.category === 'account').length }
  ]

  const filteredFAQs = faqs.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-blue-100 text-blue-800'
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'resolved':
        return 'bg-green-100 text-green-800'
      case 'closed':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-green-600'
      default:
        return 'text-gray-600'
    }
  }

  const tabs = [
    { key: 'faq', label: 'FAQ', icon: <HelpCircle className="w-4 h-4" /> },
    { key: 'contact', label: 'Contact Support', icon: <MessageCircle className="w-4 h-4" /> },
    { key: 'tickets', label: 'My Tickets', icon: <Clock className="w-4 h-4" /> }
  ]

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Help Center</h1>
        <p className="text-gray-600">Find answers to common questions or get in touch with our support team</p>
      </div>

      {/* Navigation Tabs */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.key
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* FAQ Tab */}
      {activeTab === 'faq' && (
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Categories Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-sm p-4">
              <h3 className="font-semibold text-gray-900 mb-4">Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`w-full flex items-center justify-between p-2 rounded-lg text-left transition-colors ${
                      selectedCategory === category.id
                        ? 'bg-blue-100 text-blue-700'
                        : 'hover:bg-gray-100 text-gray-700'
                    }`}
                  >
                    <span className="text-sm font-medium">{category.name}</span>
                    <span className="text-xs text-gray-500">{category.count}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* FAQ Content */}
          <div className="lg:col-span-3">
            {/* Search */}
            <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search for answers..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* FAQ List */}
            <div className="bg-white rounded-xl shadow-sm">
              {filteredFAQs.length === 0 ? (
                <div className="text-center py-12">
                  <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No FAQs Found</h3>
                  <p className="text-gray-600">Try adjusting your search or category filter.</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {filteredFAQs.map((faq) => (
                    <div key={faq.id} className="p-6">
                      <button
                        onClick={() => setExpandedFAQ(expandedFAQ === faq.id ? null : faq.id)}
                        className="w-full flex items-center justify-between text-left"
                      >
                        <h3 className="text-lg font-medium text-gray-900">{faq.question}</h3>
                        {expandedFAQ === faq.id ? (
                          <ChevronDown className="w-5 h-5 text-gray-500" />
                        ) : (
                          <ChevronRight className="w-5 h-5 text-gray-500" />
                        )}
                      </button>
                      
                      {expandedFAQ === faq.id && (
                        <div className="mt-4">
                          <p className="text-gray-700 mb-4">{faq.answer}</p>
                          
                          {/* Tags */}
                          <div className="flex flex-wrap gap-2 mb-4">
                            {faq.tags.map((tag) => (
                              <span
                                key={tag}
                                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                          
                          {/* Helpful Buttons */}
                          <div className="flex items-center space-x-4 text-sm text-gray-600">
                            <span>Was this helpful?</span>
                            <button className="flex items-center space-x-1 hover:text-green-600">
                              <ThumbsUp className="w-4 h-4" />
                              <span>{faq.helpful}</span>
                            </button>
                            <button className="flex items-center space-x-1 hover:text-red-600">
                              <ThumbsDown className="w-4 h-4" />
                              <span>{faq.not_helpful}</span>
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Contact Tab */}
      {activeTab === 'contact' && (
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Contact Support</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center p-6 border border-gray-200 rounded-lg">
              <Mail className="w-8 h-8 text-blue-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Email Support</h3>
              <p className="text-gray-600 text-sm mb-3">Get help via email</p>
              <p className="text-blue-600 font-medium"><EMAIL></p>
            </div>
            
            <div className="text-center p-6 border border-gray-200 rounded-lg">
              <MessageCircle className="w-8 h-8 text-green-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Live Chat</h3>
              <p className="text-gray-600 text-sm mb-3">Chat with our team</p>
              <button className="bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700">
                Start Chat
              </button>
            </div>
            
            <div className="text-center p-6 border border-gray-200 rounded-lg">
              <Phone className="w-8 h-8 text-purple-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Phone Support</h3>
              <p className="text-gray-600 text-sm mb-3">Call us directly</p>
              <p className="text-purple-600 font-medium">+63 ************</p>
            </div>
          </div>

          {/* Contact Form */}
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                <input
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Brief description of your issue"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
              <textarea
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Please describe your issue in detail..."
              />
            </div>
            
            <button
              type="submit"
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700"
            >
              Submit Ticket
            </button>
          </form>
        </div>
      )}

      {/* Tickets Tab */}
      {activeTab === 'tickets' && (
        <div className="bg-white rounded-xl shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">My Support Tickets</h2>
          </div>
          
          {tickets.length === 0 ? (
            <div className="text-center py-12">
              <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Support Tickets</h3>
              <p className="text-gray-600">You haven't submitted any support tickets yet.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {tickets.map((ticket) => (
                <div key={ticket.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-medium text-gray-900">{ticket.subject}</h3>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(ticket.status)}`}>
                      {ticket.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Ticket #{ticket.id}</span>
                    <span className={`font-medium ${getPriorityColor(ticket.priority)}`}>
                      {ticket.priority.toUpperCase()} Priority
                    </span>
                    <span>Created {new Date(ticket.created_at).toLocaleDateString()}</span>
                    <span>Updated {new Date(ticket.last_updated).toLocaleDateString()}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
