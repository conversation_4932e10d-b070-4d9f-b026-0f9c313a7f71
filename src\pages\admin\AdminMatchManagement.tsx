import { useState, useEffect } from 'react'
import {
  G<PERSON>l, Eye, AlertTriangle, Users, Diamond, Trophy, Flag, RefreshCw, Search,
  Calendar, DollarSign, User, Shield, Play, Clock, CheckCircle, XCircle
} from 'lucide-react'
import { supabase } from '../../services/supabaseClient'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'
import MatchDetailModal from '../../components/admin/MatchDetailModal'

interface Match {
  id: string
  title: string
  game_type: string
  diamond_pot: number
  status: 'open' | 'waiting' | 'in_progress' | 'completed' | 'cancelled' | 'disputed'
  created_at: string
  scheduled_time?: string
  host_id: string
  referee_id?: string
  winner_id?: string
  dispute_reason?: string
  host: {
    username: string
    email: string
  }
  referee?: {
    username: string
    email: string
  }
  participants: Array<{
    user_id: string
    user: {
      username: string
      email: string
    }
  }>
  _count?: {
    participants: number
  }
}

interface MatchStats {
  totalMatches: number
  activeMatches: number
  completedMatches: number
  disputedMatches: number
  totalDiamonds: number
  averageMatchValue: number
}

export default function AdminMatchManagement() {
  const { } = useAuth() // TODO: Confirm user usage
  const { addNotification } = useNotifications()
  const [matches, setMatches] = useState<Match[]>([])
  const [stats, setStats] = useState<MatchStats>({
    totalMatches: 0,
    activeMatches: 0,
    completedMatches: 0,
    disputedMatches: 0,
    totalDiamonds: 0,
    averageMatchValue: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedMatch, setSelectedMatch] = useState<Match | null>(null)
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [showDisputes, setShowDisputes] = useState(false)

  useEffect(() => {
    loadMatches()
    loadStats()

    // Set up real-time subscription for admin match updates
    console.log('Setting up real-time admin match subscriptions')
    let isSubscriptionActive = true

    const matchesSubscription = supabase
      .channel('admin-matches')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches' },
        async () => {
          if (!isSubscriptionActive) return
          console.log('Real-time admin matches update received')
          await loadMatches()
          await loadStats()
        }
      )
      .subscribe()

    // Set up real-time subscription for match participants
    const participantsSubscription = supabase
      .channel('admin-participants')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'match_participants' },
        async () => {
          if (!isSubscriptionActive) return
          console.log('Real-time match participants update received')
          await loadMatches()
        }
      )
      .subscribe()

    // Cleanup subscriptions on unmount
    return () => {
      console.log('Cleaning up admin real-time subscriptions')
      isSubscriptionActive = false
      if (matchesSubscription) {
        try {
          supabase.removeChannel(matchesSubscription)
        } catch (error) {
          console.error('Error cleaning up admin matches subscription:', error)
        }
      }
      if (participantsSubscription) {
        try {
          supabase.removeChannel(participantsSubscription)
        } catch (error) {
          console.error('Error cleaning up admin participants subscription:', error)
        }
      }
    }
  }, [])

  const loadMatches = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('matches')
        .select(`
          *,
          host:users!host_id(username, email),
          referee:users!referee_id(username, email),
          participants:match_participants(
            user_id,
            user:users(username, email)
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setMatches(data || [])
    } catch (error) {
      console.error('Error loading matches:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load matches'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // Get match counts by status
      const { data: matchCounts } = await supabase
        .from('matches')
        .select('status, diamond_pot')

      if (matchCounts) {
        const stats = {
          totalMatches: matchCounts.length,
          activeMatches: matchCounts.filter(m => ['open', 'waiting', 'in_progress'].includes(m.status)).length,
          completedMatches: matchCounts.filter(m => m.status === 'completed').length,
          disputedMatches: matchCounts.filter(m => m.status === 'disputed').length,
          totalDiamonds: matchCounts.reduce((sum, m) => sum + (m.diamond_pot || 0), 0),
          averageMatchValue: matchCounts.length > 0 
            ? matchCounts.reduce((sum, m) => sum + (m.diamond_pot || 0), 0) / matchCounts.length 
            : 0
        }
        setStats(stats)
      }
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const handleMatchAction = async (matchId: string, action: string, reason?: string) => {
    try {
      let updateData: any = {}
      
      switch (action) {
        case 'cancel':
          updateData = { status: 'cancelled' }
          break
        case 'resolve_dispute':
          updateData = { status: 'completed', dispute_reason: null }
          break
        case 'mark_disputed':
          updateData = { status: 'disputed', dispute_reason: reason || 'Admin review required' }
          break
        case 'force_complete':
          updateData = { status: 'completed' }
          break
        case 'admin_note':
          // For admin notes, we could add them to dispute_reason or create a separate admin_notes field
          updateData = { dispute_reason: reason }
          break
        default:
          return
      }

      const { error } = await supabase
        .from('matches')
        .update(updateData)
        .eq('id', matchId)

      if (error) throw error

      addNotification({
        type: 'success',
        title: 'Match Updated',
        message: `Match ${action.replace('_', ' ')} successfully`
      })

      await loadMatches()
      await loadStats()
      setSelectedMatch(null)
    } catch (error) {
      console.error('Error updating match:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to update match'
      })
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      open: { color: 'bg-blue-100 text-blue-800', icon: Clock },
      waiting: { color: 'bg-yellow-100 text-yellow-800', icon: Users },
      in_progress: { color: 'bg-green-100 text-green-800', icon: Play },
      completed: { color: 'bg-gray-100 text-gray-800', icon: CheckCircle },
      cancelled: { color: 'bg-red-100 text-red-800', icon: XCircle },
      disputed: { color: 'bg-orange-100 text-orange-800', icon: AlertTriangle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.replace('_', ' ').toUpperCase()}
      </span>
    )
  }

  const filteredMatches = matches.filter(match => {
    const matchesStatus = filterStatus === 'all' || match.status === filterStatus
    const matchesSearch = searchTerm === '' || 
      match.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.host.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      match.game_type.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesDispute = !showDisputes || match.status === 'disputed'
    
    return matchesStatus && matchesSearch && (!showDisputes || matchesDispute)
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Match Management</h1>
            <p className="text-purple-100 mt-1">Oversee matches, resolve disputes, and manage platform integrity</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={loadMatches}
              disabled={loading}
              className="bg-white/20 hover:bg-white/30 rounded-lg px-4 py-2 transition-colors flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Matches</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalMatches}</p>
            </div>
            <Gavel className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active</p>
              <p className="text-2xl font-bold text-green-600">{stats.activeMatches}</p>
            </div>
            <Play className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-blue-600">{stats.completedMatches}</p>
            </div>
            <Trophy className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Disputes</p>
              <p className="text-2xl font-bold text-orange-600">{stats.disputedMatches}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-orange-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Diamonds</p>
              <p className="text-2xl font-bold text-purple-600">{stats.totalDiamonds.toLocaleString()}</p>
            </div>
            <Diamond className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Value</p>
              <p className="text-2xl font-bold text-indigo-600">{Math.round(stats.averageMatchValue)}</p>
            </div>
            <DollarSign className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search matches, hosts, or game types..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>
          
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value="open">Open</option>
            <option value="waiting">Waiting</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="disputed">Disputed</option>
          </select>

          <button
            onClick={() => setShowDisputes(!showDisputes)}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              showDisputes 
                ? 'bg-orange-600 text-white' 
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Flag className="w-4 h-4 inline mr-2" />
            {showDisputes ? 'Show All' : 'Disputes Only'}
          </button>
        </div>
      </div>

      {/* Matches List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Matches ({filteredMatches.length})
          </h2>
        </div>

        <div className="divide-y divide-gray-200">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading matches...</p>
            </div>
          ) : filteredMatches.length === 0 ? (
            <div className="p-8 text-center">
              <Gavel className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No matches found</h3>
              <p className="text-gray-600">No matches match your current filters.</p>
            </div>
          ) : (
            filteredMatches.map((match) => (
              <div key={match.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{match.title}</h3>
                      {getStatusBadge(match.status)}
                      {match.status === 'disputed' && (
                        <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                          NEEDS ATTENTION
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span>Host: {match.host.username}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Diamond className="w-4 h-4" />
                        <span>{match.diamond_pot.toLocaleString()} diamonds</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4" />
                        <span>{match.participants?.length || 0} participants</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(match.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {match.referee && (
                      <div className="mt-2 flex items-center space-x-2 text-sm text-gray-600">
                        <Shield className="w-4 h-4" />
                        <span>Referee: {match.referee.username}</span>
                      </div>
                    )}

                    {match.dispute_reason && (
                      <div className="mt-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-orange-800">
                          <AlertTriangle className="w-4 h-4" />
                          <span className="font-medium">Dispute Reason:</span>
                        </div>
                        <p className="text-orange-700 mt-1">{match.dispute_reason}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => setSelectedMatch(match)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                    >
                      <Eye className="w-4 h-4" />
                      <span>View Details</span>
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Match Detail Modal */}
      {selectedMatch && (
        <MatchDetailModal
          match={selectedMatch}
          onClose={() => setSelectedMatch(null)}
          onAction={handleMatchAction}
          getStatusBadge={getStatusBadge}
        />
      )}
    </div>
  )
}
