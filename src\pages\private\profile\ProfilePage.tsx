import type React from "react"
import { useState, useEffect } from "react"
import { useAuth } from "../../../contexts/AuthContext"
import { userService } from "../../../services/userService"
// TODO: dashboardService not yet extracted - using old import temporarily
// TODO: dashboardService not yet extracted - using old import temporarily
import { dashboardService } from "../../../services/supabase"
import { Button } from "../../../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { Input } from "../../../components/ui/input"
import { Label } from "../../../components/ui/label"
import { Badge } from "../../../components/ui/badge"
import {
  User,
  Mail,
  Wallet,
  Trophy,
  Gamepad2,
  CheckCircle,
  Edit3,
  Upload,
  Camera,
  X,
  Eye,
  EyeOff,
  Shield,
  Star,
  Award,
  Users,
  Plus,
  Crown,
  Target,
  Zap,
  Flame,
  Medal,
  UserCheck
} from "lucide-react"

export default function ProfilePage() {
  const { user } = useAuth()
  const [userData, setUserData] = useState<any>(null)
  const [, setUserStats] = useState<any>(null) // userStats unused, keeping setter
  const [userAchievements, setUserAchievements] = useState<any[]>([])
  const [userGroup, setUserGroup] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [mobileLegends, setMobileLegends] = useState("")
  const [showSuccess, setShowSuccess] = useState(false)

  // Load user profile data
  useEffect(() => {
    const loadProfileData = async () => {
      if (!user?.id) {
        setIsLoading(false)
        return
      }

      setIsLoading(true)
      try {
        // Use user data from AuthContext first
        setUserData(user)
        setMobileLegends(user?.mlbb_id || "")

        // Try to get additional statistics
        try {
          const { data: stats, error: statsError } = await dashboardService.getUserStats(user.id)
          if (!statsError && stats) {
            setUserStats(stats)
          }
        } catch (statsErr) {
          console.log('Stats not available, using basic user data')
        }

        // Load user achievements
        try {
          const { data: achievements, error: achievementsError } = await dashboardService.getUserAchievements(user.id)
          if (!achievementsError && achievements) {
            setUserAchievements(achievements)
          }
        } catch (achievementsErr) {
          console.log('Achievements not available')
        }

        // Load user group information
        try {
          const { data: group, error: groupError } = await dashboardService.getUserGroup(user.id)
          if (!groupError && group) {
            setUserGroup(group)
          }
        } catch (groupErr) {
          console.log('Group information not available')
        }

      } catch (error) {
        console.error('Error loading profile data:', error)
        // Fallback to basic user data
        setUserData(user)
      } finally {
        setIsLoading(false)
      }
    }

    loadProfileData()
  }, [user])
  const [isEditing, setIsEditing] = useState(false)
  const [profileImage, setProfileImage] = useState<string | null>(null)
  const [showImageUpload, setShowImageUpload] = useState(false)
  const [uploadSuccess, setUploadSuccess] = useState(false)
  const [showPasswordForm, setShowPasswordForm] = useState(false)
  const [passwordForm, setPasswordForm] = useState({
    current: '',
    new: '',
    confirm: ''
  })
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  })

  // Utility functions
  const calculateWinRate = () => {
    if (!userData) return 0
    const totalMatches = userData.total_matches || 0
    if (totalMatches === 0) return 0
    return Math.round(((userData.wins || 0) / totalMatches) * 100)
  }

  const getRank = () => {
    const wins = userData?.wins || 0
    if (wins >= 100) return { name: "Legend", color: "text-purple-700", bgColor: "bg-purple-100" }
    if (wins >= 75) return { name: "Diamond", color: "text-blue-700", bgColor: "bg-blue-100" }
    if (wins >= 50) return { name: "Gold", color: "text-yellow-700", bgColor: "bg-yellow-100" }
    if (wins >= 25) return { name: "Silver", color: "text-gray-700", bgColor: "bg-gray-100" }
    return { name: "Bronze", color: "text-orange-700", bgColor: "bg-orange-100" }
  }

  const getBadgeIcon = (badgeType: string) => {
    switch (badgeType) {
      case "first_win": return <Trophy className="w-4 h-4" />
      case "streak_master": return <Flame className="w-4 h-4" />
      case "tournament_champion": return <Crown className="w-4 h-4" />
      case "sharpshooter": return <Target className="w-4 h-4" />
      case "speed_demon": return <Zap className="w-4 h-4" />
      case "veteran": return <Medal className="w-4 h-4" />
      default: return <Award className="w-4 h-4" />
    }
  }

  const getBadgeName = (badgeType: string) => {
    switch (badgeType) {
      case "first_win": return "First Victory"
      case "streak_master": return "Streak Master"
      case "tournament_champion": return "Tournament Champion"
      case "sharpshooter": return "Sharpshooter"
      case "speed_demon": return "Speed Demon"
      case "veteran": return "Veteran Player"
      default: return "Achievement"
    }
  }

  const getUserStatus = () => {
    const totalMatches = userData?.total_matches || 0
    const wins = userData?.wins || 0
    const diamondBalance = userData?.diamond_balance || 0

    if (userData?.is_referee) return "Verified Referee"
    if (wins >= 100 && diamondBalance >= 10000) return "Elite Player"
    if (wins >= 50 && diamondBalance >= 5000) return "Premium Member"
    if (wins >= 25) return "Active Player"
    if (totalMatches >= 10) return "Regular Member"
    return "New Member"
  }

  const handleSave = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)

      // Update Mobile Legends ID in database
      const { data, error } = await userService.updateProfile(user.id, {
        mlbb_id: mobileLegends
      })

      if (error) {
        throw new Error((error as any)?.message || 'Failed to update profile')
      }

      // Update local user data
      if (data) {
        setUserData((prev: any) => ({ ...prev, mlbbId: mobileLegends }))
      }

      setShowSuccess(true)
      setIsEditing(false)

      // Hide success message after 3 seconds
      setTimeout(() => {
        setShowSuccess(false)
      }, 3000)
    } catch (error: any) {
      console.error('Error updating profile:', error)
      alert(error.message || 'Failed to update profile. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        alert("Please select a valid image file")
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert("Image size should be less than 5MB")
        return
      }

      const reader = new FileReader()
      reader.onload = (e) => {
        setProfileImage(e.target?.result as string)
        setShowImageUpload(false)
        setUploadSuccess(true)

        // Hide success message after 3 seconds
        setTimeout(() => {
          setUploadSuccess(false)
        }, 3000)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeProfileImage = () => {
    setProfileImage(null)
    setShowImageUpload(false)
  }

  const handlePasswordChange = () => {
    if (passwordForm.new !== passwordForm.confirm) {
      alert('New passwords do not match')
      return
    }
    if (passwordForm.new.length < 6) {
      alert('Password must be at least 6 characters long')
      return
    }
    // Simulate password change
    setShowSuccess(true)
    setShowPasswordForm(false)
    setPasswordForm({ current: '', new: '', confirm: '' })
    setTimeout(() => setShowSuccess(false), 3000)
  }

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }))
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 animate-spin text-blue-600 mx-auto mb-4">⏳</div>
          <p className="text-gray-600">Loading your profile...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Profile Settings</h1>
        <p className="text-gray-600">Manage your account information and game preferences</p>
      </div>

          <div className="grid gap-6">
            {/* User Information Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5 text-blue-600" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center overflow-hidden border-2 border-gray-200">
                      {profileImage ? (
                        <img
                          src={profileImage || "/placeholder.svg"}
                          alt="Profile"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <User className="w-8 h-8 text-blue-600" />
                      )}
                    </div>

                    {/* Upload Button */}
                    <button
                      onClick={() => setShowImageUpload(!showImageUpload)}
                      className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
                    >
                      <Camera className="w-3 h-3 text-white" />
                    </button>

                    {/* Upload Options */}
                    {showImageUpload && (
                      <div className="absolute top-20 left-0 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-10 min-w-48">
                        <div className="space-y-2">
                          <label className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer">
                            <Upload className="w-4 h-4 text-gray-600" />
                            <span className="text-sm text-gray-700">Upload new photo</span>
                            <input type="file" accept="image/*" onChange={handleImageUpload} className="hidden" />
                          </label>

                          {profileImage && (
                            <button
                              onClick={removeProfileImage}
                              className="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded w-full text-left"
                            >
                              <X className="w-4 h-4 text-red-600" />
                              <span className="text-sm text-red-600">Remove photo</span>
                            </button>
                          )}

                          <button
                            onClick={() => setShowImageUpload(false)}
                            className="text-xs text-gray-500 hover:text-gray-700 p-2 w-full text-left"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    )}
                  </div>

                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="text-xl font-semibold text-gray-900">
                        {userData ? `${userData.first_name} ${userData.last_name}` : 'Loading...'}
                      </h3>
                      {userData?.is_referee && (
                        <Badge className="bg-green-100 text-green-700 border-green-200">
                          <UserCheck className="w-3 h-3 mr-1" />
                          Verified Referee ✅
                        </Badge>
                      )}
                    </div>
                    <p className="text-gray-600">{getUserStatus()}</p>
                    <div className="flex items-center space-x-2 mt-2">
                      <Badge variant="secondary" className={`${getRank().bgColor} ${getRank().color}`}>
                        <Crown className="w-3 h-3 mr-1" />
                        {getRank().name}
                      </Badge>
                      <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                        <Trophy className="w-3 h-3 mr-1" />
                        {calculateWinRate()}% Win Rate
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Profile Image Upload Success */}
                {uploadSuccess && (
                  <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                    <CheckCircle className="w-4 h-4 text-green-600" />
                    <span className="text-green-700 text-sm font-medium">Profile picture updated successfully</span>
                  </div>
                )}

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Email Address</Label>
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Mail className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-900">{userData?.email || 'Not available'}</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Diamond Balance</Label>
                    <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                      <Wallet className="w-4 h-4 text-gray-500" />
                      <span className="text-gray-900 font-semibold">{(userData?.diamond_balance || 0).toLocaleString()} Diamonds</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Total Wins</Label>
                    <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg">
                      <Trophy className="w-4 h-4 text-green-600" />
                      <span className="text-gray-900 font-semibold">{userData?.wins || 0} Wins</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Total Losses</Label>
                    <div className="flex items-center space-x-2 p-3 bg-red-50 rounded-lg">
                      <X className="w-4 h-4 text-red-600" />
                      <span className="text-gray-900 font-semibold">{userData?.losses || 0} Losses</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Win Rate</Label>
                    <div className="flex items-center space-x-2 p-3 bg-blue-50 rounded-lg">
                      <Star className="w-4 h-4 text-blue-600" />
                      <span className="text-gray-900 font-semibold">{calculateWinRate()}%</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-gray-700">Current Rank</Label>
                    <div className={`flex items-center space-x-2 p-3 ${getRank().bgColor} rounded-lg`}>
                      <Crown className={`w-4 h-4 ${getRank().color.replace('text-', 'text-')}`} />
                      <span className={`font-semibold ${getRank().color}`}>{getRank().name}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Group Information Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-600" />
                  Group Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                {userGroup ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                          <Users className="w-6 h-6 text-white" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900">{userGroup.group?.name || "Unknown Group"}</h4>
                          <p className="text-sm text-gray-600">
                            {userGroup.role === 'owner' ? 'Group Owner' :
                             userGroup.role === 'moderator' ? 'Moderator' : 'Active Member'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {userGroup.group?.member_count || 0} members • Rank #{userGroup.group?.rank || 'N/A'}
                          </p>
                        </div>
                      </div>
                      <Badge className="bg-green-100 text-green-700 border-green-200">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        {userGroup.status === 'active' ? 'Active' : 'Joined'}
                      </Badge>
                    </div>
                    <div className="flex space-x-3">
                      <Button variant="outline" className="flex-1 bg-white text-blue-600 border-blue-200 hover:bg-blue-50">
                        View Group
                      </Button>
                      {userGroup.role !== 'owner' && (
                        <Button variant="outline" className="flex-1 bg-white text-red-600 border-red-200 hover:bg-red-50">
                          Leave Group
                        </Button>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="text-center p-8 bg-gray-50 rounded-lg">
                      <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h4 className="font-semibold text-gray-900 mb-2">No Group Yet</h4>
                      <p className="text-gray-600 mb-6">Join a group to team up with other players and compete together!</p>
                      <div className="flex space-x-3 justify-center">
                        <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                          <Users className="w-4 h-4 mr-2" />
                          Join Group
                        </Button>
                        <Button variant="outline" className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50">
                          <Plus className="w-4 h-4 mr-2" />
                          Create Group
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Badges Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Award className="w-5 h-5 text-blue-600" />
                  Achievements & Badges
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {userAchievements.map((achievement, index) => (
                      <div key={achievement.id || index} className="flex flex-col items-center p-4 bg-gradient-to-br from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg hover:shadow-md transition-shadow">
                        <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mb-2 text-yellow-600">
                          {achievement.achievement?.icon ? (
                            <span className="text-lg">{achievement.achievement.icon}</span>
                          ) : (
                            getBadgeIcon(achievement.achievement?.type || 'default')
                          )}
                        </div>
                        <h4 className="font-medium text-gray-900 text-sm text-center">
                          {achievement.achievement?.name || getBadgeName(achievement.achievement?.type || 'default')}
                        </h4>
                        <p className="text-xs text-gray-600 text-center mt-1">
                          {achievement.unlocked_at ?
                            new Date(achievement.unlocked_at).toLocaleDateString() :
                            'Earned'
                          }
                        </p>
                      </div>
                    ))}
                  </div>

                  {userAchievements.length === 0 && (
                    <div className="text-center p-8 bg-gray-50 rounded-lg">
                      <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <h4 className="font-semibold text-gray-900 mb-2">No Achievements Yet</h4>
                      <p className="text-gray-600">Start playing matches to unlock your first achievement!</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Linked Game IDs Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gamepad2 className="w-5 h-5 text-blue-600" />
                  Linked Game IDs
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="mobile-legends-id" className="text-sm font-medium text-gray-700">
                      Mobile Legends ID
                    </Label>
                    <div className="space-y-3">
                      <Input
                        id="mobile-legends-id"
                        type="text"
                        placeholder="Enter your Mobile Legends user ID"
                        value={mobileLegends}
                        onChange={(e) => setMobileLegends(e.target.value)}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                        disabled={!isEditing && !!mobileLegends}
                      />

                      <div className="flex items-center gap-3">
                        {!isEditing && mobileLegends ? (
                          <Button
                            variant="outline"
                            onClick={() => setIsEditing(true)}
                            className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                          >
                            <Edit3 className="w-4 h-4 mr-2" />
                            Edit
                          </Button>
                        ) : (
                          <Button
                            onClick={handleSave}
                            disabled={!mobileLegends.trim()}
                            className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            Save
                          </Button>
                        )}

                        {isEditing && (
                          <Button
                            variant="outline"
                            onClick={() => {
                              setIsEditing(false)
                              // Reset to original value if needed
                            }}
                            className="bg-white text-gray-600 border-gray-300 hover:bg-gray-50"
                          >
                            Cancel
                          </Button>
                        )}
                      </div>

                      {/* Success Message */}
                      {showSuccess && (
                        <div className="flex items-center space-x-2 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                          <span className="text-green-700 text-sm font-medium">Saved successfully</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Placeholder for future game IDs */}
                  <div className="space-y-2 opacity-50">
                    <Label className="text-sm font-medium text-gray-700">Valorant ID</Label>
                    <Input
                      type="text"
                      placeholder="Coming soon..."
                      disabled
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100"
                    />
                  </div>

                  <div className="space-y-2 opacity-50">
                    <Label className="text-sm font-medium text-gray-700">Call of Duty ID</Label>
                    <Input
                      type="text"
                      placeholder="Coming soon..."
                      disabled
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-100"
                    />
                  </div>
                </div>

                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-sm text-blue-700">
                    <strong>Note:</strong> Your game IDs are used to verify match results and ensure fair play. Make
                    sure to enter the correct ID that matches your in-game profile.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Account Settings Section */}
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                    <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
                  </div>
                  <Button variant="outline" className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50">
                    Enable
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Email Notifications</h4>
                    <p className="text-sm text-gray-600">Receive updates about your matches and account</p>
                  </div>
                  <Button variant="outline" className="bg-white text-gray-600 border-gray-300 hover:bg-gray-50">
                    Manage
                  </Button>
                </div>

                <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Change Password</h4>
                    <p className="text-sm text-gray-600">Update your account password</p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={() => setShowPasswordForm(!showPasswordForm)}
                    className="bg-white text-blue-600 border-blue-200 hover:bg-blue-50"
                  >
                    <Shield className="w-4 h-4 mr-2" />
                    Change
                  </Button>
                </div>

                {/* Password Change Form */}
                {showPasswordForm && (
                  <div className="p-4 border border-gray-200 rounded-lg bg-gray-50">
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-700">Current Password</Label>
                        <div className="relative">
                          <Input
                            type={showPasswords.current ? "text" : "password"}
                            value={passwordForm.current}
                            onChange={(e) => setPasswordForm(prev => ({ ...prev, current: e.target.value }))}
                            className="pr-10"
                            placeholder="Enter current password"
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility('current')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-700">New Password</Label>
                        <div className="relative">
                          <Input
                            type={showPasswords.new ? "text" : "password"}
                            value={passwordForm.new}
                            onChange={(e) => setPasswordForm(prev => ({ ...prev, new: e.target.value }))}
                            className="pr-10"
                            placeholder="Enter new password"
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility('new')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium text-gray-700">Confirm New Password</Label>
                        <div className="relative">
                          <Input
                            type={showPasswords.confirm ? "text" : "password"}
                            value={passwordForm.confirm}
                            onChange={(e) => setPasswordForm(prev => ({ ...prev, confirm: e.target.value }))}
                            className="pr-10"
                            placeholder="Confirm new password"
                          />
                          <button
                            type="button"
                            onClick={() => togglePasswordVisibility('confirm')}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                          >
                            {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                          </button>
                        </div>
                      </div>

                      <div className="flex space-x-3">
                        <Button
                          onClick={handlePasswordChange}
                          disabled={!passwordForm.current || !passwordForm.new || !passwordForm.confirm}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          Update Password
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => {
                            setShowPasswordForm(false)
                            setPasswordForm({ current: '', new: '', confirm: '' })
                          }}
                          className="bg-white text-gray-600 border-gray-300 hover:bg-gray-50"
                        >
                          Cancel
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>


          </div>
    </div>
  )
}
