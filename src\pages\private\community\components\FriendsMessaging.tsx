import React, { useState, useEffect, useRef } from 'react'
import {
  MessageCircle,
  Send,
  Search,
  UserPlus,
  Users,
  Phone,
  Video,
  Info,
  Smile,
  Paperclip,
  Image,
  Gift,
  Check<PERSON>he<PERSON>,
  Clock
} from 'lucide-react'
import { useAuth } from '../../../../contexts/AuthContext'

interface Friend {
  id: string
  username: string
  avatar?: string
  isOnline: boolean
  lastSeen?: string
  unreadCount: number
  lastMessage?: {
    content: string
    timestamp: string
    isFromMe: boolean
  }
  status?: 'online' | 'away' | 'busy' | 'offline'
  gameStatus?: {
    game: string
    status: string
  }
}

interface Message {
  id: string
  content: string
  senderId: string
  timestamp: string
  type: 'text' | 'image' | 'gift' | 'game_invite'
  status: 'sending' | 'sent' | 'delivered' | 'read'
  replyTo?: {
    messageId: string
    content: string
    senderName: string
  }
}

interface FriendsMessagingProps {
  friends: Friend[]
  onSendMessage: (friendId: string, content: string, type?: string) => void
  onAddFriend: () => void
}

const FriendsMessaging: React.FC<FriendsMessagingProps> = ({
  friends,
  onSendMessage,
  onAddFriend
}) => {
  const { user } = useAuth()
  const [selectedFriend, setSelectedFriend] = useState<Friend | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  // const [isLoading, setIsLoading] = useState(false) // TODO: Confirm usage
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Mock messages for demo
  useEffect(() => {
    if (selectedFriend) {
      setMessages([
        {
          id: '1',
          content: 'Hey! Want to play some Mobile Legends?',
          senderId: selectedFriend.id,
          timestamp: new Date(Date.now() - 300000).toISOString(),
          type: 'text',
          status: 'read'
        },
        {
          id: '2',
          content: 'Sure! Let me finish this match first',
          senderId: user?.id || '',
          timestamp: new Date(Date.now() - 240000).toISOString(),
          type: 'text',
          status: 'read'
        },
        {
          id: '3',
          content: 'I just got a new skin! Check it out 🔥',
          senderId: selectedFriend.id,
          timestamp: new Date(Date.now() - 120000).toISOString(),
          type: 'text',
          status: 'read'
        }
      ])
    }
  }, [selectedFriend, user])

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [messages])

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedFriend) return

    const message: Message = {
      id: Date.now().toString(),
      content: newMessage,
      senderId: user?.id || '',
      timestamp: new Date().toISOString(),
      type: 'text',
      status: 'sending'
    }

    setMessages(prev => [...prev, message])
    onSendMessage(selectedFriend.id, newMessage)
    setNewMessage('')

    // Simulate message status updates
    setTimeout(() => {
      setMessages(prev => prev.map(m => 
        m.id === message.id ? { ...m, status: 'sent' } : m
      ))
    }, 1000)
  }

  const filteredFriends = friends.filter(friend =>
    friend.username.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'online': return 'bg-green-500'
      case 'away': return 'bg-yellow-500'
      case 'busy': return 'bg-red-500'
      default: return 'bg-gray-400'
    }
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else {
      return date.toLocaleDateString()
    }
  }

  const getMessageStatusIcon = (status: string) => {
    switch (status) {
      case 'sending': return <Clock className="w-3 h-3 text-gray-400" />
      case 'sent': return <CheckCheck className="w-3 h-3 text-gray-400" />
      case 'delivered': return <CheckCheck className="w-3 h-3 text-blue-500" />
      case 'read': return <CheckCheck className="w-3 h-3 text-blue-600" />
      default: return null
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 h-[600px] flex">
      {/* Friends List */}
      <div className="w-80 border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Friends</h3>
            <button
              onClick={onAddFriend}
              className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
            >
              <UserPlus className="w-4 h-4" />
            </button>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="Search friends..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-9 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        {/* Friends List */}
        <div className="flex-1 overflow-y-auto">
          {filteredFriends.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <Users className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm">No friends found</p>
              <button
                onClick={onAddFriend}
                className="mt-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                Add friends to start chatting
              </button>
            </div>
          ) : (
            <div className="p-2">
              {filteredFriends.map((friend) => (
                <button
                  key={friend.id}
                  onClick={() => setSelectedFriend(friend)}
                  className={`w-full p-3 rounded-lg text-left transition-colors mb-1 ${
                    selectedFriend?.id === friend.id
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className="relative">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                        {friend.username.charAt(0).toUpperCase()}
                      </div>
                      <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(friend.status)}`} />
                    </div>

                    {/* Friend Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="font-medium text-gray-900 truncate">{friend.username}</p>
                        {friend.unreadCount > 0 && (
                          <span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5 min-w-[20px] text-center">
                            {friend.unreadCount}
                          </span>
                        )}
                      </div>
                      
                      {friend.lastMessage && (
                        <p className="text-sm text-gray-600 truncate">
                          {friend.lastMessage.isFromMe ? 'You: ' : ''}
                          {friend.lastMessage.content}
                        </p>
                      )}
                      
                      {friend.gameStatus && (
                        <p className="text-xs text-green-600 truncate">
                          Playing {friend.gameStatus.game}
                        </p>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedFriend ? (
          <>
            {/* Chat Header */}
            <div className="p-4 border-b border-gray-200 bg-gray-50">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-medium">
                      {selectedFriend.username.charAt(0).toUpperCase()}
                    </div>
                    <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(selectedFriend.status)}`} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{selectedFriend.username}</h3>
                    <p className="text-sm text-gray-600">
                      {selectedFriend.status === 'online' ? 'Online' : `Last seen ${selectedFriend.lastSeen}`}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors">
                    <Phone className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors">
                    <Video className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-200 rounded-lg transition-colors">
                    <Info className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>

            {/* Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
              {messages.map((message) => (
                <div
                  key={message.id}
                  className={`flex ${message.senderId === user?.id ? 'justify-end' : 'justify-start'}`}
                >
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.senderId === user?.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-900'
                  }`}>
                    <p className="text-sm">{message.content}</p>
                    <div className={`flex items-center justify-between mt-1 ${
                      message.senderId === user?.id ? 'text-blue-100' : 'text-gray-500'
                    }`}>
                      <span className="text-xs">{formatTime(message.timestamp)}</span>
                      {message.senderId === user?.id && (
                        <div className="ml-2">
                          {getMessageStatusIcon(message.status)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Paperclip className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Image className="w-4 h-4" />
                </button>
                <div className="flex-1 relative">
                  <input
                    type="text"
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder="Type a message..."
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Smile className="w-4 h-4" />
                </button>
                <button className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <Gift className="w-4 h-4" />
                </button>
                <button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            <div className="text-center">
              <MessageCircle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a friend to start chatting</h3>
              <p className="text-gray-600">Choose a friend from the list to begin your conversation</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default FriendsMessaging
