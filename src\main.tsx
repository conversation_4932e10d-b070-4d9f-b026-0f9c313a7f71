import React from "react"
import <PERSON>act<PERSON><PERSON> from "react-dom/client"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom"
import App from "./App.tsx"
import "./index.css"

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true
      }}
    >
      <App />
    </BrowserRouter>
  </React.StrictMode>
)
