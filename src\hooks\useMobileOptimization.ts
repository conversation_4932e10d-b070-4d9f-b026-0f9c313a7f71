import { useState, useEffect, useCallback, useRef } from 'react'
import { 
  isMobileDevice, 
  isSlowConnection, 
  throttle, 
  debounce,
  PerformanceMonitor,
  MemoryManager
} from '../utils/mobilePerformance'

interface MobileOptimizationConfig {
  enablePerformanceMonitoring?: boolean
  enableMemoryManagement?: boolean
  throttleScrollEvents?: boolean
  debounceResizeEvents?: boolean
  preloadCriticalImages?: boolean
}

interface MobileOptimizationState {
  isMobile: boolean
  isSlowConnection: boolean
  screenSize: { width: number; height: number }
  orientation: 'portrait' | 'landscape'
  isOnline: boolean
  batteryLevel?: number
  isLowPowerMode?: boolean
}

export function useMobileOptimization(config: MobileOptimizationConfig = {}) {
  const {
    enablePerformanceMonitoring = true,
    enableMemoryManagement = true,
    throttleScrollEvents = true,
    debounceResizeEvents = true,
    preloadCriticalImages = false
  } = config

  const [state, setState] = useState<MobileOptimizationState>({
    isMobile: false,
    isSlowConnection: false,
    screenSize: { width: 0, height: 0 },
    orientation: 'portrait',
    isOnline: true
  })

  const performanceTimers = useRef<Map<string, number>>(new Map())
  const cleanupFunctions = useRef<(() => void)[]>([])

  // Initialize mobile detection and state
  useEffect(() => {
    const updateState = () => {
      setState(prev => ({
        ...prev,
        isMobile: isMobileDevice(),
        isSlowConnection: isSlowConnection(),
        screenSize: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait',
        isOnline: navigator.onLine
      }))
    }

    updateState()

    // Battery API (experimental)
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        setState(prev => ({
          ...prev,
          batteryLevel: battery.level,
          isLowPowerMode: battery.level < 0.2
        }))

        const updateBattery = () => {
          setState(prev => ({
            ...prev,
            batteryLevel: battery.level,
            isLowPowerMode: battery.level < 0.2
          }))
        }

        battery.addEventListener('levelchange', updateBattery)
        cleanupFunctions.current.push(() => {
          battery.removeEventListener('levelchange', updateBattery)
        })
      })
    }

    // Network status
    const handleOnline = () => setState(prev => ({ ...prev, isOnline: true }))
    const handleOffline = () => setState(prev => ({ ...prev, isOnline: false }))

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    cleanupFunctions.current.push(() => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    })

    // Resize handler
    const handleResize = debounceResizeEvents 
      ? debounce(updateState, 250)
      : updateState

    window.addEventListener('resize', handleResize)
    cleanupFunctions.current.push(() => {
      window.removeEventListener('resize', handleResize)
    })

    // Orientation change
    const handleOrientationChange = () => {
      setTimeout(updateState, 100) // Small delay for accurate measurements
    }

    window.addEventListener('orientationchange', handleOrientationChange)
    cleanupFunctions.current.push(() => {
      window.removeEventListener('orientationchange', handleOrientationChange)
    })

    return () => {
      cleanupFunctions.current.forEach(cleanup => cleanup())
      cleanupFunctions.current = []
    }
  }, [debounceResizeEvents])

  // Performance monitoring
  const startTiming = useCallback((label: string) => {
    if (enablePerformanceMonitoring) {
      performanceTimers.current.set(label, performance.now())
      PerformanceMonitor.startTiming(label)
    }
  }, [enablePerformanceMonitoring])

  const endTiming = useCallback((label: string) => {
    if (enablePerformanceMonitoring) {
      const duration = PerformanceMonitor.endTiming(label)
      performanceTimers.current.delete(label)
      return duration
    }
    return 0
  }, [enablePerformanceMonitoring])

  // Memory management
  const cleanup = useCallback(() => {
    if (enableMemoryManagement) {
      MemoryManager.cleanup()
    }
  }, [enableMemoryManagement])

  // Optimized scroll handler
  const createScrollHandler = useCallback((handler: () => void) => {
    if (throttleScrollEvents) {
      return throttle(handler, 16) // ~60fps
    }
    return handler
  }, [throttleScrollEvents])

  // Image preloading
  const preloadImages = useCallback(async (urls: string[]) => {
    if (!preloadCriticalImages || state.isSlowConnection) return

    const promises = urls.map(url => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image()
        img.onload = () => resolve()
        img.onerror = reject
        img.src = url
      })
    })

    try {
      await Promise.allSettled(promises)
    } catch (error) {
      console.warn('Failed to preload some images:', error)
    }
  }, [preloadCriticalImages, state.isSlowConnection])

  // Adaptive loading strategy
  const getLoadingStrategy = useCallback(() => {
    if (state.isSlowConnection || state.isLowPowerMode) {
      return {
        imageQuality: 60,
        enableAnimations: false,
        lazyLoadThreshold: 200,
        preloadCount: 2
      }
    }

    if (state.isMobile) {
      return {
        imageQuality: 80,
        enableAnimations: true,
        lazyLoadThreshold: 100,
        preloadCount: 5
      }
    }

    return {
      imageQuality: 90,
      enableAnimations: true,
      lazyLoadThreshold: 50,
      preloadCount: 10
    }
  }, [state.isSlowConnection, state.isLowPowerMode, state.isMobile])

  // Touch optimization
  const optimizeForTouch = useCallback((element: HTMLElement) => {
    if (!state.isMobile) return

    // Add touch-action for better scroll performance
    element.style.touchAction = 'manipulation'
    
    // Add will-change for animations
    element.style.willChange = 'transform'
    
    // Optimize for GPU acceleration
    element.style.transform = 'translateZ(0)'

    return () => {
      element.style.touchAction = ''
      element.style.willChange = ''
      element.style.transform = ''
    }
  }, [state.isMobile])

  // Viewport utilities
  const isInViewport = useCallback((element: HTMLElement, threshold = 0) => {
    const rect = element.getBoundingClientRect()
    return (
      rect.top >= -threshold &&
      rect.left >= -threshold &&
      rect.bottom <= state.screenSize.height + threshold &&
      rect.right <= state.screenSize.width + threshold
    )
  }, [state.screenSize])

  // Performance metrics
  const getPerformanceMetrics = useCallback(() => {
    if (!enablePerformanceMonitoring) return {}
    return PerformanceMonitor.getMetrics()
  }, [enablePerformanceMonitoring])

  return {
    // State
    ...state,
    
    // Performance
    startTiming,
    endTiming,
    getPerformanceMetrics,
    
    // Memory
    cleanup,
    
    // Scroll optimization
    createScrollHandler,
    
    // Image optimization
    preloadImages,
    getLoadingStrategy,
    
    // Touch optimization
    optimizeForTouch,
    
    // Viewport utilities
    isInViewport,
    
    // Utility functions
    shouldReduceAnimations: state.isSlowConnection || state.isLowPowerMode,
    shouldPreloadImages: !state.isSlowConnection && state.isOnline,
    recommendedImageQuality: getLoadingStrategy().imageQuality
  }
}

// Hook for component-level mobile optimization
export function useComponentMobileOptimization(componentName: string) {
  const mobile = useMobileOptimization({
    enablePerformanceMonitoring: true,
    enableMemoryManagement: true
  })

  useEffect(() => {
    if (mobile.isMobile) {
      mobile.startTiming(`${componentName}-render`)
    }

    return () => {
      if (mobile.isMobile) {
        const renderTime = mobile.endTiming(`${componentName}-render`)
        if (renderTime > 100) {
          console.warn(`🐌 ${componentName} slow render:`, renderTime + 'ms')
        }
      }
    }
  }, [mobile, componentName])

  return mobile
}
