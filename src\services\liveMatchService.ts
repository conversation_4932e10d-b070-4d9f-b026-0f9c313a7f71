import { supabase } from './supabase'
import { smartNotificationService } from './smartNotifications'

export interface LiveMatchEvent {
  type: 'match_created' | 'match_joined' | 'match_started' | 'match_completed' | 'referee_assigned' | 'match_cancelled'
  matchId: string
  userId?: string
  data: any
  timestamp: string
}

export interface LiveMatchSubscription {
  matchId: string
  channel: any
  callbacks: ((event: LiveMatchEvent) => void)[]
}

class LiveMatchService {
  private subscriptions = new Map<string, LiveMatchSubscription>()
  private globalSubscription: any = null
  private globalCallbacks: ((event: LiveMatchEvent) => void)[] = []

  /**
   * Subscribe to all match events globally
   */
  subscribeToAllMatches(callback: (event: LiveMatchEvent) => void) {
    this.globalCallbacks.push(callback)

    if (!this.globalSubscription) {
      this.globalSubscription = supabase
        .channel('global-matches')
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'matches' },
          (payload) => {
            const event: LiveMatchEvent = {
              type: this.getEventType(payload),
              matchId: (payload.new as any)?.id || (payload.old as any)?.id,
              data: payload.new || payload.old,
              timestamp: new Date().toISOString()
            }
            this.notifyGlobalCallbacks(event)
          }
        )
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'match_participants' },
          async (payload) => {
            const matchId = (payload.new as any)?.match_id || (payload.old as any)?.match_id
            if (matchId) {
              const event: LiveMatchEvent = {
                type: payload.eventType === 'INSERT' ? 'match_joined' : 'match_joined',
                matchId: matchId,
                userId: (payload.new as any)?.user_id || (payload.old as any)?.user_id,
                data: payload.new || payload.old,
                timestamp: new Date().toISOString()
              }
              this.notifyGlobalCallbacks(event)
            }
          }
        )
        .subscribe()
    }

    return () => {
      this.globalCallbacks = this.globalCallbacks.filter(cb => cb !== callback)
      if (this.globalCallbacks.length === 0 && this.globalSubscription) {
        supabase.removeChannel(this.globalSubscription)
        this.globalSubscription = null
      }
    }
  }

  /**
   * Subscribe to specific match events
   */
  subscribeToMatch(matchId: string, callback: (event: LiveMatchEvent) => void) {
    let subscription = this.subscriptions.get(matchId)

    if (!subscription) {
      const channel = supabase
        .channel(`match-${matchId}`)
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'matches', filter: `id=eq.${matchId}` },
          (payload) => {
            const event: LiveMatchEvent = {
              type: this.getEventType(payload),
              matchId: matchId,
              data: payload.new || payload.old,
              timestamp: new Date().toISOString()
            }
            subscription?.callbacks.forEach(cb => cb(event))
          }
        )
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'match_participants', filter: `match_id=eq.${matchId}` },
          (payload) => {
            const event: LiveMatchEvent = {
              type: 'match_joined',
              matchId: matchId,
              userId: (payload.new as any)?.user_id || (payload.old as any)?.user_id,
              data: payload.new || payload.old,
              timestamp: new Date().toISOString()
            }
            subscription?.callbacks.forEach(cb => cb(event))
          }
        )
        .subscribe()

      subscription = {
        matchId,
        channel,
        callbacks: []
      }
      this.subscriptions.set(matchId, subscription)
    }

    subscription.callbacks.push(callback)

    return () => {
      if (subscription) {
        subscription.callbacks = subscription.callbacks.filter(cb => cb !== callback)
        if (subscription.callbacks.length === 0) {
          supabase.removeChannel(subscription.channel)
          this.subscriptions.delete(matchId)
        }
      }
    }
  }

  /**
   * Send live notification to match participants
   */
  async notifyMatchParticipants(matchId: string, notification: {
    title: string
    message: string
    type: 'info' | 'success' | 'warning' | 'error'
  }) {
    try {
      // Get match participants
      const { data: participants } = await supabase
        .from('match_participants')
        .select('user_id')
        .eq('match_id', matchId)

      if (participants) {
        // Send notification to each participant
        for (const participant of participants) {
          await (smartNotificationService as any).sendNotification({
            id: crypto.randomUUID(),
            user_id: participant.user_id,
            type: 'perfect_match',
            title: notification.title,
            message: notification.message,
            priority: 'high',
            data: { match_id: matchId },
            read: false,
            created_at: new Date().toISOString()
          })
        }
      }
    } catch (error) {
      console.error('Error notifying match participants:', error)
    }
  }

  /**
   * Notify referee about match assignment
   */
  async notifyRefereeAssignment(refereeId: string, matchId: string, matchData: any) {
    try {
      await (smartNotificationService as any).sendNotification({
        id: crypto.randomUUID(),
        user_id: refereeId,
        type: 'perfect_match',
        title: 'New Match Assignment',
        message: `You have been assigned to referee a ${matchData.game} match`,
        action_url: `/referee`,
        action_label: 'View Match',
        priority: 'high',
        data: { match_id: matchId },
        read: false,
        created_at: new Date().toISOString()
      })
    } catch (error) {
      console.error('Error notifying referee assignment:', error)
    }
  }

  /**
   * Cleanup all subscriptions
   */
  cleanup() {
    // Cleanup global subscription
    if (this.globalSubscription) {
      supabase.removeChannel(this.globalSubscription)
      this.globalSubscription = null
    }
    this.globalCallbacks = []

    // Cleanup match-specific subscriptions
    for (const subscription of this.subscriptions.values()) {
      supabase.removeChannel(subscription.channel)
    }
    this.subscriptions.clear()
  }

  private getEventType(payload: any): LiveMatchEvent['type'] {
    if (payload.eventType === 'INSERT') {
      return 'match_created'
    } else if (payload.eventType === 'UPDATE') {
      const oldStatus = payload.old?.status
      const newStatus = payload.new?.status
      
      if (oldStatus !== newStatus) {
        switch (newStatus) {
          case 'ongoing':
          case 'in_progress':
            return 'match_started'
          case 'completed':
            return 'match_completed'
          case 'cancelled':
            return 'match_cancelled'
          default:
            return 'match_created'
        }
      }
      
      // Check for referee assignment
      if (!payload.old?.referee_id && payload.new?.referee_id) {
        return 'referee_assigned'
      }
    }
    
    return 'match_created'
  }

  private notifyGlobalCallbacks(event: LiveMatchEvent) {
    this.globalCallbacks.forEach(callback => {
      try {
        callback(event)
      } catch (error) {
        console.error('Error in global match callback:', error)
      }
    })
  }
}

export const liveMatchService = new LiveMatchService()

// Cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    liveMatchService.cleanup()
  })
}
