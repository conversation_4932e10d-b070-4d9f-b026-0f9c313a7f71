import { useState, useEffect } from 'react'
import { Database, CheckCircle, AlertTriangle, RefreshCw, Copy, ExternalLink } from 'lucide-react'
import { checkDatabaseStatus, getSystemStats, DatabaseStatus as DatabaseStatusType, SystemStats } from '../../utils/databaseSetup'

interface DatabaseStatusProps {
  onStatusChange?: (isReady: boolean) => void
}

export default function DatabaseStatus({ onStatusChange }: DatabaseStatusProps) {
  const [status, setStatus] = useState<DatabaseStatusType | null>(null)
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  useEffect(() => {
    checkStatus()
  }, [])

  const checkStatus = async () => {
    setLoading(true)
    try {
      const statusResult = await checkDatabaseStatus()
      const statsResult = await getSystemStats()

      setStatus(statusResult)
      setStats(statsResult)
      onStatusChange?.(statusResult.isConnected)
    } catch (error) {
      console.error('Error checking database status:', error)
    } finally {
      setLoading(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // Could add a toast notification here
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3">
          <RefreshCw className="w-5 h-5 text-blue-600 animate-spin" />
          <span className="text-gray-600">Checking database status...</span>
        </div>
      </div>
    )
  }

  if (!status) {
    return (
      <div className="bg-red-50 rounded-lg border border-red-200 p-6">
        <div className="flex items-center space-x-3">
          <AlertTriangle className="w-5 h-5 text-red-600" />
          <span className="text-red-800">Failed to check database status</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Status Overview */}
      <div className={`rounded-lg border p-6 ${
        status.isReady 
          ? 'bg-green-50 border-green-200' 
          : 'bg-orange-50 border-orange-200'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {status.isReady ? (
              <CheckCircle className="w-6 h-6 text-green-600" />
            ) : (
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            )}
            <div>
              <h3 className={`font-semibold ${
                status.isReady ? 'text-green-800' : 'text-orange-800'
              }`}>
                Database Status
              </h3>
              <p className={`text-sm ${
                status.isReady ? 'text-green-600' : 'text-orange-600'
              }`}>
                {status.isReady 
                  ? 'All required tables exist and are ready'
                  : `${status.missingTables.length} tables missing`
                }
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={checkStatus}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
              title="Refresh status"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </button>
          </div>
        </div>

        {/* Missing Tables Warning */}
        {!status.isReady && (
          <div className="mt-4 p-4 bg-white rounded-lg border border-orange-200">
            <h4 className="font-medium text-orange-800 mb-2">Setup Required</h4>
            <p className="text-sm text-orange-700 mb-3">
              Some features won't work until missing database tables are created.
            </p>
            <div className="flex items-center space-x-2">
              <a
                href="https://supabase.com/dashboard"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-orange-600 rounded-lg hover:bg-orange-700"
              >
                <ExternalLink className="w-4 h-4 mr-2" />
                Open Supabase Dashboard
              </a>
              <button
                onClick={() => copyToClipboard(status.setupInstructions?.join('\n') || 'No setup instructions available')}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200"
              >
                <Copy className="w-4 h-4 mr-2" />
                Copy Instructions
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Detailed Information */}
      {showDetails && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-4 flex items-center">
            <Database className="w-5 h-5 mr-2" />
            Database Details
          </h4>

          {/* Table Status */}
          <div className="mb-6">
            <h5 className="font-medium text-gray-700 mb-3">Table Status</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {stats && Object.entries(stats).map(([table, count]) => {
                const numCount = typeof count === 'number' ? count : -1;
                return (
                <div
                  key={table}
                  className={`p-3 rounded-lg border ${
                    numCount >= 0
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-medium ${
                      numCount >= 0 ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {table}
                    </span>
                    <span className={`text-xs ${
                      numCount >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {numCount >= 0 ? `${numCount} rows` : 'Missing'}
                    </span>
                  </div>
                </div>
                );
              })}
            </div>
          </div>

          {/* Setup Instructions */}
          {!status.isReady && (
            <div className="mb-6">
              <h5 className="font-medium text-gray-700 mb-3">Setup Instructions</h5>
              <div className="bg-gray-50 rounded-lg p-4">
                <pre className="text-sm text-gray-700 whitespace-pre-wrap font-mono">
                  {status.setupInstructions}
                </pre>
              </div>
            </div>
          )}

          {/* Missing Tables List */}
          {status.missingTables.length > 0 && (
            <div>
              <h5 className="font-medium text-gray-700 mb-3">Missing Tables</h5>
              <div className="flex flex-wrap gap-2">
                {status.missingTables.map((table) => (
                  <span
                    key={table}
                    className="px-2 py-1 text-xs font-medium text-red-700 bg-red-100 rounded-full"
                  >
                    {table}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Quick Actions */}
      {status.isReady && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-4">Database Management</h4>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={async () => {
                try {
                  // TODO: Implement data integrity verification
                  console.log('Data integrity check initiated')
                } catch (error) {
                  console.error('Integrity check failed:', error)
                }
              }}
              className="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200"
            >
              Verify Data Integrity
            </button>
            <button
              onClick={async () => {
                try {
                  // TODO: Implement orphaned data cleanup
                  await checkStatus() // Refresh stats
                } catch (error) {
                  console.error('Cleanup failed:', error)
                }
              }}
              className="px-4 py-2 text-sm font-medium text-orange-700 bg-orange-100 rounded-lg hover:bg-orange-200"
            >
              Cleanup Orphaned Data
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

// Compact version for dashboard
export function DatabaseStatusBadge() {
  const [isReady, setIsReady] = useState<boolean | null>(null)

  useEffect(() => {
    checkDatabaseStatus().then(status => {
      setIsReady(status.isConnected)
    })
  }, [])

  if (isReady === null) {
    return (
      <div className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 rounded-full">
        <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
        Checking...
      </div>
    )
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${
      isReady 
        ? 'text-green-700 bg-green-100' 
        : 'text-orange-700 bg-orange-100'
    }`}>
      {isReady ? (
        <CheckCircle className="w-3 h-3 mr-1" />
      ) : (
        <AlertTriangle className="w-3 h-3 mr-1" />
      )}
      {isReady ? 'DB Ready' : 'DB Setup Needed'}
    </div>
  )
}
