import React, { useState, useRef, useEffect } from 'react'

interface SwipeAction {
  icon: string
  label: string
  action: () => void
  color: string
  bgColor: string
}

interface SwipeableCardProps {
  children: React.ReactNode
  leftAction?: SwipeAction
  rightAction?: SwipeAction
  onSwipe?: (direction: 'left' | 'right') => void
  disabled?: boolean
  className?: string
}

export default function SwipeableCard({
  children,
  leftAction,
  rightAction,
  onSwipe,
  disabled = false,
  className = ''
}: SwipeableCardProps) {
  const [isDragging, setIsDragging] = useState(false)
  const [dragOffset, setDragOffset] = useState(0)
  const [startX, setStartX] = useState(0)
  const cardRef = useRef<HTMLDivElement>(null)
  const animationRef = useRef<number>()

  const SWIPE_THRESHOLD = 80
  const MAX_DRAG = 120

  // Haptic feedback (if supported)
  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if ('vibrate' in navigator) {
      const patterns = {
        light: [10],
        medium: [20],
        heavy: [30]
      }
      navigator.vibrate(patterns[type])
    }
  }

  const handleStart = (clientX: number) => {
    if (disabled) return
    setIsDragging(true)
    setStartX(clientX)
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current)
    }
  }

  const handleMove = (clientX: number) => {
    if (!isDragging || disabled) return

    const deltaX = clientX - startX
    const clampedDelta = Math.max(-MAX_DRAG, Math.min(MAX_DRAG, deltaX))
    setDragOffset(clampedDelta)

    // Trigger haptic feedback at threshold
    if (Math.abs(clampedDelta) >= SWIPE_THRESHOLD && Math.abs(dragOffset) < SWIPE_THRESHOLD) {
      triggerHaptic('light')
    }
  }

  const handleEnd = () => {
    if (!isDragging || disabled) return

    setIsDragging(false)

    if (Math.abs(dragOffset) >= SWIPE_THRESHOLD) {
      const direction = dragOffset > 0 ? 'right' : 'left'
      const action = direction === 'right' ? rightAction : leftAction

      if (action) {
        triggerHaptic('medium')
        action.action()
        onSwipe?.(direction)
      }
    }

    // Animate back to center
    animateToCenter()
  }

  const animateToCenter = () => {
    const animate = () => {
      setDragOffset(prev => {
        const newOffset = prev * 0.8
        if (Math.abs(newOffset) < 1) {
          return 0
        }
        animationRef.current = requestAnimationFrame(animate)
        return newOffset
      })
    }
    animationRef.current = requestAnimationFrame(animate)
  }

  // Touch events
  const handleTouchStart = (e: React.TouchEvent) => {
    handleStart(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    handleMove(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    handleEnd()
  }

  // Mouse events (for desktop testing)
  const handleMouseDown = (e: React.MouseEvent) => {
    handleStart(e.clientX)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    handleMove(e.clientX)
  }

  const handleMouseUp = () => {
    handleEnd()
  }

  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        handleMove(e.clientX)
      }
    }

    const handleGlobalMouseUp = () => {
      if (isDragging) {
        handleEnd()
      }
    }

    if (isDragging) {
      document.addEventListener('mousemove', handleGlobalMouseMove)
      document.addEventListener('mouseup', handleGlobalMouseUp)
    }

    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove)
      document.removeEventListener('mouseup', handleGlobalMouseUp)
    }
  }, [isDragging, startX])

  const getActionOpacity = (side: 'left' | 'right') => {
    const relevantOffset = side === 'left' ? -dragOffset : dragOffset
    return Math.max(0, Math.min(1, relevantOffset / SWIPE_THRESHOLD))
  }

  const getActionScale = (side: 'left' | 'right') => {
    const opacity = getActionOpacity(side)
    return 0.8 + (opacity * 0.2)
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* Left Action */}
      {leftAction && (
        <div 
          className="absolute left-0 top-0 h-full flex items-center justify-center px-6 z-10"
          style={{
            backgroundColor: leftAction.bgColor,
            opacity: getActionOpacity('left'),
            transform: `scale(${getActionScale('left')})`
          }}
        >
          <div className="flex flex-col items-center space-y-1">
            <span className="text-2xl">{leftAction.icon}</span>
            <span className={`text-xs font-medium ${leftAction.color}`}>
              {leftAction.label}
            </span>
          </div>
        </div>
      )}

      {/* Right Action */}
      {rightAction && (
        <div 
          className="absolute right-0 top-0 h-full flex items-center justify-center px-6 z-10"
          style={{
            backgroundColor: rightAction.bgColor,
            opacity: getActionOpacity('right'),
            transform: `scale(${getActionScale('right')})`
          }}
        >
          <div className="flex flex-col items-center space-y-1">
            <span className="text-2xl">{rightAction.icon}</span>
            <span className={`text-xs font-medium ${rightAction.color}`}>
              {rightAction.label}
            </span>
          </div>
        </div>
      )}

      {/* Main Card Content */}
      <div
        ref={cardRef}
        className={`relative z-20 transition-transform ${isDragging ? '' : 'duration-200'}`}
        style={{
          transform: `translateX(${dragOffset}px)`,
          cursor: isDragging ? 'grabbing' : 'grab'
        }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onMouseDown={handleMouseDown}
        onMouseMove={isDragging ? handleMouseMove : undefined}
        onMouseUp={handleMouseUp}
      >
        {children}
      </div>

      {/* Swipe Hint (show on first few interactions) */}
      {!disabled && (leftAction || rightAction) && (
        <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 z-30">
          <div className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full animate-pulse">
            👈 Swipe for actions 👉
          </div>
        </div>
      )}
    </div>
  )
}

// Touch-optimized button component
interface TouchButtonProps {
  children: React.ReactNode
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'disabled'
  size?: 'small' | 'medium' | 'large'
  className?: string
  disabled?: boolean
}

export function TouchButton({
  children,
  onClick,
  variant = 'primary',
  size = 'medium',
  className = '',
  disabled = false
}: TouchButtonProps) {
  const handleClick = () => {
    if (!disabled && onClick) {
      // Haptic feedback
      if ('vibrate' in navigator) {
        navigator.vibrate([10])
      }
      onClick()
    }
  }

  const baseClasses = 'font-medium rounded-lg transition-all duration-200 active:scale-95 select-none'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800 shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 active:bg-gray-300',
    disabled: 'bg-gray-100 text-gray-400 cursor-not-allowed'
  }

  const sizeClasses = {
    small: 'px-3 py-2 text-sm min-h-[36px]',
    medium: 'px-4 py-3 text-base min-h-[44px]',
    large: 'px-6 py-4 text-lg min-h-[52px]'
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses[disabled ? 'disabled' : variant]} ${sizeClasses[size]} ${className}`}
    >
      {children}
    </button>
  )
}
