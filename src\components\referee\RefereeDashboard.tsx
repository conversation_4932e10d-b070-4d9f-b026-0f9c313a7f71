import { useState, useEffect } from 'react'
import { Shield, Clock, Trophy, Users, CheckCircle, MessageCircle } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'
// TODO: platformService not yet extracted - using old import temporarily
import { platformService } from '../../services/supabase'
import MatchChat from '../chat/MatchChat'

interface Match {
  id: string
  game: string
  mode: string
  status: string
  current_players: number
  max_players: number
  entry_fee: number
  created_at: string
  team1_players: any[]
  team2_players: any[]
}

export default function RefereeDashboard() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [assignedMatches, setAssignedMatches] = useState<Match[]>([])
  const [loading, setLoading] = useState(true)
  const [completingMatch, setCompletingMatch] = useState<string | null>(null)
  const [selectedMatchChat, setSelectedMatchChat] = useState<string | null>(null)

  useEffect(() => {
    // Load matches for all referees (both regular and level referees)
    if (user?.role === 'referee' || user?.referee_status === 'approved' || user?.admin_level >= 1) {
      loadAssignedMatches()
    }
  }, [user])

  const loadAssignedMatches = async () => {
    try {
      // This would need to be implemented in the backend
      // For now, we'll simulate it
      setAssignedMatches([])
    } catch (error) {
      console.error('Error loading assigned matches:', error)
    } finally {
      setLoading(false)
    }
  }

  const completeMatch = async (matchId: string, winnerTeam: number) => {
    if (!user?.id) return

    setCompletingMatch(matchId)
    try {
      const result = await platformService.completeMatch(matchId, winnerTeam, user.id)
      
      if (result.error) {
        addNotification({
          type: 'error',
          title: 'Match Completion Failed',
          message: (result.error as any)?.message || 'Failed to complete match'
        })
      } else {
        addNotification({
          type: 'success',
          title: 'Match Completed',
          message: `Match completed successfully. Winners received ${result.data.winner_share} diamonds each.`
        })
        loadAssignedMatches()
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Error',
        message: error.message || 'Failed to complete match'
      })
    } finally {
      setCompletingMatch(null)
    }
  }

  // Allow access for all referees (regular referees, level referees, and admins)
  if (!user || (user.role !== 'referee' && user.referee_status !== 'approved' && user.admin_level < 1)) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">You need referee privileges to access this dashboard.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <Shield className="w-8 h-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-900">Referee Dashboard</h1>
          </div>
          <p className="text-gray-600">Manage and complete assigned matches</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <Clock className="w-8 h-8 text-blue-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Matches</p>
                <p className="text-2xl font-bold text-gray-900">{assignedMatches.filter(m => m.status === 'ongoing').length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <Users className="w-8 h-8 text-green-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-gray-900">{assignedMatches.filter(m => m.status === 'full').length}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <Trophy className="w-8 h-8 text-amber-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Completed Today</p>
                <p className="text-2xl font-bold text-gray-900">0</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-purple-500" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Assigned</p>
                <p className="text-2xl font-bold text-gray-900">{assignedMatches.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Assigned Matches */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Assigned Matches</h2>
          </div>
          
          <div className="p-6">
            {assignedMatches.length === 0 ? (
              <div className="text-center py-12">
                <Shield className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Matches Assigned</h3>
                <p className="text-gray-600">You'll see matches here when they're assigned to you.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {assignedMatches.map((match) => (
                  <div key={match.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{match.game} - {match.mode}</h3>
                        <p className="text-sm text-gray-600">Entry Fee: {match.entry_fee} diamonds</p>
                      </div>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        match.status === 'ongoing' ? 'bg-blue-100 text-blue-800' :
                        match.status === 'full' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {match.status.charAt(0).toUpperCase() + match.status.slice(1)}
                      </span>
                    </div>

                    {/* Chat and Action Buttons */}
                    <div className="flex space-x-3 mt-4">
                      <button
                        onClick={() => setSelectedMatchChat(selectedMatchChat === match.id ? null : match.id)}
                        className="flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200"
                      >
                        <MessageCircle className="w-4 h-4" />
                        <span>{selectedMatchChat === match.id ? 'Hide Chat' : 'Match Chat'}</span>
                      </button>

                      {match.status === 'ongoing' && (
                        <>
                          <button
                            onClick={() => completeMatch(match.id, 1)}
                            disabled={completingMatch === match.id}
                            className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-green-700 disabled:opacity-50"
                          >
                            {completingMatch === match.id ? 'Completing...' : 'Team 1 Wins'}
                          </button>
                          <button
                            onClick={() => completeMatch(match.id, 2)}
                            disabled={completingMatch === match.id}
                            className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50"
                          >
                            {completingMatch === match.id ? 'Completing...' : 'Team 2 Wins'}
                          </button>
                        </>
                      )}
                    </div>

                    {/* Match Chat */}
                    {selectedMatchChat === match.id && (
                      <div className="mt-4 border-t pt-4">
                        <MatchChat matchId={match.id} isReferee={true} />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
