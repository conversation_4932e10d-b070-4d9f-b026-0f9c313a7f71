import { createClient, SupabaseClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://your-project.supabase.co'
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || 'your-anon-key'

// Singleton pattern to prevent multiple instances
let supabaseInstance: SupabaseClient | null = null

function createSupabaseClient(): SupabaseClient {
  if (supabaseInstance) {
    return supabaseInstance
  }

  supabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      storage: window.localStorage
    }
  })

  return supabaseInstance
}

export const supabase = createSupabaseClient()

// Types - Updated to match your existing database schema
export interface User {
  id: string
  email: string
  username: string
  full_name?: string
  avatar_url?: string
  diamond_balance: number
  total_matches: number
  wins: number
  losses: number
  win_rate: number
  level: number
  experience_points: number
  is_referee: boolean
  referee_level?: number
  created_at: string
  updated_at: string
  last_active?: string
  status: 'active' | 'inactive' | 'banned'
  verification_status: 'unverified' | 'pending' | 'verified'
  phone?: string
  date_of_birth?: string
  location?: string
  bio?: string
  mlbb_id?: string
  valorant_id?: string
  dota_id?: string
  cod_id?: string
  pubg_id?: string
  lol_id?: string
  cs_id?: string
  social_links?: {
    facebook?: string
    instagram?: string
    tiktok?: string
    youtube?: string
  }
  preferences?: {
    notifications: boolean
    privacy_level: 'public' | 'friends' | 'private'
    language: string
    timezone: string
  }
  referee_stats?: {
    matches_refereed: number
    rating: number
    specializations: string[]
  }
}

export interface Match {
  id: string
  title: string
  game: string
  game_mode: string
  max_participants: number
  current_participants: number
  entry_fee: number
  prize_pool: number
  status: 'upcoming' | 'live' | 'completed' | 'cancelled'
  start_time: string
  end_time?: string
  created_by: string
  winner_id?: string
  rules: string
  requirements: string[]
  is_tournament: boolean
  tournament_id?: string
  bracket_position?: number
  round?: number
  referee_id?: string
  stream_url?: string
  replay_url?: string
  created_at: string
  updated_at: string
  participants: MatchParticipant[]
  bets: Bet[]
  chat_messages: ChatMessage[]
}

export interface MatchParticipant {
  id: string
  match_id: string
  user_id: string
  joined_at: string
  status: 'joined' | 'ready' | 'playing' | 'finished' | 'disqualified'
  score?: number
  placement?: number
  earnings?: number
}

export interface Bet {
  id: string
  match_id: string
  user_id: string
  participant_id: string
  amount: number
  potential_winnings: number
  status: 'pending' | 'won' | 'lost' | 'cancelled'
  placed_at: string
  settled_at?: string
}

export interface Tournament {
  id: string
  title: string
  description: string
  game: string
  format: string
  max_participants: number
  current_participants: number
  entry_fee: number
  prize_pool: number
  prize_distribution: number[]
  status: 'upcoming' | 'registration' | 'ongoing' | 'completed' | 'cancelled'
  start_date: string
  end_date?: string
  registration_deadline: string
  created_by: string
  rules: string
  requirements: string[]
  bracket_type: 'single_elimination' | 'double_elimination' | 'round_robin'
  is_featured: boolean
  banner_url?: string
  sponsor?: string
  created_at: string
  updated_at: string
}

export interface Transaction {
  id: string
  user_id: string
  type: 'deposit' | 'withdrawal' | 'bet' | 'winnings' | 'refund' | 'referral_bonus' | 'tournament_entry' | 'tournament_prize'
  amount: number
  balance_after: number
  description: string
  reference_id?: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  payment_method?: string
  payment_reference?: string
  created_at: string
  processed_at?: string
}

export interface ChatMessage {
  id: string
  user_id: string
  username: string
  message: string
  type: 'text' | 'system' | 'emoji' | 'sticker'
  match_id?: string
  tournament_id?: string
  is_global: boolean
  created_at: string
  edited_at?: string
  is_deleted: boolean
}

export interface Notification {
  id: string
  user_id: string
  title: string
  message: string
  type: 'match' | 'tournament' | 'payment' | 'system' | 'social'
  data?: any
  is_read: boolean
  created_at: string
}

export interface ReferralCode {
  id: string
  user_id: string
  code: string
  uses: number
  max_uses?: number
  bonus_amount: number
  is_active: boolean
  expires_at?: string
  created_at: string
}

export interface Group {
  id: string
  name: string
  description: string
  type: 'public' | 'private' | 'invite_only'
  member_count: number
  max_members: number
  created_by: string
  avatar_url?: string
  banner_url?: string
  rules?: string
  tags: string[]
  is_verified: boolean
  created_at: string
  updated_at: string
}

export default supabase
