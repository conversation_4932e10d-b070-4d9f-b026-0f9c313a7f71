import { Link, useNavigate, useLocation } from "react-router-dom"
import { useState, useEffect } from "react"
import { Trophy, Eye, EyeOff, Gamepad2, User, Mail, Target, Lock, Gift, Gem, Rocket, AlertCircle, Loader2, ArrowLeft } from "lucide-react"
import { useAuth } from "../../../contexts/AuthContext"

export default function SignUpPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    username: "",
    password: "",
    confirmPassword: "",
    mlbbId: "",
    agreeToTerms: false
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [showConfirmation, setShowConfirmation] = useState(false)
  const [userEmail, setUserEmail] = useState('')
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  const { signUp, signInWithGoogle, isAuthenticated, isLoading, error, clearError } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  // Check if user came from marketplace
  const marketplaceContext = location.state as { from?: string; listingId?: string; listingTitle?: string } | null
  const fromMarketplace = marketplaceContext?.from === 'marketplace'

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      // If they came from marketplace, redirect to marketplace, otherwise dashboard
      const redirectTo = fromMarketplace ? '/marketplace' : '/dashboard'
      navigate(redirectTo, { replace: true })
    }
  }, [isAuthenticated, navigate, fromMarketplace])

  // Clear error when inputs change
  useEffect(() => {
    if (error) {
      clearError()
    }
    setValidationErrors([])
  }, [formData.email, formData.username, formData.password])

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = (): string[] => {
    const errors: string[] = []

    if (!formData.name.trim()) {
      errors.push("Full name is required")
    }

    if (!formData.email.trim()) {
      errors.push("Email is required")
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push("Please enter a valid email address")
    } else {
      // Check for valid email domains to prevent bounces
      const emailDomain = formData.email.toLowerCase().split('@')[1]
      const validDomains = [
        'gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com',
        'icloud.com', 'protonmail.com', 'aol.com', 'live.com'
      ]

      if (!validDomains.includes(emailDomain)) {
        errors.push('Please use a valid email provider (Gmail, Yahoo, Outlook, etc.)')
      }

      // Prevent obviously fake emails
      if (emailDomain.includes('test') || emailDomain.includes('example') ||
          emailDomain.includes('fake') || emailDomain.includes('temp')) {
        errors.push('Please use a real email address')
      }
    }

    if (!formData.username.trim()) {
      errors.push("Username is required")
    } else if (formData.username.length < 3) {
      errors.push("Username must be at least 3 characters long")
    }

    if (!formData.password) {
      errors.push("Password is required")
    } else if (formData.password.length < 8) {
      errors.push("Password must be at least 8 characters long")
    }

    if (formData.password !== formData.confirmPassword) {
      errors.push("Passwords don't match")
    }

    if (!formData.agreeToTerms) {
      errors.push("Please agree to the terms and conditions")
    }

    return errors
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const errors = validateForm()
    if (errors.length > 0) {
      setValidationErrors(errors)
      return
    }

    setIsSubmitting(true)
    setValidationErrors([])

    try {
      // Split name into first and last name
      const nameParts = formData.name.trim().split(' ')
      const firstName = nameParts[0] || ''
      const lastName = nameParts.slice(1).join(' ') || ''

      const result = await signUp(formData.email, formData.password, {
        username: formData.username,
        firstName,
        lastName,
        mlbbId: formData.mlbbId || undefined
      })

      if (result.success) {
        if (result.needsConfirmation) {
          // Show email confirmation message
          setUserEmail(formData.email)
          setShowConfirmation(true)
        }
        // If no confirmation needed, navigation will be handled by the useEffect above
      }
    } catch (err) {
      console.error('Signup error:', err)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true)
    clearError()

    try {
      const result = await signInWithGoogle()
      if (result.success) {
        // OAuth redirect will handle the rest
        console.log('Google OAuth initiated successfully')
      }
    } catch (err) {
      console.error('Google sign-in error:', err)
    } finally {
      setIsGoogleLoading(false)
    }
  }

  // Email Confirmation View
  if (showConfirmation) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
        {/* Navigation */}
        <nav className="bg-white shadow-sm border-b border-gray-200">
          <div className="container mx-auto px-4 lg:px-6">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center space-x-4">
                <Link to="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                  <ArrowLeft className="w-5 h-5" />
                  <span className="font-medium">Back to Home</span>
                </Link>
                <div className="h-6 w-px bg-gray-300"></div>
                <Link to="/" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <Trophy className="w-5 h-5 text-white" />
                  </div>
                  <span className="text-xl font-bold text-gray-900">Gambets</span>
                </Link>
              </div>
              <div className="flex items-center space-x-4">
                <Link to="/login">
                  <button className="text-gray-700 hover:text-blue-600 font-medium">Login</button>
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Email Confirmation Message */}
        <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full">
            <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-8 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Mail className="w-8 h-8 text-green-600" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Check Your Email</h2>
              <p className="text-gray-600 mb-4">
                We've sent a confirmation link to <strong>{userEmail}</strong>
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Please check your email and click the confirmation link to complete your registration.
              </p>
              <div className="space-y-3">
                <button
                  onClick={() => setShowConfirmation(false)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                >
                  Back to Sign Up
                </button>
                <Link
                  to="/login"
                  className="block w-full text-center text-blue-600 hover:text-blue-700 font-medium py-2 px-4 border border-blue-600 rounded-lg transition-colors"
                >
                  Go to Login
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Regular Signup Form View
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-4">
              <Link to="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                <ArrowLeft className="w-5 h-5" />
                <span className="font-medium">Back to Home</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <Trophy className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-gray-900">Gambets</span>
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <button className="text-gray-700 hover:text-blue-600 font-medium">Login</button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Sign Up Form */}
      <div className="flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Error Alerts */}
          {(error || validationErrors.length > 0) && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start">
                <AlertCircle className="w-5 h-5 text-red-600 mr-2 mt-0.5" />
                <div>
                  {error && <p className="text-red-700 font-medium mb-2">{error}</p>}
                  {validationErrors.length > 0 && (
                    <ul className="text-red-700 text-sm space-y-1">
                      {validationErrors.map((err, index) => (
                        <li key={index}>• {err}</li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          )}

          <div className="text-center">
            <div className="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Gamepad2 className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Join Gambets!</h2>
            <p className="text-gray-600">Create your account and start winning diamonds</p>

            {/* Marketplace Context Message */}
            {fromMarketplace && marketplaceContext?.listingTitle && (
              <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center justify-center space-x-2 text-blue-800">
                  <span>🛒</span>
                  <span className="font-medium">Ready to purchase:</span>
                </div>
                <p className="text-blue-700 font-semibold mt-1">{marketplaceContext.listingTitle}</p>
                <p className="text-blue-600 text-sm mt-1">Sign up to buy with referee protection!</p>
              </div>
            )}
          </div>

          <div className="bg-white rounded-2xl shadow-xl p-8">
            <form className="space-y-6" onSubmit={handleSubmit}>
              <div>
                <label htmlFor="name" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <User className="w-4 h-4 mr-2" />
                  Full Name
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  required
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label htmlFor="email" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Mail className="w-4 h-4 mr-2" />
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Enter your email"
                />
              </div>

              <div>
                <label htmlFor="username" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Target className="w-4 h-4 mr-2" />
                  Username
                </label>
                <input
                  id="username"
                  name="username"
                  type="text"
                  required
                  value={formData.username}
                  onChange={(e) => handleInputChange("username", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Choose a unique username"
                />
              </div>

              <div>
                <label htmlFor="mlbbId" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Gamepad2 className="w-4 h-4 mr-2" />
                  Mobile Legends ID (Optional)
                </label>
                <input
                  id="mlbbId"
                  name="mlbbId"
                  type="text"
                  value={formData.mlbbId}
                  onChange={(e) => handleInputChange("mlbbId", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Your MLBB ID (can add later)"
                />
              </div>

              <div>
                <label htmlFor="password" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Lock className="w-4 h-4 mr-2" />
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    value={formData.password}
                    onChange={(e) => handleInputChange("password", e.target.value)}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors pr-12"
                    placeholder="Create a strong password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="flex items-center text-sm font-medium text-gray-700 mb-2">
                  <Lock className="w-4 h-4 mr-2" />
                  Confirm Password
                </label>
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  required
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                  placeholder="Confirm your password"
                />
              </div>

              <div className="flex items-center">
                <input
                  id="agreeToTerms"
                  name="agreeToTerms"
                  type="checkbox"
                  checked={formData.agreeToTerms}
                  onChange={(e) => handleInputChange("agreeToTerms", e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="agreeToTerms" className="ml-2 block text-sm text-gray-700">
                  I agree to the{" "}
                  <Link to="/terms" className="text-blue-600 hover:text-blue-500">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link to="/privacy" className="text-blue-600 hover:text-blue-500">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              <button
                type="submit"
                disabled={isSubmitting || isLoading}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white font-semibold py-3 px-4 rounded-lg transition-colors shadow-lg hover:shadow-xl disabled:shadow-none flex items-center justify-center"
              >
                {isSubmitting || isLoading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Creating Account...
                  </>
                ) : (
                  <>
                    <Rocket className="w-5 h-5 mr-2" />
                    Create Account & Start Playing
                  </>
                )}
              </button>
            </form>

            <div className="mt-6 text-center">
              <p className="text-gray-600">
                Already have an account?{" "}
                <Link to="/login" className="text-blue-600 hover:text-blue-500 font-semibold">
                  Sign in here
                </Link>
              </p>
            </div>

            {/* Social Login */}
            <div className="mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">Or continue with</span>
                </div>
              </div>

              <div className="mt-6">
                <button
                  onClick={handleGoogleSignIn}
                  disabled={isGoogleLoading || isLoading || isSubmitting}
                  className="w-full inline-flex justify-center items-center py-3 px-4 border border-gray-300 rounded-lg shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isGoogleLoading ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Connecting to Google...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                      </svg>
                      Continue with Google
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Benefits */}
          <div className="bg-blue-50 rounded-lg p-4">
            <h3 className="flex items-center font-semibold text-blue-900 mb-2">
              <Gift className="w-5 h-5 mr-2" />
              Join and Get:
            </h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li className="flex items-center">
                <Gem className="w-4 h-4 mr-2" />
                100 free diamonds to start
              </li>
              <li className="flex items-center">
                <Gamepad2 className="w-4 h-4 mr-2" />
                Access to all supported games
              </li>
              <li className="flex items-center">
                <Trophy className="w-4 h-4 mr-2" />
                Compete in tournaments
              </li>
              <li className="flex items-center">
                <Gem className="w-4 h-4 mr-2" />
                Real money payouts
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
