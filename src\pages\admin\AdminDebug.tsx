import React, { useState, useEffect } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'
import { supabase } from '../../services/supabaseClient'

const AdminDebug: React.FC = () => {
  const { user } = useAuth()
  const [debugInfo, setDebugInfo] = useState<any>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    checkDebugInfo()
  }, [user])

  const checkDebugInfo = async () => {
    try {
      setIsLoading(true)
      const info: any = {
        timestamp: new Date().toISOString(),
        user: null,
        adminCheck: null,
        adminUser: null,
        databaseCheck: null,
        error: null
      }

      // Check current user
      if (user) {
        info.user = {
          id: user.id,
          email: user.email,
          user_metadata: user.user_metadata
        }

        // Check if user is admin
        try {
          info.adminCheck = await AdminService.isAdmin(user.id)
        } catch (error: any) {
          info.adminCheck = { error: error.message }
        }

        // Get admin user details
        try {
          info.adminUser = await AdminService.getAdminUser(user.id)
        } catch (error: any) {
          info.adminUser = { error: error.message }
        }
      } else {
        info.user = 'Not logged in'
      }

      // Check database structure
      try {
        const { error } = await supabase // TODO: Confirm data usage
          .from('users')
          .select('id, email, role, admin_level')
          .limit(1)

        if (error) {
          info.databaseCheck = { error: error.message }
        } else {
          info.databaseCheck = 'Admin columns exist'
        }
      } catch (error: any) {
        info.databaseCheck = { error: error.message }
      }

      setDebugInfo(info)
    } catch (error: any) {
      setDebugInfo({ error: error.message })
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-lg">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-center mt-4">Checking admin status...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white rounded-lg shadow-lg p-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">🔍 Admin Debug Information</h1>
          
          <div className="space-y-6">
            {/* Current User */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-blue-900 mb-2">Current User</h2>
              <pre className="text-sm text-blue-800 overflow-x-auto">
                {JSON.stringify(debugInfo.user, null, 2)}
              </pre>
            </div>

            {/* Admin Check */}
            <div className="bg-green-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-green-900 mb-2">Admin Check Result</h2>
              <pre className="text-sm text-green-800 overflow-x-auto">
                {JSON.stringify(debugInfo.adminCheck, null, 2)}
              </pre>
            </div>

            {/* Admin User Details */}
            <div className="bg-purple-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-purple-900 mb-2">Admin User Details</h2>
              <pre className="text-sm text-purple-800 overflow-x-auto">
                {JSON.stringify(debugInfo.adminUser, null, 2)}
              </pre>
            </div>

            {/* Database Check */}
            <div className="bg-yellow-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-yellow-900 mb-2">Database Structure</h2>
              <pre className="text-sm text-yellow-800 overflow-x-auto">
                {JSON.stringify(debugInfo.databaseCheck, null, 2)}
              </pre>
            </div>

            {/* Actions */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
              <div className="space-y-2">
                <button
                  onClick={checkDebugInfo}
                  className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mr-2"
                >
                  Refresh Debug Info
                </button>
                <a
                  href="/admin-login"
                  className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 inline-block mr-2"
                >
                  Go to Admin Login
                </a>
                <a
                  href="/login"
                  className="bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 inline-block mr-2"
                >
                  Go to User Login
                </a>
                <a
                  href="/"
                  className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 inline-block"
                >
                  Go to Home
                </a>
              </div>
            </div>

            {/* Instructions */}
            <div className="bg-red-50 p-4 rounded-lg">
              <h2 className="text-lg font-semibold text-red-900 mb-2">Setup Instructions</h2>
              <div className="text-sm text-red-800 space-y-2">
                <p><strong>If admin columns don't exist:</strong></p>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Go to Supabase Dashboard → SQL Editor</li>
                  <li>Run the admin schema from database/admin_system.sql</li>
                  <li>Set your user to admin with: UPDATE users SET role = 'super_admin', admin_level = 3 WHERE email = '<EMAIL>';</li>
                </ol>
                
                <p className="mt-4"><strong>If you're not logged in:</strong></p>
                <ol className="list-decimal list-inside space-y-1 ml-4">
                  <li>Go to /login and sign in with your Gambets account</li>
                  <li>Then try accessing the admin dashboard again</li>
                </ol>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminDebug
