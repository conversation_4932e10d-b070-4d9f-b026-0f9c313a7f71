import { useState, useEffect } from 'react'
import { X, Users, Trophy, Clock, DollarSign, User, Shield } from 'lucide-react'
import { useAuth } from '../../../../contexts/AuthContext'
import { useNotifications } from '../../../../contexts/NotificationContext'
import { matchService } from '../../../../services/matchService'
import { bettingService } from '../../../../services/bettingService'
import { TouchButton } from '../../../../components/mobile/TouchOptimized'
import { ProgressBar } from '../../../../components/ui/GamingComponents'
import type { Match } from '../../../../types'

interface MatchDetailModalProps {
  match: Match | null
  isOpen: boolean
  onClose: () => void
  onJoinMatch?: (matchId: string) => void
  onLeaveMatch?: (matchId: string) => void
}

export default function MatchDetailModal({ 
  match, 
  isOpen, 
  onClose, 
  onJoinMatch, 
  onLeaveMatch 
}: MatchDetailModalProps) {
  const { user } = useAuth()
  const { } = useNotifications()
  
  const [isLoading, setIsLoading] = useState(false)
  const [participants, setParticipants] = useState<any[]>([])
  const [userBets, setUserBets] = useState<any[]>([])
  const [isParticipant, setIsParticipant] = useState(false)

  useEffect(() => {
    if (match && isOpen) {
      loadMatchDetails()
    }
  }, [match, isOpen])

  const loadMatchDetails = async () => {
    if (!match || !user) return

    try {
      setIsLoading(true)

      // Load participants
      const { data: participantsData } = await matchService.supabase
        .from('match_participants')
        .select(`
          *,
          user:users(id, username, first_name, last_name, avatar_url)
        `)
        .eq('match_id', match.id)

      if (participantsData) {
        setParticipants(participantsData)
        setIsParticipant(participantsData.some(p => p.user_id === user.id))
      }

      // Load user's bets for this match
      const betsData = await bettingService.getUserBets(user.id, match.id)
      if (betsData) {
        setUserBets(betsData)
      }

    } catch (error) {
      console.error('Error loading match details:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleJoinMatch = async () => {
    if (!match || !user || !onJoinMatch) return

    try {
      setIsLoading(true)
      await onJoinMatch(match.id)
      await loadMatchDetails() // Refresh data
    } catch (error) {
      console.error('Error joining match:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleLeaveMatch = async () => {
    if (!match || !user || !onLeaveMatch) return

    try {
      setIsLoading(true)
      await onLeaveMatch(match.id)
      await loadMatchDetails() // Refresh data
    } catch (error) {
      console.error('Error leaving match:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'text-green-400'
      case 'full': return 'text-yellow-400'
      case 'in_progress': return 'text-blue-400'
      case 'completed': return 'text-gray-400'
      case 'cancelled': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  if (!isOpen || !match) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-xl border border-gray-700 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-white">{match.title}</h2>
            <p className="text-gray-400">{match.game} • {match.mode}</p>
          </div>
          <TouchButton
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white"
          >
            <X className="w-5 h-5" />
          </TouchButton>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Match Info */}
          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <DollarSign className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-400">Entry Fee</span>
              </div>
              <p className="text-lg font-bold text-white">{match.entry_fee} 💎</p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Trophy className="w-4 h-4 text-yellow-400" />
                <span className="text-sm text-gray-400">Prize Pool</span>
              </div>
              <p className="text-lg font-bold text-white">{match.pot_amount} 💎</p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Users className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-gray-400">Players</span>
              </div>
              <p className="text-lg font-bold text-white">
                {match.current_players}/{match.max_players}
              </p>
            </div>
            
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-400">Status</span>
              </div>
              <p className={`text-lg font-bold capitalize ${getStatusColor(match.status)}`}>
                {match.status.replace('_', ' ')}
              </p>
            </div>
          </div>

          {/* Progress Bar */}
          <div>
            <div className="flex justify-between text-sm text-gray-400 mb-2">
              <span>Match Progress</span>
              <span>{match.current_players}/{match.max_players} players</span>
            </div>
            <ProgressBar
              current={match.current_players}
              max={match.max_players}
            />
          </div>

          {/* Host Info */}
          <div className="bg-gray-800 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              <User className="w-4 h-4 text-purple-400" />
              <span className="text-sm text-gray-400">Host</span>
            </div>
            <p className="text-white font-medium">
              {match.host_first_name} {match.host_last_name} (@{match.host_username})
            </p>
          </div>

          {/* Referee Info */}
          {match.referee_username && (
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Shield className="w-4 h-4 text-green-400" />
                <span className="text-sm text-gray-400">Referee</span>
              </div>
              <p className="text-white font-medium">
                {match.referee_first_name} {match.referee_last_name} (@{match.referee_username})
              </p>
            </div>
          )}

          {/* Participants */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-3">Participants</h3>
            <div className="space-y-2">
              {participants.map((participant) => (
                <div key={participant.id} className="flex items-center justify-between bg-gray-800 rounded-lg p-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                      <User className="w-4 h-4 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-white font-medium">
                        {participant.user.first_name} {participant.user.last_name}
                      </p>
                      <p className="text-sm text-gray-400">@{participant.user.username}</p>
                    </div>
                  </div>
                  <div className="text-sm text-gray-400">
                    Team {participant.team_number || 1}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Scheduled Time */}
          {match.scheduled_start_time && (
            <div className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <Clock className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-gray-400">Scheduled Start</span>
              </div>
              <p className="text-white">{formatDateTime(match.scheduled_start_time)}</p>
            </div>
          )}

          {/* User Bets */}
          {userBets.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Your Bets</h3>
              <div className="space-y-2">
                {userBets.map((bet) => (
                  <div key={bet.id} className="bg-gray-800 rounded-lg p-3">
                    <div className="flex justify-between items-center">
                      <span className="text-white">Bet: {bet.bet_amount} 💎</span>
                      <span className={`text-sm px-2 py-1 rounded ${
                        bet.status === 'won' ? 'bg-green-900 text-green-300' :
                        bet.status === 'lost' ? 'bg-red-900 text-red-300' :
                        'bg-yellow-900 text-yellow-300'
                      }`}>
                        {bet.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="p-6 border-t border-gray-700">
          <div className="flex gap-3">
            {!isParticipant && match.status === 'open' && match.current_players < match.max_players && (
              <TouchButton
                onClick={handleJoinMatch}
                disabled={isLoading}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium"
              >
                {isLoading ? 'Joining...' : `Join Match (${match.entry_fee} 💎)`}
              </TouchButton>
            )}
            
            {isParticipant && match.status === 'open' && (
              <TouchButton
                onClick={handleLeaveMatch}
                disabled={isLoading}
                className="flex-1 bg-red-600 hover:bg-red-700 text-white py-3 rounded-lg font-medium"
              >
                {isLoading ? 'Leaving...' : 'Leave Match'}
              </TouchButton>
            )}
            
            <TouchButton
              onClick={onClose}
              className="px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg font-medium"
            >
              Close
            </TouchButton>
          </div>
        </div>
      </div>
    </div>
  )
}
