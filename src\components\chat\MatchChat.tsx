import { useState, useEffect, useRef } from 'react'
import { Send, Smile, Paperclip, MoreVertical, Shield } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

interface ChatMessage {
  id: string
  user_id: string
  username: string
  message: string
  timestamp: string
  type: 'message' | 'system' | 'referee'
}

interface MatchChatProps {
  matchId: string
  isReferee?: boolean
}

export default function MatchChat({ matchId, isReferee = false }: MatchChatProps) {
  const { user } = useAuth()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    loadMessages()
    // Set up real-time subscription for match chat
    setupRealtimeSubscription()
  }, [matchId])

  const setupRealtimeSubscription = () => {
    if (!matchId) return

    // Import realtime service
    import('../../services/realtime').then(({ realtimeService }) => {
      realtimeService.subscribeToMatchChat(matchId, (newMessage) => {
        setMessages(prev => [...prev, {
          id: newMessage.id,
          user_id: newMessage.user_id,
          username: newMessage.username || 'Unknown',
          message: newMessage.message,
          timestamp: newMessage.created_at,
          type: newMessage.is_referee ? 'referee' : 'message'
        }])
      })
    })

    // Cleanup subscription on unmount
    return () => {
      import('../../services/realtime').then(({ realtimeService }) => {
        realtimeService.unsubscribe(`match-chat-${matchId}`)
      })
    }
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    setLoading(true)
    try {
      // Simulate loading messages
      const mockMessages: ChatMessage[] = [
        {
          id: '1',
          user_id: 'system',
          username: 'System',
          message: 'Match chat started. Good luck to all players!',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          type: 'system'
        },
        {
          id: '2',
          user_id: 'user1',
          username: 'Player1',
          message: 'Good luck everyone! 🎮',
          timestamp: new Date(Date.now() - 240000).toISOString(),
          type: 'message'
        },
        {
          id: '3',
          user_id: 'user2',
          username: 'Player2',
          message: 'Let\'s have a fair game!',
          timestamp: new Date(Date.now() - 180000).toISOString(),
          type: 'message'
        }
      ]
      setMessages(mockMessages)
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !user) return

    try {
      // Import supabase
      const { supabase } = await import('../../services/supabase')

      // Send message to database
      const { error } = await supabase // TODO: Confirm data usage
        .from('match_chat')
        .insert({
          match_id: matchId,
          user_id: user.id,
          username: user.username || user.email,
          message: newMessage.trim(),
          is_referee: isReferee,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error sending message:', error)
        return
      }

      // Clear input - message will be added via real-time subscription
      setNewMessage('')

    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: false 
    })
  }

  const getMessageStyle = (message: ChatMessage) => {
    if (message.type === 'system') {
      return 'bg-gray-100 text-gray-700 text-center text-sm py-2 px-4 rounded-lg mx-4'
    }
    if (message.type === 'referee') {
      return 'bg-yellow-50 border-l-4 border-yellow-400'
    }
    if (message.user_id === user?.id) {
      return 'bg-blue-500 text-white ml-auto'
    }
    return 'bg-gray-100 text-gray-900'
  }

  const getUsernameColor = (message: ChatMessage) => {
    if (message.type === 'referee') return 'text-yellow-600'
    if (message.user_id === user?.id) return 'text-blue-200'
    return 'text-gray-600'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-96 bg-white rounded-lg border border-gray-200">
      {/* Chat Header */}
      <div className="px-4 py-3 border-b border-gray-200 bg-gray-50 rounded-t-lg">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-gray-900">Match Chat</h3>
          <div className="flex items-center space-x-2">
            {isReferee && (
              <div className="flex items-center space-x-1 text-xs text-yellow-600">
                <Shield className="w-3 h-3" />
                <span>Referee</span>
              </div>
            )}
            <button className="p-1 text-gray-400 hover:text-gray-600 rounded">
              <MoreVertical className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-3">
        {messages.map((message) => (
          <div key={message.id}>
            {message.type === 'system' ? (
              <div className={getMessageStyle(message)}>
                {message.message}
              </div>
            ) : (
              <div className={`flex ${message.user_id === user?.id ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-xs lg:max-w-md px-3 py-2 rounded-lg ${getMessageStyle(message)}`}>
                  {message.user_id !== user?.id && (
                    <div className="flex items-center space-x-2 mb-1">
                      <p className={`text-xs font-medium ${getUsernameColor(message)}`}>
                        {message.username}
                      </p>
                      {message.type === 'referee' && (
                        <Shield className="w-3 h-3 text-yellow-500" />
                      )}
                    </div>
                  )}
                  <p className="text-sm">{message.message}</p>
                  <p className={`text-xs mt-1 ${
                    message.user_id === user?.id ? 'text-blue-200' : 'text-gray-500'
                  }`}>
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              </div>
            )}
          </div>
        ))}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg">
        <div className="flex items-center space-x-2">
          <div className="flex-1 relative">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              placeholder={isReferee ? "Send message as referee..." : "Type a message..."}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Smile className="w-4 h-4" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Paperclip className="w-4 h-4" />
          </button>
          <button
            onClick={sendMessage}
            disabled={!newMessage.trim()}
            className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Chat Rules */}
      <div className="px-4 py-2 bg-yellow-50 border-t border-yellow-200 rounded-b-lg">
        <p className="text-xs text-yellow-700">
          💡 Keep it respectful! Toxic behavior will result in chat restrictions.
        </p>
      </div>
    </div>
  )
}
