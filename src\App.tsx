import { Routes, Route } from "react-router-dom"
import { AuthProvider, useAuth } from "./contexts/AuthContext"
import { NotificationProvider } from "./contexts/NotificationContext"
import ProtectedRoute from "./components/common/ProtectedRoute"
import ErrorBoundary from "./components/common/ErrorBoundary"
import { lazy, Suspense } from "react"

import { FullPageLoading } from "./components/ui/LoadingSpinner"
// Database test removed for cleaner console output

// 🚀 LAZY LOADED PUBLIC PAGES - Reduce initial bundle size
const LandingPage = lazy(() => import("./pages/public/landing/LandingPage"))
const LeaderboardsPage = lazy(() => import("./pages/public/leaderboards/LeaderboardsPage"))
const LoginPage = lazy(() => import("./pages/public/auth/LoginPage"))
const SignUpPage = lazy(() => import("./pages/public/auth/SignupPage"))
const RulesPage = lazy(() => import("./pages/public/rules/RulesPage"))
const SupportPage = lazy(() => import("./pages/public/support/SupportPage"))
const NotFoundPage = lazy(() => import("./pages/common/NotFoundPage"))

// 🚀 LAZY LOADED PRIVATE PAGES - Authentication required
const DashboardPage = lazy(() => import("./pages/private/dashboard/DashboardPage"))
const MatchesPage = lazy(() => import("./pages/private/matches/MatchesPage"))
const ProfilePage = lazy(() => import("./pages/private/profile/ProfilePage"))
const WalletPage = lazy(() => import("./pages/private/wallet/WalletPage"))
const BuyDiamondsPage = lazy(() => import("./pages/private/wallet/BuyDiamondsPage"))

const TournamentsPage = lazy(() => import("./pages/private/tournaments/TournamentsPage"))
const TournamentDashboardPage = lazy(() => import("./pages/private/tournaments/TournamentDashboardPage"))
const MarketplacePage = lazy(() => import("./pages/private/marketplace/MarketplacePage"))
const ListingDetailsPage = lazy(() => import("./pages/private/marketplace/ListingDetailsPage"))
const PublicMarketplacePage = lazy(() => import("./pages/public/marketplace/PublicMarketplacePage"))
const CommunityPage = lazy(() => import("./pages/private/community/CommunityPage"))
const RefereeApplicationPage = lazy(() => import("./pages/private/referee/RefereeApplicationPage"))
const RefereePage = lazy(() => import("./pages/private/referee/RefereePage"))
const ReferralsPage = lazy(() => import("./pages/private/referrals/ReferralsPage"))
const HistoryPage = lazy(() => import("./pages/private/history/HistoryPage"))
const SettingsPage = lazy(() => import("./pages/private/settings/SettingsPage"))
const NotificationsPage = lazy(() => import("./pages/private/notifications/SimpleNotificationsPage"))

// 🚀 LAZY LOADED ADMIN PAGES - Protected with higher security
import AdminProtectedRoute from "./components/admin/AdminProtectedRoute"
import AdminLayout from "./components/admin/AdminLayout"
const AdminDashboard = lazy(() => import("./pages/admin/AdminDashboard"))
const AdminRefereeManagement = lazy(() => import("./pages/admin/AdminRefereeManagement"))
const AdminUserManagement = lazy(() => import("./pages/admin/AdminUserManagement"))
const AdminInviteTeam = lazy(() => import("./pages/admin/AdminInviteTeam"))
const WorkingAdminLogin = lazy(() => import("./pages/admin/WorkingAdminLogin"))
const DatabaseSetupPage = lazy(() => import("./pages/admin/DatabaseSetupPage"))
const AdminMatchManagement = lazy(() => import("./pages/admin/AdminMatchManagement"))
const AdminPaymentConfirmation = lazy(() => import("./pages/admin/AdminPaymentConfirmation"))
const AdminSystemSettings = lazy(() => import("./pages/admin/AdminSystemSettings"))

// Layout Components
import Layout from "./components/layout/Layout"

// Remove all test components - using real pages now

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <NotificationProvider>
          <AppContent />
        </NotificationProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}

function AppContent() {
  const { isLoading } = useAuth()

  // Check for stored session to skip loading screen
  const hasStoredAuth = () => {
    try {
      return !!localStorage.getItem('sb-bajzrybikkmrcwtadmpv-auth-token')
    } catch {
      return false
    }
  }

  // Only show loading if we don't have stored auth and we're actually loading
  if (isLoading && !hasStoredAuth()) {
    return <FullPageLoading text="Loading Gambets..." />
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Suspense fallback={<FullPageLoading text="Loading page..." />}>
        <Routes>
          {/* 🚀 LAZY LOADED PUBLIC ROUTES */}
          <Route path="/" element={<LandingPage />} />
          <Route path="/leaderboards" element={<LeaderboardsPage />} />
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignUpPage />} />
          <Route path="/rules" element={<RulesPage />} />
          <Route path="/support" element={<SupportPage />} />
          <Route path="/browse" element={<PublicMarketplacePage />} />

          {/* Private Routes - Require Authentication */}
          <Route path="/dashboard" element={
            <ProtectedRoute>
              <Layout>
                <DashboardPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/matches" element={
            <ProtectedRoute>
              <Layout>
                <MatchesPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/tournaments" element={
            <ProtectedRoute>
              <Layout>
                <TournamentsPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/tournament-dashboard" element={
            <ProtectedRoute>
              <Layout>
                <TournamentDashboardPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/marketplace" element={
            <ProtectedRoute>
              <Layout>
                <MarketplacePage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/marketplace/listing/:id" element={
            <ProtectedRoute>
              <Layout>
                <ListingDetailsPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/profile" element={
            <ProtectedRoute>
              <Layout>
                <ProfilePage />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/wallet" element={
            <ProtectedRoute>
              <Layout>
                <WalletPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/buy-diamonds" element={
            <ProtectedRoute>
              <Layout>
                <BuyDiamondsPage />
              </Layout>
            </ProtectedRoute>
          } />

          <Route path="/matchmaking" element={
            <ProtectedRoute>
              <Layout>
                <MatchesPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/referrals" element={
            <ProtectedRoute>
              <Layout>
                <ReferralsPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/community-group" element={
            <ProtectedRoute>
              <Layout>
                <CommunityPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/community" element={
            <ProtectedRoute>
              <Layout>
                <CommunityPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/history" element={
            <ProtectedRoute>
              <Layout>
                <HistoryPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/settings" element={
            <ProtectedRoute>
              <Layout>
                <SettingsPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/notifications" element={
            <ProtectedRoute>
              <Layout>
                <NotificationsPage />
              </Layout>
            </ProtectedRoute>
          } />

          {/* Referee Routes */}
          <Route path="/apply-referee" element={
            <ProtectedRoute>
              <Layout>
                <RefereeApplicationPage />
              </Layout>
            </ProtectedRoute>
          } />
          <Route path="/referee" element={
            <ProtectedRoute requireReferee={true}>
              <Layout>
                <RefereePage />
              </Layout>
            </ProtectedRoute>
          } />

          {/* 👑 ADMIN ROUTES - Secure Access Only Through Login */}
          {/* Admin Login - Only way to access admin dashboard */}
          <Route path="/admin-login" element={<WorkingAdminLogin />} />


          <Route path="/admin-dashboard-2024" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <AdminDashboard />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/referees" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <AdminRefereeManagement />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/users" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <AdminUserManagement />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/invite-team" element={
            <AdminProtectedRoute requiredLevel={3}>
              <AdminLayout>
                <AdminInviteTeam />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/matches" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <AdminMatchManagement />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/payments" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <AdminPaymentConfirmation />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/settings" element={
            <AdminProtectedRoute requiredLevel={3}>
              <AdminLayout>
                <AdminSystemSettings />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/database-setup" element={
            <AdminProtectedRoute requiredLevel={3}>
              <AdminLayout>
                <DatabaseSetupPage />
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/audit" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <div className="p-6">
                  <h1 className="text-2xl font-bold">Audit Logs</h1>
                  <p className="text-gray-600 mt-2">Coming soon...</p>
                </div>
              </AdminLayout>
            </AdminProtectedRoute>
          } />
          <Route path="/admin-dashboard-2024/analytics" element={
            <AdminProtectedRoute requiredLevel={2}>
              <AdminLayout>
                <div className="p-6">
                  <h1 className="text-2xl font-bold">Analytics</h1>
                  <p className="text-gray-600 mt-2">Coming soon...</p>
                </div>
              </AdminLayout>
            </AdminProtectedRoute>
          } />

          {/* Catch-all route for 404 */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </Suspense>
    </div>
  )
}

export default App
