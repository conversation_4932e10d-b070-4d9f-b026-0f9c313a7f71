import React, { useState, useEffect } from 'react'
import {
  X,
  Smartphone,
  CreditCard,
  Building2,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { walletService } from '../../services/walletService'
import { useAuth } from '../../contexts/AuthContext'

interface WithdrawalModalProps {
  isOpen: boolean
  onClose: () => void
  currentBalance: number
  onWithdrawalSuccess: () => void
}

interface WithdrawalMethod {
  id: string
  name: string
  icon: React.ComponentType<any>
  description: string
  processingTime: string
  fee: number
}

export const WithdrawalModal: React.FC<WithdrawalModalProps> = ({
  isOpen,
  onClose,
  currentBalance,
  onWithdrawalSuccess
}) => {
  const { user } = useAuth()
  const [amount, setAmount] = useState('')
  const [selectedMethod, setSelectedMethod] = useState<string>('gcash')
  const [accountDetails, setAccountDetails] = useState({
    accountNumber: '',
    accountName: ''
  })
  const [isProcessing, setIsProcessing] = useState(false)
  const [withdrawalInfo, setWithdrawalInfo] = useState<any>(null)
  const [errors, setErrors] = useState<any>({})
  // const [currency] = useState<'USD' | 'PHP'>('PHP') // TODO: Confirm setCurrency usage

  const withdrawalMethods: WithdrawalMethod[] = [
    {
      id: 'gcash',
      name: 'GCash',
      icon: Smartphone,
      description: 'Most popular e-wallet in Philippines',
      processingTime: '1-3 business days',
      fee: 0 // No additional fee (5% platform fee applies)
    },
    {
      id: 'maya',
      name: 'Maya (PayMaya)',
      icon: CreditCard,
      description: 'Reliable digital wallet',
      processingTime: '1-3 business days',
      fee: 0 // No additional fee (5% platform fee applies)
    },
    {
      id: 'crypto',
      name: 'Cryptocurrency',
      icon: Building2,
      description: 'USDT, USDC, BNB, ETH withdrawals',
      processingTime: '24-48 hours',
      fee: 0 // No additional fee (5% platform fee applies)
    }
  ]

  useEffect(() => {
    if (isOpen) {
      loadWithdrawalInfo()
    }
  }, [isOpen])

  const loadWithdrawalInfo = async () => {
    try {
      const { data, error } = await walletService.getWithdrawalInfo(user?.id || '')
      if (error) {
        console.error('Error loading withdrawal info:', error)
      } else {
        setWithdrawalInfo(data)
      }
    } catch (error) {
      console.error('Error loading withdrawal info:', error)
    }
  }

  const validateForm = () => {
    const newErrors: any = {}
    const withdrawalAmount = parseFloat(amount)

    // Validate amount
    if (!amount || withdrawalAmount <= 0) {
      newErrors.amount = 'Please enter a valid amount'
    } else if (withdrawalAmount < (withdrawalInfo?.minimum_amount || 100)) {
      newErrors.amount = `Minimum withdrawal is ₱${withdrawalInfo?.minimum_amount || 100}`
    } else if (withdrawalAmount > (withdrawalInfo?.maximum_amount || 50000)) {
      newErrors.amount = `Maximum withdrawal is ₱${withdrawalInfo?.maximum_amount || 50000}`
    } else if (withdrawalAmount > currentBalance) {
      newErrors.amount = 'Insufficient diamond balance'
    }

    // Validate account details
    if (!accountDetails.accountNumber) {
      newErrors.accountNumber = 'Account number is required'
    }
    if (!accountDetails.accountName) {
      newErrors.accountName = 'Account name is required'
    }


    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleWithdraw = async () => {
    if (!validateForm()) return

    setIsProcessing(true)
    try {
      const withdrawalAmount = parseFloat(amount)
      // const diamonds = withdrawalAmount // 1:1 ratio // TODO: Confirm usage
      const selectedMethodData = withdrawalMethods.find(m => m.id === selectedMethod)
      const totalAmount = withdrawalAmount - (selectedMethodData?.fee || 0)

      const { error } = await walletService.createWithdrawal(
        user?.id || '',
        totalAmount,
        selectedMethod,
        accountDetails.accountNumber,
        selectedMethod === 'crypto' ? 'USDT' : undefined
      )

      if (error) {
        throw new Error((error as any)?.message || 'Failed to process withdrawal')
      }

      alert(`Withdrawal request submitted successfully!\n\nAmount: ₱${totalAmount}\nMethod: ${selectedMethodData?.name}\nProcessing Time: ${selectedMethodData?.processingTime}\n\nYou will receive a confirmation once processed.`)
      
      onWithdrawalSuccess()
      onClose()
      
      // Reset form
      setAmount('')
      setAccountDetails({
        accountNumber: '',
        accountName: ''
      })
      
    } catch (error: any) {
      alert(`Withdrawal failed: ${error.message}`)
    } finally {
      setIsProcessing(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-PH', {
      style: 'currency',
      currency: 'PHP'
    }).format(amount)
  }

  const getNetAmount = () => {
    const withdrawalAmount = parseFloat(amount) || 0
    const selectedMethodData = withdrawalMethods.find(m => m.id === selectedMethod)
    const fee = selectedMethodData?.fee || 0
    return Math.max(0, withdrawalAmount - fee)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-gray-900">Withdraw Diamonds</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Current Balance */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <span className="text-blue-700 font-medium">Available Balance</span>
              <span className="text-xl font-bold text-blue-900">
                {currentBalance.toLocaleString()} 💎
              </span>
            </div>
            <p className="text-sm text-blue-600 mt-1">
              = {formatCurrency(currentBalance)} (1 Diamond = ₱1)
            </p>
          </div>

          {/* Amount Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Withdrawal Amount (₱)
            </label>
            <input
              type="number"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="Enter amount"
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.amount ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.amount && (
              <p className="text-red-500 text-sm mt-1">{errors.amount}</p>
            )}
            {withdrawalInfo && (
              <p className="text-gray-500 text-sm mt-1">
                Min: ₱{withdrawalInfo.minimum_amount} • Max: ₱{withdrawalInfo.maximum_amount}
              </p>
            )}
          </div>

          {/* Withdrawal Method */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Withdrawal Method
            </label>
            <div className="space-y-2">
              {withdrawalMethods.map((method) => (
                <div
                  key={method.id}
                  onClick={() => setSelectedMethod(method.id)}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedMethod === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <method.icon className="w-5 h-5 text-gray-600" />
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{method.name}</div>
                      <div className="text-sm text-gray-600">{method.description}</div>
                      <div className="text-xs text-gray-500">
                        {method.processingTime} • Fee: {method.fee === 0 ? 'Free' : `₱${method.fee}`}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Account Details */}
          <div className="space-y-4">
            <h3 className="font-medium text-gray-900">Account Details</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {selectedMethod === 'crypto' ? 'Wallet Address' : 'Mobile Number'}
              </label>
              <input
                type="text"
                value={accountDetails.accountNumber}
                onChange={(e) => setAccountDetails(prev => ({ ...prev, accountNumber: e.target.value }))}
                placeholder={selectedMethod === 'crypto' ? 'Enter wallet address' : 'Enter mobile number'}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.accountNumber ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.accountNumber && (
                <p className="text-red-500 text-sm mt-1">{errors.accountNumber}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Account Name
              </label>
              <input
                type="text"
                value={accountDetails.accountName}
                onChange={(e) => setAccountDetails(prev => ({ ...prev, accountName: e.target.value }))}
                placeholder="Enter account holder name"
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.accountName ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {errors.accountName && (
                <p className="text-red-500 text-sm mt-1">{errors.accountName}</p>
              )}
            </div>

            {selectedMethod === 'crypto' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Cryptocurrency
                </label>
                <select
                  value={accountDetails.accountName}
                  onChange={(e) => setAccountDetails(prev => ({ ...prev, accountName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select cryptocurrency</option>
                  <option value="USDT">USDT (TRC20)</option>
                  <option value="USDC">USDC (Polygon)</option>
                  <option value="BNB">BNB (BSC)</option>
                  <option value="ETH">ETH (Ethereum)</option>
                </select>
              </div>
            )}
          </div>

          {/* Summary */}
          {amount && parseFloat(amount) > 0 && (
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Withdrawal Summary</h4>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Withdrawal Amount:</span>
                  <span>{formatCurrency(parseFloat(amount))}</span>
                </div>
                {withdrawalMethods.find(m => m.id === selectedMethod)?.fee! > 0 && (
                  <div className="flex justify-between">
                    <span>Processing Fee:</span>
                    <span>-{formatCurrency(withdrawalMethods.find(m => m.id === selectedMethod)?.fee || 0)}</span>
                  </div>
                )}
                <div className="flex justify-between font-medium border-t pt-1">
                  <span>You'll Receive:</span>
                  <span>{formatCurrency(getNetAmount())}</span>
                </div>
              </div>
            </div>
          )}

          {/* Warning */}
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium">Important Notes:</p>
                <ul className="mt-1 space-y-1 list-disc list-inside">
                  <li>Withdrawals cannot be cancelled once submitted</li>
                  <li>Processing times may vary during peak hours</li>
                  <li>Ensure account details are correct to avoid delays</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <button
            onClick={handleWithdraw}
            disabled={isProcessing || !amount || parseFloat(amount) <= 0}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <span>Submit Withdrawal Request</span>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}
