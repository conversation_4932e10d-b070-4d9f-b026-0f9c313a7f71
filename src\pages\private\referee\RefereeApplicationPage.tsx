import type React from "react"
import { useState } from "react"
import { useAuth } from "../../../contexts/AuthContext"
// TODO: refereeService not yet extracted - using old import temporarily
import { refereeService } from "../../../services/supabase"

import { Button } from "../../../components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "../../../components/ui/card"
import { Input } from "../../../components/ui/input"
import { Label } from "../../../components/ui/label"
import {
  Trophy,
  User,
  Gamepad2,
  Facebook,
  CheckCircle,
  Shield,
  Users,
  Target,
  Upload,
  Camera,
  CreditCard,
  MapPin,
  Loader2,
  AlertCircle,
  X,
} from "lucide-react"

interface FormData {
  fullName: string
  gambetsUsername: string
  mobileLegends: string
  facebookProfile: string
  motivation: string
  agreement: boolean
  governmentId: File | null
  selfieWithId: File | null
  payoutMethod: "gcash" | "maya" | "bank"
  gcashMayaNumber: string
  bankName: string
  accountNumber: string
  streetAddress: string
  city: string
  province: string
  zipCode: string
}

export default function RefereeApplicationPage() {
  const { user } = useAuth()

  // Hide referee application for users who already have referee access
  const hasRefereeAccess = user?.admin_level >= 1 ||
                          user?.referee_status === 'approved' ||
                          user?.role === 'referee' ||
                          user?.role === 'admin' ||
                          user?.role === 'super_admin'

  if (hasRefereeAccess) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-6">
          <Shield className="w-16 h-16 text-green-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">You're Already a Referee!</h2>
          <p className="text-gray-600 mb-4">
            {user?.admin_level >= 1
              ? "You have Level Referee access and can manage referee duties through the admin dashboard."
              : "You already have referee privileges and can access the referee dashboard."
            }
          </p>
          <div className="space-y-2">
            <button
              onClick={() => window.location.href = user?.admin_level >= 1 ? '/admin' : '/referee-dashboard'}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              {user?.admin_level >= 1 ? 'Go to Admin Dashboard' : 'Go to Referee Dashboard'}
            </button>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg transition-colors"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    )
  }

  const [formData, setFormData] = useState<FormData>({
    fullName: "",
    gambetsUsername: "",
    mobileLegends: "",
    facebookProfile: "",
    motivation: "",
    agreement: false,
    governmentId: null,
    selfieWithId: null,
    payoutMethod: "gcash",
    gcashMayaNumber: "",
    bankName: "",
    accountNumber: "",
    streetAddress: "",
    city: "",
    province: "",
    zipCode: "",
  })

  const [showSuccess, setShowSuccess] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: keyof FormData, value: string | boolean | File | null) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))

    // Clear error when user starts typing/changing
    if (errors[field]) {
      setErrors((prev) => {
        const newErrors = { ...prev }
        delete newErrors[field]
        return newErrors
      })
    }
  }

  const handleFileUpload = (field: "governmentId" | "selfieWithId", file: File | null) => {
    if (file) {
      // Validate file type
      if (!file.type.startsWith("image/")) {
        setErrors((prev) => ({
          ...prev,
          [field]: "Please select a valid image file (JPG, PNG, etc.)",
        }))
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setErrors((prev) => ({
          ...prev,
          [field]: "Image size should be less than 5MB",
        }))
        return
      }
    }

    handleInputChange(field, file)
  }

  const removeFile = (field: "governmentId" | "selfieWithId") => {
    handleInputChange(field, null)
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Required fields validation
    if (!formData.fullName.trim()) {
      newErrors.fullName = "Full name is required"
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = "Full name must be at least 2 characters"
    }

    if (!formData.gambetsUsername.trim()) {
      newErrors.gambetsUsername = "Gambets username is required"
    } else if (formData.gambetsUsername.trim().length < 3) {
      newErrors.gambetsUsername = "Username must be at least 3 characters"
    }

    if (!formData.mobileLegends.trim()) {
      newErrors.mobileLegends = "Mobile Legends IGN is required"
    }

    if (!formData.motivation.trim()) {
      newErrors.motivation = "Please explain why you want to be a referee"
    } else if (formData.motivation.trim().length < 50) {
      newErrors.motivation = "Please provide at least 50 characters"
    } else if (formData.motivation.trim().length > 500) {
      newErrors.motivation = "Please keep your response under 500 characters"
    }

    if (!formData.governmentId) {
      newErrors.governmentId = "Government ID is required for verification"
    }

    // Payout method validation
    if (formData.payoutMethod === "gcash" || formData.payoutMethod === "maya") {
      if (!formData.gcashMayaNumber.trim()) {
        newErrors.gcashMayaNumber = `${formData.payoutMethod === "gcash" ? "GCash" : "Maya"} number is required`
      } else if (!/^09\d{9}$/.test(formData.gcashMayaNumber.replace(/\s/g, ""))) {
        newErrors.gcashMayaNumber = "Please enter a valid Philippine mobile number (09XXXXXXXXX)"
      }
    } else if (formData.payoutMethod === "bank") {
      if (!formData.bankName.trim()) {
        newErrors.bankName = "Bank name is required"
      }
      if (!formData.accountNumber.trim()) {
        newErrors.accountNumber = "Account number is required"
      } else if (formData.accountNumber.trim().length < 8) {
        newErrors.accountNumber = "Please enter a valid account number"
      }
    }

    // Address validation
    if (!formData.streetAddress.trim()) {
      newErrors.streetAddress = "Street address is required"
    }
    if (!formData.city.trim()) {
      newErrors.city = "City is required"
    }
    if (!formData.province.trim()) {
      newErrors.province = "Province is required"
    }
    if (!formData.zipCode.trim()) {
      newErrors.zipCode = "ZIP code is required"
    } else if (!/^\d{4}$/.test(formData.zipCode.trim())) {
      newErrors.zipCode = "Please enter a valid 4-digit ZIP code"
    }

    // Facebook URL validation (if provided)
    if (formData.facebookProfile.trim() && !formData.facebookProfile.match(/^https?:\/\/(www\.)?facebook\.com\/.+/)) {
      newErrors.facebookProfile = "Please enter a valid Facebook profile URL"
    }

    if (!formData.agreement) {
      newErrors.agreement = "You must agree to the referee responsibilities"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      // Scroll to first error
      const firstErrorField = Object.keys(errors)[0]
      const element = document.getElementById(firstErrorField)
      if (element) {
        element.scrollIntoView({ behavior: "smooth", block: "center" })
        element.focus()
      }
      return
    }

    setIsSubmitting(true)

    try {
      if (!user?.id) {
        throw new Error('User not authenticated')
      }

      // Submit referee application to Supabase
      const { error } = await refereeService.submitApplication(user.id, { // TODO: Confirm data usage
        full_name: formData.fullName,
        mlbb_id: formData.mobileLegends,
        facebook_profile: formData.facebookProfile,
        motivation: formData.motivation,
        payout_method: formData.payoutMethod,
        gcash_maya_number: formData.gcashMayaNumber,
        bank_name: formData.bankName,
        account_number: formData.accountNumber,
        street_address: formData.streetAddress,
        city: formData.city,
        province: formData.province,
        zip_code: formData.zipCode
      })

      if (error) {
        throw new Error((error as any)?.message || 'Failed to submit application')
      }

      // Show success message
      setShowSuccess(true)

      // Reset form after successful submission
      setFormData({
        fullName: "",
        gambetsUsername: "",
        mobileLegends: "",
        facebookProfile: "",
        motivation: "",
        agreement: false,
        governmentId: null,
        selfieWithId: null,
        payoutMethod: "gcash",
        gcashMayaNumber: "",
        bankName: "",
        accountNumber: "",
        streetAddress: "",
        city: "",
        province: "",
        zipCode: "",
      })

      // Scroll to top to show success message
      window.scrollTo({ top: 0, behavior: "smooth" })

      // Hide success message after 8 seconds
      setTimeout(() => setShowSuccess(false), 8000)
    } catch (error: any) {
      console.error("Submission error:", error)
      setErrors({ submit: error.message || "Something went wrong. Please try again." })
    } finally {
      setIsSubmitting(false)
    }
  }

  const isFormValid = (): boolean => {
    const requiredFields = [
      formData.fullName.trim(),
      formData.gambetsUsername.trim(),
      formData.mobileLegends.trim(),
      formData.motivation.trim().length >= 50,
      formData.governmentId,
      formData.streetAddress.trim(),
      formData.city.trim(),
      formData.province.trim(),
      formData.zipCode.trim(),
      formData.agreement,
    ]

    const payoutValid =
      formData.payoutMethod === "gcash" || formData.payoutMethod === "maya"
        ? formData.gcashMayaNumber.trim()
        : formData.bankName.trim() && formData.accountNumber.trim()

    return requiredFields.every(Boolean) && !!payoutValid
  }

  const formatPhoneNumber = (value: string): string => {
    const cleaned = value.replace(/\D/g, "")
    if (cleaned.length <= 11) {
      return cleaned.replace(/(\d{2})(\d{3})(\d{3})(\d{4})/, "$1 $2 $3 $4").trim()
    }
    return value
  }

  return (
    <div className="p-6">
      <div className="max-w-4xl mx-auto">
          {/* Success Alert */}
          {showSuccess && (
            <div className="mb-6 p-4 sm:p-6 bg-green-50 border border-green-200 rounded-xl shadow-lg animate-in slide-in-from-top duration-300">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-6 h-6 text-green-600 flex-shrink-0 mt-0.5" />
                <div className="flex-1">
                  <h3 className="text-green-800 font-semibold text-lg">Application Submitted Successfully! 🎉</h3>
                  <p className="text-green-700 text-sm mt-1">
                    Thank you for applying to become a referee. We'll review your application and get back to you within
                    3-5 business days.
                  </p>
                  <p className="text-green-600 text-xs mt-2">
                    You'll receive an email confirmation shortly with your application reference number.
                  </p>
                </div>
                <button
                  onClick={() => setShowSuccess(false)}
                  className="text-green-600 hover:text-green-800 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
          )}

          {/* Error Alert */}
          {errors.submit && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl shadow-lg">
              <div className="flex items-center gap-3">
                <AlertCircle className="w-5 h-5 text-red-600 flex-shrink-0" />
                <p className="text-red-700 font-medium">{errors.submit}</p>
              </div>
            </div>
          )}

          <Card className="shadow-xl border-0 rounded-2xl overflow-hidden">
            <CardHeader className="text-center pb-6 px-4 sm:px-6 md:px-8 bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              {/* Logo */}
              <div className="flex items-center justify-center space-x-2 mb-4">
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center">
                  <Trophy className="w-6 h-6 sm:w-7 sm:h-7 text-white" />
                </div>
                <span className="text-2xl sm:text-3xl font-bold">Gambets</span>
              </div>

              <CardTitle className="text-xl sm:text-2xl md:text-3xl font-bold mb-2">Become a Match Referee</CardTitle>
              <p className="text-blue-100 text-sm sm:text-base max-w-2xl mx-auto">
                Help ensure fair play by becoming a trusted referee for our gaming community
              </p>
            </CardHeader>

            <CardContent className="pt-6 px-4 sm:px-6 md:px-8 pb-8">
              {/* Benefits Section */}
              <div className="mb-8 p-4 sm:p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl shadow-sm">
                <h3 className="font-semibold text-blue-900 mb-4 flex items-center gap-2 text-lg">
                  <Shield className="w-5 h-5" />
                  Referee Benefits
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm">
                  {[
                    "Earn diamonds for each match refereed",
                    "Special referee badge and recognition",
                    "Priority support and exclusive features",
                    "Help build a fair gaming community",
                  ].map((benefit, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-blue-500 flex-shrink-0" />
                      <span className="text-blue-800">{benefit}</span>
                    </div>
                  ))}
                </div>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6 sm:space-y-8">
                {/* Personal Information */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4 pb-2 border-b border-gray-200">
                    <User className="w-5 h-5 text-blue-500" />
                    Personal Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="fullName" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        Full Name <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="fullName"
                        type="text"
                        placeholder="Enter your full name"
                        value={formData.fullName}
                        onChange={(e) => handleInputChange("fullName", e.target.value)}
                        className={`h-11 rounded-lg transition-all duration-200 ${
                          errors.fullName
                            ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                            : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                        }`}
                        disabled={isSubmitting}
                      />
                      {errors.fullName && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.fullName}
                        </p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label
                        htmlFor="gambetsUsername"
                        className="text-sm font-medium text-gray-700 flex items-center gap-1"
                      >
                        Gambets Username <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="gambetsUsername"
                        type="text"
                        placeholder="Your current Gambets username"
                        value={formData.gambetsUsername}
                        onChange={(e) => handleInputChange("gambetsUsername", e.target.value)}
                        className={`h-11 rounded-lg transition-all duration-200 ${
                          errors.gambetsUsername
                            ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                            : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                        }`}
                        disabled={isSubmitting}
                      />
                      {errors.gambetsUsername && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.gambetsUsername}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Gaming Information */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4 pb-2 border-b border-gray-200">
                    <Gamepad2 className="w-5 h-5 text-blue-500" />
                    Gaming Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-2">
                      <Label
                        htmlFor="mobileLegends"
                        className="text-sm font-medium text-gray-700 flex items-center gap-1"
                      >
                        Mobile Legends IGN <span className="text-red-500">*</span>
                      </Label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Target className="h-4 w-4 text-gray-400" />
                        </div>
                        <Input
                          id="mobileLegends"
                          type="text"
                          placeholder="Your Mobile Legends in-game name"
                          value={formData.mobileLegends}
                          onChange={(e) => handleInputChange("mobileLegends", e.target.value)}
                          className={`pl-10 h-11 rounded-lg transition-all duration-200 ${
                            errors.mobileLegends
                              ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                              : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                          }`}
                          disabled={isSubmitting}
                        />
                      </div>
                      {errors.mobileLegends && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.mobileLegends}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Required for verifying match results in Mobile Legends games
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="facebookProfile" className="text-sm font-medium text-gray-700">
                        Facebook Profile Link <span className="text-gray-400">(Optional)</span>
                      </Label>
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <Facebook className="h-4 w-4 text-gray-400" />
                        </div>
                        <Input
                          id="facebookProfile"
                          type="url"
                          placeholder="https://facebook.com/yourprofile"
                          value={formData.facebookProfile}
                          onChange={(e) => handleInputChange("facebookProfile", e.target.value)}
                          className={`pl-10 h-11 rounded-lg transition-all duration-200 ${
                            errors.facebookProfile
                              ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                              : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                          }`}
                          disabled={isSubmitting}
                        />
                      </div>
                      {errors.facebookProfile && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.facebookProfile}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Helps us verify your identity and build trust with players
                      </p>
                    </div>
                  </div>
                </div>

                {/* Identity Verification */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4 pb-2 border-b border-gray-200">
                    <Shield className="w-5 h-5 text-blue-500" />
                    Identity Verification
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        Government ID <span className="text-red-500">*</span>
                      </Label>
                      <div
                        className={`border-2 border-dashed rounded-lg p-4 sm:p-6 text-center transition-all duration-200 ${
                          errors.governmentId
                            ? "border-red-300 bg-red-50"
                            : formData.governmentId
                              ? "border-green-300 bg-green-50"
                              : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
                        }`}
                      >
                        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 mb-2">Upload your Government ID</p>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileUpload("governmentId", e.target.files?.[0] || null)}
                          className="hidden"
                          id="governmentId"
                          disabled={isSubmitting}
                        />
                        <label
                          htmlFor="governmentId"
                          className="inline-block px-4 py-2 text-blue-500 hover:text-blue-600 cursor-pointer text-sm font-medium border border-blue-500 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          Choose File
                        </label>
                        {formData.governmentId && (
                          <div className="mt-3 p-2 bg-green-100 rounded-lg">
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-green-700 font-medium">✓ {formData.governmentId.name}</p>
                              <button
                                type="button"
                                onClick={() => removeFile("governmentId")}
                                className="text-red-500 hover:text-red-700 transition-colors"
                                disabled={isSubmitting}
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                      {errors.governmentId && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.governmentId}
                        </p>
                      )}
                      <p className="text-xs text-gray-500">
                        Accepted: Driver's License, Passport, National ID, or other valid government-issued ID
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium text-gray-700">
                        Selfie with ID <span className="text-gray-400">(Optional but recommended)</span>
                      </Label>
                      <div
                        className={`border-2 border-dashed rounded-lg p-4 sm:p-6 text-center transition-all duration-200 ${
                          formData.selfieWithId
                            ? "border-green-300 bg-green-50"
                            : "border-gray-300 hover:border-blue-400 hover:bg-blue-50"
                        }`}
                      >
                        <Camera className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600 mb-2">Upload selfie holding your ID</p>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileUpload("selfieWithId", e.target.files?.[0] || null)}
                          className="hidden"
                          id="selfieWithId"
                          disabled={isSubmitting}
                        />
                        <label
                          htmlFor="selfieWithId"
                          className="inline-block px-4 py-2 text-blue-500 hover:text-blue-600 cursor-pointer text-sm font-medium border border-blue-500 rounded-lg hover:bg-blue-50 transition-colors"
                        >
                          Choose File
                        </label>
                        {formData.selfieWithId && (
                          <div className="mt-3 p-2 bg-green-100 rounded-lg">
                            <div className="flex items-center justify-between">
                              <p className="text-sm text-green-700 font-medium">✓ {formData.selfieWithId.name}</p>
                              <button
                                type="button"
                                onClick={() => removeFile("selfieWithId")}
                                className="text-red-500 hover:text-red-700 transition-colors"
                                disabled={isSubmitting}
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                      <p className="text-xs text-gray-500">
                        Helps speed up verification process and builds additional trust
                      </p>
                    </div>
                  </div>
                </div>

                {/* Payout Information */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4 pb-2 border-b border-gray-200">
                    <CreditCard className="w-5 h-5 text-blue-500" />
                    Payout Information
                  </h3>

                  <div className="space-y-4 sm:space-y-6">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium text-gray-700 flex items-center gap-1">
                        Preferred Payout Method <span className="text-red-500">*</span>
                      </Label>
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                        {[
                          { value: "gcash", label: "GCash", popular: true },
                          { value: "maya", label: "Maya", popular: true },
                          { value: "bank", label: "Bank Transfer", popular: false },
                        ].map((method) => (
                          <button
                            key={method.value}
                            type="button"
                            onClick={() => handleInputChange("payoutMethod", method.value)}
                            disabled={isSubmitting}
                            className={`relative p-3 sm:p-4 rounded-lg border-2 font-medium transition-all duration-200 ${
                              formData.payoutMethod === method.value
                                ? "border-blue-500 bg-blue-50 text-blue-700 shadow-md"
                                : "border-gray-300 bg-white text-gray-700 hover:border-gray-400 hover:shadow-sm"
                            } disabled:opacity-50 disabled:cursor-not-allowed`}
                          >
                            {method.popular && (
                              <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-2 py-1 rounded-full">
                                Popular
                              </span>
                            )}
                            {method.label}
                          </button>
                        ))}
                      </div>
                    </div>

                    {(formData.payoutMethod === "gcash" || formData.payoutMethod === "maya") && (
                      <div className="space-y-2">
                        <Label
                          htmlFor="gcashMayaNumber"
                          className="text-sm font-medium text-gray-700 flex items-center gap-1"
                        >
                          {formData.payoutMethod === "gcash" ? "GCash" : "Maya"} Number{" "}
                          <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="gcashMayaNumber"
                          type="tel"
                          placeholder="09XX XXX XXXX"
                          value={formatPhoneNumber(formData.gcashMayaNumber)}
                          onChange={(e) => handleInputChange("gcashMayaNumber", e.target.value.replace(/\s/g, ""))}
                          className={`h-11 rounded-lg transition-all duration-200 ${
                            errors.gcashMayaNumber
                              ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                              : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                          }`}
                          disabled={isSubmitting}
                          maxLength={13}
                        />
                        {errors.gcashMayaNumber && (
                          <p className="text-red-500 text-sm flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {errors.gcashMayaNumber}
                          </p>
                        )}
                        <p className="text-xs text-gray-500">
                          Make sure this number is registered to your{" "}
                          {formData.payoutMethod === "gcash" ? "GCash" : "Maya"} account
                        </p>
                      </div>
                    )}

                    {formData.payoutMethod === "bank" && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                        <div className="space-y-2">
                          <Label
                            htmlFor="bankName"
                            className="text-sm font-medium text-gray-700 flex items-center gap-1"
                          >
                            Bank Name <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="bankName"
                            type="text"
                            placeholder="e.g., BPI, BDO, Metrobank"
                            value={formData.bankName}
                            onChange={(e) => handleInputChange("bankName", e.target.value)}
                            className={`h-11 rounded-lg transition-all duration-200 ${
                              errors.bankName
                                ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                                : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                            }`}
                            disabled={isSubmitting}
                          />
                          {errors.bankName && (
                            <p className="text-red-500 text-sm flex items-center gap-1">
                              <AlertCircle className="w-3 h-3" />
                              {errors.bankName}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label
                            htmlFor="accountNumber"
                            className="text-sm font-medium text-gray-700 flex items-center gap-1"
                          >
                            Account Number <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            id="accountNumber"
                            type="text"
                            placeholder="Your bank account number"
                            value={formData.accountNumber}
                            onChange={(e) => handleInputChange("accountNumber", e.target.value)}
                            className={`h-11 rounded-lg transition-all duration-200 ${
                              errors.accountNumber
                                ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                                : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                            }`}
                            disabled={isSubmitting}
                          />
                          {errors.accountNumber && (
                            <p className="text-red-500 text-sm flex items-center gap-1">
                              <AlertCircle className="w-3 h-3" />
                              {errors.accountNumber}
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Billing Address */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4 pb-2 border-b border-gray-200">
                    <MapPin className="w-5 h-5 text-blue-500" />
                    Billing Address
                  </h3>

                  <div className="space-y-4 sm:space-y-6">
                    <div className="space-y-2">
                      <Label
                        htmlFor="streetAddress"
                        className="text-sm font-medium text-gray-700 flex items-center gap-1"
                      >
                        Street Address <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="streetAddress"
                        type="text"
                        placeholder="House/Unit Number, Street Name, Barangay"
                        value={formData.streetAddress}
                        onChange={(e) => handleInputChange("streetAddress", e.target.value)}
                        className={`h-11 rounded-lg transition-all duration-200 ${
                          errors.streetAddress
                            ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                            : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                        }`}
                        disabled={isSubmitting}
                      />
                      {errors.streetAddress && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.streetAddress}
                        </p>
                      )}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="city" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          City <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="city"
                          type="text"
                          placeholder="City/Municipality"
                          value={formData.city}
                          onChange={(e) => handleInputChange("city", e.target.value)}
                          className={`h-11 rounded-lg transition-all duration-200 ${
                            errors.city
                              ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                              : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                          }`}
                          disabled={isSubmitting}
                        />
                        {errors.city && (
                          <p className="text-red-500 text-sm flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {errors.city}
                          </p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="province" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          Province <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="province"
                          type="text"
                          placeholder="Province/Region"
                          value={formData.province}
                          onChange={(e) => handleInputChange("province", e.target.value)}
                          className={`h-11 rounded-lg transition-all duration-200 ${
                            errors.province
                              ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                              : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                          }`}
                          disabled={isSubmitting}
                        />
                        {errors.province && (
                          <p className="text-red-500 text-sm flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {errors.province}
                          </p>
                        )}
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="zipCode" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                          ZIP Code <span className="text-red-500">*</span>
                        </Label>
                        <Input
                          id="zipCode"
                          type="text"
                          placeholder="1234"
                          value={formData.zipCode}
                          onChange={(e) => handleInputChange("zipCode", e.target.value.replace(/\D/g, "").slice(0, 4))}
                          className={`h-11 rounded-lg transition-all duration-200 ${
                            errors.zipCode
                              ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                              : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                          }`}
                          disabled={isSubmitting}
                          maxLength={4}
                        />
                        {errors.zipCode && (
                          <p className="text-red-500 text-sm flex items-center gap-1">
                            <AlertCircle className="w-3 h-3" />
                            {errors.zipCode}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Application Questions */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <h3 className="text-lg sm:text-xl font-semibold text-gray-900 flex items-center gap-2 mb-4 pb-2 border-b border-gray-200">
                    <Users className="w-5 h-5 text-blue-500" />
                    Application Questions
                  </h3>

                  <div className="space-y-2">
                    <Label htmlFor="motivation" className="text-sm font-medium text-gray-700 flex items-center gap-1">
                      Why do you want to be a referee? <span className="text-red-500">*</span>
                    </Label>
                    <textarea
                      id="motivation"
                      value={formData.motivation}
                      onChange={(e) => handleInputChange("motivation", e.target.value)}
                      className={`w-full p-4 border rounded-lg focus:ring-2 focus:ring-offset-2 resize-none transition-all duration-200 ${
                        errors.motivation
                          ? "border-red-500 focus:border-red-500 focus:ring-red-500 bg-red-50"
                          : "border-gray-300 focus:border-blue-500 focus:ring-blue-500 hover:border-gray-400"
                      }`}
                      rows={5}
                      placeholder="Tell us about your gaming experience, why you want to help ensure fair play, and what makes you a good candidate for being a referee..."
                      disabled={isSubmitting}
                      maxLength={500}
                    />
                    <div className="flex justify-between items-center">
                      {errors.motivation && (
                        <p className="text-red-500 text-sm flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.motivation}
                        </p>
                      )}
                      <p
                        className={`text-xs ml-auto ${
                          formData.motivation.length < 50
                            ? "text-red-500"
                            : formData.motivation.length > 450
                              ? "text-orange-500"
                              : "text-gray-500"
                        }`}
                      >
                        {formData.motivation.length}/500 characters (minimum 50)
                      </p>
                    </div>
                  </div>
                </div>

                {/* Agreement */}
                <div className="bg-white p-4 sm:p-6 rounded-2xl shadow-md border border-gray-100 space-y-4">
                  <div className="flex items-start space-x-3">
                    <input
                      type="checkbox"
                      id="agreement"
                      checked={formData.agreement}
                      onChange={(e) => handleInputChange("agreement", e.target.checked)}
                      className="mt-1 w-4 h-4 text-blue-500 border-gray-300 rounded focus:ring-blue-500 transition-colors"
                      disabled={isSubmitting}
                    />
                    <div className="flex-1">
                      <Label htmlFor="agreement" className="text-sm text-gray-700 cursor-pointer">
                        <span className="font-medium">I understand and agree to the referee responsibilities:</span>
                        <ul className="mt-3 ml-4 space-y-2 text-xs text-gray-600">
                          <li className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>Join matches in-game to observe and verify results</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>Submit fair and unbiased match reports</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>Be available during scheduled match times</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>Follow Gambets referee guidelines and code of conduct</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>Maintain confidentiality of player information</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-blue-500 mt-0.5">•</span>
                            <span>Provide accurate identity and payout information</span>
                          </li>
                        </ul>
                      </Label>
                      {errors.agreement && (
                        <p className="text-red-500 text-sm mt-2 flex items-center gap-1">
                          <AlertCircle className="w-3 h-3" />
                          {errors.agreement}
                        </p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Submit Button */}
                <div className="pt-4">
                  <Button
                    type="submit"
                    disabled={!isFormValid() || isSubmitting}
                    className={`w-full h-12 sm:h-14 font-medium text-base rounded-lg transition-all duration-200 ${
                      isFormValid() && !isSubmitting
                        ? "bg-blue-500 hover:bg-blue-600 text-white shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                    }`}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        Submitting Application...
                      </>
                    ) : (
                      <>
                        <Shield className="w-5 h-5 mr-2" />
                        Apply to Become a Referee
                      </>
                    )}
                  </Button>

                  {!isFormValid() && (
                    <p className="text-center text-sm text-gray-500 mt-2">
                      Please fill in all required fields to submit your application
                    </p>
                  )}
                </div>
              </form>

              {/* Additional Info */}
              <div className="mt-8 p-4 sm:p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl shadow-sm border border-gray-200">
                <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-blue-500" />
                  What happens next?
                </h4>
                <div className="space-y-3 text-sm text-gray-600">
                  {[
                    "We'll review your application within 3-5 business days",
                    "Identity verification process (1-2 business days)",
                    "If approved, you'll receive referee training materials",
                    "Complete a short assessment to become a certified referee",
                    "Start earning diamonds by refereeing matches!",
                  ].map((step, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold flex-shrink-0">
                        {index + 1}
                      </div>
                      <span>{step}</span>
                    </div>
                  ))}
                </div>

                <div className="mt-4 p-3 bg-blue-100 rounded-lg">
                  <p className="text-blue-800 text-sm font-medium">
                    💡 <strong>Pro Tip:</strong> Applications with complete information and optional documents are
                    processed faster!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
      </div>
    </div>
  )
}
