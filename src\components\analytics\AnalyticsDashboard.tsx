import React, { useState, useEffect } from 'react'
import {
  Bar<PERSON>hart3,
  TrendingUp,
  TrendingDown,
  Trophy,
  Target,
  Zap,
  Award,
  Pie<PERSON><PERSON>,
  LineChart
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

interface AnalyticsData {
  totalMatches: number
  totalWins: number
  totalLosses: number
  winRate: number
  totalEarnings: number
  averageMatchDuration: number
  favoriteGame: string
  currentStreak: number
  longestStreak: number
  rankPoints: number
  weeklyStats: {
    matches: number
    wins: number
    earnings: number
  }
  monthlyStats: {
    matches: number
    wins: number
    earnings: number
  }
  gameBreakdown: {
    game: string
    matches: number
    winRate: number
    earnings: number
  }[]
  performanceTrend: {
    date: string
    wins: number
    losses: number
    earnings: number
  }[]
}

export default function AnalyticsDashboard() {
  const { user } = useAuth()
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<'week' | 'month' | 'all'>('week')

  useEffect(() => {
    if (user?.id) {
      loadAnalytics()
    }
  }, [user, timeframe])

  const loadAnalytics = async () => {
    setLoading(true)
    try {
      // Mock analytics data - in real app, fetch from API
      const mockData: AnalyticsData = {
        totalMatches: 45,
        totalWins: 28,
        totalLosses: 17,
        winRate: 62.2,
        totalEarnings: 12450,
        averageMatchDuration: 18.5,
        favoriteGame: 'Mobile Legends',
        currentStreak: 3,
        longestStreak: 8,
        rankPoints: 1850,
        weeklyStats: {
          matches: 8,
          wins: 5,
          earnings: 1200
        },
        monthlyStats: {
          matches: 32,
          wins: 20,
          earnings: 8900
        },
        gameBreakdown: [
          { game: 'Mobile Legends', matches: 25, winRate: 68, earnings: 7500 },
          { game: 'Valorant', matches: 12, winRate: 58.3, earnings: 3200 },
          { game: 'Call of Duty Mobile', matches: 8, winRate: 50, earnings: 1750 }
        ],
        performanceTrend: [
          { date: '2024-01-01', wins: 3, losses: 1, earnings: 450 },
          { date: '2024-01-02', wins: 2, losses: 2, earnings: 200 },
          { date: '2024-01-03', wins: 4, losses: 0, earnings: 800 },
          { date: '2024-01-04', wins: 1, losses: 3, earnings: -150 },
          { date: '2024-01-05', wins: 3, losses: 2, earnings: 350 }
        ]
      }
      setAnalyticsData(mockData)
    } catch (error) {
      console.error('Error loading analytics:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatChange = (current: number, previous: number) => {
    if (previous === 0) return { value: 0, isPositive: true }
    const change = ((current - previous) / previous) * 100
    return { value: Math.abs(change), isPositive: change >= 0 }
  }

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color, 
    change 
  }: { 
    title: string
    value: string | number
    icon: React.ReactNode
    color: string
    change?: { value: number; isPositive: boolean }
  }) => (
    <div className="bg-white rounded-xl shadow-sm p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {change && (
            <div className={`flex items-center mt-2 text-sm ${
              change.isPositive ? 'text-green-600' : 'text-red-600'
            }`}>
              {change.isPositive ? (
                <TrendingUp className="w-4 h-4 mr-1" />
              ) : (
                <TrendingDown className="w-4 h-4 mr-1" />
              )}
              <span>{change.value.toFixed(1)}%</span>
            </div>
          )}
        </div>
        <div className={`${color} p-3 rounded-lg`}>
          {icon}
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!analyticsData) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6 text-center">
        <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Analytics Data</h3>
        <p className="text-gray-600">Play some matches to see your analytics!</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 flex items-center space-x-2">
          <BarChart3 className="w-6 h-6 text-blue-600" />
          <span>Performance Analytics</span>
        </h2>
        <div className="flex space-x-1">
          {[
            { key: 'week', label: 'Week' },
            { key: 'month', label: 'Month' },
            { key: 'all', label: 'All Time' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setTimeframe(tab.key as any)}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeframe === tab.key
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Key Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Matches"
          value={analyticsData.totalMatches}
          icon={<Trophy className="w-6 h-6 text-white" />}
          color="bg-blue-500"
          change={getStatChange(analyticsData.weeklyStats.matches, 5)}
        />
        <StatCard
          title="Win Rate"
          value={`${analyticsData.winRate}%`}
          icon={<Target className="w-6 h-6 text-white" />}
          color="bg-green-500"
          change={getStatChange(analyticsData.winRate, 58)}
        />
        <StatCard
          title="Total Earnings"
          value={`${analyticsData.totalEarnings} 💎`}
          icon={<Award className="w-6 h-6 text-white" />}
          color="bg-purple-500"
          change={getStatChange(analyticsData.totalEarnings, 10200)}
        />
        <StatCard
          title="Current Streak"
          value={analyticsData.currentStreak}
          icon={<Zap className="w-6 h-6 text-white" />}
          color="bg-amber-500"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Game Breakdown */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <PieChart className="w-5 h-5 text-blue-600" />
            <span>Game Performance</span>
          </h3>
          <div className="space-y-4">
            {analyticsData.gameBreakdown.map((game, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    index === 0 ? 'bg-blue-500' :
                    index === 1 ? 'bg-green-500' : 'bg-purple-500'
                  }`}></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">{game.game}</p>
                    <p className="text-xs text-gray-600">{game.matches} matches</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-semibold text-gray-900">{game.winRate}%</p>
                  <p className="text-xs text-gray-600">{game.earnings} 💎</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Trend */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <LineChart className="w-5 h-5 text-green-600" />
            <span>Recent Performance</span>
          </h3>
          <div className="space-y-3">
            {analyticsData.performanceTrend.slice(-5).map((day, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-sm text-gray-600">
                    {new Date(day.date).toLocaleDateString('en-US', { 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">
                      {day.wins}W
                    </span>
                    <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                      {day.losses}L
                    </span>
                  </div>
                </div>
                <div className={`text-sm font-semibold ${
                  day.earnings >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {day.earnings >= 0 ? '+' : ''}{day.earnings} 💎
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Favorite Game</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.favoriteGame}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Longest Streak</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.longestStreak} wins</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Avg Match Duration</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.averageMatchDuration} min</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Rank Points</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.rankPoints}</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">This Week</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Matches Played</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.weeklyStats.matches}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Wins</span>
              <span className="text-sm font-medium text-green-600">{analyticsData.weeklyStats.wins}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Earnings</span>
              <span className="text-sm font-medium text-purple-600">{analyticsData.weeklyStats.earnings} 💎</span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">This Month</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Matches Played</span>
              <span className="text-sm font-medium text-gray-900">{analyticsData.monthlyStats.matches}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Wins</span>
              <span className="text-sm font-medium text-green-600">{analyticsData.monthlyStats.wins}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Earnings</span>
              <span className="text-sm font-medium text-purple-600">{analyticsData.monthlyStats.earnings} 💎</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
