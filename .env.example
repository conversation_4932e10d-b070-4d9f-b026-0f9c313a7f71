# 🎮 Gambets Platform - Environment Configuration
# Copy this file to .env and fill in your actual values

# 🔗 Supabase Configuration
# Get these values from your Supabase project dashboard
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here

# 🌍 App Configuration
VITE_APP_ENV=development
VITE_APP_NAME=Gambets
VITE_APP_VERSION=1.0.0
VITE_APP_URL=http://localhost:5173

# 🔐 Security
VITE_APP_SECRET_KEY=your-secret-key-here

# 💳 Payment Configuration
VITE_PAYMENT_API_URL=https://api.payment-gateway.com
VITE_PAYMENT_API_KEY=your-payment-api-key
VITE_GCASH_API_KEY=your-gcash-api-key
VITE_MAYA_API_KEY=your-maya-api-key

# 🪙 Crypto Payment Configuration
VITE_NOWPAYMENTS_API_KEY=your-nowpayments-api-key
VITE_NOWPAYMENTS_PUBLIC_KEY=your-nowpayments-public-key

# 📧 Email Configuration (Optional)
VITE_EMAIL_SERVICE_ID=your-email-service-id
VITE_EMAIL_TEMPLATE_ID=your-email-template-id

# 📊 Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=your-ga-id
VITE_FACEBOOK_PIXEL_ID=your-fb-pixel-id

# 🎯 Feature Flags
VITE_ENABLE_CHAT=true
VITE_ENABLE_REFERRALS=true
VITE_ENABLE_TOURNAMENTS=false

# 🔧 Development Settings
VITE_DEBUG_MODE=true
VITE_API_TIMEOUT=10000
NODE_ENV=development
