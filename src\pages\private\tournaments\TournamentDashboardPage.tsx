import { useState } from 'react'
import {
  Trophy, Users, Gem, Eye, Play, Crown, Target, TrendingUp, Plus,
  Settings, Bell, RefreshCw, BarChart3, DollarSign, Share2, Download
} from 'lucide-react'
// import { useAuth } from '../../../contexts/AuthContext' // TODO: Confirm usage

export default function TournamentDashboardPage() {
  const [activeView, setActiveView] = useState<'player' | 'organizer'>('player')
  const [, setIsLoading] = useState(false) // isLoading unused, keeping setter
  const [selectedTournament, setSelectedTournament] = useState<string | null>(null)

  // Mock data for player tournaments
  const playerTournaments = [
    {
      id: '1',
      title: 'Mobile Legends World Championship',
      game: 'Mobile Legends',
      status: 'ongoing',
      position: 'Quarter Finals',
      nextMatch: '2024-12-25T18:00:00Z',
      prizePool: 50000,
      currentRank: 8,
      totalParticipants: 128,
      earnings: 2500,
      isLive: true,
      participants: 128,
      maxParticipants: 128,
      revenue: 0,
      registrationDeadline: null
    },
    {
      id: '2',
      title: 'Valorant Champions Series',
      game: 'Valorant',
      status: 'upcoming',
      position: 'Registered',
      nextMatch: '2024-12-30T19:00:00Z',
      prizePool: 35000,
      currentRank: null,
      totalParticipants: 64,
      earnings: 0,
      isLive: false,
      participants: 64,
      maxParticipants: 64,
      revenue: 0,
      registrationDeadline: null
    },
    {
      id: '3',
      title: 'CS:GO Weekend Warriors',
      game: 'CS:GO',
      status: 'completed',
      position: '3rd Place',
      nextMatch: null,
      prizePool: 8000,
      currentRank: 3,
      totalParticipants: 64,
      earnings: 1200,
      isLive: false,
      participants: 64,
      maxParticipants: 64,
      revenue: 0,
      registrationDeadline: null
    }
  ]

  // Mock data for organized tournaments
  const organizedTournaments = [
    {
      id: '4',
      title: 'Gambets Weekly Cup',
      game: 'Mobile Legends',
      status: 'registration',
      participants: 45,
      maxParticipants: 64,
      prizePool: 10000,
      revenue: 4500,
      startDate: '2024-12-28T20:00:00Z',
      registrationDeadline: '2024-12-26T23:59:59Z',
      position: null,
      nextMatch: null,
      currentRank: null,
      totalParticipants: 64,
      earnings: 0,
      isLive: false
    },
    {
      id: '5',
      title: 'Pro League Qualifier',
      game: 'Valorant',
      status: 'ongoing',
      participants: 32,
      maxParticipants: 32,
      prizePool: 25000,
      revenue: 8000,
      startDate: '2024-12-20T14:00:00Z',
      registrationDeadline: '2024-12-18T23:59:59Z',
      position: null,
      nextMatch: null,
      currentRank: null,
      totalParticipants: 32,
      earnings: 0,
      isLive: false
    }
  ]

  const formatTimeUntil = (dateString: string) => {
    const now = new Date()
    const target = new Date(dateString)
    const diff = target.getTime() - now.getTime()
    
    if (diff < 0) return 'Started'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    
    if (days > 0) return `${days}d ${hours}h`
    return `${hours}h`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ongoing': return 'bg-red-100 text-red-700 border-red-200'
      case 'upcoming': return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'registration': return 'bg-green-100 text-green-700 border-green-200'
      case 'completed': return 'bg-gray-100 text-gray-700 border-gray-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const getGameIcon = (game: string) => {
    switch (game) {
      case 'Mobile Legends': return '🏆'
      case 'Valorant': return '🎯'
      case 'Call of Duty': return '💥'
      case 'Dota 2': return '⚔️'
      case 'CS:GO': return '🔫'
      default: return '🎮'
    }
  }

  const handleQuickAction = (action: string, tournamentId?: string) => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      alert(`${action} ${tournamentId ? `for tournament ${tournamentId}` : ''}`)
    }, 1000)
  }

  const playerStats = {
    totalTournaments: playerTournaments.length,
    activeTournaments: playerTournaments.filter(t => t.status === 'ongoing' || t.status === 'upcoming').length,
    totalEarnings: playerTournaments.reduce((sum, t) => sum + t.earnings, 0),
    bestRank: Math.min(...playerTournaments.filter(t => t.currentRank).map(t => t.currentRank!)),
    winRate: 75
  }

  const organizerStats = {
    totalTournaments: organizedTournaments.length,
    activeTournaments: organizedTournaments.filter(t => t.status === 'ongoing' || t.status === 'registration').length,
    totalRevenue: organizedTournaments.reduce((sum, t) => sum + t.revenue, 0),
    totalParticipants: organizedTournaments.reduce((sum, t) => sum + t.participants, 0)
  }

  return (
    <div className="h-screen bg-gray-50 overflow-hidden">
      {/* Compact Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <Trophy className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">Tournament Dashboard</h1>
              <p className="text-sm text-gray-600">Manage your tournaments</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setActiveView('player')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  activeView === 'player' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600'
                }`}
              >
                Player
              </button>
              <button
                onClick={() => setActiveView('organizer')}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  activeView === 'organizer' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600'
                }`}
              >
                Organizer
              </button>
            </div>
            
            <button 
              onClick={() => handleQuickAction('Refresh')}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4 text-gray-600" />
            </button>
            <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Settings className="w-4 h-4 text-gray-600" />
            </button>
          </div>
        </div>
      </div>

      {/* Main Content - Single Screen Layout */}
      <div className="h-[calc(100vh-73px)] p-4 overflow-y-auto">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 h-full">
          
          {/* Stats Overview - Compact */}
          <div className="lg:col-span-4">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 h-full">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-bold text-gray-900">
                  {activeView === 'player' ? 'Player Stats' : 'Organizer Stats'}
                </h2>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-600 font-medium">Live</span>
                </div>
              </div>

              {activeView === 'player' ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-blue-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Trophy className="w-4 h-4 text-blue-600" />
                        <span className="text-xs font-medium text-blue-700">Tournaments</span>
                      </div>
                      <div className="text-xl font-bold text-blue-900">{playerStats.totalTournaments}</div>
                      <div className="text-xs text-blue-600">{playerStats.activeTournaments} active</div>
                    </div>
                    
                    <div className="bg-green-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Gem className="w-4 h-4 text-green-600" />
                        <span className="text-xs font-medium text-green-700">Earnings</span>
                      </div>
                      <div className="text-xl font-bold text-green-900">{playerStats.totalEarnings.toLocaleString()} 💎</div>
                      <div className="text-xs text-green-600">Total earned</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-purple-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Crown className="w-4 h-4 text-purple-600" />
                        <span className="text-xs font-medium text-purple-700">Best Rank</span>
                      </div>
                      <div className="text-xl font-bold text-purple-900">#{playerStats.bestRank}</div>
                      <div className="text-xs text-purple-600">Highest position</div>
                    </div>
                    
                    <div className="bg-orange-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Target className="w-4 h-4 text-orange-600" />
                        <span className="text-xs font-medium text-orange-700">Win Rate</span>
                      </div>
                      <div className="text-xl font-bold text-orange-900">{playerStats.winRate}%</div>
                      <div className="text-xs text-orange-600">Success rate</div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="pt-2 border-t border-gray-200">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Quick Actions</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => handleQuickAction('Find Tournaments')}
                        className="flex items-center justify-center space-x-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                      >
                        <Plus className="w-3 h-3" />
                        <span>Find</span>
                      </button>
                      <button
                        onClick={() => handleQuickAction('View History')}
                        className="flex items-center justify-center space-x-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                      >
                        <BarChart3 className="w-3 h-3" />
                        <span>History</span>
                      </button>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-blue-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Trophy className="w-4 h-4 text-blue-600" />
                        <span className="text-xs font-medium text-blue-700">Tournaments</span>
                      </div>
                      <div className="text-xl font-bold text-blue-900">{organizerStats.totalTournaments}</div>
                      <div className="text-xs text-blue-600">{organizerStats.activeTournaments} active</div>
                    </div>
                    
                    <div className="bg-green-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <DollarSign className="w-4 h-4 text-green-600" />
                        <span className="text-xs font-medium text-green-700">Revenue</span>
                      </div>
                      <div className="text-xl font-bold text-green-900">{organizerStats.totalRevenue.toLocaleString()} 💎</div>
                      <div className="text-xs text-green-600">Total earned</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div className="bg-purple-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <Users className="w-4 h-4 text-purple-600" />
                        <span className="text-xs font-medium text-purple-700">Participants</span>
                      </div>
                      <div className="text-xl font-bold text-purple-900">{organizerStats.totalParticipants}</div>
                      <div className="text-xs text-purple-600">Total players</div>
                    </div>
                    
                    <div className="bg-orange-50 rounded-lg p-3">
                      <div className="flex items-center space-x-2 mb-1">
                        <TrendingUp className="w-4 h-4 text-orange-600" />
                        <span className="text-xs font-medium text-orange-700">Success</span>
                      </div>
                      <div className="text-xl font-bold text-orange-900">92%</div>
                      <div className="text-xs text-orange-600">Completion rate</div>
                    </div>
                  </div>

                  {/* Quick Actions */}
                  <div className="pt-2 border-t border-gray-200">
                    <h3 className="text-sm font-medium text-gray-700 mb-2">Quick Actions</h3>
                    <div className="grid grid-cols-2 gap-2">
                      <button
                        onClick={() => handleQuickAction('Create Tournament')}
                        className="flex items-center justify-center space-x-1 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                      >
                        <Plus className="w-3 h-3" />
                        <span>Create</span>
                      </button>
                      <button
                        onClick={() => handleQuickAction('Analytics')}
                        className="flex items-center justify-center space-x-1 bg-gray-100 text-gray-700 py-2 px-3 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                      >
                        <BarChart3 className="w-3 h-3" />
                        <span>Analytics</span>
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Tournament List - Compact */}
          <div className="lg:col-span-5">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 h-full">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-bold text-gray-900">
                  {activeView === 'player' ? 'My Tournaments' : 'Organized Tournaments'}
                </h2>
                <span className="text-sm text-gray-600">
                  {activeView === 'player' ? playerTournaments.length : organizedTournaments.length} total
                </span>
              </div>

              <div className="space-y-3 overflow-y-auto max-h-[calc(100%-60px)]">
                {(activeView === 'player' ? playerTournaments : organizedTournaments).map((tournament) => (
                  <div
                    key={tournament.id}
                    className={`border rounded-lg p-3 hover:shadow-md transition-all duration-200 cursor-pointer ${
                      selectedTournament === tournament.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                    }`}
                    onClick={() => setSelectedTournament(tournament.id)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className="text-lg">{getGameIcon(tournament.game)}</div>
                        <div>
                          <h3 className="font-medium text-gray-900 text-sm">{tournament.title}</h3>
                          <div className="flex items-center space-x-2 text-xs text-gray-600">
                            <span>{tournament.game}</span>
                            {activeView === 'player' && tournament.isLive && (
                              <>
                                <span>•</span>
                                <div className="flex items-center space-x-1">
                                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                                  <span className="text-red-600 font-medium">LIVE</span>
                                </div>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(tournament.status)}`}>
                        {tournament.status}
                      </span>
                    </div>

                    {activeView === 'player' ? (
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center">
                          <div className="font-medium text-gray-900">
                            {tournament.currentRank ? `#${tournament.currentRank}` : tournament.position}
                          </div>
                          <div className="text-gray-600">Position</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-900">{tournament.prizePool.toLocaleString()} 💎</div>
                          <div className="text-gray-600">Prize Pool</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-900">{tournament.earnings.toLocaleString()} 💎</div>
                          <div className="text-gray-600">Earnings</div>
                        </div>
                      </div>
                    ) : (
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="text-center">
                          <div className="font-medium text-gray-900">{tournament.participants}/{tournament.maxParticipants}</div>
                          <div className="text-gray-600">Participants</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-900">{tournament.prizePool.toLocaleString()} 💎</div>
                          <div className="text-gray-600">Prize Pool</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-900">{tournament.revenue.toLocaleString()} 💎</div>
                          <div className="text-gray-600">Revenue</div>
                        </div>
                      </div>
                    )}

                    {activeView === 'player' && tournament.nextMatch && (
                      <div className="mt-2 pt-2 border-t border-gray-200">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-600">Next Match:</span>
                          <span className="font-medium text-blue-600">{formatTimeUntil(tournament.nextMatch)}</span>
                        </div>
                      </div>
                    )}

                    {activeView === 'organizer' && tournament.registrationDeadline && (
                      <div className="mt-2 pt-2 border-t border-gray-200">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-600">Registration:</span>
                          <span className="font-medium text-orange-600">{formatTimeUntil(tournament.registrationDeadline)}</span>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Activity & Actions - Compact */}
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 h-full">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-bold text-gray-900">Activity</h2>
                <Bell className="w-4 h-4 text-gray-600" />
              </div>

              <div className="space-y-3 mb-6">
                {/* Live Activity */}
                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Tournament Live</p>
                    <p className="text-xs text-gray-600">Mobile Legends Championship is ongoing</p>
                    <p className="text-xs text-gray-500">2 minutes ago</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Registration Open</p>
                    <p className="text-xs text-gray-600">Valorant Champions Series accepting players</p>
                    <p className="text-xs text-gray-500">1 hour ago</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Prize Awarded</p>
                    <p className="text-xs text-gray-600">Received 1,200 💎 from CS:GO tournament</p>
                    <p className="text-xs text-gray-500">3 hours ago</p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">New Tournament</p>
                    <p className="text-xs text-gray-600">Weekly Cup registration starts tomorrow</p>
                    <p className="text-xs text-gray-500">6 hours ago</p>
                  </div>
                </div>
              </div>

              {/* Quick Tournament Actions */}
              <div className="border-t border-gray-200 pt-4">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Quick Actions</h3>
                <div className="space-y-2">
                  {selectedTournament ? (
                    <>
                      {activeView === 'player' ? (
                        <>
                          <button
                            onClick={() => handleQuickAction('View Tournament', selectedTournament)}
                            className="w-full flex items-center justify-center space-x-2 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                          >
                            <Eye className="w-3 h-3" />
                            <span>View Details</span>
                          </button>
                          <button
                            onClick={() => handleQuickAction('Join Match', selectedTournament)}
                            className="w-full flex items-center justify-center space-x-2 bg-green-600 text-white py-2 px-3 rounded-lg hover:bg-green-700 transition-colors text-sm"
                          >
                            <Play className="w-3 h-3" />
                            <span>Join Match</span>
                          </button>
                        </>
                      ) : (
                        <>
                          <button
                            onClick={() => handleQuickAction('Manage Tournament', selectedTournament)}
                            className="w-full flex items-center justify-center space-x-2 bg-blue-600 text-white py-2 px-3 rounded-lg hover:bg-blue-700 transition-colors text-sm"
                          >
                            <Settings className="w-3 h-3" />
                            <span>Manage</span>
                          </button>
                          <button
                            onClick={() => handleQuickAction('View Analytics', selectedTournament)}
                            className="w-full flex items-center justify-center space-x-2 bg-purple-600 text-white py-2 px-3 rounded-lg hover:bg-purple-700 transition-colors text-sm"
                          >
                            <BarChart3 className="w-3 h-3" />
                            <span>Analytics</span>
                          </button>
                        </>
                      )}
                      <div className="grid grid-cols-2 gap-2">
                        <button
                          onClick={() => handleQuickAction('Share', selectedTournament)}
                          className="flex items-center justify-center space-x-1 bg-gray-100 text-gray-700 py-2 px-2 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                        >
                          <Share2 className="w-3 h-3" />
                          <span>Share</span>
                        </button>
                        <button
                          onClick={() => handleQuickAction('Export', selectedTournament)}
                          className="flex items-center justify-center space-x-1 bg-gray-100 text-gray-700 py-2 px-2 rounded-lg hover:bg-gray-200 transition-colors text-sm"
                        >
                          <Download className="w-3 h-3" />
                          <span>Export</span>
                        </button>
                      </div>
                    </>
                  ) : (
                    <div className="text-center py-4">
                      <Trophy className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600">Select a tournament to see actions</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
