// 💳 BUY FLOW MODAL
// Referee escrow confirmation and purchase flow

import React, { useState } from 'react'
import { X, Shield, AlertTriangle, CheckCircle, Loader2, Copy } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { marketplaceService, MarketplaceListing } from '../../services/marketplace'
import { walletService } from '../../services/walletService'

interface BuyFlowModalProps {
  isOpen: boolean
  onClose: () => void
  listing: MarketplaceListing
  onSuccess: () => void
}

const BuyFlowModal: React.FC<BuyFlowModalProps> = ({
  isOpen,
  onClose,
  listing,
  onSuccess
}) => {
  const { user } = useAuth()
  const [isProcessing, setIsProcessing] = useState(false)
  const [step, setStep] = useState<'confirm' | 'processing' | 'success'>('confirm')
  const [orderDetails, setOrderDetails] = useState<any>(null)

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat().format(price)
  }

  const calculateFees = () => {
    const platformFee = Math.floor(listing.price_diamonds * 0.05) // 5%
    const refereeFee = Math.floor(listing.price_diamonds * 0.02) // 2%
    const total = listing.price_diamonds + platformFee + refereeFee
    
    return {
      itemPrice: listing.price_diamonds,
      platformFee,
      refereeFee,
      total
    }
  }

  const fees = calculateFees()

  const handlePurchase = async () => {
    if (!user) return

    setIsProcessing(true)
    setStep('processing')

    try {
      // Check if user has enough diamonds
      const { data: balance } = await walletService.getBalance(user.id)
      if (!balance || balance < fees.total) {
        throw new Error('Insufficient diamond balance')
      }

      // Create order
      const { data: order, error: orderError } = await marketplaceService.createOrder({
        listing_id: listing.id,
        buyer_id: user.id,
        seller_id: listing.seller_id,
        price_diamonds: listing.price_diamonds,
        payment_method: 'diamonds'
      })

      if (orderError || !order) {
        throw new Error('Failed to create order')
      }

      // Deduct diamonds from user balance (escrow)
      const { error: deductError } = await walletService.deductBalance(user.id, fees.total)
      if (deductError) {
        throw new Error('Failed to process payment')
      }

      // Update order status to paid
      await marketplaceService.updateOrderStatus(order.id, 'paid', {
        escrow_amount: fees.total
      })

      setOrderDetails(order)
      setStep('success')
    } catch (error: any) {
      alert(`Purchase failed: ${error.message}`)
      setStep('confirm')
    } finally {
      setIsProcessing(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('Copied to clipboard!')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-bold text-gray-900">
            {step === 'confirm' && 'Confirm Purchase'}
            {step === 'processing' && 'Processing...'}
            {step === 'success' && 'Purchase Successful!'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        <div className="p-6">
          {step === 'confirm' && (
            <>
              {/* Listing Summary */}
              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 mb-2">{listing.title}</h3>
                <div className="text-sm text-gray-600">
                  {listing.game} • {listing.rank || 'Unranked'}
                </div>
              </div>

              {/* Price Breakdown */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Price Breakdown</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Item Price</span>
                    <span>{formatPrice(fees.itemPrice)} 💎</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Platform Fee (5%)</span>
                    <span>{formatPrice(fees.platformFee)} 💎</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Referee Fee (2%)</span>
                    <span>{formatPrice(fees.refereeFee)} 💎</span>
                  </div>
                  <div className="border-t pt-2 flex justify-between font-medium">
                    <span>Total</span>
                    <span>{formatPrice(fees.total)} 💎</span>
                  </div>
                </div>
              </div>

              {/* Referee Protection Notice */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-start space-x-3">
                  <Shield className="w-5 h-5 text-green-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-green-800 mb-1">Referee Protection</h4>
                    <p className="text-sm text-green-700">
                      Your diamonds will be held in escrow until a referee confirms the account transfer. 
                      You'll receive the account credentials once verified.
                    </p>
                  </div>
                </div>
              </div>

              {/* Warning */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-800 mb-1">Important</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• Account transfers are final - no refunds after delivery</li>
                      <li>• Change the password immediately after receiving the account</li>
                      <li>• Contact support if you don't receive credentials within 24 hours</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Purchase Button */}
              <button
                onClick={handlePurchase}
                disabled={isProcessing}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-3 px-4 rounded-lg font-medium transition-colors"
              >
                Confirm Purchase ({formatPrice(fees.total)} 💎)
              </button>
            </>
          )}

          {step === 'processing' && (
            <div className="text-center py-8">
              <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Processing Purchase</h3>
              <p className="text-gray-600">
                Creating order and setting up escrow protection...
              </p>
            </div>
          )}

          {step === 'success' && (
            <>
              <div className="text-center mb-6">
                <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Purchase Successful!</h3>
                <p className="text-gray-600">
                  Your order has been created and diamonds are in escrow.
                </p>
              </div>

              {/* Order Details */}
              <div className="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-gray-900 mb-3">Order Details</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Order ID</span>
                    <div className="flex items-center space-x-2">
                      <span className="font-mono">{orderDetails?.id?.slice(0, 8)}...</span>
                      <button
                        onClick={() => copyToClipboard(orderDetails?.id)}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Copy className="w-3 h-3" />
                      </button>
                    </div>
                  </div>
                  <div className="flex justify-between">
                    <span>Status</span>
                    <span className="text-green-600 font-medium">Paid - Awaiting Referee</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Escrow Amount</span>
                    <span>{formatPrice(fees.total)} 💎</span>
                  </div>
                </div>
              </div>

              {/* Next Steps */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <h4 className="font-medium text-blue-800 mb-2">What happens next?</h4>
                <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
                  <li>A referee will be assigned to your order</li>
                  <li>The referee will verify the account details</li>
                  <li>You'll receive the account credentials</li>
                  <li>Confirm receipt to release payment to seller</li>
                </ol>
              </div>

              <button
                onClick={() => {
                  onSuccess()
                  onClose()
                }}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
              >
                Continue
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default BuyFlowModal
