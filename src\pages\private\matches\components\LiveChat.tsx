import { useState, useEffect, useRef } from 'react'
import { Send, MessageCircle, Users } from 'lucide-react'
import { useAuth } from '../../../../contexts/AuthContext'
import { supabase } from '../../../../services/supabase'
import { TouchButton } from '../../../../components/mobile/TouchOptimized'
import type { Match } from '../../../../types'

interface ChatMessage {
  id: string
  match_id: string
  user_id: string
  message: string
  message_type: 'text' | 'system' | 'emoji'
  created_at: string
  user?: {
    username: string
    first_name: string
    last_name: string
  }
}

interface LiveChatProps {
  match: Match
  isParticipant: boolean
}

export default function LiveChat({ match, isParticipant }: LiveChatProps) {
  const { user } = useAuth()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isExpanded, setIsExpanded] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (match && isExpanded) {
      loadMessages()
      subscribeToMessages()
    }
  }, [match, isExpanded])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const loadMessages = async () => {
    try {
      const { data, error } = await supabase
        .from('match_chat')
        .select(`
          *,
          user:users(username, first_name, last_name)
        `)
        .eq('match_id', match.id)
        .order('created_at', { ascending: true })
        .limit(50)

      if (error) throw error
      if (data) setMessages(data)
    } catch (error) {
      console.error('Error loading messages:', error)
    }
  }

  const subscribeToMessages = () => {
    const channel = supabase
      .channel(`match-chat-${match.id}`)
      .on('postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'match_chat',
          filter: `match_id=eq.${match.id}`
        },
        async (payload) => {
          const newMessage = payload.new as ChatMessage
          
          // Get user info for the new message
          const { data: userData } = await supabase
            .from('users')
            .select('username, first_name, last_name')
            .eq('id', newMessage.user_id)
            .single()

          if (userData) {
            newMessage.user = userData
          }

          setMessages(prev => [...prev, newMessage])
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }

  const sendMessage = async () => {
    if (!user || !newMessage.trim() || !isParticipant) return

    try {
      setIsLoading(true)

      const { error } = await supabase
        .from('match_chat')
        .insert({
          match_id: match.id,
          user_id: user.id,
          message: newMessage.trim(),
          message_type: 'text'
        })

      if (error) throw error

      setNewMessage('')
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case 'system': return 'text-yellow-400'
      case 'emoji': return 'text-blue-400'
      default: return 'text-white'
    }
  }

  if (!isExpanded) {
    return (
      <div className="bg-gray-800 rounded-lg border border-gray-700">
        <TouchButton
          onClick={() => setIsExpanded(true)}
          className="w-full p-4 flex items-center justify-between hover:bg-gray-700 transition-colors"
        >
          <div className="flex items-center gap-2">
            <MessageCircle className="w-4 h-4 text-blue-400" />
            <span className="text-white font-medium">Live Chat</span>
            {messages.length > 0 && (
              <span className="bg-blue-600 text-white text-xs px-2 py-1 rounded-full">
                {messages.length}
              </span>
            )}
          </div>
          <Users className="w-4 h-4 text-gray-400" />
        </TouchButton>
      </div>
    )
  }

  return (
    <div className="bg-gray-800 rounded-lg border border-gray-700">
      {/* Chat Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="flex items-center gap-2">
          <MessageCircle className="w-4 h-4 text-blue-400" />
          <span className="text-white font-medium">Live Chat</span>
          <span className="bg-gray-700 text-gray-300 text-xs px-2 py-1 rounded-full">
            {messages.length} messages
          </span>
        </div>
        <TouchButton
          onClick={() => setIsExpanded(false)}
          className="text-gray-400 hover:text-white"
        >
          ×
        </TouchButton>
      </div>

      {/* Messages */}
      <div className="h-64 overflow-y-auto p-4 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center text-gray-400 py-8">
            <MessageCircle className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p>No messages yet. Start the conversation!</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="flex gap-3">
              <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center flex-shrink-0">
                <span className="text-xs text-gray-300">
                  {message.user?.first_name?.[0] || '?'}
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium text-gray-300">
                    {message.user?.first_name} {message.user?.last_name}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatTime(message.created_at)}
                  </span>
                </div>
                <p className={`text-sm break-words ${getMessageTypeColor(message.message_type)}`}>
                  {message.message}
                </p>
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      {isParticipant ? (
        <div className="p-4 border-t border-gray-700">
          <div className="flex gap-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              disabled={isLoading}
              className="flex-1 bg-gray-700 border border-gray-600 rounded-lg px-3 py-2 text-white placeholder-gray-400 focus:border-blue-500 focus:outline-none disabled:opacity-50"
            />
            <TouchButton
              onClick={sendMessage}
              disabled={!newMessage.trim() || isLoading}
              className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg"
            >
              <Send className="w-4 h-4" />
            </TouchButton>
          </div>
        </div>
      ) : (
        <div className="p-4 border-t border-gray-700">
          <div className="text-center text-gray-400 text-sm">
            Join the match to participate in chat
          </div>
        </div>
      )}
    </div>
  )
}
