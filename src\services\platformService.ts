import { supabase } from './supabaseClient'

export const platformService = {
  // Complete match with winner determination
  async completeMatch(matchId: string, winnerTeam: number, refereeId: string) {
    try {
      const { data, error } = await supabase.rpc('complete_match', {
        match_id_param: matchId,
        winner_team_param: winnerTeam,
        referee_id_param: refereeId
      })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Complete match error:', error)
      return { data: null, error }
    }
  },

  // Get user transaction history
  async getUserTransactionHistory(userId: string, limit: number = 50) {
    try {
      const { data, error } = await supabase
        .from('user_transaction_history')
        .select('*')
        .eq('user_id', userId)
        .limit(limit)

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Get transaction history error:', error)
      return { data: null, error }
    }
  },

  // Send friend request
  async sendFriendRequest(senderUsername: string, receiverUsername: string) {
    try {
      const { data: senderData } = await supabase
        .from('users')
        .select('id')
        .eq('username', senderUsername)
        .single()

      if (!senderData) throw new Error('Sender not found')

      const { data, error } = await supabase.rpc('send_friend_request', {
        sender_id_param: senderData.id,
        receiver_username_param: receiverUsername
      })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Send friend request error:', error)
      return { data: null, error }
    }
  },

  // Accept friend request
  async acceptFriendRequest(requestId: string, userId: string) {
    try {
      const { data, error } = await supabase.rpc('accept_friend_request', {
        request_id_param: requestId,
        receiver_id_param: userId
      })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Accept friend request error:', error)
      return { data: null, error }
    }
  },

  // Get leaderboard data
  async getLeaderboard(type: 'weekly' | 'monthly' = 'weekly') {
    try {
      const { data, error } = await supabase
        .from(`${type}_leaderboard`)
        .select('*')

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Get leaderboard error:', error)
      return { data: null, error }
    }
  },

  // Generate tournament bracket
  async generateTournamentBracket(tournamentId: string) {
    try {
      const { data, error } = await supabase.rpc('generate_tournament_bracket', {
        tournament_id_param: tournamentId
      })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      console.error('Generate tournament bracket error:', error)
      return { data: null, error }
    }
  }
}
