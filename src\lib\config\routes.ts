// Application Routes Configuration
export const ROUTES = {
  // Public Routes
  PUBLIC: {
    LANDING: '/',
    LOGIN: '/login',
    SIGNUP: '/signup',
    LEADERBOARDS: '/leaderboards',
    RULES: '/rules',
    SUPPORT: '/support',
  },
  
  // Private Routes
  PRIVATE: {
    DASHBOARD: '/dashboard',
    MATCHES: '/matches',
    MATCH_DETAIL: '/matches/:id',
    TOURNAMENTS: '/tournaments',
    TOURNAMENT_DASHBOARD: '/tournament-dashboard',
    PROFILE: '/profile',
    WALLET: '/wallet',
    BUY_DIAMONDS: '/buy-diamonds',
    COMMUNITY: '/community',
    REFEREE_APPLICATION: '/referee-application',
    REFEREE_DASHBOARD: '/referee-dashboard',
    REFERRALS: '/referrals',
    HISTORY: '/history',
    SETTINGS: '/settings',
  },
  
  // Admin Routes
  ADMIN: {
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    MATCHES: '/admin/matches',
    TOURNAMENTS: '/admin/tournaments',
    PAYMENTS: '/admin/payments',
    SETTINGS: '/admin/settings',
  },
  
  // Common Routes
  COMMON: {
    NOT_FOUND: '/404',
    ERROR: '/error',
    MAINTENANCE: '/maintenance',
  }
} as const

export type RouteKeys = keyof typeof ROUTES
export type PublicRoutes = typeof ROUTES.PUBLIC[keyof typeof ROUTES.PUBLIC]
export type PrivateRoutes = typeof ROUTES.PRIVATE[keyof typeof ROUTES.PRIVATE]
export type AdminRoutes = typeof ROUTES.ADMIN[keyof typeof ROUTES.ADMIN]
