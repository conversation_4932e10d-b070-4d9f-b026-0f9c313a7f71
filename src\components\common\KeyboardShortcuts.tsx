import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'

export default function KeyboardShortcuts() {
  const navigate = useNavigate()

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only trigger if no input is focused and Ctrl/Cmd is pressed
      if (
        (event.ctrlKey || event.metaKey) && 
        !['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement)?.tagName)
      ) {
        switch (event.key.toLowerCase()) {
          case 'd':
            event.preventDefault()
            navigate('/dashboard')
            break
          case 'm':
            event.preventDefault()
            navigate('/matches')
            break
          case 't':
            event.preventDefault()
            navigate('/tournaments')
            break
          case 'p':
            event.preventDefault()
            navigate('/profile')
            break
          case 'w':
            event.preventDefault()
            navigate('/buy-diamonds')
            break
          case 'c':
            event.preventDefault()
            navigate('/community')
            break
          case 'h':
            event.preventDefault()
            navigate('/history')
            break
          case 's':
            event.preventDefault()
            navigate('/settings')
            break
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [navigate])

  return null // This component doesn't render anything
}

// Keyboard shortcuts help component
export function KeyboardShortcutsHelp() {
  const shortcuts = [
    { key: 'Ctrl + D', action: 'Go to Dashboard' },
    { key: 'Ctrl + M', action: 'Go to Matches' },
    { key: 'Ctrl + T', action: 'Go to Tournaments' },
    { key: 'Ctrl + P', action: 'Go to Profile' },
    { key: 'Ctrl + W', action: 'Go to Wallet' },
    { key: 'Ctrl + C', action: 'Go to Community' },
    { key: 'Ctrl + H', action: 'Go to History' },
    { key: 'Ctrl + S', action: 'Go to Settings' },
  ]

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-3">Keyboard Shortcuts</h3>
      <div className="space-y-2">
        {shortcuts.map((shortcut, index) => (
          <div key={index} className="flex justify-between items-center">
            <span className="text-sm text-gray-600">{shortcut.action}</span>
            <kbd className="px-2 py-1 bg-gray-100 border border-gray-300 rounded text-xs font-mono">
              {shortcut.key}
            </kbd>
          </div>
        ))}
      </div>
    </div>
  )
}
