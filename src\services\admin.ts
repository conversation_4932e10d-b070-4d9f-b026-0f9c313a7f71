// 👑 ADMIN SERVICES
// Comprehensive admin functionality for Gambets platform

import { supabase } from './supabase'

// Admin Types
export interface AdminUser {
  id: string
  username: string
  email: string
  role: 'user' | 'referee' | 'admin' | 'super_admin'
  admin_level: number
  diamond_balance: number
  total_matches: number
  wins: number
  losses: number
  is_verified: boolean
  last_admin_login?: string
  admin_notes?: string
  created_at: string
  created_by_admin?: string
}

export interface RefereeApplication {
  id: string
  user_id: string
  experience_years: number
  games_expertise: string[]
  previous_experience: string
  availability_hours: number
  motivation: string
  status: 'pending' | 'approved' | 'rejected'
  reviewed_by?: string
  review_notes?: string
  applied_at: string
  reviewed_at?: string
  // Joined data
  user?: {
    username: string
    email: string
    total_matches: number
    wins: number
    losses: number
  }
}

export interface AdminAuditLog {
  id: string
  admin_id: string
  action: string
  target_type?: string
  target_id?: string
  details?: any
  ip_address?: string
  user_agent?: string
  created_at: string
  // Joined data
  admin?: {
    username: string
    email: string
  }
}

export interface SystemSettings {
  id: string
  setting_key: string
  setting_value: any
  description?: string
  updated_by?: string
  updated_at: string
}

export interface AdminNotification {
  id: string
  admin_id: string
  title: string
  message: string
  type: 'info' | 'warning' | 'error' | 'success'
  is_read: boolean
  action_url?: string
  created_at: string
}

// Admin Service Class
export class AdminService {
  // Check if user is admin
  static async isAdmin(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('role, admin_level')
        .eq('id', userId)
        .single()

      if (error) {
        console.log('Admin check error:', error.message)
        // If columns don't exist, user is definitely not admin
        if (error.message.includes('column') && error.message.includes('does not exist')) {
          console.log('Admin columns not found in database. Please run admin setup.')
          return false
        }
        return false
      }

      const isAdmin = (data.admin_level && data.admin_level >= 1) ||
                     (data.role && ['admin', 'super_admin'].includes(data.role))

      console.log('Admin check result:', {
        userId,
        admin_level: data.admin_level,
        role: data.role,
        isAdmin
      })

      return isAdmin
    } catch (error) {
      console.error('Admin check failed:', error)
      return false
    }
  }

  // Get admin user details
  static async getAdminUser(userId: string): Promise<AdminUser | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) throw error
      return data
    } catch (error) {
      console.error('Error getting admin user:', error)
      return null
    }
  }

  // Update admin login timestamp
  static async updateAdminLogin(userId: string): Promise<void> {
    try {
      await supabase
        .from('users')
        .update({ last_admin_login: new Date().toISOString() })
        .eq('id', userId)
    } catch (error) {
      console.error('Error updating admin login:', error)
    }
  }

  // Get all users with pagination and filters
  static async getUsers(filters: {
    search?: string
    role?: string
    page?: number
    limit?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}) {
    try {
      const { search, role, page = 1, limit = 20, sortBy = 'created_at', sortOrder = 'desc' } = filters
      const offset = (page - 1) * limit

      let query = supabase
        .from('users')
        .select('*', { count: 'exact' })

      // Apply filters
      if (search) {
        query = query.or(`username.ilike.%${search}%,email.ilike.%${search}%`)
      }
      if (role) {
        query = query.eq('role', role)
      }

      // Apply sorting
      query = query.order(sortBy, { ascending: sortOrder === 'asc' })

      // Apply pagination
      query = query.range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) throw error

      return {
        data: data || [],
        count: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        currentPage: page
      }
    } catch (error) {
      console.error('Error getting users:', error)
      return { data: [], count: 0, totalPages: 0, currentPage: 1 }
    }
  }

  // Get referee applications
  static async getRefereeApplications(status?: 'pending' | 'approved' | 'rejected') {
    try {
      let query = supabase
        .from('referee_applications')
        .select(`
          *,
          user:users!user_id(username, email, total_matches, wins, losses)
        `)
        .order('applied_at', { ascending: false })

      if (status) {
        query = query.eq('status', status)
      }

      const { data, error } = await query

      if (error) throw error
      return { data: data || [], error: null }
    } catch (error) {
      console.error('Error getting referee applications:', error)
      return { data: [], error }
    }
  }

  // Approve/Reject referee application
  static async reviewRefereeApplication(
    applicationId: string,
    adminId: string,
    decision: 'approved' | 'rejected',
    notes?: string
  ) {
    try {
      // Update application status
      const { data: application, error: updateError } = await supabase
        .from('referee_applications')
        .update({
          status: decision,
          reviewed_by: adminId,
          review_notes: notes,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', applicationId)
        .select('user_id')
        .single()

      if (updateError) throw updateError

      // If approved, update user role
      if (decision === 'approved') {
        await supabase
          .from('users')
          .update({ role: 'referee' })
          .eq('id', application.user_id)
      }

      // Log admin action
      await this.logAdminAction(
        adminId,
        `REFEREE_APPLICATION_${decision.toUpperCase()}`,
        'referee_application',
        applicationId,
        { decision, notes, user_id: application.user_id }
      )

      return { error: null }
    } catch (error) {
      console.error('Error reviewing referee application:', error)
      return { error }
    }
  }

  // Update user role and admin level
  static async updateUserRole(
    userId: string,
    adminId: string,
    role: string,
    adminLevel: number,
    notes?: string
  ) {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          role,
          admin_level: adminLevel,
          admin_notes: notes,
          created_by_admin: adminId
        })
        .eq('id', userId)

      if (error) throw error

      // Log admin action
      await this.logAdminAction(
        adminId,
        'USER_ROLE_UPDATED',
        'user',
        userId,
        { new_role: role, new_admin_level: adminLevel, notes }
      )

      return { error: null }
    } catch (error) {
      console.error('Error updating user role:', error)
      return { error }
    }
  }

  // Ban/Unban user
  static async toggleUserBan(userId: string, adminId: string, banned: boolean, reason?: string) {
    try {
      const { error } = await supabase
        .from('users')
        .update({
          is_banned: banned,
          ban_reason: banned ? reason : null,
          banned_at: banned ? new Date().toISOString() : null,
          banned_by: banned ? adminId : null
        })
        .eq('id', userId)

      if (error) throw error

      // Log admin action
      await this.logAdminAction(
        adminId,
        banned ? 'USER_BANNED' : 'USER_UNBANNED',
        'user',
        userId,
        { reason }
      )

      return { error: null }
    } catch (error) {
      console.error('Error toggling user ban:', error)
      return { error }
    }
  }

  // Adjust user diamond balance
  static async adjustDiamondBalance(
    userId: string,
    adminId: string,
    amount: number,
    reason: string,
    type: 'add' | 'subtract' | 'set'
  ) {
    try {
      // Get current balance
      const { data: user, error: getUserError } = await supabase
        .from('users')
        .select('diamond_balance')
        .eq('id', userId)
        .single()

      if (getUserError) throw getUserError

      let newBalance: number
      switch (type) {
        case 'add':
          newBalance = user.diamond_balance + amount
          break
        case 'subtract':
          newBalance = Math.max(0, user.diamond_balance - amount)
          break
        case 'set':
          newBalance = amount
          break
        default:
          throw new Error('Invalid adjustment type')
      }

      // Update balance
      const { error: updateError } = await supabase
        .from('users')
        .update({ diamond_balance: newBalance })
        .eq('id', userId)

      if (updateError) throw updateError

      // Create transaction record
      await supabase
        .from('transactions')
        .insert({
          user_id: userId,
          type: 'admin_adjustment',
          amount: type === 'subtract' ? -amount : amount,
          status: 'completed',
          description: reason,
          created_by_admin: adminId
        })

      // Log admin action
      await this.logAdminAction(
        adminId,
        'DIAMOND_BALANCE_ADJUSTED',
        'user',
        userId,
        { 
          old_balance: user.diamond_balance,
          new_balance: newBalance,
          adjustment_amount: amount,
          adjustment_type: type,
          reason
        }
      )

      return { error: null, newBalance }
    } catch (error) {
      console.error('Error adjusting diamond balance:', error)
      return { error, newBalance: null }
    }
  }

  // Get admin audit logs
  static async getAuditLogs(filters: {
    adminId?: string
    action?: string
    targetType?: string
    page?: number
    limit?: number
  } = {}) {
    try {
      const { adminId, action, targetType, page = 1, limit = 50 } = filters
      const offset = (page - 1) * limit

      let query = supabase
        .from('admin_audit_log')
        .select(`
          *,
          admin:users!admin_id(username, email)
        `, { count: 'exact' })
        .order('created_at', { ascending: false })

      // Apply filters
      if (adminId) query = query.eq('admin_id', adminId)
      if (action) query = query.eq('action', action)
      if (targetType) query = query.eq('target_type', targetType)

      // Apply pagination
      query = query.range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) throw error

      return {
        data: data || [],
        count: count || 0,
        totalPages: Math.ceil((count || 0) / limit),
        currentPage: page
      }
    } catch (error) {
      console.error('Error getting audit logs:', error)
      return { data: [], count: 0, totalPages: 0, currentPage: 1 }
    }
  }

  // Log admin action
  static async logAdminAction(
    adminId: string,
    action: string,
    targetType?: string,
    targetId?: string,
    details?: any,
    ipAddress?: string,
    userAgent?: string
  ) {
    try {
      await supabase
        .from('admin_audit_log')
        .insert({
          admin_id: adminId,
          action,
          target_type: targetType,
          target_id: targetId,
          details,
          ip_address: ipAddress,
          user_agent: userAgent
        })
    } catch (error) {
      console.error('Error logging admin action:', error)
    }
  }

  // Get system statistics
  static async getSystemStats() {
    try {
      const [
        { count: totalUsers },
        { count: totalMatches },
        { count: pendingReferees },
        { count: activeReferees },
        { data: recentActivity }
      ] = await Promise.all([
        supabase.from('users').select('*', { count: 'exact', head: true }),
        supabase.from('matches').select('*', { count: 'exact', head: true }),
        supabase.from('referee_applications').select('*', { count: 'exact', head: true }).eq('status', 'pending'),
        supabase.from('users').select('*', { count: 'exact', head: true }).eq('role', 'referee'),
        supabase
          .from('admin_audit_log')
          .select('action, created_at')
          .order('created_at', { ascending: false })
          .limit(10)
      ])

      return {
        totalUsers: totalUsers || 0,
        totalMatches: totalMatches || 0,
        pendingReferees: pendingReferees || 0,
        activeReferees: activeReferees || 0,
        recentActivity: recentActivity || []
      }
    } catch (error) {
      console.error('Error getting system stats:', error)
      return {
        totalUsers: 0,
        totalMatches: 0,
        pendingReferees: 0,
        activeReferees: 0,
        recentActivity: []
      }
    }
  }
}

// Create instance for export
export const adminService = AdminService

export default AdminService
