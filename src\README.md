# 🎮 Gambets Platform - Source Code Documentation

## 📁 Folder Structure Overview

This document provides a comprehensive guide to the Gambets platform source code organization. Each folder has a specific purpose and contains files that are clearly categorized by access level and functionality.

## 🗂️ Directory Structure

```
src/
├── 📁 pages/                    # All page components
│   ├── 📁 public/              # 🌐 PUBLIC PAGES (No authentication required)
│   ├── 📁 private/             # 🔒 PRIVATE PAGES (Authentication required)
│   └── 📁 admin/               # 👑 ADMIN PAGES (Admin role required)
├── 📁 components/              # Reusable UI components
│   ├── 📁 common/              # Shared components
│   ├── 📁 forms/               # Form-related components
│   ├── 📁 layout/              # Layout components
│   └── 📁 ui/                  # UI library components
├── 📁 services/                # Backend services & API calls
├── 📁 hooks/                   # Custom React hooks
├── 📁 contexts/                # React context providers
├── 📁 utils/                   # Utility functions
├── 📁 types/                   # TypeScript type definitions
├── 📁 assets/                  # Static assets (images, icons)
├── App.tsx                     # Main application component
├── main.tsx                    # Application entry point
└── index.css                   # Global styles
```

## 🌐 PUBLIC PAGES (pages/public/)

**Access Level:** No authentication required - Anyone can access these pages

| File | Route | Description | Status |
|------|-------|-------------|---------|
| `LandingPage.tsx` | `/` | Homepage with platform overview | ✅ Active |
| `LeaderboardsPage.tsx` | `/leaderboards` | Public leaderboards and statistics | ✅ Active |
| `LoginPage.tsx` | `/login` | User authentication page | ✅ Active |
| `SignUpPage.tsx` | `/signup` | User registration page | ✅ Active |
| `RulesPage.tsx` | `/rules` | Game rules and guidelines | ✅ Active |
| `SupportPage.tsx` | `/support` | Help center and contact info | ✅ Active |
| `AuthPage.tsx` | `/auth` | Alternative auth page | 📝 Legacy |

### 🎯 Purpose
These pages are accessible to all visitors and serve as the entry point to the platform. They focus on:
- User acquisition and onboarding
- Platform information and trust-building
- Authentication and registration
- Public information (rules, support, leaderboards)

## 🔒 PRIVATE PAGES (pages/private/)

**Access Level:** Authentication required - Only logged-in users can access

| File | Route | Description | Status |
|------|-------|-------------|---------|
| `DashboardPage.tsx` | `/dashboard` | User main dashboard | ✅ Active |
| `ProfilePage.tsx` | `/profile` | User profile management | ✅ Active |
| `WalletPage.tsx` | `/wallet` | Diamond balance and transactions | ✅ Active |
| `MatchesPage.tsx` | `/matches` | Browse and join matches | ✅ Active |
| `MatchDetailPage.tsx` | `/match/:id` | Individual match details | ✅ Active |
| `CommunityPage.tsx` | `/community` | Community features and chat | ✅ Active |
| `RefereeDashboardPage.tsx` | `/referee-dashboard` | Referee management panel | ✅ Active |
| `RefereeApplicationPage.tsx` | `/apply-referee` | Referee application form | ✅ Active |

### 🎯 Purpose
These pages require user authentication and provide core platform functionality:
- User account management
- Gaming and match participation
- Financial transactions (diamonds)
- Community interaction
- Referee system participation

## 👑 ADMIN PAGES (pages/admin/)

**Access Level:** Admin role required - Only administrators can access

| File | Route | Description | Status |
|------|-------|-------------|---------|
| `AdminRefereeReviewPage.tsx` | `/admin/referee-review` | Review referee applications | ✅ Active |

### 🎯 Purpose
These pages are restricted to platform administrators and provide:
- User management
- Content moderation
- System administration
- Referee approval process

## 🧩 COMPONENTS

### 📁 components/common/
**Shared components used across multiple pages**
- `ProtectedRoute.tsx` - Route protection for authenticated pages
- `NotificationSystem.tsx` - Global notification system

### 📁 components/forms/
**Form-related components**
- `RefereeGuide.tsx` - Referee application guidance

### 📁 components/layout/
**Layout and navigation components**
- `Navbar.tsx` - Main navigation component

### 📁 components/ui/
**UI library components (shadcn/ui)**
- Button, Card, Input, etc. - Reusable UI elements

## 🔧 SERVICES

### 📁 services/
**Backend integration and API services**
- `supabase.ts` - Supabase client configuration and database services

## 🎣 HOOKS

### 📁 hooks/
**Custom React hooks for reusable logic**
- Authentication hooks
- Data fetching hooks
- UI state management hooks

## 🌍 CONTEXTS

### 📁 contexts/
**React context providers for global state**
- Authentication context
- User profile context
- Notification context

## 🛠️ UTILITIES

### 📁 utils/
**Helper functions and utilities**
- Date formatting
- Currency formatting
- Validation functions
- API helpers

## 📝 TYPES

### 📁 types/
**TypeScript type definitions**
- User types
- Match types
- Transaction types
- API response types

## 🎨 ASSETS

### 📁 assets/
**Static assets**
- Images
- Icons
- Logos

## 🔐 Authentication Flow

1. **Public Access**: Users start on public pages (landing, login, signup)
2. **Authentication**: Users log in through LoginPage or SignUpPage
3. **Private Access**: Authenticated users access private pages
4. **Admin Access**: Admin users can access admin pages
5. **Route Protection**: ProtectedRoute component enforces access control

## 🚀 Getting Started

1. **Public Development**: Work on files in `pages/public/` for public features
2. **Private Development**: Work on files in `pages/private/` for user features
3. **Admin Development**: Work on files in `pages/admin/` for admin features
4. **Component Development**: Create reusable components in appropriate `components/` subfolders
5. **Service Development**: Add backend integration in `services/`

## 📋 Development Guidelines

- **Public Pages**: No authentication checks, focus on conversion and information
- **Private Pages**: Always check authentication, provide user-specific content
- **Admin Pages**: Check for admin role, provide administrative functionality
- **Components**: Keep them reusable and well-documented
- **Services**: Handle all backend communication and error handling
- **Types**: Define clear TypeScript interfaces for all data structures

## 🔗 Backend Connection

The platform is connected to **Supabase** for:
- User authentication
- Database operations
- Real-time subscriptions
- File storage

Configuration is in `services/supabase.ts` with comprehensive service functions for all backend operations.
