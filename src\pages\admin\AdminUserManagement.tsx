// 👥 ADMIN USER MANAGEMENT
// Super Admin can promote users to admin roles

import React, { useState, useEffect } from 'react'
import {
  Users,
  Crown,
  Shield,
  UserPlus,
  UserCheck,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Calendar,
  Award
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'

interface User {
  id: string
  email: string
  username: string
  role: string
  admin_level: number
  diamond_balance: number
  total_matches: number
  wins: number
  losses: number
  is_verified: boolean
  created_at: string
  admin_notes?: string
}

const AdminUserManagement: React.FC = () => {
  const { user } = useAuth()
  const [users, setUsers] = useState<User[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [isPromoting, setIsPromoting] = useState(false)
  const [promotionData, setPromotionData] = useState({
    role: 'admin',
    admin_level: 2,
    notes: ''
  })
  const [showRefereeModal, setShowRefereeModal] = useState(false)
  const [refereeUser, setRefereeUser] = useState<User | null>(null)

  useEffect(() => {
    loadUsers()
  }, [roleFilter])

  const loadUsers = async () => {
    try {
      setIsLoading(true)
      const { data } = await AdminService.getUsers({
        role: roleFilter === 'all' ? undefined : roleFilter,
        search: searchTerm,
        limit: 50
      })
      setUsers(data)
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePromoteUser = async () => {
    if (!selectedUser || !user) return

    try {
      setIsPromoting(true)
      const { error } = await AdminService.updateUserRole(
        selectedUser.id,
        user.id,
        promotionData.role,
        promotionData.admin_level,
        promotionData.notes
      )

      if (!error) {
        await loadUsers()
        setSelectedUser(null)
        setPromotionData({ role: 'admin', admin_level: 2, notes: '' })
        alert('User promoted successfully!')
      } else {
        alert('Error promoting user. Please try again.')
      }
    } catch (error) {
      console.error('Error promoting user:', error)
      alert('Error promoting user. Please try again.')
    } finally {
      setIsPromoting(false)
    }
  }

  const handleGrantRefereeAccess = async () => {
    if (!refereeUser || !user) return

    try {
      setIsPromoting(true)
      const { error } = await AdminService.updateUserRole(
        refereeUser.id,
        user.id,
        'referee',
        1,
        'Granted referee access - Level 1'
      )

      if (!error) {
        await loadUsers()
        setShowRefereeModal(false)
        setRefereeUser(null)
        alert('Referee access granted successfully! User will now see the Referee Dashboard in their navigation.')
      } else {
        alert('Error granting referee access. Please try again.')
      }
    } catch (error) {
      console.error('Error granting referee access:', error)
      alert('Error granting referee access. Please try again.')
    } finally {
      setIsPromoting(false)
    }
  }

  const getRoleBadge = (role: string, adminLevel: number) => {
    if (adminLevel >= 3) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
          <Crown className="w-3 h-3 mr-1" />
          Super Admin
        </span>
      )
    } else if (adminLevel >= 2) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
          <Shield className="w-3 h-3 mr-1" />
          Platform Admin
        </span>
      )
    } else if (adminLevel >= 1) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
          <Award className="w-3 h-3 mr-1" />
          Referee Coordinator
        </span>
      )
    } else if (role === 'referee') {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <CheckCircle className="w-3 h-3 mr-1" />
          Referee
        </span>
      )
    } else {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          <Users className="w-3 h-3 mr-1" />
          User
        </span>
      )
    }
  }

  const filteredUsers = users.filter(u =>
    u.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    u.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">User Management</h1>
            <p className="text-purple-100 mt-1">Manage users and admin privileges</p>
          </div>
          <div className="bg-white/20 rounded-lg px-4 py-2">
            <div className="text-sm text-purple-100">Total Users</div>
            <div className="text-2xl font-bold">{users.length}</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">Filter by role:</span>
            </div>
            <select
              value={roleFilter}
              onChange={(e) => setRoleFilter(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-1 text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Users</option>
              <option value="user">Regular Users</option>
              <option value="referee">Referees</option>
              <option value="admin">Admins</option>
              <option value="super_admin">Super Admins</option>
            </select>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Users Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 animate-pulse">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          ))
        ) : filteredUsers.length === 0 ? (
          <div className="col-span-full bg-white rounded-xl shadow-lg p-12 text-center">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Users Found</h3>
            <p className="text-gray-600">No users match your current filters.</p>
          </div>
        ) : (
          filteredUsers.map((userItem) => (
            <div key={userItem.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
              {/* User Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <Users className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{userItem.username}</h3>
                      <p className="text-sm text-gray-600">{userItem.email}</p>
                    </div>
                  </div>
                  {getRoleBadge(userItem.role, userItem.admin_level)}
                </div>

                {/* User Stats */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center p-2 bg-blue-50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900">{userItem.diamond_balance}</div>
                    <div className="text-xs text-gray-600">Diamonds</div>
                  </div>
                  <div className="text-center p-2 bg-green-50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900">{userItem.total_matches}</div>
                    <div className="text-xs text-gray-600">Matches</div>
                  </div>
                  <div className="text-center p-2 bg-purple-50 rounded-lg">
                    <div className="text-lg font-bold text-gray-900">
                      {userItem.total_matches > 0 ? Math.round((userItem.wins / userItem.total_matches) * 100) : 0}%
                    </div>
                    <div className="text-xs text-gray-600">Win Rate</div>
                  </div>
                </div>

                {/* Join Date */}
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="w-4 h-4 mr-2" />
                  Joined {new Date(userItem.created_at).toLocaleDateString()}
                </div>
              </div>

              {/* Actions */}
              <div className="p-4 bg-gray-50 space-y-2">
                {userItem.admin_level < 1 && userItem.role !== 'referee' && (
                  <button
                    onClick={() => {
                      setRefereeUser(userItem)
                      setShowRefereeModal(true)
                    }}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    <UserCheck className="w-4 h-4" />
                    <span>Grant Referee Access</span>
                  </button>
                )}
                {userItem.admin_level < 3 && userItem.admin_level !== 1 && userItem.role !== 'referee' && (
                  <button
                    onClick={() => setSelectedUser(userItem)}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    <UserPlus className="w-4 h-4" />
                    <span>Promote to Admin</span>
                  </button>
                )}
                {(userItem.admin_level >= 1 && userItem.role === 'referee') && (
                  <div className="text-center text-sm text-green-600">
                    <UserCheck className="w-4 h-4 mx-auto mb-1" />
                    Referee Access Granted
                  </div>
                )}
                {userItem.admin_level >= 3 && (
                  <div className="text-center text-sm text-gray-500">
                    <Crown className="w-4 h-4 mx-auto mb-1" />
                    Super Admin
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Promotion Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
            <h3 className="text-lg font-bold text-gray-900 mb-4">Promote User to Admin</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">User</label>
                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                  <Users className="w-5 h-5 text-gray-400" />
                  <div>
                    <div className="font-medium">{selectedUser.username}</div>
                    <div className="text-sm text-gray-600">{selectedUser.email}</div>
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Admin Role</label>
                <select
                  value={promotionData.role}
                  onChange={(e) => setPromotionData({...promotionData, role: e.target.value})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="admin">Platform Admin</option>
                  <option value="referee_coordinator">Referee Coordinator</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Admin Level</label>
                <select
                  value={promotionData.admin_level}
                  onChange={(e) => setPromotionData({...promotionData, admin_level: parseInt(e.target.value)})}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value={1}>Level 1 - Referee Coordinator</option>
                  <option value={2}>Level 2 - Platform Admin</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                <textarea
                  value={promotionData.notes}
                  onChange={(e) => setPromotionData({...promotionData, notes: e.target.value})}
                  placeholder="Reason for promotion..."
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => setSelectedUser(null)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handlePromoteUser}
                disabled={isPromoting}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isPromoting ? 'Promoting...' : 'Promote User'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Referee Access Modal */}
      {showRefereeModal && refereeUser && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Grant Referee Access</h3>
              <button
                onClick={() => {
                  setShowRefereeModal(false)
                  setRefereeUser(null)
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <XCircle className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-3">
                  <UserCheck className="w-8 h-8 text-green-600" />
                  <div>
                    <p className="font-medium text-green-900">Referee Access - Level 1</p>
                    <p className="text-sm text-green-700">User will gain access to the Referee Dashboard</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">User Details:</p>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{refereeUser.username}</p>
                  <p className="text-sm text-gray-600">{refereeUser.email}</p>
                  <p className="text-sm text-gray-600">
                    {refereeUser.total_matches} matches • {refereeUser.wins}W/{refereeUser.losses}L
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium text-gray-700">What this grants:</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Access to Referee Dashboard</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Ability to referee matches</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Match result submission rights</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>Referee navigation panel access</span>
                  </li>
                </ul>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={() => {
                    setShowRefereeModal(false)
                    setRefereeUser(null)
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleGrantRefereeAccess}
                  disabled={isPromoting}
                  className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
                >
                  {isPromoting ? 'Granting...' : 'Grant Access'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default AdminUserManagement
