// 🎯 UNIFIED MATCHES HUB - Discovery + Live Lobby
// Intelligent page that transitions between match browsing and live match lobby

import type React from "react"
import { useState, useEffect, useMemo, useCallback } from "react"
import {
  Trophy,
  Clock,
  Gamepad2,
  Plus,
  Shield,
  X,
  Copy,
  MessageCircle,
  Users,
  Star,
  Crown,
  Sword,
  Target,
  Heart,
  Zap,
  CheckCircle,
  Send,
  Gem,
  ArrowLeft,
  UserPlus,
  Share2,
  Timer,
  Info,
  Volume2,
  VolumeX,
  Wifi,
  WifiOff,
  LogOut,
  RefreshCw,
  Loader2
} from "lucide-react"
import { useAuth } from "../../../contexts/AuthContext"
import { matchService } from "../../../services/matchService"
import { supabase } from "../../../services/supabaseClient"
import { useNotifications } from "../../../contexts/NotificationContext"
import { bettingService } from "../../../services/bettingService"
import { GAMES } from "../../../constants"
import MatchesErrorBoundary from "../../../components/common/MatchesErrorBoundary"
import { NotificationBell } from "../../../components/notifications/SmartNotificationCenter"
import MatchCard from "./components/MatchCard"
import { favoritesService } from "../../../services/analytics"
import QuickMatchPanel from "../../../components/matches/QuickMatchPanel"
import ConfirmationDialog from "../../../components/common/ConfirmationDialog"
import { liveMatchService } from "../../../services/liveMatchService"
// Removed debug imports
// Removed unused test imports
// Removed debug imports for cleaner code

// Comprehensive interfaces for merged functionality
interface Player {
  id: string
  username: string
  mlUsername: string
  guildName?: string
  rank: 'Mythic' | 'Legend' | 'Epic' | 'Grandmaster' | 'Master'
  role: 'Tank' | 'Fighter' | 'Marksman' | 'Assassin' | 'Support' | 'Mage'
  isHost?: boolean
  isReady?: boolean
  isOnline?: boolean
  joinedAt?: string
  avatar?: string
}

interface ChatMessage {
  id: string
  sender: string
  message: string
  timestamp: string
  isReferee?: boolean
  isSystem?: boolean
}

// Use the unified Match interface from types
import { Match } from '../../../types'

// Extended interface for UI-specific properties
interface UIMatch extends Match {
  // UI-specific computed properties
  timeLeft?: string
  mapName?: string
  referee: {
    name: string
    mlId: string
    isVerified: boolean
    isOnline?: boolean
    avatar?: string
  }
  team1: Player[]
  team2: Player[]
  guildName?: string
}

interface CreateMatchForm {
  game: string
  format: '1v1' | '2v2' | '3v3' | '4v4' | '5v5'
  diamondBet: string
  rules: string[]
}

interface Filters {
  game: string
  format: string
  minDiamonds: string
  maxDiamonds: string
  verifiedOnly: boolean
  search: string
  sortBy: 'newest' | 'highest-bet' | 'soonest'
}

type ViewMode = 'discovery' | 'lobby' | 'create'

function MatchesPageContent() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()

  // Core State Management
  const [viewMode, setViewMode] = useState<ViewMode>('discovery')
  const [selectedMatch, setSelectedMatch] = useState<UIMatch | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [matches, setMatches] = useState<UIMatch[]>([])
  const [userMatches, setUserMatches] = useState<string[]>([]) // IDs of matches user has joined
  const [favoriteMatches, setFavoriteMatches] = useState<string[]>([]) // IDs of favorited matches
  const [userActiveMatch, setUserActiveMatch] = useState<UIMatch | null>(null) // User's created active match
  const [isMobile, setIsMobile] = useState(false)

  // Confirmation Dialog States (using existing naming convention)
  const [showCancelConfirm, setShowCancelConfirm] = useState(false)
  const [showJoinConfirm, setShowJoinConfirm] = useState(false)

  const [showQuickMatch, setShowQuickMatch] = useState(false)
  const [availableReferees, setAvailableReferees] = useState<number>(0)
  const [userStats, setUserStats] = useState({
    totalMatches: 0,
    wins: 0,
    losses: 0,
    winRate: 0,
    totalEarnings: 0
  })
  const [cancellingMatches, setCancellingMatches] = useState<Set<string>>(new Set())

  // Discovery Mode State
  const [filters, setFilters] = useState<Filters>({
    game: '',
    format: '',
    minDiamonds: '',
    maxDiamonds: '',
    verifiedOnly: false,
    search: '',
    sortBy: 'newest'
  })
  // Removed showFilters state - filters are always visible now

  // Create Match State
  const [createForm, setCreateForm] = useState<CreateMatchForm>({
    game: '',
    format: '5v5',
    diamondBet: '',
    rules: []
  })
  const [showCreateModal, setShowCreateModal] = useState(false)
  // Removed duplicate showJoinConfirm and showCancelConfirm - already declared above
  const [selectedMatchForAction, setSelectedMatchForAction] = useState<UIMatch | null>(null)
  const [actionLoading, setActionLoading] = useState(false)
  // Removed duplicate showSuccess state

  // Lobby Mode State
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([])
  const [newMessage, setNewMessage] = useState('')
  const [betAmount, setBetAmount] = useState('')
  const [hasBet, setHasBet] = useState(false)
  // const [showBetModal, setShowBetModal] = useState(false) // Unused for now
  const [userReady, setUserReady] = useState(false)
  const [timeLeft, setTimeLeft] = useState(300) // 5 minutes
  const [isConnected, setIsConnected] = useState(true)
  const [soundEnabled, setSoundEnabled] = useState(true)

  // Loading state
  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="w-12 h-12 text-blue-600 animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Loading matches...</p>
        </div>
      </div>
    )
  }

  // Check if user has an active match
  const checkUserActiveMatch = async () => {
    try {
      const { data: activeMatches } = await supabase
        .from('matches')
        .select('*')
        .eq('host_id', user.id)
        .in('status', ['open', 'waiting', 'full', 'ongoing', 'in_progress', 'active', 'battling', 'fighting'])
        .limit(1)

      if (activeMatches && activeMatches.length > 0) {
        const activeMatch = activeMatches[0]
        console.log('✅ Found user active match:', activeMatch.id, activeMatch.title, 'host_id:', activeMatch.host_id)
        // Transform to UIMatch format
        const uiMatch: UIMatch = {
          id: activeMatch.id,
          title: activeMatch.title || `${activeMatch.game} Match`,
          game: activeMatch.game,
          format: activeMatch.mode,
          host_id: activeMatch.host_id,
          diamondPot: activeMatch.entry_fee,
          totalPot: activeMatch.pot_amount,
          playersJoined: activeMatch.current_players,
          max_players: activeMatch.max_players,
          status: activeMatch.status,
          entry_fee: activeMatch.entry_fee,
          pot_amount: activeMatch.pot_amount,
          current_players: activeMatch.current_players,
          diamond_pot: activeMatch.entry_fee, // Add missing required property
          rules: activeMatch.rules,
          region: activeMatch.region,
          scheduled_start_time: activeMatch.scheduled_start_time,
          referee_id: activeMatch.referee_id,
          created_at: activeMatch.created_at,
          updated_at: activeMatch.updated_at,
          mode: activeMatch.mode,
          timeLeft: undefined,
          mapName: activeMatch.game,
          referee: {
            name: 'Auto-assigned',
            mlId: 'system',
            isVerified: true,
            isOnline: true
          },
          team1: [],
          team2: [],
          guildName: undefined
        }
        setUserActiveMatch(uiMatch)
      } else {
        setUserActiveMatch(null)
      }
    } catch (error) {
      console.error('Error checking user active match:', error)
      setUserActiveMatch(null)
    }
  }

  // Clear any cached data
  const clearCache = () => {
    setMatches([])
    setUserMatches([])
    setFavoriteMatches([])
    setUserActiveMatch(null)
    console.log('🧹 Cache cleared')
  }

  // Emergency cleanup of fake data
  const emergencyCleanupFakeData = async () => {
    try {
      const fakeIds = ['11111111', '22222222', '33333333', '44444444', '55555555',
                      '66666666', '77777777', '88888888', '99999999', 'aaaaaaaa']

      // Delete fake match participants first
      await supabase.from('match_participants').delete().in('match_id', fakeIds)

      // Delete fake matches
      const { error } = await supabase.from('matches').delete().in('id', fakeIds)

      if (!error) {
        console.log('🧹 Emergency cleanup completed - fake data removed')
      }
    } catch (error) {
      console.warn('⚠️ Emergency cleanup failed:', error)
    }
  }

  // 🚀 MEMOIZED: Load matches from database
  const loadMatches = useCallback(async () => {
    if (!user) return

    try {
      setIsLoading(true)
      console.log('Loading matches with filters:', filters)

      // Clear cache first to ensure fresh data
      clearCache()

      // Emergency cleanup of fake data
      await emergencyCleanupFakeData()

      // Check for user's active match first
      await checkUserActiveMatch()

      // Get matches with filters (empty string means "show all")
      console.log('🔍 Getting matches from database with filters:', filters)
      const { data: matchesData, error: matchesError } = await matchService.getMatches({
        game: filters.game || undefined,
        format: filters.format || undefined,
        minPot: filters.minDiamonds ? Number(filters.minDiamonds) : undefined,
        maxPot: filters.maxDiamonds ? Number(filters.maxDiamonds) : undefined,
        search: filters.search || undefined,
        sortBy: filters.sortBy || 'newest'
      })

      console.log('🔍 Database response:', {
        matchesCount: matchesData?.length || 0,
        error: matchesError,
        sampleMatch: matchesData?.[0] ? {
          id: matchesData[0].id?.slice(0, 8),
          title: matchesData[0].title,
          host_id: matchesData[0].host_id?.slice(0, 8),
          status: matchesData[0].status
        } : null
      })

      // Check for persistent fake data
      if (matchesData && matchesData.length > 0) {
        const fakeMatches = matchesData.filter(match =>
          ['11111111', '22222222', '33333333', '44444444', '55555555',
           '66666666', '77777777', '88888888', '99999999', 'aaaaaaaa'].includes(match.id) ||
          match.title?.toLowerCase().includes('test') ||
          match.title?.toLowerCase().includes('mock')
        )

        if (fakeMatches.length > 0) {
          console.warn('⚠️ FAKE DATA STILL EXISTS:', fakeMatches.length, 'fake matches found')
          console.warn('🔍 Fake matches:', fakeMatches.map(m => ({ id: m.id, title: m.title })))
        }
      }

      console.log('🔍 Raw matches data received:', matchesData)
      console.log('🔍 Total matches from database:', matchesData?.length || 0)
      console.log('🔍 Matches error:', matchesError)

      // Log first few matches to see structure
      if (matchesData && matchesData.length > 0) {
        console.log('🔍 Sample match structure:', matchesData[0])
        console.log('🔍 All match statuses:', matchesData.map(m => ({ id: m.id, status: m.status, game: m.game })))
      }

      if (matchesError) {
        console.error('Match loading error:', matchesError)
        addNotification({
          type: 'error',
          title: 'Failed to load matches',
          message: (matchesError as any)?.message || 'Database connection error. Please try again.'
        })
        setMatches([])
        return
      }

      // Get user's joined matches - handle errors gracefully
      try {
        const { data: userMatchesData, error: userMatchesError } = await matchService.getUserMatches(user?.id || '')

        if (userMatchesError) {
          console.warn('Could not load user matches from database:', userMatchesError)
          // Keep existing userMatches state instead of resetting to empty array
          // This prevents incorrect button states when there are temporary database issues
        } else {
          const joinedMatchIds = (userMatchesData || []).map((participation: any) => participation.match_id)
          setUserMatches(joinedMatchIds)
        }
      } catch (error) {
        console.warn('Could not load user matches due to database permissions:', error)
        // Keep existing userMatches state instead of resetting
        // Only reset if this is the first load
        if (matches.length === 0) {
          setUserMatches([])
        }
      }

      // Transform database matches to UI format with proper host/referee data
      const transformedMatches: UIMatch[] = (matchesData || []).map((match: any) => {
        // Calculate time left if scheduled
        const timeLeft = match.scheduled_start_time ?
          Math.max(0, Math.floor((new Date(match.scheduled_start_time).getTime() - Date.now()) / 60000)) + ' min' :
          undefined

        // Extract host information from joined data
        const hostInfo = match.host || {}
        const refereeInfo = match.referee || {}
        const winnerInfo = match.winner || {}

        return {
          // Core database fields
          id: match.id,
          title: match.title || `${match.game} ${match.mode}`,
          game: match.game,
          mode: match.mode,
          // game_type removed - not in database schema
          status: match.status,
          entry_fee: match.entry_fee,
          pot_amount: match.pot_amount,
          diamond_pot: match.diamond_pot || match.entry_fee,
          max_players: match.max_players,
          current_players: match.current_players,
          host_id: match.host_id,
          host_username: hostInfo.username,
          host_first_name: hostInfo.first_name,
          host_last_name: hostInfo.last_name,
          referee_id: match.referee_id,
          referee_username: refereeInfo.username,
          referee_first_name: refereeInfo.first_name,
          referee_last_name: refereeInfo.last_name,
          winner_id: match.winner_id,
          winner_username: winnerInfo.username,
          room_code: match.room_code,
          match_link: match.match_link,
          region: match.region || 'Global',
          rules: (() => {
            // Ensure rules is always an array for UI consistency
            if (!match.rules) return getDefaultRules(match.game)
            if (Array.isArray(match.rules)) return match.rules
            if (typeof match.rules === 'string') {
              return match.rules.split('\n').map((rule: string) => rule.trim()).filter((rule: string) => rule.length > 0)
            }
            return getDefaultRules(match.game)
          })(),
          scheduled_start_time: match.scheduled_start_time,
          actual_start_time: match.actual_start_time,
          actual_end_time: match.actual_end_time,
          created_at: match.created_at,
          updated_at: match.updated_at,

          // Legacy/computed fields for backward compatibility
          format: match.mode,
          diamondPot: match.entry_fee,
          totalPot: match.pot_amount,
          creator: hostInfo.username || `${hostInfo.first_name || ''} ${hostInfo.last_name || ''}`.trim() || 'Host',
          playersJoined: match.current_players,
          startTime: match.scheduled_start_time || match.created_at,

          // UI-specific computed fields
          timeLeft,
          mapName: getMapName(match.game),
          referee: getRefereeInfo(match.referee_id, refereeInfo, match.game),
          team1: [], // Will be populated when we implement team management
          team2: [], // Will be populated when we implement team management
          guildName: undefined
        }
      })

      console.log('🔍 Transformed matches:', transformedMatches.length)
      console.log('🔍 Sample transformed match:', transformedMatches[0])
      setMatches(transformedMatches)
      console.log('🔍 Matches state updated with', transformedMatches.length, 'matches')

      // Analyze matches for smart notifications (temporarily disabled)
      // if (user && transformedMatches.length > 0) {
      //   analyzeMatchesForNotifications(transformedMatches)
      // }
    } catch (error: any) {
      console.error('Error in loadMatches:', error)
      addNotification({
        type: 'error',
        title: 'Failed to load matches',
        message: error?.message || 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }, [user, filters, addNotification, matches.length])

  // Helper functions for data transformation
  const getGameConfig = (gameName: string) => {
    // Convert game name to key format (e.g., "Mobile Legends" -> "mobile-legends")
    const gameKey = gameName.toLowerCase().replace(/[:\s]+/g, '-').replace(/[^a-z0-9-]/g, '')

    // Find matching game config
    const gameConfig = Object.entries(GAMES).find(([key, config]) =>
      key === gameKey ||
      config.name.toLowerCase() === gameName.toLowerCase() ||
      config.name.toLowerCase().includes(gameName.toLowerCase())
    )

    return gameConfig ? gameConfig[1] : null
  }

  const getDefaultRules = (game: string): string[] => {
    const gameConfig = getGameConfig(game)
    return gameConfig?.defaultRules || [
      'No cheating or exploits allowed',
      'Respect all players and referee',
      'Follow referee instructions',
      'No toxic behavior',
      `Follow ${game} official rules`
    ]
  }

  const getMapName = (game: string): string => {
    // Use game-specific default maps
    switch (game.toLowerCase()) {
      case 'mobile legends':
      case 'mobile legends: bang bang':
        return 'Land of Dawn'
      case 'valorant':
        return 'Haven'
      case 'wild rift':
      case 'league of legends: wild rift':
        return 'Summoner\'s Rift'
      case 'cs:go':
      case 'cs2':
      case 'counter-strike':
        return 'Dust II'
      case 'dota 2':
        return 'Ancient Battlefield'
      case 'call of duty':
      case 'call of duty mobile':
        return 'Nuketown'
      default:
        return 'Default Map'
    }
  }

  const getRefereeInfo = (refereeId?: string, refereeData?: any, matchGame?: string) => {
    // Use actual referee data if available from the match query
    if (refereeId && refereeData) {
      // Get the appropriate game ID based on the match game
      let gameId = ''
      let gameIdLabel = ''

      switch (matchGame?.toLowerCase()) {
        case 'mobile legends':
        case 'mobile legends: bang bang':
          gameId = refereeData.mlbb_id || ''
          gameIdLabel = 'ML ID'
          break
        case 'valorant':
          gameId = refereeData.valorant_id || ''
          gameIdLabel = 'Valorant ID'
          break
        case 'call of duty':
        case 'cod':
          gameId = refereeData.cod_id || ''
          gameIdLabel = 'COD ID'
          break
        case 'dota 2':
          gameId = refereeData.dota_id || ''
          gameIdLabel = 'Dota ID'
          break
        default:
          gameId = refereeData.mlbb_id || refereeData.valorant_id || refereeData.cod_id || ''
          gameIdLabel = 'Game ID'
      }

      if (gameId) {
        return {
          name: refereeData.username || `${refereeData.first_name || ''} ${refereeData.last_name || ''}`.trim() || 'Referee',
          mlId: gameId, // Game-specific ID from database
          isVerified: true,
          isOnline: refereeData.is_online || false,
          avatar: '🛡️'
        }
      } else {
        return {
          name: refereeData.username || 'Assigned Referee',
          mlId: `${gameIdLabel}_REQUIRED`, // Referee needs to add game-specific ID
          isVerified: true,
          isOnline: false,
          avatar: '⚠️'
        }
      }
    }

    // System auto-referee as fallback when no referee assigned
    return {
      name: 'Auto-Referee',
      mlId: 'PENDING_ASSIGNMENT', // Indicates referee not yet assigned
      isVerified: false,
      isOnline: false,
      avatar: '⏳'
    }
  }





  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])



  // Load matches and user stats on component mount and when filters change
  useEffect(() => {
    if (user?.id) {
      loadMatches()
      loadUserStats()
      loadFavoriteMatches()
      loadRefereeCount()
    }
  }, [user?.id, filters])

  // Load user statistics
  const loadUserStats = async () => {
    if (!user?.id) return

    try {
      const { data } = await matchService.getUserStats(user.id)
      if (data) {
        setUserStats(data)
      }
    } catch (error) {
      console.log('Could not load user stats:', error)
    }
  }

  // Load available referee count with fallback
  const loadRefereeCount = async () => {
    try {
      const count = await matchService.getAvailableRefereeCount()
      setAvailableReferees(count)
    } catch (error) {
      console.error('Error loading referee count:', error)
      // Set a reasonable fallback number instead of 0
      setAvailableReferees(3) // Show 3 as default to indicate referees are available
    }
  }

  // Set up real-time subscriptions for live updates
  useEffect(() => {
    if (!user?.id) return

    console.log('Setting up real-time match subscriptions')
    let isSubscriptionActive = true

    // Subscribe to all matches changes with proper error handling
    const matchesSubscription = matchService.subscribeToMatches(async (updatedMatches) => {
      // Check if subscription is still active to prevent state updates after cleanup
      if (!isSubscriptionActive) return

      try {
        console.log('Real-time matches update received:', updatedMatches?.length || 0, 'matches')

        // Validate data before processing
        if (!Array.isArray(updatedMatches)) {
          console.warn('Invalid matches data received from real-time subscription')
          return
        }

        // Get user's joined matches for proper state
        try {
          const { data: userMatchesData, error: userMatchesError } = await matchService.getUserMatches(user.id)

          if (userMatchesError) {
            console.warn('Could not update user matches in real-time:', userMatchesError)
            // Don't update userMatches state on error to prevent incorrect button states
          } else {
            const joinedMatchIds = (userMatchesData || []).map((participation: any) => participation.match_id)
            if (isSubscriptionActive) {
              setUserMatches(joinedMatchIds)
            }
          }
        } catch (error) {
          console.warn('Could not update user matches in real-time:', error)
          // Don't reset userMatches on error
        }

        // Check for important match events to notify user
        const previousMatches = matches
        const newMatches = updatedMatches.filter((match: any) =>
          !previousMatches.find(prev => prev.id === match.id)
        )

        // Notify about new matches that user might be interested in
        newMatches.forEach((match: any) => {
          if (match.status === 'open' && match.host_id !== user.id) {
            // Only notify about matches in user's preferred games
            const userPreferredGames = ['Mobile Legends: Bang Bang', 'League of Legends', 'Valorant']
            if (userPreferredGames.includes(match.game)) {
              addNotification({
                type: 'info',
                title: 'New Match Available',
                message: `${match.game} match created with ${match.entry_fee} diamonds entry fee`
              })
            }
          }
        })

        // Check for matches where user is assigned as referee
        updatedMatches.forEach((match: any) => {
          if (match.referee_id === user.id) {
            const prevMatch = previousMatches.find(prev => prev.id === match.id)
            if (!prevMatch || prevMatch.status !== match.status) {
              if (match.status === 'ongoing') {
                addNotification({
                  type: 'success',
                  title: 'Match Started',
                  message: `${match.game} match is now ongoing and needs your attention as referee`
                })
              }
            }
          }
        })

        // Transform and update matches
        const transformedMatches: UIMatch[] = updatedMatches.map((match: any) => ({
          // Base Match properties
          id: match.id,
          title: `${match.game} ${match.mode}`,
          game: match.game,
          mode: match.mode,
          entry_fee: match.entry_fee,
          pot_amount: match.pot_amount,
          diamond_pot: match.entry_fee,
          max_players: match.max_players,
          current_players: match.current_players,
          host_id: match.host_id,
          referee_id: match.referee_id,
          winner_id: match.winner_id,
          status: match.status as "waiting" | "open" | "full" | "ongoing" | "in_progress" | "completed" | "cancelled" | "disputed",
          rules: (() => {
            if (!match.rules) return getDefaultRules(match.game)
            if (Array.isArray(match.rules)) return match.rules
            if (typeof match.rules === 'string') return match.rules.split('\n').filter((rule: string) => rule.trim())
            return getDefaultRules(match.game)
          })(),
          room_code: match.room_code,
          match_link: match.match_link,
          region: match.region || 'Global',
          referee_notes: match.referee_notes,
          proof_required: match.proof_required,
          scheduled_start_time: match.scheduled_start_time,
          actual_start_time: match.actual_start_time,
          actual_end_time: match.actual_end_time,
          match_duration_minutes: match.match_duration_minutes,
          dispute_count: match.dispute_count,
          created_at: match.created_at,
          updated_at: match.updated_at,

          // UI-specific properties
          format: match.mode as '1v1' | '2v2' | '3v3' | '4v4' | '5v5',
          diamondPot: match.entry_fee,
          totalPot: match.pot_amount,
          creator: match.host_username || 'Host',
          playersJoined: match.current_players,
          maxPlayers: match.max_players,
          timeLeft: match.scheduled_start_time ?
            Math.max(0, Math.floor((new Date(match.scheduled_start_time).getTime() - Date.now()) / 60000)) + ' min' :
            undefined,
          startTime: match.scheduled_start_time || match.created_at,
          matchType: match.mode,
          gameMode: match.mode,
          mapName: getMapName(match.game),
          referee: getRefereeInfo(match.referee_id, null, match.game),
          team1: [],
          team2: [],
          roomCode: match.room_code || undefined,
          matchLink: match.match_link || undefined
        }))

        // Only update state if subscription is still active
        if (isSubscriptionActive) {
          setMatches(transformedMatches)
        }

      } catch (error) {
        console.error('Error processing real-time matches update:', error)
        // Don't show error notification for real-time updates to avoid spam
      }
    })

    // Check if subscription was successful
    if (!matchesSubscription) {
      console.error('Failed to create matches subscription')
      return
    }

    // Set up real-time user balance updates
    const userSubscription = supabase
      .channel(`user-${user.id}`)
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'users', filter: `id=eq.${user.id}` },
        (payload) => {
          if (!isSubscriptionActive) return
          console.log('Real-time user balance update received:', payload.new)
          // The AuthContext will handle the user update automatically
        }
      )
      .subscribe()

    // Set up live match event notifications
    const liveMatchUnsubscribe = liveMatchService.subscribeToAllMatches((event) => {
      if (!isSubscriptionActive) return

      // Notify user about relevant match events
      switch (event.type) {
        case 'referee_assigned':
          if (event.data.referee_id === user.id) {
            addNotification({
              type: 'info',
              title: 'Referee Assignment',
              message: `You have been assigned to referee a ${event.data.game} match`
            })
          }
          break

        case 'match_started':
          // Notify if user is participant or referee
          if (userMatches.includes(event.matchId) || event.data.referee_id === user.id) {
            addNotification({
              type: 'success',
              title: 'Match Started',
              message: `${event.data.game} match has started`
            })
          }
          break

        case 'match_completed':
          if (userMatches.includes(event.matchId)) {
            addNotification({
              type: 'info',
              title: 'Match Completed',
              message: `${event.data.game} match has been completed`
            })
          }
          break
      }
    })

    // Cleanup subscription on unmount
    return () => {
      console.log('Cleaning up real-time subscriptions')
      isSubscriptionActive = false
      if (matchesSubscription) {
        try {
          supabase.removeChannel(matchesSubscription)
        } catch (error) {
          console.error('Error cleaning up matches subscription:', error)
        }
      }
      if (userSubscription) {
        try {
          supabase.removeChannel(userSubscription)
        } catch (error) {
          console.error('Error cleaning up user subscription:', error)
        }
      }
      if (liveMatchUnsubscribe) {
        try {
          liveMatchUnsubscribe()
        } catch (error) {
          console.error('Error cleaning up live match subscription:', error)
        }
      }
    }
  }, [user?.id])

  // Sample data for development - will be replaced with real data
  // Using real database data now, mockMatches commented out
  /*
  const mockMatches: Match[] = [
    {
      id: "match_001",
      title: "Epic Ranked Showdown",
      game: "Mobile Legends",
      format: "5v5",
      diamondPot: 100,
      totalPot: 1000,
      creator: "ProGamer123",
      status: "Open",
      playersJoined: 8,
      maxPlayers: 10,
      timeLeft: "15 min",
      startTime: "2024-01-15T20:00:00Z",
      matchType: "Ranked Classic",
      gameMode: "Classic",
      mapName: "Land of Dawn",
      rules: [
        "No cheating or exploits allowed",
        "Respect all players and referee",
        "Follow referee instructions",
        "Use designated heroes only",
        "No toxic behavior"
      ],
      referee: {
        name: "RefMaster_Pro",
        mlId: "REF_12345_VERIFIED",
        isVerified: true,
        isOnline: true,
        avatar: "🛡️"
      },
      team1: [
        { id: "1", username: "ProGamer123", mlUsername: "MLPro_123", guildName: "Elite Squad", rank: "Mythic", role: "Tank", isHost: true, isReady: true, isOnline: true, joinedAt: "19:30" },
        { id: "2", username: "SkillPlayer", mlUsername: "Skill_999", guildName: "Elite Squad", rank: "Legend", role: "Fighter", isReady: true, isOnline: true, joinedAt: "19:32" },
        { id: "3", username: "TopMarksman", mlUsername: "TopMM_456", guildName: "Elite Squad", rank: "Epic", role: "Marksman", isReady: false, isOnline: true, joinedAt: "19:35" },
        { id: "4", username: "AssassinPro", mlUsername: "Assassin_789", guildName: "Elite Squad", rank: "Legend", role: "Assassin", isReady: true, isOnline: false, joinedAt: "19:33" }
      ],
      team2: [
        { id: "5", username: "SupportKing", mlUsername: "Support_321", guildName: "Warriors", rank: "Mythic", role: "Support", isReady: true, isOnline: true, joinedAt: "19:31" },
        { id: "6", username: "TankMaster", mlUsername: "Tank_654", guildName: "Warriors", rank: "Legend", role: "Tank", isReady: true, isOnline: true, joinedAt: "19:34" },
        { id: "7", username: "MidLaner", mlUsername: "Mid_987", guildName: "Warriors", rank: "Epic", role: "Mage", isReady: false, isOnline: true, joinedAt: "19:38" },
        { id: "8", username: user?.username || "You", mlUsername: "YourML_ID", guildName: "Warriors", rank: "Epic", role: "Support", isReady: false, isOnline: true, joinedAt: "19:40" }
      ],
      guildName: "Elite Squad",
      region: "Southeast Asia",
      roomCode: "ROOM123",
      matchLink: "https://gambets.com/match/match_001"
    },
    {
      id: "match_002",
      title: "1v1 Tank Showdown",
      game: "Mobile Legends",
      format: "1v1",
      diamondPot: 50,
      totalPot: 100,
      creator: "TankMaster",
      status: "Open",
      playersJoined: 1,
      maxPlayers: 2,
      timeLeft: "30 min",
      startTime: "2024-01-15T21:00:00Z",
      matchType: "Tank vs Tank",
      gameMode: "Custom",
      mapName: "Land of Dawn",
      rules: ["Tank heroes only", "No items restrictions", "Best of 3"],
      referee: {
        name: "TankRef_Pro",
        mlId: "TANK_REF_001",
        isVerified: true,
        isOnline: true
      },
      team1: [
        { id: "1", username: "TankMaster", mlUsername: "Tank_Pro_999", guildName: "Tank Legion", rank: "Mythic", role: "Tank", isHost: true, isReady: true, isOnline: true, joinedAt: "19:45" }
      ],
      team2: [],
      guildName: "Tank Legion",
      region: "Southeast Asia",
      roomCode: "TANK01",
      matchLink: "https://gambets.com/match/match_002"
    },
    {
      id: "match_003",
      title: "Marksman Duel",
      game: "Mobile Legends",
      format: "1v1",
      diamondPot: 75,
      totalPot: 150,
      creator: "MMPro",
      status: "Full",
      playersJoined: 2,
      maxPlayers: 2,
      timeLeft: "5 min",
      startTime: "2024-01-15T19:55:00Z",
      matchType: "Marksman vs Marksman",
      gameMode: "Custom",
      mapName: "Land of Dawn",
      rules: ["Marksman heroes only", "No support items", "First to 3 kills wins"],
      referee: {
        name: "MM_Referee",
        mlId: "MM_REF_789",
        isVerified: true,
        isOnline: true
      },
      team1: [
        { id: "1", username: "MMPro", mlUsername: "MM_Pro_456", guildName: "Shooters", rank: "Legend", role: "Marksman", isHost: true, isReady: true, isOnline: true, joinedAt: "19:40" }
      ],
      team2: [
        { id: "2", username: "ADCKing", mlUsername: "ADC_King_123", guildName: "Snipers", rank: "Epic", role: "Marksman", isReady: true, isOnline: true, joinedAt: "19:42" }
      ],
      guildName: "Shooters",
      region: "Southeast Asia",
      roomCode: "MM123",
      matchLink: "https://gambets.com/match/match_003"
    }
  ]
  */

  // Generate initial chat messages for match lobby
  const generateInitialChatMessages = (match: UIMatch): ChatMessage[] => {
    const now = new Date()
    const formatTime = (offset: number) => {
      const time = new Date(now.getTime() + offset * 60000)
      return time.toLocaleTimeString('en-US', { hour12: false, hour: '2-digit', minute: '2-digit' })
    }

    return [
      {
        id: '1',
        sender: 'System',
        message: `${match.game} ${match.format} match lobby created. Waiting for players...`,
        timestamp: formatTime(-5),
        isSystem: true
      },
      {
        id: '2',
        sender: match.referee?.name || 'Referee',
        message: `Welcome to the match! I'm your referee. ${match.referee?.mlId && !match.referee.mlId.includes('PENDING') && !match.referee.mlId.includes('REQUIRED') ? `Please add me: ${match.referee?.mlId}` : 'I will be assigned shortly and provide my game ID.'}`,
        timestamp: formatTime(-2),
        isReferee: true
      }
    ]
  }

  // Analyze matches for smart notifications
  // const analyzeMatchesForNotifications = async (matches: Match[]) => {
  //   if (!user) return

  //   try {
  //     for (const match of matches) {
  //       // Skip matches user has already joined
  //       if (userMatches.includes(match.id)) continue

  //       // Skip matches that are not open
  //       if (match.status !== 'open') continue

  //       // Get real analytics data for the match
  //       const analytics = await analyticsService.calculateMatchAnalytics({
  //         id: match.id,
  //         game: match.game,
  //         mode: match.format,
  //         entry_fee: match.diamondPot,
  //         pot_amount: match.totalPot,
  //         current_players: match.playersJoined,
  //         max_players: match.max_players,
  //         region: match.region
  //       }, user.id)

  //       // Analyze match opportunity with real data
  //       const opportunity = await smartNotificationService.analyzeMatchForUser(user, {
  //         id: match.id,
  //         game: match.game,
  //         mode: match.format,
  //         entry_fee: match.diamondPot,
  //         avg_skill_level: analytics.avg_skill_level,
  //         current_players: match.playersJoined,
  //         max_players: match.max_players
  //       })

  //       if (opportunity) {
  //         // Create and send perfect match notification
  //         const notification = await smartNotificationService.createPerfectMatchNotification(user, opportunity)
  //         await smartNotificationService.sendNotification(notification)
  //       }
  //     }
  //   } catch (error) {
  //     console.error('Error analyzing matches for notifications:', error)
  //   }
  // }

  // Load user's favorite matches from database
  const loadFavoriteMatches = async () => {
    if (!user?.id) return

    try {
      const favorites = await favoritesService.getUserFavoriteMatches(user.id)
      setFavoriteMatches(favorites)
    } catch (error) {
      console.error('Error loading favorite matches:', error)
    }
  }

  // 🚀 MEMOIZED: Handle favorite/unfavorite match with database
  const handleFavoriteMatch = useCallback(async (matchId: string) => {
    if (!user?.id) return

    try {
      const isFavorited = favoriteMatches.includes(matchId)
      let success = false

      if (isFavorited) {
        success = await favoritesService.removeFromFavorites(user.id, matchId)
        if (success) {
          setFavoriteMatches(prev => prev.filter(id => id !== matchId))
          addNotification({
            type: 'info',
            title: 'Removed from Favorites',
            message: 'Match removed from your favorites'
          })
        }
      } else {
        success = await favoritesService.addToFavorites(user.id, matchId)
        if (success) {
          setFavoriteMatches(prev => [...prev, matchId])
          addNotification({
            type: 'success',
            title: 'Added to Favorites',
            message: 'Match added to your favorites'
          })
        }
      }

      if (!success) {
        addNotification({
          type: 'error',
          title: 'Error',
          message: 'Failed to update favorites. Please try again.'
        })
      }
    } catch (error) {
      console.error('Error handling favorite:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to update favorites. Please try again.'
      })
    }
  }, [user?.id, favoriteMatches, addNotification])

  // 🚀 MEMOIZED: Handle share match
  const handleShareMatch = useCallback(async (matchId: string) => {
    const match = matches.find(m => m.id === matchId)
    if (!match) return

    const shareData = {
      title: `Join my ${match.game} match on Gambets!`,
      text: `${match.format} match with ${match.diamondPot}💎 entry fee. ${(match.max_players || 0) - (match.playersJoined || 0)} spots left!`,
      url: `${window.location.origin}/matches?highlight=${matchId}`
    }

    try {
      if (navigator.share && isMobile) {
        await navigator.share(shareData)
      } else {
        // Fallback to clipboard
        await navigator.clipboard.writeText(shareData.url)
        addNotification({
          type: 'success',
          title: 'Link Copied!',
          message: 'Match link copied to clipboard'
        })
      }
    } catch (error) {
      console.error('Error sharing match:', error)
      addNotification({
        type: 'error',
        title: 'Share Failed',
        message: 'Could not share match link'
      })
    }
  }, [matches, addNotification])

  // Game-specific configurations
  const gameConfigs = {
    "Mobile Legends": {
      formats: ["1v1", "2v2", "3v3", "4v4", "5v5"]
    },
    "Valorant": {
      formats: ["1v1", "2v2", "3v3", "4v4", "5v5"]
    },
    "Call of Duty": {
      formats: ["1v1", "2v2", "3v3", "4v4", "5v5"],
      matchTypes: {
        "1v1": ["Sniper Only", "Assault Rifle", "SMG Only", "Pistol Only", "Any Weapon"],
        "2v2": ["Team Deathmatch", "Search & Destroy", "Domination", "Hardpoint"],
        "3v3": ["Team Deathmatch", "Search & Destroy", "Gun Game", "Free for All"],
        "4v4": ["Team Deathmatch", "Domination", "Hardpoint", "Search & Destroy"],
        "5v5": ["Team Deathmatch", "Search & Destroy", "Domination", "Hardpoint", "Battle Royale"]
      }
    },
    "Wild Rift": {
      formats: ["1v1", "2v2", "5v5"],
      matchTypes: {
        "1v1": ["Mid Lane Only", "ADC vs ADC", "Support vs Support", "Jungle vs Jungle", "Top Lane Only"],
        "2v2": ["Bot Lane Duo", "Mid + Jungle", "Top + Support", "Mixed Roles"],
        "5v5": ["Ranked", "Normal", "ARAM", "Custom Game", "Draft Pick"]
      }
    },
    "Dota 2": {
      formats: ["1v1", "2v2", "5v5"],
      matchTypes: {
        "1v1": ["Mid Only", "Carry vs Carry", "Support vs Support", "Offlaner vs Offlaner", "Any Hero"],
        "2v2": ["Carry + Support", "Mid + Roamer", "Offlane Duo", "Mixed Roles"],
        "5v5": ["All Pick", "Captain's Mode", "Random Draft", "Single Draft", "Turbo Mode"]
      }
    },
    "Honor of Kings": {
      formats: ["1v1", "2v2", "3v3", "5v5"],
      matchTypes: {
        "1v1": ["Tank vs Tank", "Fighter vs Fighter", "Marksman vs Marksman", "Assassin vs Assassin", "Mage vs Mage", "Support vs Support"],
        "2v2": ["Tank + Support", "Fighter + Marksman", "Mixed Roles", "Same Role Duo"],
        "3v3": ["3v3 Arena", "Ranked 3v3", "Custom Rules", "Mixed Roles"],
        "5v5": ["Ranked Classic", "Draft Pick", "All Random", "Peak Match", "Mirror Match"]
      }
    }
  }

  // Get available formats for selected game
  const getAvailableFormats = (game: string) => {
    return gameConfigs[game as keyof typeof gameConfigs]?.formats || ["1v1", "2v2", "5v5"]
  }

  // Match types function removed - no longer needed

  // Constants
  const games = ["Mobile Legends", "Call of Duty", "Valorant", "Wild Rift", "Dota 2", "Honor of Kings"]

  // Game icons
  const getGameIcon = (game: string) => {
    switch (game) {
      case 'Mobile Legends': return '🏆'
      case 'Call of Duty': return '🔫'
      case 'Valorant': return '🎯'
      case 'Wild Rift': return '⚔️'
      case 'Dota 2': return '🛡️'
      case 'Honor of Kings': return '👑'
      default: return '🎮'
    }
  }

  // Utility functions
  const getRankIcon = (rank: string) => {
    switch (rank) {
      case 'Mythic': return <Crown className="w-4 h-4 text-red-500" />
      case 'Legend': return <Star className="w-4 h-4 text-purple-500" />
      case 'Epic': return <Gem className="w-4 h-4 text-blue-500" />
      case 'Grandmaster': return <Trophy className="w-4 h-4 text-orange-500" />
      case 'Master': return <Shield className="w-4 h-4 text-green-500" />
      default: return <Shield className="w-4 h-4 text-gray-500" />
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'Tank': return <Shield className="w-4 h-4 text-blue-600" />
      case 'Fighter': return <Sword className="w-4 h-4 text-red-600" />
      case 'Marksman': return <Target className="w-4 h-4 text-green-600" />
      case 'Assassin': return <Zap className="w-4 h-4 text-purple-600" />
      case 'Support': return <Heart className="w-4 h-4 text-pink-600" />
      case 'Mage': return <Star className="w-4 h-4 text-indigo-600" />
      default: return <Users className="w-4 h-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'open': return 'bg-green-100 text-green-700 border-green-200'
      case 'full': return 'bg-yellow-100 text-yellow-700 border-yellow-200'
      case 'ongoing': return 'bg-blue-100 text-blue-700 border-blue-200'
      case 'waiting': return 'bg-orange-100 text-orange-700 border-orange-200'
      case 'completed': return 'bg-gray-100 text-gray-700 border-gray-200'
      case 'cancelled': return 'bg-red-100 text-red-700 border-red-200'
      default: return 'bg-gray-100 text-gray-700 border-gray-200'
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      console.log('Copied to clipboard:', text)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getReadyPlayersCount = (match: UIMatch) => {
    const team1Ready = match.team1.filter((p: any) => p.isReady).length
    const team2Ready = match.team2.filter((p: any) => p.isReady).length
    return team1Ready + team2Ready
  }

  const getTotalPlayersCount = (match: UIMatch) => {
    return match.team1.length + match.team2.length
  }

  const handleCreateFormChange = (field: string, value: string) => {
    setCreateForm((prev) => {
      const newForm = { ...prev, [field]: value }

      // Reset dependent fields when game changes
      if (field === 'game') {
        newForm.format = '5v5' // Default format
      }

      return newForm
    })
  }

  // const handleFilterChange = (field: string, value: string) => {
  //   setFilters((prev) => ({
  //     ...prev,
  //     [field]: value,
  //   }))
  // } // Commented out - not used yet

  const handleCreateMatch = () => {
    // Check if user has an active match
    if (userActiveMatch) {
      addNotification({
        type: 'error',
        title: 'Cannot Create Match',
        message: `You already have an active match: "${userActiveMatch.title}". Please cancel it first.`
      })
      return
    }

    setShowCreateModal(true)
  }

  const handleSubmitCreateMatch = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validation
    if (!createForm.game || !createForm.format || !createForm.diamondBet) {
      addNotification({
        type: 'error',
        title: 'Validation Error',
        message: 'Please fill in all required fields (Game, Format, and Diamond Bet)'
      })
      return
    }

    const betAmount = parseInt(createForm.diamondBet)
    if (betAmount < 1) {
      addNotification({
        type: 'error',
        title: 'Validation Error',
        message: 'Bet amount must be at least 1 diamond'
      })
      return
    }

    if (betAmount > (user?.diamond_balance || 0)) {
      addNotification({
        type: 'error',
        title: 'Insufficient Balance',
        message: `You have ${user?.diamond_balance || 0} diamonds available.`
      })
      return
    }

    if (!user?.id) {
      addNotification({
        type: 'error',
        title: 'Authentication Error',
        message: 'Please log in to create a match'
      })
      return
    }

    try {
      setIsLoading(true)
      console.log('Creating match with form data:', createForm)

      // Calculate max players based on format
      const maxPlayers = createForm.format === '1v1' ? 2 :
                        createForm.format === '2v2' ? 4 :
                        createForm.format === '3v3' ? 6 :
                        createForm.format === '4v4' ? 8 : 10

      const matchData = {
        title: `${createForm.game} ${createForm.format} Custom Fight`,
        game: createForm.game,
        mode: createForm.format,
        pot_amount: betAmount * maxPlayers, // Total pot (e.g., 10 players × 100 = 1000 diamonds)
        entry_fee: betAmount, // Individual entry fee (e.g., 100 diamonds per player)
        diamond_pot: betAmount * maxPlayers, // Total prize pool for display
        max_players: maxPlayers,
        current_players: 1, // Host joins automatically
        host_id: user.id,
        status: 'open' as const, // Changed from 'upcoming' to 'open' to match valid statuses
        rules: createForm.rules.join('\n'),
        region: 'Global', // Default region
        proof_required: true,
        scheduled_start_time: new Date().toISOString()
        // Removed invalid fields: max_participants, requirements, is_tournament
        // PAYMENT STRUCTURE (Team vs Team):
        // - Team Blue: 5 players × 100 = 500 diamonds
        // - Team Red: 5 players × 100 = 500 diamonds
        // - Total Prize Pool: 1000 diamonds
        // - Referee Fee: 2.5% of total pot = 25 diamonds
        // - Winners Share: 975 diamonds (195 each)
        // - Platform Fee: Minimal processing fee
      }

      console.log('🔍 Creating match with data:', matchData)

      // Create match in database
      console.log('🚀 Calling matchService.createMatch...')
      const { data: createdMatch, error } = await matchService.createMatch(matchData)

      if (error) {
        console.error('❌ Match creation failed:', error)
        console.error('❌ Error details:', {
          message: (error as any)?.message,
          code: (error as any)?.code,
          details: (error as any)?.details,
          hint: (error as any)?.hint
        })

        // Enhanced error handling with specific error messages
        let errorMessage = 'Please check your diamond balance and try again'
        let errorTitle = 'Failed to create match'

        const errorObj = error as any

        // Handle Supabase/PostgreSQL errors
        if (errorObj.code) {
          switch (errorObj.code) {
            case '23505': // Unique constraint violation
              errorMessage = 'You already have an active match. Please cancel it first.'
              errorTitle = 'Active Match Found'
              break
            case '23503': // Foreign key violation
              errorMessage = 'Invalid user or game data. Please refresh and try again.'
              break
            case '42703': // Undefined column
              errorMessage = 'Database schema error. Please contact support.'
              errorTitle = 'System Error'
              break
            case '23514': // Check constraint violation
              errorMessage = 'Invalid match data. Please check your inputs.'
              break
            case '42P01': // Undefined table
              errorMessage = 'Database configuration error. Please contact support.'
              errorTitle = 'System Error'
              break
            default:
              if (errorObj.message?.includes('insufficient')) {
                errorMessage = 'Insufficient diamond balance to create this match.'
              } else if (errorObj.message?.includes('already has an active match')) {
                errorMessage = errorObj.message
                errorTitle = 'Active Match Found'
              } else {
                errorMessage = errorObj.message || 'Database error occurred'
              }
          }
        }
        // Handle custom application errors
        else if (errorObj.message) {
          errorMessage = errorObj.message
          if (errorObj.message.includes('already has an active match')) {
            errorTitle = 'Active Match Found'
          } else if (errorObj.message.includes('Insufficient')) {
            errorTitle = 'Insufficient Balance'
          }
        }

        addNotification({
          type: 'error',
          title: errorTitle,
          message: errorMessage
        })
        return
      }

      console.log('✅ Match created successfully:', createdMatch)

      // Close modal and reset form immediately
      setShowCreateModal(false)
      setCreateForm({
        game: '',
        format: '5v5',
        diamondBet: '',
        rules: []
      })

      // Show success notification
      addNotification({
        type: 'success',
        title: '🎉 Match Created!',
        message: `Your ${createForm.game} ${createForm.format} match is now live!`
      })

      // Add the new match to local state immediately (don't wait for reload)
      if (createdMatch) {
        console.log('🎮 Created match data:', {
          id: createdMatch.id,
          host_id: createdMatch.host_id,
          userId: user.id,
          title: createdMatch.title
        })

        const newUIMatch: UIMatch = {
          id: createdMatch.id,
          title: createdMatch.title || `${createdMatch.game} Match`,
          game: createdMatch.game,
          mode: createdMatch.mode,
          status: createdMatch.status,
          entry_fee: createdMatch.entry_fee,
          pot_amount: createdMatch.pot_amount,
          diamond_pot: createdMatch.entry_fee,
          max_players: createdMatch.max_players,
          current_players: 1,
          host_id: createdMatch.host_id,
          rules: createdMatch.rules || [],
          region: createdMatch.region || 'Global',
          scheduled_start_time: createdMatch.scheduled_start_time,
          referee_id: createdMatch.referee_id,
          created_at: createdMatch.created_at,
          updated_at: createdMatch.updated_at,

          // UI-specific computed fields
          timeLeft: undefined,
          mapName: createdMatch.game,
          referee: {
            name: 'Auto-assigned',
            mlId: 'PENDING_ASSIGNMENT',
            isVerified: false,
            isOnline: false,
            avatar: '⏳'
          },
          team1: [],
          team2: []
        }

        // Add to matches state immediately
        setMatches(prev => [newUIMatch, ...prev])
        console.log('✅ New match added to local state')
      }

      // Also reload matches to ensure consistency
      setTimeout(() => {
        loadMatches()
        console.log('🔄 Matches reloaded for consistency')
      }, 1000)

    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Failed to create match',
        message: (error as any)?.message || 'Unknown error occurred'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Effects
  useEffect(() => {
    if (viewMode === 'lobby' && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000)
      return () => clearTimeout(timer)
    }
  }, [timeLeft, viewMode])

  useEffect(() => {
    const interval = setInterval(() => {
      setIsConnected(Math.random() > 0.1) // 90% uptime simulation
    }, 5000)
    return () => clearInterval(interval)
  }, [])

  // Handler functions
  const cancelMatchHandler = async (match: UIMatch, event?: React.MouseEvent) => {
    if (event) event.stopPropagation() // Prevent triggering join match

    if (!user?.id) {
      addNotification({
        type: 'error',
        title: 'Authentication Error',
        message: 'Please log in to cancel matches'
      })
      return
    }

    // Prevent multiple cancellations of the same match
    if (cancellingMatches.has(match.id)) {
      addNotification({
        type: 'warning',
        title: 'Already Cancelling',
        message: 'This match is already being cancelled'
      })
      return
    }

    // Check if match is already cancelled
    if (match.status === 'cancelled') {
      addNotification({
        type: 'warning',
        title: 'Already Cancelled',
        message: 'This match has already been cancelled'
      })
      return
    }

    // Show confirmation dialog
    setSelectedMatchForAction(match as any)
    setShowCancelConfirm(true)
  }

  const confirmCancelMatch = async () => {
    if (!selectedMatchForAction || !user?.id) return

    try {
      setActionLoading(true)
      // Mark this match as being cancelled
      setCancellingMatches(prev => new Set(prev).add(selectedMatchForAction.id))

      console.log('🔍 Cancelling match:', selectedMatchForAction.id)
      const { error } = await matchService.cancelMatch(selectedMatchForAction.id, user.id)

      if (error) {
        console.error('❌ Cancel match failed:', error)
        addNotification({
          type: 'error',
          title: 'Failed to cancel match',
          message: (error as any)?.message || 'Unknown error occurred'
        })
        return
      }

      console.log('✅ Match cancelled successfully in database')

      // Success notification
      addNotification({
        type: 'success',
        title: '✅ Match Cancelled!',
        message: `Match cancelled successfully. ${selectedMatchForAction.entry_fee || selectedMatchForAction.diamondPot} diamonds refunded.`
      })

      // IMMEDIATELY REMOVE the match from local state (don't just mark as cancelled)
      setMatches(prevMatches => {
        const filtered = prevMatches.filter(m => m.id !== selectedMatchForAction.id)
        console.log('🔍 Removed match from local state. Before:', prevMatches.length, 'After:', filtered.length)
        return filtered
      })

      // Remove from user matches
      setUserMatches(prev => prev.filter(id => id !== selectedMatchForAction.id))

      // Clear active match if this was the user's active match
      if (userActiveMatch && userActiveMatch.id === selectedMatchForAction.id) {
        setUserActiveMatch(null)
      }

      console.log('✅ Match removed from local state immediately')

      // Close confirmation dialog
      setShowCancelConfirm(false)
      setSelectedMatchForAction(null)

    } catch (error: any) {
      console.error('Cancel match error:', error)
      addNotification({
        type: 'error',
        title: 'Cancel Failed',
        message: error.message || 'Failed to cancel match'
      })
    } finally {
      setActionLoading(false)
      // Remove from cancelling set
      setCancellingMatches(prev => {
        const newSet = new Set(prev)
        newSet.delete(selectedMatchForAction.id)
        return newSet
      })
    }
  }

  // 🚀 MEMOIZED: Handle join match
  const handleJoinMatch = useCallback(async (match: UIMatch) => {
    if (!user?.id) {
      addNotification({
        type: 'error',
        title: 'Authentication Error',
        message: 'Please log in to join matches'
      })
      return
    }

    // Validate match ID
    if (!match?.id) {
      addNotification({
        type: 'error',
        title: 'Invalid Match',
        message: 'Match information is incomplete'
      })
      return
    }

    // Prevent creator from joining their own match
    if (match.host_id === user.id) {
      addNotification({
        type: 'error',
        title: 'Cannot Join Own Match',
        message: 'You cannot join your own match. Wait for other players to join.'
      })
      return
    }

    // Check if user already joined
    if (userMatches.includes(match.id)) {
      // If already joined, go to lobby - convert to UIMatch
      const uiMatch = matches.find(m => m.id === match.id)
      if (uiMatch) {
        setSelectedMatch(uiMatch)
        setChatMessages(generateInitialChatMessages(uiMatch))
        setViewMode('lobby')
      }
      return
    }

    // Check if match is full
    if ((match.current_players || 0) >= (match.max_players || 0)) {
      addNotification({
        type: 'error',
        title: 'Match Full',
        message: 'This match is already full'
      })
      return
    }

    // Check if match is still open
    if (match.status !== 'open') {
      addNotification({
        type: 'error',
        title: 'Match Unavailable',
        message: 'This match is no longer available to join'
      })
      return
    }

    // Check if user has enough diamonds for entry fee
    const entryFee = match.entry_fee || (match.diamond_pot || 0) / (match.max_players || 1)
    if (entryFee > (user.diamond_balance || 0)) {
      addNotification({
        type: 'error',
        title: 'Insufficient Balance',
        message: `Entry fee: ${entryFee} diamonds. You have ${user.diamond_balance || 0} diamonds available.`
      })
      return
    }

    // Show confirmation dialog - convert to UIMatch
    const uiMatch = matches.find(m => m.id === match.id)
    if (uiMatch) {
      setSelectedMatchForAction(uiMatch)
      setShowJoinConfirm(true)
    }
  }, [user?.id, addNotification, matches, userMatches, setSelectedMatch, setChatMessages, generateInitialChatMessages, setViewMode, setSelectedMatchForAction, setShowJoinConfirm])

  // 🚀 MEMOIZED: Confirm join match
  const confirmJoinMatch = useCallback(async () => {
    if (!selectedMatchForAction || !user?.id) return

    try {
      setActionLoading(true)

      // Join match in database (handles diamond deduction automatically)
      const { error } = await matchService.joinMatch(selectedMatchForAction.id, user.id)

      if (error) {
        console.error('Join match error:', error)
        addNotification({
          type: 'error',
          title: 'Failed to join match',
          message: (error as any)?.message || 'Please check your diamond balance and try again'
        })
        return
      }

      addNotification({
        type: 'success',
        title: 'Joined Match!',
        message: 'You have successfully joined the match'
      })

      // Update local state
      setUserMatches(prev => [...prev, selectedMatchForAction.id])

      // Reload matches to get updated data
      await loadMatches()

      // Go to lobby
      setSelectedMatch(selectedMatchForAction)
      setChatMessages(generateInitialChatMessages(selectedMatchForAction))
      setViewMode('lobby')

      // Close confirmation dialog
      setShowJoinConfirm(false)
      setSelectedMatchForAction(null)

    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Failed to join match',
        message: error?.message || 'Unknown error occurred'
      })
    } finally {
      setActionLoading(false)
    }
  }, [selectedMatchForAction, user?.id, addNotification, setShowJoinConfirm, setSelectedMatchForAction])





  const handleBackToDiscovery = () => {
    setViewMode('discovery')
    setSelectedMatch(null)
    setChatMessages([])
    setHasBet(false)
    setUserReady(false)
    setBetAmount('')
    setNewMessage('')
  }

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message: ChatMessage = {
        id: Date.now().toString(),
        sender: user?.username || 'You',
        message: newMessage,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isReferee: false
      }
      setChatMessages([...chatMessages, message])
      setNewMessage('')
    }
  }

  const handlePlaceBet = async () => {
    if (!selectedMatch || !betAmount || !user) return

    const betAmountNum = parseInt(betAmount)
    if (betAmountNum !== selectedMatch.diamondPot) {
      addNotification({
        type: 'error',
        title: 'Invalid Bet Amount',
        message: `You must bet exactly ${selectedMatch.diamondPot} diamonds to join this match`
      })
      return
    }

    try {
      setIsLoading(true)

      // Use betting service with conflict checking
      const result = await bettingService.placeBet(
        user.id,
        selectedMatch.id,
        betAmountNum,
        user.id, // Predicting self as winner for now
        1.0 // Default odds
      )

      if (!result.success) {
        addNotification({
          type: 'error',
          title: 'Cannot Place Bet',
          message: result.error || 'Failed to place bet'
        })
        return
      }

      // Success - update UI
      setHasBet(true)
      setBetAmount('')

      addNotification({
        type: 'success',
        title: 'Bet Placed!',
        message: `Successfully placed ${betAmountNum} diamonds bet`
      })

      const systemMessage: ChatMessage = {
        id: Date.now().toString(),
        sender: 'System',
        message: `${user?.username || 'You'} has placed their bet and joined the match!`,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        isSystem: true
      }
      setChatMessages(prev => [...prev, systemMessage])

      // Reload user data to update balance
      // This would typically be handled by a global state update

    } catch (error) {
      console.error('Error placing bet:', error)
      addNotification({
        type: 'error',
        title: 'Betting Error',
        message: 'An unexpected error occurred while placing your bet'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleReady = async () => {
    const newReadyStatus = !userReady
    setUserReady(newReadyStatus)

    const systemMessage: ChatMessage = {
      id: Date.now().toString(),
      sender: 'System',
      message: `${user?.username || 'You'} is ${newReadyStatus ? 'ready' : 'not ready'}!`,
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isSystem: true
    }
    setChatMessages(prev => [...prev, systemMessage])

    // Check if both players are ready and start countdown
    if (newReadyStatus && selectedMatch) {
      // For now, simulate that the other player is also ready
      // In a real implementation, you'd track both players' ready status
      const bothPlayersReady = true // This should be checked from database/real-time state

      if (bothPlayersReady && selectedMatch.status === 'full') {
        try {
          console.log('🎮 Both players ready, starting countdown...')

          // Add countdown message
          const countdownMessage: ChatMessage = {
            id: Date.now().toString(),
            sender: 'System',
            message: '⏰ Both players ready! Match starting in 30 seconds...',
            timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            isSystem: true
          }
          setChatMessages(prev => [...prev, countdownMessage])

          // Start 30-second countdown
          let countdown = 30
          const countdownInterval = setInterval(() => {
            countdown--

            if (countdown === 10) {
              const tenSecMessage: ChatMessage = {
                id: Date.now().toString(),
                sender: 'System',
                message: '🔟 Match starting in 10 seconds!',
                timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                isSystem: true
              }
              setChatMessages(prev => [...prev, tenSecMessage])
            }

            if (countdown <= 0) {
              clearInterval(countdownInterval)

              // Start the match
              matchService.startMatch(selectedMatch.id).then(({ error }) => {
                if (error) {
                  console.error('Failed to start match:', error)
                  addNotification({
                    type: 'error',
                    title: 'Failed to Start Match',
                    message: 'Could not start the match. Please try again.'
                  })
                  return
                }

                // Update local match status
                setSelectedMatch(prev => prev ? { ...prev, status: 'in_progress' } : null)

                // Add system message about match starting
                const startMessage: ChatMessage = {
                  id: Date.now().toString(),
                  sender: 'System',
                  message: '🎮 Match has started! Good luck to both players!',
                  timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
                  isSystem: true
                }
                setChatMessages(prev => [...prev, startMessage])

                addNotification({
                  type: 'success',
                  title: '🎮 Match Started!',
                  message: 'The match has begun. Good luck!'
                })
              })
            }
          }, 1000)

        } catch (error) {
          console.error('Error starting countdown:', error)
        }
      }
    }
  }



  // Check if user has created any active matches
  const userCreatedMatches = matches.filter(match =>
    match.host_id === user?.id &&
    ['open', 'full', 'ongoing'].includes(match.status)
  )

  // Filter matches based on business logic
  // Filter matches based on status and user preferences (memoized to prevent infinite loops)
  const filteredMatches = useMemo(() => {
    console.log('🔍 Filtering matches. Total matches:', matches.length)
    console.log('🔍 User ID for filtering:', user?.id?.slice(0, 8))

    const filtered = matches.filter(match => {
      // Cancelled matches are now filtered at database level, no need to filter here
      const status = match.status?.toLowerCase()
      const isUserMatch = match.host_id === user?.id

      // Debug each user match
      if (isUserMatch) {
        console.log(`🔴 USER'S MATCH found:`, {
          id: match.id?.slice(0, 8),
          title: match.title,
          status: match.status,
          host_id: match.host_id?.slice(0, 8)
        })
      }

      // Show open, full, ongoing, in_progress, and active matches to everyone
      const visibleStatuses = ['open', 'full', 'ongoing', 'waiting', 'in_progress', 'active', 'battling', 'fighting']
      if (visibleStatuses.includes(status)) return true

      // Show completed matches to everyone (not just host)
      if (status === 'completed') return true

      // Hide other statuses
      return false
    })

    console.log('🔍 Total matches in state:', matches.length)
    console.log('🔍 Filtered matches to show:', filtered.length)

    // DEBUG: Show what matches are actually in state
    if (matches.length > 0) {
      console.log('🔍 MATCHES IN STATE:', matches.slice(0, 3).map(m => ({
        id: m.id?.slice(0, 8),
        title: m.title,
        host_id: m.host_id?.slice(0, 8),
        status: m.status
      })))
    }

    return filtered
  }, [matches]) // Only recalculate when matches array changes

  // Performance optimizations ready for future use when needed

  // Render different views based on current mode
  const renderDiscoveryView = () => (
    <div className="min-h-screen bg-gray-50">
      {/* Simple Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <Gamepad2 className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Matches</h1>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span>Find and join matches</span>
                  {userStats.totalMatches > 0 && (
                    <div className="hidden sm:flex items-center space-x-2 text-xs">
                      <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                        {userStats.wins}W
                      </span>
                      <span className="bg-red-100 text-red-700 px-2 py-1 rounded-full font-medium">
                        {userStats.losses}L
                      </span>
                      <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full font-medium">
                        {userStats.winRate}% WR
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleCreateMatch}
                className={`px-3 py-1.5 rounded-md font-medium transition-colors flex items-center space-x-1 text-sm ${
                  userActiveMatch
                    ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
                title={userActiveMatch ? 'Cancel your active match first' : 'Create Match'}
                disabled={!!userActiveMatch}
              >
                <Plus className="w-3 h-3" />
                <span>Create</span>
              </button>

              <button
                onClick={() => setShowQuickMatch(true)}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 rounded-md font-medium transition-colors flex items-center space-x-1 text-sm"
                title="Quick Match"
              >
                <Zap className="w-3 h-3" />
                <span>Quick</span>
              </button>

              {/* Simple diagnostic and cleanup buttons */}
              <button
                onClick={async () => {
                  console.log('🔍 DIAGNOSTIC: Checking for persistent fake data...')
                  const { data: allMatches } = await supabase.from('matches').select('*').limit(10)
                  console.log('📊 Current database matches:', allMatches?.map(m => ({
                    id: m.id,
                    title: m.title,
                    status: m.status,
                    host_id: m.host_id?.slice(0, 8)
                  })))

                  const fakeCount = allMatches?.filter(m =>
                    ['11111111', '22222222', '33333333'].includes(m.id) ||
                    m.title?.includes('Test')
                  ).length || 0

                  if (fakeCount > 0) {
                    console.warn('⚠️ FOUND', fakeCount, 'fake matches still in database!')
                  } else {
                    console.log('✅ No fake data found in database')
                  }
                }}
                className="bg-gray-600 hover:bg-gray-700 text-white px-2 py-1.5 rounded-md transition-colors"
                title="Check Database"
              >
                <Info className="w-3 h-3" />
              </button>

              {/* Cleanup button removed - database is now clean */}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        {/* Active Match Warning */}
        {userActiveMatch && (
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <Crown className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <h3 className="font-medium text-orange-900">You have an active match</h3>
                  <p className="text-sm text-orange-700">
                    "{userActiveMatch.title}" - You cannot join other matches until you cancel this one.
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  setSelectedMatchForAction(userActiveMatch)
                  setShowCancelConfirm(true)
                }}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 rounded-md text-sm font-medium transition-colors"
              >
                Cancel Match
              </button>
            </div>
          </div>
        )}

        {/* Compact Filters - Always Visible */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 mb-4">
          <div className="flex flex-wrap items-center gap-3">
            {/* Game Filter */}
            <select
              value={filters.game}
              onChange={(e) => setFilters(prev => ({ ...prev, game: e.target.value }))}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Games</option>
              {games.map(game => (
                <option key={game} value={game}>{game}</option>
              ))}
            </select>

            {/* Format Filter */}
            <select
              value={filters.format}
              onChange={(e) => setFilters(prev => ({ ...prev, format: e.target.value }))}
              className="px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Formats</option>
              {["1v1", "2v2", "3v3", "4v4", "5v5"].map((format: string) => (
                <option key={format} value={format}>{format}</option>
              ))}
            </select>

            {/* Diamond Range */}
            <input
              type="number"
              placeholder="Min ♦"
              value={filters.minDiamonds}
              onChange={(e) => setFilters(prev => ({ ...prev, minDiamonds: e.target.value }))}
              className="w-20 px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />
            <span className="text-gray-400 text-sm">-</span>
            <input
              type="number"
              placeholder="Max ♦"
              value={filters.maxDiamonds}
              onChange={(e) => setFilters(prev => ({ ...prev, maxDiamonds: e.target.value }))}
              className="w-20 px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            />

            {/* Verified Only Checkbox */}
            <label className="flex items-center space-x-2 text-sm text-gray-700">
              <input
                type="checkbox"
                checked={filters.verifiedOnly}
                onChange={(e) => setFilters(prev => ({ ...prev, verifiedOnly: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span>Verified</span>
            </label>

            {/* Clear Filters */}
            <button
              onClick={() => setFilters({
                search: '',
                game: '',
                format: '',
                minDiamonds: '',
                maxDiamonds: '',
                verifiedOnly: false,
                sortBy: 'newest'
              })}
              className="px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>

        {/* Matches Container */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* User Created Match Banner */}
          {userCreatedMatches.length > 0 && (
            <div className="bg-blue-50 border-l-4 border-blue-400 p-4 mx-4 mt-4 rounded-r-lg">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Shield className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <p className="text-sm text-blue-700">
                    <strong>You have created a match!</strong> Other matches are hidden until you cancel your match or it completes.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <Gamepad2 className="w-5 h-5 text-blue-600" />
                <span>Available Matches</span>
              </h2>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">{filteredMatches.length} matches</span>

                {/* Referee Count Badge */}
                <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium">
                  <Shield className="w-3 h-3" />
                  <span>{availableReferees} referees</span>
                </div>

                <NotificationBell />
                <button
                  onClick={async () => {
                    setIsLoading(true)
                    await loadMatches()
                    await loadUserStats()
                    setIsLoading(false)
                  }}
                  disabled={isLoading}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors disabled:opacity-50"
                  title="Refresh matches"
                >
                  <RefreshCw className={`w-4 h-4 text-gray-600 ${isLoading ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>
          </div>

          <div className="p-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="relative mb-4">
                    <div className="w-12 h-12 border-4 border-blue-200 rounded-full"></div>
                    <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin absolute top-0 left-0"></div>
                  </div>
                  <p className="text-gray-600 animate-pulse">Loading matches...</p>
                  <div className="flex items-center justify-center space-x-1 mt-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            ) : filteredMatches.length === 0 ? (
              <div className="text-center py-12">
                <Gamepad2 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-900 mb-2">No matches found</h3>
                <p className="text-gray-600 mb-6">Try adjusting your filters or create a new match</p>
                <button
                  onClick={handleCreateMatch}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center space-x-2 mx-auto"
                >
                  <Plus className="w-5 h-5" />
                  <span>Create Match</span>
                </button>
              </div>
            ) : (
              <div className={`${isMobile ? 'space-y-4' : 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6'}`}>
                {filteredMatches.map((match) => (
                  <MatchCard
                    key={match.id}
                    match={{
                      id: match.id,
                      game: match.game,
                      host: match.host_username || match.creator || 'Unknown Host',
                      hostId: match.host_id,
                      betAmount: match.entry_fee,
                      entryFee: match.entry_fee,
                      maxPlayers: match.max_players,
                      currentPlayers: match.current_players,
                      timeCreated: match.created_at,
                      status: match.status as 'open' | 'full' | 'ongoing' | 'completed',
                      gameMode: match.mode,
                      mode: match.mode,
                      title: match.title || `${match.game} ${match.mode || '1v1'} Match`,
                      region: match.region || 'Global',
                      pot_amount: match.pot_amount || (match.entry_fee * match.max_players),
                      diamond_pot: match.entry_fee,
                      rules: Array.isArray(match.rules) ? match.rules.join(', ') : 'Standard rules',
                      timeLeft: match.timeLeft
                    }}
                    onJoin={handleJoinMatch}
                    onCancel={(matchId) => {
                      const matchToCancel = matches.find(m => m.id === matchId)
                      if (matchToCancel) {
                        cancelMatchHandler(matchToCancel)
                      }
                    }}
                    onView={(matchId) => {
                      const selectedMatch = matches.find(m => m.id === matchId)
                      if (selectedMatch) {
                        setSelectedMatch(selectedMatch)
                        setChatMessages(generateInitialChatMessages(selectedMatch))
                        setViewMode('lobby')
                      }
                    }}
                    onFavorite={handleFavoriteMatch}
                    onShare={handleShareMatch}
                    isFavorited={favoriteMatches.includes(match.id)}
                    isMobile={isMobile}
                    showJoinButton={!userMatches.includes(match.id)}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
  const renderLobbyView = () => {
    if (!selectedMatch) return null

    return (
      <div className="min-h-screen bg-gray-50">
        {/* Lobby Header */}
        <div className="bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-200 sticky top-0 z-40">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleBackToDiscovery}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="w-5 h-5 text-gray-600" />
                </button>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <Gamepad2 className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-lg sm:text-xl font-bold text-gray-900">{selectedMatch.title}</h1>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span>{selectedMatch.game}</span>
                      <span>•</span>
                      <span>{selectedMatch.mode || 'Classic'}</span>
                      <span>•</span>
                      <span className="flex items-center space-x-1">
                        {isConnected ? (
                          <Wifi className="w-3 h-3 text-green-500" />
                        ) : (
                          <WifiOff className="w-3 h-3 text-red-500" />
                        )}
                        <span className={isConnected ? 'text-green-600' : 'text-red-600'}>
                          {isConnected ? 'Connected' : 'Reconnecting...'}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setSoundEnabled(!soundEnabled)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  {soundEnabled ? (
                    <Volume2 className="w-5 h-5 text-gray-600" />
                  ) : (
                    <VolumeX className="w-5 h-5 text-gray-600" />
                  )}
                </button>

                <div className="hidden sm:block text-right">
                  <p className="text-xs text-gray-500">Your Balance</p>
                  <p className="text-lg font-bold text-blue-600 flex items-center">
                    <Gem className="w-4 h-4 mr-1" />
                    {user?.diamond_balance?.toLocaleString() || '2,450'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Lobby Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          {/* Match Overview */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4 lg:p-6 mb-6">
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between space-y-3 sm:space-y-0 mb-6">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Gamepad2 className="w-7 h-7 text-white" />
                </div>
                <div>
                  <h2 className="text-xl sm:text-2xl font-bold text-gray-900">{selectedMatch.format} Match</h2>
                  <p className="text-sm sm:text-base text-gray-600">{selectedMatch.mode || 'Classic'} • {selectedMatch.mapName || 'Land of Dawn'}</p>
                </div>
              </div>
              <span className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(selectedMatch.status)}`}>
                {selectedMatch.status.charAt(0).toUpperCase() + selectedMatch.status.slice(1)}
              </span>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
              {/* Diamond Pot */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Gem className="w-5 h-5 text-blue-600" />
                  <span className="font-medium text-blue-900">Diamond Pot</span>
                </div>
                <p className="text-2xl font-bold text-blue-600">{(selectedMatch.totalPot || selectedMatch.pot_amount || 0).toLocaleString()}</p>
                <p className="text-sm text-blue-700">Entry: {selectedMatch.diamondPot} diamonds</p>
              </div>

              {/* Players Status */}
              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-4 border border-green-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Users className="w-5 h-5 text-green-600" />
                  <span className="font-medium text-green-900">Players</span>
                </div>
                <p className="text-2xl font-bold text-green-600">{getTotalPlayersCount(selectedMatch)}/{selectedMatch.max_players}</p>
                <p className="text-sm text-green-700">{getReadyPlayersCount(selectedMatch)} ready</p>
              </div>

              {/* Countdown Timer */}
              <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl p-4 border border-orange-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Timer className="w-5 h-5 text-orange-600" />
                  <span className="font-medium text-orange-900">Time Left</span>
                </div>
                <p className="text-2xl font-bold text-orange-600">{formatTime(timeLeft)}</p>
                <p className="text-sm text-orange-700">Until match starts</p>
              </div>

              {/* Match Info */}
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-4 border border-purple-200">
                <div className="flex items-center space-x-2 mb-2">
                  <Info className="w-5 h-5 text-purple-600" />
                  <span className="font-medium text-purple-900">Match Info</span>
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-purple-700">Region:</span>
                    <span className="font-medium text-purple-900">{selectedMatch.region}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-purple-700">Room:</span>
                    <span className="font-medium text-purple-900">{selectedMatch.room_code || 'TBD'}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Match Rules */}
            <div className="mt-6 bg-yellow-50 rounded-xl p-4 border border-yellow-200">
              <h3 className="font-semibold text-yellow-900 mb-3 flex items-center">
                <Info className="w-5 h-5 mr-2" />
                Match Rules & Guidelines
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {(Array.isArray(selectedMatch.rules) ? selectedMatch.rules : ['Standard tournament rules apply']).map((rule: string, index: number) => (
                  <div key={index} className="flex items-start space-x-2">
                    <CheckCircle className="w-4 h-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-yellow-800">{rule}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Team Slots */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-6">
            {/* Team 1 */}
            <div className="bg-white rounded-xl shadow-sm border-l-4 border-l-blue-500 border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900 flex items-center">
                  <Shield className="w-5 h-5 text-blue-600 mr-2" />
                  Team 1 ({selectedMatch.team1.length}/{Math.ceil((selectedMatch.max_players || 2) / 2)})
                  <span className="ml-2 text-sm text-gray-500">({selectedMatch.team1.filter(p => p.isReady).length} ready)</span>
                </h3>
                <div className="flex items-center space-x-2">
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium flex items-center space-x-1 px-2 py-1 rounded-lg hover:bg-blue-50 transition-colors">
                    <UserPlus className="w-4 h-4" />
                    <span>Invite</span>
                  </button>
                  <button
                    onClick={() => copyToClipboard(selectedMatch.match_link || '')}
                    className="text-gray-600 hover:text-gray-700 text-sm font-medium flex items-center space-x-1 px-2 py-1 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <Share2 className="w-4 h-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                {selectedMatch.team1.map((player) => (
                  <div key={player.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        {getRankIcon(player.rank)}
                        {player.isOnline ? (
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        ) : (
                          <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">{player.username}</p>
                          {player.isHost && <Crown className="w-4 h-4 text-yellow-500" />}
                        </div>
                        <p className="text-sm text-gray-600">{player.mlUsername}</p>
                        <p className="text-xs text-gray-500">{player.guildName}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        {getRoleIcon(player.role)}
                        <span className="text-sm text-gray-600">{player.role}</span>
                      </div>
                      {player.isReady ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <Clock className="w-5 h-5 text-orange-500" />
                      )}
                    </div>
                  </div>
                ))}

                {/* Empty slots */}
                {Array.from({ length: Math.ceil((selectedMatch.max_players || 2) / 2) - selectedMatch.team1.length }).map((_, index) => (
                  <div key={`empty-1-${index}`} className="flex items-center justify-center p-3 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <Users className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                      <p className="text-sm text-gray-500">Waiting for player...</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Team 2 */}
            <div className="bg-white rounded-xl shadow-sm border-l-4 border-l-red-500 border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900 flex items-center">
                  <Shield className="w-5 h-5 text-red-600 mr-2" />
                  Team 2 ({selectedMatch.team2.length}/{Math.ceil((selectedMatch.max_players || 2) / 2)})
                  <span className="ml-2 text-sm text-gray-500">({selectedMatch.team2.filter(p => p.isReady).length} ready)</span>
                </h3>
              </div>

              <div className="space-y-3">
                {selectedMatch.team2.map((player) => (
                  <div key={player.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        {getRankIcon(player.rank)}
                        {player.isOnline ? (
                          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        ) : (
                          <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                        )}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <p className="font-medium text-gray-900">{player.username}</p>
                          {player.isHost && <Crown className="w-4 h-4 text-yellow-500" />}
                        </div>
                        <p className="text-sm text-gray-600">{player.mlUsername}</p>
                        <p className="text-xs text-gray-500">{player.guildName}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        {getRoleIcon(player.role)}
                        <span className="text-sm text-gray-600">{player.role}</span>
                      </div>
                      {player.isReady ? (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      ) : (
                        <Clock className="w-5 h-5 text-orange-500" />
                      )}
                    </div>
                  </div>
                ))}

                {/* Empty slots */}
                {Array.from({ length: Math.ceil((selectedMatch.max_players || 2) / 2) - selectedMatch.team2.length }).map((_, index) => (
                  <div key={`empty-2-${index}`} className="flex items-center justify-center p-3 bg-gray-100 rounded-lg border-2 border-dashed border-gray-300">
                    <div className="text-center">
                      <Users className="w-6 h-6 text-gray-400 mx-auto mb-1" />
                      <p className="text-sm text-gray-500">Waiting for player...</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Referee Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div className="bg-green-50 rounded-xl p-4 border border-green-200">
              <h3 className="font-semibold text-green-900 mb-3 flex items-center">
                <Shield className="w-5 h-5 mr-2" />
                Match Referee
                {selectedMatch.referee.isVerified && (
                  <CheckCircle className="w-4 h-4 ml-2 text-green-600" />
                )}
                {selectedMatch.referee.isOnline && (
                  <div className="w-2 h-2 bg-green-500 rounded-full ml-2"></div>
                )}
              </h3>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-green-900">{selectedMatch.referee.name}</p>
                  <p className="text-sm text-green-700">ML ID: {selectedMatch.referee.mlId}</p>
                </div>
                <button
                  onClick={() => copyToClipboard(selectedMatch.referee.mlId)}
                  className="flex items-center space-x-2 px-3 py-2 bg-green-100 hover:bg-green-200 rounded-lg transition-colors"
                >
                  <Copy className="w-4 h-4 text-green-700" />
                  <span className="text-sm text-green-700">Copy ID</span>
                </button>
              </div>
              <p className="text-xs text-green-600 mt-2">
                💡 Add referee to your friends list before the match starts
              </p>
            </div>
          </div>

          {/* Player Controls */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6 mb-6">
            {/* Betting Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="bg-blue-50 rounded-xl p-4 border border-blue-200">
                <h3 className="font-semibold text-blue-900 mb-3 flex items-center">
                  <Gem className="w-5 h-5 mr-2" />
                  Place Your Bet
                </h3>
                {!hasBet ? (
                  <div className="space-y-3">
                    <div>
                      <label className="block text-sm font-medium text-blue-900 mb-1">
                        Diamond Amount (Required: {selectedMatch.diamondPot})
                      </label>
                      <div className="relative">
                        <input
                          type="number"
                          value={betAmount}
                          onChange={(e) => setBetAmount(e.target.value)}
                          placeholder={`Enter ${selectedMatch.diamondPot} diamonds`}
                          className="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <Gem className="absolute right-3 top-2.5 w-4 h-4 text-blue-500" />
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-blue-700">Your Balance:</span>
                      <span className="font-bold text-blue-900">{user?.diamond_balance?.toLocaleString() || '2,450'} 💎</span>
                    </div>
                    <button
                      onClick={handlePlaceBet}
                      disabled={!betAmount || parseInt(betAmount) !== selectedMatch.diamondPot}
                      className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                    >
                      <Zap className="w-4 h-4" />
                      <span>Place Bet & Join</span>
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-2" />
                    <p className="text-green-700 font-medium">Bet Placed Successfully!</p>
                    <p className="text-sm text-green-600">{selectedMatch.diamondPot} diamonds</p>
                  </div>
                )}
              </div>
            </div>

            {/* Ready Status */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="bg-orange-50 rounded-xl p-4 border border-orange-200">
                <h3 className="font-semibold text-orange-900 mb-3 flex items-center">
                  <Clock className="w-5 h-5 mr-2" />
                  Player Status
                </h3>
                <div className="space-y-3">
                  <button
                    onClick={handleToggleReady}
                    className={`w-full py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${
                      userReady
                        ? 'bg-green-600 hover:bg-green-700 text-white'
                        : 'bg-orange-600 hover:bg-orange-700 text-white'
                    }`}
                  >
                    {userReady ? (
                      <>
                        <CheckCircle className="w-4 h-4" />
                        <span>Ready!</span>
                      </>
                    ) : (
                      <>
                        <Clock className="w-4 h-4" />
                        <span>Mark as Ready</span>
                      </>
                    )}
                  </button>

                  <button
                    onClick={handleBackToDiscovery}
                    className="w-full py-2 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Leave Match</span>
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Section */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="font-semibold text-gray-900 mb-3 flex items-center">
              <MessageCircle className="w-5 h-5 mr-2" />
              Match Chat
            </h3>
            <div className="space-y-3">
              <div className="bg-gray-50 rounded-lg p-3 max-h-48 overflow-y-auto border">
                <div className="space-y-2">
                  {chatMessages.map((message) => (
                    <div key={message.id} className="flex items-start space-x-2">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                        message.isSystem ? 'bg-gray-100' : message.isReferee ? 'bg-green-100' : 'bg-blue-100'
                      }`}>
                        {message.isSystem ? (
                          <Info className="w-3 h-3 text-gray-600" />
                        ) : message.isReferee ? (
                          <Shield className="w-3 h-3 text-green-600" />
                        ) : (
                          <Users className="w-3 h-3 text-blue-600" />
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <p className={`text-xs font-medium ${
                            message.isSystem ? 'text-gray-600' : message.isReferee ? 'text-green-600' : 'text-blue-600'
                          }`}>
                            {message.sender}
                          </p>
                          <span className="text-xs text-gray-500">{message.timestamp}</span>
                        </div>
                        <p className="text-sm text-gray-900">{message.message}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  placeholder="Type your message..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                />
                <button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim()}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center space-x-1"
                >
                  <Send className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }



  // Main render function
  return (
    <div className="particle-bg min-h-screen">
      {viewMode === 'discovery' && renderDiscoveryView()}
      {viewMode === 'lobby' && renderLobbyView()}

      {/* Create Match Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg w-full max-w-md max-h-[85vh] overflow-y-auto shadow-xl">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Create Match</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center hover:bg-gray-200 transition-colors"
              >
                <X className="w-4 h-4 text-gray-600" />
              </button>
            </div>

            <form onSubmit={handleSubmitCreateMatch} className="p-4 space-y-4">
              {/* Game Selection */}
              <div className="space-y-1">
                <label className="text-xs font-medium text-gray-700 uppercase tracking-wide">Game *</label>
                <select
                  value={createForm.game}
                  onChange={(e) => handleCreateFormChange("game", e.target.value)}
                  className="w-full px-2 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">Select a game</option>
                  {games.map((game) => (
                    <option key={game} value={game}>{getGameIcon(game)} {game}</option>
                  ))}
                </select>
                {createForm.game && (
                  <div className="flex items-center space-x-2 p-2 bg-blue-50 rounded-lg">
                    <span className="text-2xl">{getGameIcon(createForm.game)}</span>
                    <div>
                      <p className="text-sm font-medium text-blue-900">{createForm.game}</p>
                      <p className="text-xs text-blue-600">
                        Available formats: {getAvailableFormats(createForm.game).join(', ')}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Match Format */}
              {createForm.game && (
                <div className="space-y-2">
                  <label className="text-sm font-medium text-gray-700">Match Format *</label>
                  <div className="grid grid-cols-3 sm:grid-cols-5 gap-2">
                    {getAvailableFormats(createForm.game).map((format) => (
                      <button
                        key={format}
                        type="button"
                        onClick={() => handleCreateFormChange("format", format)}
                        className={`py-2 px-2 sm:px-4 rounded-lg border-2 font-medium transition-colors text-sm ${
                          createForm.format === format
                            ? "border-blue-500 bg-blue-50 text-blue-700"
                            : "border-gray-300 bg-white text-gray-700 hover:border-gray-400"
                        }`}
                      >
                        {format}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Match Type removed - all matches are custom fights */}

              {/* Diamond Bet */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">Diamond Bet *</label>
                <div className="relative">
                  <Gem className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="number"
                    placeholder="Enter diamond amount"
                    value={createForm.diamondBet}
                    onChange={(e) => handleCreateFormChange("diamondBet", e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    min="1"
                    required
                  />
                </div>
                <p className="text-xs text-gray-600">
                  Your balance: {user?.diamond_balance?.toLocaleString() || '2,450'} diamonds
                </p>
              </div>

              {/* Room code and start time removed - not needed for custom fights */}

              {/* Submit Button */}
              <div className="flex flex-col sm:flex-row items-stretch sm:items-center justify-end space-y-3 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowCreateModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center space-x-2"
                >
                  <Plus className="w-4 h-4" />
                  <span>Create Match</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Success notifications are now handled by NotificationContext */}

      {/* Quick Match Panel */}
      <QuickMatchPanel
        isOpen={showQuickMatch}
        onClose={() => setShowQuickMatch(false)}
        onMatchFound={(matchId) => {
          // Navigate to the found match
          const foundMatch = matches.find(m => m.id === matchId)
          if (foundMatch) {
            setSelectedMatch(foundMatch)
            setChatMessages(generateInitialChatMessages(foundMatch))
            setViewMode('lobby')
          }
        }}
      />

      {/* Join Match Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showJoinConfirm}
        onClose={() => {
          setShowJoinConfirm(false)
          setSelectedMatchForAction(null)
        }}
        onConfirm={confirmJoinMatch}
        title="Join Match"
        message={selectedMatchForAction ?
          `Are you sure you want to join this ${selectedMatchForAction.game} match? Entry fee: ${selectedMatchForAction.entry_fee || selectedMatchForAction.diamondPot} diamonds.` :
          'Are you sure you want to join this match?'
        }
        confirmText="Join Match"
        cancelText="Cancel"
        type="info"
        isLoading={actionLoading}
      />

      {/* Cancel Match Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showCancelConfirm}
        onClose={() => {
          setShowCancelConfirm(false)
          setSelectedMatchForAction(null)
        }}
        onConfirm={confirmCancelMatch}
        title="Cancel Match"
        message={selectedMatchForAction ?
          `Are you sure you want to cancel this match? You will get your ${selectedMatchForAction.entry_fee || selectedMatchForAction.diamondPot} diamonds back.` :
          'Are you sure you want to cancel this match?'
        }
        confirmText="Cancel Match"
        cancelText="Keep Match"
        type="danger"
        isLoading={actionLoading}
      />
    </div>
  )
}

// Export wrapped component with error boundary
export default function MatchesPage() {
  return (
    <MatchesErrorBoundary
      fallbackTitle="Matches Page Error"
      fallbackMessage="There was an error loading the matches page. This could be due to a network issue or temporary server problem."
    >
      <MatchesPageContent />
    </MatchesErrorBoundary>
  )
}
