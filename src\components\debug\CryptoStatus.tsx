import React from 'react'
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react'

const CryptoStatus: React.FC = () => {
  const apiKey = import.meta.env.VITE_NOWPAYMENTS_API_KEY
  const isConfigured = apiKey && apiKey.length > 0 && !apiKey.includes('your_')

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
      <div className="flex items-center space-x-2 mb-2">
        {isConfigured ? (
          <CheckCircle className="w-5 h-5 text-green-600" />
        ) : (
          <XCircle className="w-5 h-5 text-red-600" />
        )}
        <h3 className="font-medium text-gray-900">Crypto Payment Status</h3>
      </div>
      
      <div className="text-sm text-gray-600">
        {isConfigured ? (
          <p className="text-green-600">✅ NowPayments API configured and ready!</p>
        ) : (
          <div className="text-red-600">
            <p>❌ NowPayments API not configured</p>
            <p className="mt-1">Add REACT_APP_NOWPAYMENTS_API_KEY to your .env file</p>
            <p className="text-xs mt-2 text-gray-500">
              Current value: {apiKey ? `"${apiKey.substring(0, 10)}..."` : 'undefined'}
            </p>
          </div>
        )}
      </div>
      
      {!isConfigured && (
        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-4 h-4 text-yellow-600 mt-0.5" />
            <div className="text-xs text-yellow-800">
              <p className="font-medium">Quick Setup:</p>
              <ol className="mt-1 space-y-1 list-decimal list-inside">
                <li>Sign up at nowpayments.io</li>
                <li>Get your API key</li>
                <li>Add to .env: REACT_APP_NOWPAYMENTS_API_KEY=your_key</li>
                <li>Restart your dev server</li>
              </ol>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CryptoStatus
