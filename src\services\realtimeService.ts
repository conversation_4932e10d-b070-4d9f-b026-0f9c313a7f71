import { supabase } from './supabaseClient'

export const realtimeService = {
  // Subscribe to match updates
  subscribeToMatches(callback: (payload: any) => void) {
    return supabase
      .channel('matches')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'matches' },
        callback
      )
      .subscribe()
  },

  // Subscribe to user profile updates
  subscribeToProfile(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`user:${userId}`)
      .on('postgres_changes',
        { event: 'UPDATE', schema: 'public', table: 'users', filter: `id=eq.${userId}` },
        callback
      )
      .subscribe()
  },

  // Subscribe to transactions
  subscribeToTransactions(userId: string, callback: (payload: any) => void) {
    return supabase
      .channel(`transactions:${userId}`)
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'transactions', filter: `user_id=eq.${userId}` },
        callback
      )
      .subscribe()
  }
}
