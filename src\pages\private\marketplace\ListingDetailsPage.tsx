// 🛒 LISTING DETAILS PAGE
// Detailed view of marketplace listing with buy flow

import React, { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import {
  ArrowLeft,
  Shield,
  Star,
  Eye,
  Heart,
  Crown,
  Gamepad2,
  Copy,
  CheckCircle,
  Clock,
  User
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'
import { marketplaceService, MarketplaceListing } from '../../../services/marketplace'
import BuyFlowModal from '../../../components/marketplace/BuyFlowModal'

const ListingDetailsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { user } = useAuth()
  const [listing, setListing] = useState<MarketplaceListing | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [showBuyModal, setShowBuyModal] = useState(false)
  const [isFavorited, setIsFavorited] = useState(false)

  useEffect(() => {
    if (id) {
      loadListing()
    }
  }, [id])

  const loadListing = async () => {
    if (!id) return

    setIsLoading(true)
    try {
      const { data, error } = await marketplaceService.getListing(id)
      
      if (error) {
        console.error('Error loading listing:', error)
        navigate('/marketplace')
        return
      }

      setListing(data)
    } catch (error) {
      console.error('Error loading listing:', error)
      navigate('/marketplace')
    } finally {
      setIsLoading(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat().format(price)
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `${diffInDays}d ago`
    return date.toLocaleDateString()
  }

  const getDiscountPercentage = () => {
    if (!listing?.original_price_diamonds || listing.original_price_diamonds <= listing.price_diamonds) {
      return null
    }
    return Math.round(((listing.original_price_diamonds - listing.price_diamonds) / listing.original_price_diamonds) * 100)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert('Copied to clipboard!')
  }

  const handleBuySuccess = () => {
    setShowBuyModal(false)
    alert('Purchase successful! A referee will be assigned to handle the transfer.')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="h-8 bg-gray-200 rounded w-32 mb-6 animate-pulse"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <div className="h-64 bg-gray-200 rounded-xl mb-6 animate-pulse"></div>
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-4 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-full mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
            </div>
            <div>
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4 animate-pulse"></div>
                <div className="h-12 bg-gray-200 rounded w-full animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!listing) {
    return (
      <div className="min-h-screen bg-gray-50 p-4 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Listing not found</h2>
          <p className="text-gray-600 mb-4">The listing you're looking for doesn't exist or has been removed.</p>
          <button
            onClick={() => navigate('/marketplace')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Back to Marketplace
          </button>
        </div>
      </div>
    )
  }

  const gameIcons = {
    'Mobile Legends': Gamepad2,
    'Dota 2': Shield,
    'CS:GO': Star,
    'Wild Rift': Crown,
    'Valorant': Star
  }

  const GameIcon = gameIcons[listing.game as keyof typeof gameIcons] || Gamepad2

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Back Button */}
        <button
          onClick={() => navigate('/marketplace')}
          className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 mb-6 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Marketplace</span>
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Hero Image */}
            <div className="relative h-64 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl mb-6 flex items-center justify-center">
              <GameIcon className="w-16 h-16 text-white" />
              
              {/* Badges */}
              <div className="absolute top-4 left-4 flex flex-col space-y-2">
                {listing.is_featured && (
                  <span className="bg-yellow-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                    Featured
                  </span>
                )}
                {listing.is_verified && (
                  <span className="bg-blue-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                    Verified Listing
                  </span>
                )}
              </div>

              {/* Discount Badge */}
              {getDiscountPercentage() && (
                <div className="absolute top-4 right-4 bg-red-500 text-white text-sm px-3 py-1 rounded-full font-medium">
                  -{getDiscountPercentage()}% OFF
                </div>
              )}

              {/* Stats */}
              <div className="absolute bottom-4 right-4 flex items-center space-x-4 text-white text-sm">
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{listing.views_count}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Heart className="w-4 h-4" />
                  <span>{listing.favorites_count}</span>
                </div>
              </div>
            </div>

            {/* Title and Description */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">{listing.title}</h1>
              
              <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                <div className="flex items-center space-x-1">
                  <GameIcon className="w-4 h-4" />
                  <span>{listing.game}</span>
                </div>
                {listing.rank && (
                  <div className="flex items-center space-x-1">
                    <Crown className="w-4 h-4" />
                    <span>{listing.rank}</span>
                  </div>
                )}
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{formatTimeAgo(listing.created_at)}</span>
                </div>
              </div>

              <p className="text-gray-700 whitespace-pre-wrap">{listing.description}</p>
            </div>

            {/* Game Details */}
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Account Details</h2>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {listing.account_level && (
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{listing.account_level}</div>
                    <div className="text-sm text-gray-600">Level</div>
                  </div>
                )}
                {listing.heroes_count && (
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{listing.heroes_count}</div>
                    <div className="text-sm text-gray-600">Heroes</div>
                  </div>
                )}
                {listing.skins_count && (
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{listing.skins_count}</div>
                    <div className="text-sm text-gray-600">Skins</div>
                  </div>
                )}
                {listing.rank && (
                  <div className="text-center p-3 bg-gray-50 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">{listing.rank}</div>
                    <div className="text-sm text-gray-600">Rank</div>
                  </div>
                )}
              </div>
            </div>

            {/* Seller Info */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Seller Information</h2>
              
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <User className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-900">{listing.seller?.username}</span>
                    {listing.seller?.is_verified && (
                      <div className="flex items-center space-x-1 text-green-600">
                        <Shield className="w-4 h-4" />
                        <span className="text-sm">Verified Seller</span>
                      </div>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">Member since 2024</div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Price and Buy */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="mb-4">
                {getDiscountPercentage() && (
                  <div className="text-lg text-gray-500 line-through mb-1">
                    {formatPrice(listing.original_price_diamonds!)} 💎
                  </div>
                )}
                <div className="text-3xl font-bold text-blue-600">
                  {formatPrice(listing.price_diamonds)} 💎
                </div>
                {getDiscountPercentage() && (
                  <div className="text-sm text-green-600 font-medium">
                    Save {getDiscountPercentage()}% ({formatPrice(listing.original_price_diamonds! - listing.price_diamonds)} 💎)
                  </div>
                )}
              </div>

              {user?.id === listing.seller_id ? (
                <div className="text-center py-4 text-gray-600">
                  This is your listing
                </div>
              ) : (
                <button
                  onClick={() => setShowBuyModal(true)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-lg font-medium transition-colors"
                >
                  Buy Now
                </button>
              )}

              <button
                onClick={() => setIsFavorited(!isFavorited)}
                className={`w-full mt-3 py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 ${
                  isFavorited 
                    ? 'bg-red-100 text-red-600 hover:bg-red-200' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                <Heart className={`w-4 h-4 ${isFavorited ? 'fill-current' : ''}`} />
                <span>{isFavorited ? 'Remove from Favorites' : 'Add to Favorites'}</span>
              </button>
            </div>

            {/* Referee Protection */}
            <div className="bg-green-50 border border-green-200 rounded-xl p-6">
              <div className="flex items-center space-x-2 mb-3">
                <Shield className="w-5 h-5 text-green-600" />
                <h3 className="font-semibold text-green-800">Referee Protection</h3>
              </div>
              <p className="text-sm text-green-700 mb-3">
                This transaction is protected by our referee system. Your diamonds will be held in escrow until the account is successfully transferred.
              </p>
              <div className="space-y-2 text-sm text-green-700">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4" />
                  <span>Secure escrow system</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4" />
                  <span>Referee verification</span>
                </div>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="w-4 h-4" />
                  <span>Money-back guarantee</span>
                </div>
              </div>
            </div>

            {/* Quick Copy */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="font-semibold text-gray-900 mb-3">Share Listing</h3>
              <button
                onClick={() => copyToClipboard(window.location.href)}
                className="w-full flex items-center justify-center space-x-2 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-4 rounded-lg transition-colors"
              >
                <Copy className="w-4 h-4" />
                <span>Copy Link</span>
              </button>
            </div>
          </div>
        </div>

        {/* Buy Flow Modal */}
        <BuyFlowModal
          isOpen={showBuyModal}
          onClose={() => setShowBuyModal(false)}
          listing={listing}
          onSuccess={handleBuySuccess}
        />
      </div>
    </div>
  )
}

export default ListingDetailsPage
