// 🎯 ADMIN ACCESS MENU
// Central hub for all admin access methods

import React from 'react'
import { Link } from 'react-router-dom'
import { Crown, Shield, Lock, Zap, Eye, Settings, ArrowRight } from 'lucide-react'

const AdminAccessMenu: React.FC = () => {
  const accessMethods = [
    {
      title: 'Admin Login Portal',
      description: 'Secure email/password authentication for administrators',
      href: '/admin-login',
      icon: Lock,
      color: 'from-blue-600 to-purple-600',
      recommended: true
    },
    {
      title: 'Quick Admin Access',
      description: 'Fast admin verification for logged-in users',
      href: '/admin-access',
      icon: Zap,
      color: 'from-green-600 to-blue-600',
      recommended: false
    },
    {
      title: 'Admin Debug',
      description: 'Diagnostic tools and status checking',
      href: '/admin-debug',
      icon: Eye,
      color: 'from-purple-600 to-pink-600',
      recommended: false
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="max-w-4xl w-full">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="w-24 h-24 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
            <Crown className="w-12 h-12 text-black font-bold" />
          </div>
          <h1 className="text-4xl font-bold text-white mb-4">Gambets Admin Access</h1>
          <p className="text-blue-200 text-lg">Choose your preferred method to access the admin dashboard</p>
        </div>

        {/* Access Methods */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {accessMethods.map((method) => {
            const Icon = method.icon
            return (
              <Link
                key={method.title}
                to={method.href}
                className="group relative bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20 hover:border-white/40 transition-all duration-300 transform hover:scale-105"
              >
                {/* Recommended Badge */}
                {method.recommended && (
                  <div className="absolute -top-3 -right-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold px-3 py-1 rounded-full">
                    RECOMMENDED
                  </div>
                )}

                {/* Icon */}
                <div className={`w-16 h-16 bg-gradient-to-r ${method.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform`}>
                  <Icon className="w-8 h-8 text-white" />
                </div>

                {/* Content */}
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-yellow-300 transition-colors">
                  {method.title}
                </h3>
                <p className="text-blue-200 text-sm mb-4 group-hover:text-blue-100 transition-colors">
                  {method.description}
                </p>

                {/* Arrow */}
                <div className="flex items-center text-blue-300 group-hover:text-white transition-colors">
                  <span className="text-sm font-medium mr-2">Access Now</span>
                  <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
                </div>
              </Link>
            )
          })}
        </div>

        {/* Instructions */}
        <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10">
          <h2 className="text-xl font-bold text-white mb-4 flex items-center">
            <Settings className="w-6 h-6 mr-2" />
            How to Access Admin Dashboard
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Method 1 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                <h3 className="font-semibold text-white">Email/Password Login</h3>
              </div>
              <p className="text-blue-200 text-sm">
                Use the Admin Login Portal with your regular Gambets email and password. 
                The system will verify your admin privileges automatically.
              </p>
            </div>

            {/* Method 2 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                <h3 className="font-semibold text-white">Quick Access</h3>
              </div>
              <p className="text-blue-200 text-sm">
                If you're already logged into Gambets, use Quick Admin Access for 
                instant verification and dashboard entry.
              </p>
            </div>

            {/* Method 3 */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                <h3 className="font-semibold text-white">Debug & Troubleshoot</h3>
              </div>
              <p className="text-blue-200 text-sm">
                Use Admin Debug to check your admin status, troubleshoot issues, 
                and verify your privileges are set up correctly.
              </p>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-6 bg-yellow-500/20 border border-yellow-400/30 rounded-lg p-4">
          <div className="flex items-center space-x-2 text-yellow-300 mb-2">
            <Shield className="w-5 h-5" />
            <span className="font-medium">Security Notice</span>
          </div>
          <p className="text-yellow-200 text-sm">
            All admin access attempts are logged and monitored. Only authorized personnel 
            with proper administrative privileges can access the admin dashboard.
          </p>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <Link
            to="/"
            className="text-blue-300 hover:text-white transition-colors text-sm"
          >
            ← Back to Main Site
          </Link>
        </div>
      </div>
    </div>
  )
}

export default AdminAccessMenu
