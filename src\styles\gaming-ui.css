/* Gaming-Inspired UI Enhancements */

/* Gradient Backgrounds */
.gaming-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gaming-gradient-secondary {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gaming-gradient-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gaming-gradient-warning {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.gaming-gradient-danger {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

/* Glass Morphism Effects */
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-card-dark {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Animated Status Indicators */
.status-open {
  background: #10b981;
  animation: pulse-green 2s infinite;
}

.status-full {
  background: #f59e0b;
  animation: pulse-orange 2s infinite;
}

.status-ongoing {
  background: #3b82f6;
  animation: pulse-blue 2s infinite;
}

.status-completed {
  background: #6b7280;
}

.status-cancelled {
  background: #ef4444;
}

@keyframes pulse-green {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
    transform: scale(1.05);
  }
}

@keyframes pulse-orange {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
    transform: scale(1.05);
  }
}

@keyframes pulse-blue {
  0%, 100% { 
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 0 10px rgba(59, 130, 246, 0);
    transform: scale(1.05);
  }
}

/* Enhanced Match Cards */
.match-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.match-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.match-card-mobile {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Game Icons with Glow */
.game-icon {
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
  transition: filter 0.3s ease;
}

.game-icon:hover {
  filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.6));
}

/* Diamond Counter with Shine Effect */
.diamond-count {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
  position: relative;
}

.diamond-count::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Progress Bars with Glow */
.progress-bar {
  background: rgba(59, 130, 246, 0.1);
  border-radius: 10px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  height: 100%;
  border-radius: 10px;
  position: relative;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Button Enhancements */
.gaming-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.gaming-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.gaming-button:hover::before {
  left: 100%;
}

.gaming-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.gaming-button:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Notification Enhancements */
.notification-enter {
  opacity: 0;
  transform: translateX(100%);
}

.notification-enter-active {
  opacity: 1;
  transform: translateX(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-exit {
  opacity: 1;
  transform: translateX(0);
}

.notification-exit-active {
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading Animations */
.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Floating Elements */
.floating {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Particle Effects */
.particle-bg {
  position: relative;
  overflow: hidden;
}

.particle-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
  animation: particle-float 10s ease-in-out infinite;
}

@keyframes particle-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-30px) rotate(120deg); }
  66% { transform: translateY(-60px) rotate(240deg); }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .match-card:hover {
    transform: none;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }
  
  .gaming-button {
    font-size: 14px;
    padding: 12px 16px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .match-card {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    border-color: rgba(255, 255, 255, 0.1);
    color: white;
  }
  
  .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.1);
  }
}
