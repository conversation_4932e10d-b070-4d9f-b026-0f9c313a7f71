import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON>cle,
  Clock,
  XCircle,
  Loader2,
  <PERSON><PERSON>,
  AlertCircle
} from 'lucide-react'
import { paymentService, PaymentStatus } from '../../services/payment'

interface PaymentStatusModalProps {
  isOpen: boolean
  onClose: () => void
  transactionId: string
  amount: number
  paymentMethod: string
  onSuccess?: () => void
  onFailure?: () => void
}

export const PaymentStatusModal: React.FC<PaymentStatusModalProps> = ({
  isOpen,
  onClose,
  transactionId,
  amount,
  paymentMethod,
  onSuccess,
  onFailure
}) => {
  const [status, setStatus] = useState<PaymentStatus | null>(null)
  const [isPolling, setIsPolling] = useState(true)
  const [timeElapsed, setTimeElapsed] = useState(0)

  useEffect(() => {
    if (!isOpen || !transactionId) return

    let pollInterval: NodeJS.Timeout
    let timeInterval: NodeJS.Timeout

    const pollStatus = async () => {
      try {
        const paymentStatus = await paymentService.getPaymentStatus(transactionId)
        setStatus(paymentStatus)

        if (paymentStatus.status === 'completed') {
          setIsPolling(false)
          onSuccess?.()
        } else if (paymentStatus.status === 'failed') {
          setIsPolling(false)
          onFailure?.()
        }
      } catch (error) {
        console.error('Error polling payment status:', error)
      }
    }

    // Initial status check
    pollStatus()

    // Set up polling interval
    if (isPolling) {
      pollInterval = setInterval(pollStatus, 5000) // Poll every 5 seconds
    }

    // Set up time counter
    timeInterval = setInterval(() => {
      setTimeElapsed(prev => prev + 1)
    }, 1000)

    // Cleanup
    return () => {
      if (pollInterval) clearInterval(pollInterval)
      if (timeInterval) clearInterval(timeInterval)
    }
  }, [isOpen, transactionId, isPolling, onSuccess, onFailure])

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getStatusIcon = () => {
    switch (status?.status) {
      case 'completed':
        return <CheckCircle className="w-12 h-12 text-green-500" />
      case 'failed':
        return <XCircle className="w-12 h-12 text-red-500" />
      case 'pending':
        return <Clock className="w-12 h-12 text-yellow-500" />
      default:
        return <Loader2 className="w-12 h-12 text-blue-500 animate-spin" />
    }
  }

  const getStatusMessage = () => {
    switch (status?.status) {
      case 'completed':
        return 'Payment completed successfully!'
      case 'failed':
        return 'Payment failed. Please try again.'
      case 'pending':
        return 'Waiting for payment confirmation...'
      default:
        return 'Processing payment...'
    }
  }

  const getStatusColor = () => {
    switch (status?.status) {
      case 'completed':
        return 'text-green-600'
      case 'failed':
        return 'text-red-600'
      case 'pending':
        return 'text-yellow-600'
      default:
        return 'text-blue-600'
    }
  }

  const copyTransactionId = () => {
    navigator.clipboard.writeText(transactionId)
    alert('Transaction ID copied to clipboard!')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-md w-full p-6">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            {getStatusIcon()}
          </div>
          <h2 className="text-xl font-bold text-gray-900 mb-2">Payment Status</h2>
          <p className={`text-lg font-medium ${getStatusColor()}`}>
            {getStatusMessage()}
          </p>
        </div>

        {/* Payment Details */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6 space-y-3">
          <div className="flex justify-between">
            <span className="text-gray-600">Amount:</span>
            <span className="font-medium">₱{amount.toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Payment Method:</span>
            <span className="font-medium capitalize">{paymentMethod}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-600">Transaction ID:</span>
            <div className="flex items-center space-x-2">
              <span className="font-mono text-sm">{transactionId.slice(-8)}</span>
              <button
                onClick={copyTransactionId}
                className="p-1 hover:bg-gray-200 rounded"
              >
                <Copy className="w-4 h-4 text-gray-500" />
              </button>
            </div>
          </div>
          {status?.reference && (
            <div className="flex justify-between">
              <span className="text-gray-600">Reference:</span>
              <span className="font-medium">{status.reference}</span>
            </div>
          )}
          {isPolling && (
            <div className="flex justify-between">
              <span className="text-gray-600">Time Elapsed:</span>
              <span className="font-medium">{formatTime(timeElapsed)}</span>
            </div>
          )}
        </div>

        {/* Status-specific content */}
        {status?.status === 'pending' && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800">Waiting for Payment</h4>
                <p className="text-sm text-yellow-700 mt-1">
                  Please complete your payment in the {paymentMethod} app. 
                  This window will automatically update when payment is confirmed.
                </p>
              </div>
            </div>
          </div>
        )}

        {status?.status === 'completed' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-green-800">Payment Successful</h4>
                <p className="text-sm text-green-700 mt-1">
                  Your diamonds have been added to your account. You can now use them to join matches and tournaments.
                </p>
              </div>
            </div>
          </div>
        )}

        {status?.status === 'failed' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <XCircle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-800">Payment Failed</h4>
                <p className="text-sm text-red-700 mt-1">
                  Your payment could not be processed. Please try again or contact support if the issue persists.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex space-x-3">
          {status?.status === 'completed' || status?.status === 'failed' ? (
            <button
              onClick={onClose}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
            >
              Close
            </button>
          ) : (
            <>
              <button
                onClick={onClose}
                className="flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Close
              </button>
              <button
                onClick={() => setIsPolling(false)}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-colors"
              >
                Stop Checking
              </button>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
