import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Trophy, Crown, Medal, Award, Star, Gamepad2, Gem, TrendingUp, Flame, Castle, Target, Zap, Swords, Shield, Bomb } from "lucide-react"

export default function LeaderboardsPage() {
  const [] = useState<string>('all') // selectedGame unused
  const [] = useState<'daily' | 'weekly' | 'monthly' | 'all-time'>('all-time') // timeFilter unused
  const [] = useState(false) // showFilters unused

  // Enhanced mock leaderboard data
  const topPlayers = [
    {
      rank: 1,
      name: "ProGamer2024",
      username: "@progamer",
      avatar: "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face",
      wins: 156,
      losses: 23,
      winRate: 87.4,
      diamonds: 78450,
      favoriteGame: "Mobile Legends",
      streak: 12,
      level: 45,
      totalMatches: 179,
      earnings: 2840,
      badges: ['Champion', 'Streak Master', '<PERSON> Hunter']
    },
    {
      rank: 2,
      name: "<PERSON><PERSON>unt<PERSON>",
      username: "@diamond",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=100&h=100&fit=crop&crop=face",
      wins: 142,
      losses: 31,
      winRate: 82.1,
      diamonds: 45230,
      favoriteGame: "Valorant",
      streak: 8,
      level: 42,
      totalMatches: 173,
      earnings: 2156,
      badges: ['Sharpshooter', 'Consistent', 'Elite']
    },
    {
      rank: 3,
      name: "eSportsKing",
      username: "@esports",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
      wins: 134,
      losses: 28,
      winRate: 82.7,
      diamonds: 32890,
      favoriteGame: "Call of Duty",
      streak: 15,
      level: 41,
      totalMatches: 162,
      earnings: 1987,
      badges: ['Tactician', 'Hot Streak', 'Veteran']
    },
    {
      rank: 4,
      name: "TournamentPro",
      username: "@tournament",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
      wins: 128,
      losses: 35,
      winRate: 78.5,
      diamonds: 28750,
      favoriteGame: "Mobile Legends",
      streak: 6,
      level: 39,
      totalMatches: 163,
      earnings: 1654,
      badges: ['Competitor', 'Skilled', 'Rising Star']
    },
    {
      rank: 5,
      name: "CompetitiveGamer",
      username: "@competitive",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
      wins: 119,
      losses: 29,
      winRate: 80.4,
      diamonds: 25680,
      favoriteGame: "Valorant",
      streak: 4,
      level: 38,
      totalMatches: 148,
      earnings: 1432,
      badges: ['Precision', 'Dedicated', 'Achiever']
    },
    {
      rank: 6,
      name: "MobileChampion",
      username: "@mobile",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=face",
      wins: 115,
      losses: 32,
      winRate: 78.2,
      diamonds: 22100,
      favoriteGame: "Mobile Legends",
      streak: 7,
      level: 37,
      totalMatches: 147,
      earnings: 1298,
      badges: ['Mobile Master', 'Persistent', 'Climber']
    }
  ]

  const gameStats = [
    {
      game: "Mobile Legends",
      icon: Castle,
      players: 2847,
      avgWinRate: 68.5,
      avgDiamonds: 1250,
      totalMatches: 15420,
      topPlayer: "ProGamer2024",
      color: "from-purple-500 to-pink-500"
    },
    {
      game: "Valorant",
      icon: Target,
      players: 1923,
      avgWinRate: 65.2,
      avgDiamonds: 980,
      totalMatches: 12340,
      topPlayer: "DiamondHunter",
      color: "from-red-500 to-orange-500"
    },
    {
      game: "Call of Duty",
      icon: Zap,
      players: 1456,
      avgWinRate: 62.8,
      avgDiamonds: 850,
      totalMatches: 9876,
      topPlayer: "eSportsKing",
      color: "from-green-500 to-teal-500"
    },
    {
      game: "Wild Rift",
      icon: Swords,
      players: 987,
      avgWinRate: 70.1,
      avgDiamonds: 1100,
      totalMatches: 6543,
      topPlayer: "WildRiftPro",
      color: "from-blue-500 to-cyan-500"
    },
    {
      game: "Dota 2",
      icon: Shield,
      players: 654,
      avgWinRate: 58.3,
      avgDiamonds: 750,
      totalMatches: 4321,
      topPlayer: "StrategyMaster",
      color: "from-yellow-500 to-amber-500"
    },
    {
      game: "CS:GO",
      icon: Bomb,
      players: 432,
      avgWinRate: 55.7,
      avgDiamonds: 650,
      totalMatches: 2987,
      topPlayer: "QuickShot",
      color: "from-gray-500 to-slate-500"
    }
  ]

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />
      case 3:
        return <Award className="w-6 h-6 text-amber-600" />
      default:
        return <Trophy className="w-5 h-5 text-blue-500" />
    }
  }

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white shadow-lg'
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white shadow-lg'
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600 text-white shadow-lg'
      default:
        return 'bg-gradient-to-r from-blue-400 to-blue-600 text-white shadow-md'
    }
  }

  // const getWinRateColor = (winRate: number) => { // TODO: Confirm usage
  //   if (winRate >= 85) return 'text-green-600 bg-green-50 border-green-200'
  //   if (winRate >= 75) return 'text-blue-600 bg-blue-50 border-blue-200'
  //   if (winRate >= 65) return 'text-yellow-600 bg-yellow-50 border-yellow-200'
  //   return 'text-red-600 bg-red-50 border-red-200'
  // }

  // const getGameIcon = (game: string) => { // TODO: Confirm usage
  //   const gameIconMap: { [key: string]: any } = {
  //     'Mobile Legends': Castle,
  //     'Valorant': Target,
  //     'Call of Duty': Zap,
  //     'Wild Rift': Swords,
  //     'Dota 2': Shield,
  //     'CS:GO': Bomb
  //   }
  //   return gameIconMap[game] || Gamepad2
  // }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="container mx-auto px-4 lg:px-6">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <Trophy className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-gray-900">Gambets</span>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <Link to="/" className="text-gray-700 hover:text-blue-600 font-medium">Home</Link>
              <Link to="/browse" className="text-gray-700 hover:text-blue-600 font-medium">Marketplace</Link>
              <Link to="/leaderboards" className="text-blue-600 font-medium">Leaderboards</Link>
              <Link to="/rules" className="text-gray-700 hover:text-blue-600 font-medium">Rules</Link>
              <Link to="/support" className="text-gray-700 hover:text-blue-600 font-medium">Support</Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link to="/login">
                <button className="text-gray-700 hover:text-blue-600 font-medium">Login</button>
              </Link>
              <Link to="/signup">
                <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                  Sign Up
                </button>
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="container mx-auto px-4 lg:px-6 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 flex items-center justify-center gap-4">
            <Trophy className="w-16 h-16 text-yellow-500" />
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 to-orange-500">Leaderboards</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            See the top players, their win rates, and earnings across all games.
            <span className="text-blue-600 font-semibold"> Compete for the top spot!</span>
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Top Players Leaderboard */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-3">
                  <Trophy className="w-8 h-8 text-yellow-500" />
                  Top Players
                </h2>
              </div>
              <div className="p-0">
                <div className="space-y-1">
                  {topPlayers.map((player, index) => (
                    <div
                      key={player.username}
                      className={`flex items-center justify-between p-6 hover:bg-gray-50 transition-all duration-300 ${
                        index < 3 ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-l-4 border-blue-500' : ''
                      }`}
                    >
                      <div className="flex items-center space-x-6">
                        <div className="flex items-center space-x-3">
                          {getRankIcon(player.rank)}
                          <div className={`px-3 py-1 rounded-full font-bold text-sm ${getRankBadgeColor(player.rank)}`}>
                            #{player.rank}
                          </div>
                        </div>

                        <div>
                          <h3 className="font-bold text-lg text-gray-900">{player.name}</h3>
                          <p className="text-sm text-gray-500">{player.username}</p>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                            <span className="bg-gray-100 px-2 py-1 rounded-full">{player.favoriteGame}</span>
                            <span>{player.totalMatches} matches</span>
                            <span className="flex items-center text-green-600">
                              <TrendingUp className="w-4 h-4 mr-1" />
                              {player.streak} streak
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="font-bold text-2xl text-gray-900">
                          {player.winRate}%
                        </div>
                        <div className="text-lg text-blue-600 font-semibold flex items-center">
                          <Gem className="w-5 h-5 mr-1" />
                          {player.diamonds.toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="mt-8 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 rounded-2xl p-8 text-white text-center shadow-xl">
              <h3 className="text-2xl font-bold mb-3">Ready to Compete?</h3>
              <p className="mb-6 text-blue-100 text-lg">
                Join the leaderboards and start earning diamonds today!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/signup">
                  <button className="bg-white text-blue-600 hover:bg-gray-100 px-6 py-3 rounded-xl font-bold shadow-lg hover:shadow-xl transition-all duration-300">
                    Sign Up Now →
                  </button>
                </Link>
                <Link to="/login">
                  <button className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-6 py-3 rounded-xl font-bold transition-all duration-300">
                    Login to Play
                  </button>
                </Link>
              </div>
            </div>
          </div>

          {/* Game Statistics */}
          <div className="space-y-8">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
                  <Gamepad2 className="w-6 h-6 text-blue-600" />
                  Game Statistics
                </h2>
              </div>
              <div className="p-6 space-y-6">
                {gameStats.map((game) => (
                  <div key={game.game} className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 border border-gray-100">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-bold text-lg text-gray-900">{game.game}</h4>
                      <span className="bg-blue-100 text-blue-700 px-3 py-1 rounded-full text-sm font-medium">
                        {game.players} players
                      </span>
                    </div>

                    <div className="space-y-3 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Total Matches:</span>
                        <span className="font-bold text-gray-900">{game.totalMatches.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Avg. Diamonds:</span>
                        <span className="font-bold text-blue-600 flex items-center">
                          <Gem className="w-4 h-4 mr-1" />
                          {game.avgDiamonds}
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">Top Player:</span>
                        <span className="font-bold text-purple-600">{game.topPlayer}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Platform Stats */}
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100">
              <div className="p-6 border-b border-gray-100">
                <h2 className="text-xl font-bold text-gray-900 flex items-center gap-3">
                  <Star className="w-6 h-6 text-yellow-500" />
                  Platform Stats
                </h2>
              </div>
              <div className="p-6 space-y-6">
                <div className="text-center bg-blue-50 rounded-xl p-4">
                  <div className="text-4xl font-bold text-blue-600 mb-2">2,773</div>
                  <div className="text-sm font-medium text-gray-600">Total Players</div>
                </div>

                <div className="text-center bg-green-50 rounded-xl p-4">
                  <div className="text-4xl font-bold text-green-600 mb-2">7,590</div>
                  <div className="text-sm font-medium text-gray-600">Matches Played</div>
                </div>

                <div className="text-center bg-purple-50 rounded-xl p-4">
                  <div className="text-4xl font-bold text-purple-600 mb-2">₱184,750</div>
                  <div className="text-sm font-medium text-gray-600">Total Payouts</div>
                </div>

                <div className="text-center pt-4 border-t border-gray-200">
                  <div className="text-lg font-bold text-gray-900 mb-2 flex items-center justify-center">
                    <Flame className="w-5 h-5 mr-2 text-orange-500" />
                    Hot Streak
                  </div>
                  <div className="text-sm text-gray-600 bg-orange-50 rounded-lg p-3">
                    <span className="font-semibold text-orange-600">MLLegend2024</span> - 12 wins in a row!
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
