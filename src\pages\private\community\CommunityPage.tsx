import { useState, useCallback } from 'react'
import {
  MessageCircle,
  Users,
  Hash,
  Settings,
  Search,
  Trophy,
  Bell,
  Plus,
  Filter,
  Grid3X3,
  BarChart3,
  Megaphone
} from 'lucide-react'
import { useAuth } from '../../../contexts/AuthContext'

// Import components
import GlobalChat from './components/GlobalChat'
import GroupCard from './components/GroupCard'
import CreateGroupModal from './components/CreateGroupModal'
import LeaderboardPanel from './components/LeaderboardPanel'
import UserPopover from './components/UserPopover'
import AnnouncementBanner from './components/AnnouncementBanner'


// Import hooks and types
import { useCommunityData } from './hooks/useCommunityData'
import { UserProfile, Group } from './types'

export default function CommunityPage() {
  const { user } = useAuth()
  const {
    state,
    setState,
    messages,
    groups,
    leaderboard,
    announcements,
    sendMessage,
    likeMessage,
    reactToMessage,
    createGroup,
    joinGroup,
    dismissAnnouncement,
    // fetchUserProfile // TODO: Confirm usage
  } = useCommunityData()

  // Local state for modals and popovers
  const [showCreateGroup, setShowCreateGroup] = useState(false)
  const [selectedUserProfile] = useState<UserProfile | null>(null) // TODO: Confirm setSelectedUserProfile usage
  const [userPopoverPosition] = useState({ x: 0, y: 0 }) // TODO: Confirm setUserPopoverPosition usage
  const [showUserPopover, setShowUserPopover] = useState(false)

  // Handle user profile click
  // const handleUserClick = useCallback(async (userId: string, event: React.MouseEvent) => { // TODO: Confirm usage
  //   const rect = event.currentTarget.getBoundingClientRect()
  //   setUserPopoverPosition({ x: rect.left, y: rect.bottom })
  //
  //   try {
  //     const profile = await fetchUserProfile(userId)
  //     setSelectedUserProfile(profile)
  //     setShowUserPopover(true)
  //   } catch (error) {
  //     console.error('Failed to fetch user profile:', error)
  //   }
  // }, [fetchUserProfile])

  // Handle group actions
  const handleJoinGroup = useCallback(async (groupId: string) => {
    try {
      await joinGroup(groupId)
      // Show success message or update UI
    } catch (error) {
      console.error('Failed to join group:', error)
    }
  }, [joinGroup])

  const handleViewGroup = useCallback((groupId: string) => {
    const group = groups.find(g => g.id === groupId)
    if (group) {
      setState(prev => ({ ...prev, selectedGroup: group }))
      // Navigate to group view or open modal
    }
  }, [groups, setState])

  const handleCreateGroup = useCallback(async (groupData: Partial<Group>) => {
    try {
      await createGroup(groupData, user?.id || '')
      setShowCreateGroup(false)
      // Show success message
    } catch (error) {
      console.error('Failed to create group:', error)
    }
  }, [createGroup])

  // Get leaderboard data based on type
  const getLeaderboardData = (type: 'diamonds' | 'wins' | 'referrals') => {
    return leaderboard.map(entry => ({
      ...entry,
      value: type === 'diamonds' ? ((entry as any).diamonds || entry.value) :
             type === 'wins' ? ((entry as any).wins || entry.value) :
             ((entry as any).referrals || entry.value) // For referrals
    }))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div className="max-w-7xl mx-auto px-3 sm:px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Users className="w-4 h-4 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">Community Hub</h1>
                <p className="text-xs text-gray-500 hidden sm:block">Connect, compete, and conquer together</p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <div className="relative hidden md:block">
                <Search className="w-3 h-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search players, groups..."
                  value={state.searchTerm}
                  onChange={(e) => setState(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="w-56 pl-7 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <button className="p-1.5 hover:bg-gray-100 rounded-md transition-colors relative">
                <Bell className="w-4 h-4 text-gray-600" />
                <div className="absolute -top-0.5 -right-0.5 w-2 h-2 bg-red-500 rounded-full"></div>
              </button>
              <button className="p-1.5 hover:bg-gray-100 rounded-md transition-colors">
                <Settings className="w-4 h-4 text-gray-600" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-3 sm:px-4 py-4">
        {/* Announcement Banner */}
        <AnnouncementBanner
          announcements={announcements}
          onDismiss={dismissAnnouncement}
          onReadMore={(announcement) => {
            // Handle read more action
            console.log('Read more:', announcement)
          }}
        />

        {/* Navigation Tabs */}
        <div className="mb-4">
          <div className="flex bg-white rounded-lg p-1 shadow-sm border border-gray-200">
            {([
              { key: 'chat', label: 'Global Chat', icon: MessageCircle },
              { key: 'friends', label: 'Friends', icon: Users },
              { key: 'groups', label: 'Groups', icon: Hash },
              { key: 'leaderboard', label: 'Leaderboards', icon: Trophy },
              { key: 'announcements', label: 'News', icon: Megaphone }
            ] as const).map((tab) => (
              <button
                key={tab.key}
                onClick={() => setState(prev => ({ ...prev, activeTab: tab.key as any }))}
                className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-all ${
                  state.activeTab === tab.key
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                <tab.icon className="w-3 h-3 mr-1.5" />
                <span className="hidden sm:inline">{tab.label}</span>
                <span className="sm:hidden">{tab.label.split(' ')[0]}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        {state.activeTab === 'chat' && (
          <div className="grid lg:grid-cols-4 gap-4">
            {/* Main Chat */}
            <div className="lg:col-span-3">
              <GlobalChat
                messages={messages}
                onSendMessage={(message: string) => sendMessage(message, user?.id || '')}
                onLikeMessage={likeMessage}
                onReactToMessage={reactToMessage}
                isLoading={state.isLoading}
              />
            </div>

            {/* Chat Sidebar */}
            <div className="space-y-4">
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                <h4 className="text-sm font-semibold text-gray-900 mb-3">Online Players</h4>
                <div className="space-y-2">
                  {[
                    { name: 'MLLegend', status: 'In Match', avatar: 'ML' },
                    { name: 'ValoKing', status: 'Online', avatar: 'VK' },
                    { name: 'ProGamer', status: 'Away', avatar: 'PG' }
                  ].map((player) => (
                    <div key={player.name} className="flex items-center gap-2 p-2 rounded hover:bg-gray-50 cursor-pointer">
                      <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-xs font-medium text-blue-700">
                        {player.avatar}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{player.name}</p>
                        <p className="text-xs text-gray-500">{player.status}</p>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${
                        player.status === 'Online' ? 'bg-green-500' :
                        player.status === 'In Match' ? 'bg-blue-500' :
                        'bg-gray-400'
                      }`}></div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3">
                <h4 className="text-sm font-semibold text-gray-900 mb-3">Quick Stats</h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">Messages Today</span>
                    <span className="text-sm font-semibold text-blue-600">1,247</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">Active Users</span>
                    <span className="text-sm font-semibold text-green-600">342</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-600">Peak Online</span>
                    <span className="text-sm font-semibold text-purple-600">1,856</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}



        {state.activeTab === 'groups' && (
          <div className="space-y-4">
            {/* Groups Header */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Groups & Communities</h2>
                <p className="text-sm text-gray-600">Join or create gaming communities</p>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex items-center gap-1">
                  <Filter className="w-4 h-4 text-gray-500" />
                  <select
                    value={state.groupFilter}
                    onChange={(e) => setState(prev => ({ ...prev, groupFilter: e.target.value as any }))}
                    className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Groups</option>
                    <option value="joined">My Groups</option>
                    <option value="available">Available</option>
                    <option value="recommended">Recommended</option>
                  </select>
                </div>
                <button
                  onClick={() => setShowCreateGroup(true)}
                  className="flex items-center gap-1 px-3 py-1.5 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-3 h-3" />
                  Create Group
                </button>
              </div>
            </div>

            {/* Groups Grid */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {groups.map((group) => (
                <GroupCard
                  key={group.id}
                  group={group}
                  onJoin={handleJoinGroup}
                  onView={handleViewGroup}
                  isJoined={group.memberCount > 0} // Simplified check
                />
              ))}
            </div>

            {groups.length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Hash className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No groups found</h3>
                <p className="text-gray-600 mb-4">Try adjusting your search or create a new group</p>
                <button
                  onClick={() => setShowCreateGroup(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  Create Your First Group
                </button>
              </div>
            )}
          </div>
        )}

        {state.activeTab === 'leaderboard' && (
          <div className="grid lg:grid-cols-3 gap-4">
            {/* Leaderboard Panels */}
            <LeaderboardPanel
              type="diamonds"
              entries={getLeaderboardData('diamonds')}
              isLoading={state.isLoading}
            />
            <LeaderboardPanel
              type="wins"
              entries={getLeaderboardData('wins')}
              isLoading={state.isLoading}
            />
            <LeaderboardPanel
              type="referrals"
              entries={getLeaderboardData('referrals')}
              isLoading={state.isLoading}
            />
          </div>
        )}

        {state.activeTab === 'announcements' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">News & Announcements</h2>
                <p className="text-sm text-gray-600">Stay updated with the latest news</p>
              </div>
              <div className="flex items-center gap-2">
                <button className="p-2 hover:bg-gray-100 rounded-md transition-colors">
                  <Filter className="w-4 h-4 text-gray-600" />
                </button>
                <button className="p-2 hover:bg-gray-100 rounded-md transition-colors">
                  <Grid3X3 className="w-4 h-4 text-gray-600" />
                </button>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {announcements.filter(a => a.isActive).map((announcement) => (
                <div key={announcement.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start gap-3 mb-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      announcement.type === 'update' ? 'bg-blue-100 text-blue-600' :
                      announcement.type === 'event' ? 'bg-green-100 text-green-600' :
                      announcement.type === 'tournament' ? 'bg-yellow-100 text-yellow-600' :
                      'bg-gray-100 text-gray-600'
                    }`}>
                      {announcement.type === 'update' && <BarChart3 className="w-4 h-4" />}
                      {announcement.type === 'event' && <Trophy className="w-4 h-4" />}
                      {announcement.type === 'tournament' && <Trophy className="w-4 h-4" />}
                      {announcement.type === 'maintenance' && <Settings className="w-4 h-4" />}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm font-semibold text-gray-900 mb-1">{announcement.title}</h3>
                      <span className={`inline-block px-2 py-0.5 rounded-full text-xs font-medium ${
                        announcement.type === 'update' ? 'bg-blue-100 text-blue-700' :
                        announcement.type === 'event' ? 'bg-green-100 text-green-700' :
                        announcement.type === 'tournament' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-gray-100 text-gray-700'
                      }`}>
                        {announcement.type.charAt(0).toUpperCase() + announcement.type.slice(1)}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-700 mb-3 line-clamp-3">{announcement.content}</p>
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>{new Date(announcement.createdAt).toLocaleDateString()}</span>
                    {announcement.expiresAt && (
                      <span>Expires {new Date(announcement.expiresAt).toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {announcements.filter(a => a.isActive).length === 0 && (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Megaphone className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No announcements</h3>
                <p className="text-gray-600">Check back later for updates and news</p>
              </div>
            )}
          </div>
        )}

        {/* Modals and Popovers */}
        <CreateGroupModal
          isOpen={showCreateGroup}
          onClose={() => setShowCreateGroup(false)}
          onSubmit={handleCreateGroup}
          isLoading={state.isLoading}
        />

        <UserPopover
          user={selectedUserProfile}
          isOpen={showUserPopover}
          onClose={() => setShowUserPopover(false)}
          onAddFriend={() => {
            console.log('Add friend')
            setShowUserPopover(false)
          }}
          onMessage={() => {
            console.log('Send message')
            setShowUserPopover(false)
          }}
          onReport={() => {
            console.log('Report user')
            setShowUserPopover(false)
          }}
          position={userPopoverPosition}
        />
      </div>
    </div>
  )
}
