import { useState, useEffect, useCallback } from 'react'
import {
  Message,
  Group,
  LeaderboardEntry,
  Announcement,
  UserProfile,
  GroupMember,
  CommunityState
} from '../types/index'
// TODO: communityService not yet extracted - using old import temporarily
import { communityService } from '../../../../services/supabase'
import { leaderboardService } from '../../../../services/leaderboardService'
import { supabase } from '../../../../services/supabaseClient'

// Mock data for development - replace with real API calls
// const mockMessages: Message[] = [ // TODO: Confirm usage
//   {
//     id: '1',
//     userId: 'user1',
//     username: 'MLLegend',
//     avatar: 'ML',
//     message: 'Anyone up for a 1v1 ML match? 500 diamonds 💎',
//     timestamp: '2m ago',
//     type: 'text',
//     role: 'admin',
//     likes: 5,
//     reactions: [{ emoji: '🔥', count: 3, users: ['user2', 'user3', 'user4'] }]
//   },
//   {
//     id: '2',
//     userId: 'user2',
//     username: '<PERSON>oran<PERSON>G<PERSON>',
//     avatar: 'VG',
//     message: 'Just won my 10th match in a row! On fire! 🔥',
//     timestamp: '5m ago',
//     type: 'text',
//     role: 'member',
//     likes: 12,
//     reactions: []
//   },
//   {
//     id: '3',
//     userId: 'system',
//     username: 'GameModerator',
//     avatar: 'GM',
//     message: '📢 Tournament registration closes in 2 hours!',
//     timestamp: '10m ago',
//     type: 'announcement',
//     role: 'moderator',
//     likes: 25,
//     reactions: []
//   }
// ]

// const mockGroups: Group[] = [ // TODO: Confirm usage
//   {
//     id: '1',
//     name: 'Elite Squad',
//     tag: 'ELIT',
//     description: 'Elite Mobile Legends players seeking competitive matches',
//     tagline: 'Victory through unity',
//     memberCount: 45,
//     maxMembers: 50,
//     isPrivate: false,
//     level: 15,
//     winRate: 83.9,
//     rank: 1,
//     onlineMembers: 12,
//     createdAt: '2024-01-15',
//     ownerId: 'owner1',
//     requirements: { minLevel: 10, minWinRate: 70, minDiamonds: 1000 },
//     categories: ['Mobile Legends', 'Competitive', 'Tournament'],
//     perks: { diamondBonus: 15, xpBonus: 20, matchPriority: true, customRoles: true }
//   },
//   {
//     id: '2',
//     name: 'Valorant Pros',
//     tag: 'VPRO',
//     description: 'Competitive Valorant guild for serious players',
//     memberCount: 38,
//     maxMembers: 50,
//     isPrivate: true,
//     level: 12,
//     winRate: 73.8,
//     rank: 3,
//     onlineMembers: 8,
//     createdAt: '2024-02-01',
//     ownerId: 'owner2',
//     requirements: { minLevel: 8, minWinRate: 65, minDiamonds: 500 },
//     categories: ['Valorant', 'Competitive'],
//     perks: { diamondBonus: 10, xpBonus: 15, matchPriority: false, customRoles: false }
//   }
// ]

const mockLeaderboard: LeaderboardEntry[] = [
  { rank: 1, userId: 'user1', username: 'MLLegend', avatar: 'ML', value: 15420, change: 5, guild: 'Elite Squad', isOnline: true },
  { rank: 2, userId: 'user2', username: 'ValoKing', avatar: 'VK', value: 12890, change: -2, guild: 'Valorant Pros', isOnline: true },
  { rank: 3, userId: 'user3', username: 'CoDMaster', avatar: 'CM', value: 11250, change: 1, guild: 'COD Warriors', isOnline: false }
]

const mockAnnouncements: Announcement[] = [
  {
    id: '1',
    title: 'Patch 1.2 Now Live!',
    content: 'New features include enhanced matchmaking, improved chat system, and bug fixes.',
    type: 'update',
    isActive: true,
    createdAt: '2024-03-15',
    expiresAt: '2024-03-22'
  },
  {
    id: '2',
    title: 'Weekly Tournament Starting Soon',
    content: 'Join the Mobile Legends tournament this weekend. Prize pool: 50,000 diamonds!',
    type: 'tournament',
    isActive: true,
    createdAt: '2024-03-14'
  }
]

export const useCommunityData = () => {
  const [state, setState] = useState<CommunityState>({
    activeTab: 'chat',
    selectedGroup: null,
    selectedUser: null,
    chatChannel: 'global',
    groupFilter: 'all',
    groupSort: 'rank',
    leaderboardType: 'diamonds',
    searchTerm: '',
    isLoading: true,
    error: null
  })

  const [messages, setMessages] = useState<Message[]>([])
  const [groups, setGroups] = useState<Group[]>([])
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([])
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [groupMembers] = useState<GroupMember[]>([]) // TODO: Confirm setGroupMembers usage

  // Load real data on mount
  useEffect(() => {
    const loadCommunityData = async () => {
      setState(prev => ({ ...prev, isLoading: true, error: null }))
      try {
        // Load global chat messages
        const { data: messagesData, error: messagesError } = await communityService.getGlobalMessages(50)
        if (messagesError) {
          console.error('Error loading messages:', messagesError)
        } else {
          const transformedMessages = (messagesData || []).map((msg: any) => ({
            id: msg.id,
            userId: msg.user_id,
            username: msg.user?.username || 'Unknown',
            avatar: (msg.user?.username || 'U').substring(0, 2).toUpperCase(),
            message: msg.message,
            timestamp: formatTimestamp(msg.created_at),
            type: msg.message_type || 'text',
            role: 'member',
            likes: msg.likes || 0,
            reactions: msg.reactions || []
          }))
          setMessages(transformedMessages as any)
        }

        // Load community groups
        const { data: groupsData, error: groupsError } = await communityService.getCommunityGroups(20)
        if (groupsError) {
          console.error('Error loading groups:', groupsError)
        } else {
          const transformedGroups = (groupsData || []).map((group: any) => ({
            id: group.id,
            name: group.name,
            tag: group.tag,
            description: group.description,
            tagline: group.tagline || '',
            logo: group.logo_url || '',
            banner: group.banner_url || '',
            rank: group.rank || 999,
            level: group.level || 1,
            memberCount: group.current_members || 0,
            maxMembers: group.max_members || 50,
            winRate: group.win_rate || 0,
            onlineMembers: group.online_members || Math.floor((group.current_members || 0) * 0.25),
            requirements: group.requirements || {},
            categories: group.categories || [],
            perks: group.perks || {},
            isPrivate: group.is_private || false,
            owner: group.owner?.username || 'Unknown'
          }))
          setGroups(transformedGroups as any)
        }

        // Load leaderboard using improved service
        const { data: leaderboardData, error: leaderboardError } = await leaderboardService.getLeaderboard('diamonds', 10)
        if (leaderboardError) {
          console.error('Error loading leaderboard:', leaderboardError)
          // Fallback to mock data if database fails
          setLeaderboard(mockLeaderboard)
        } else if (leaderboardData) {
          const transformedLeaderboard = leaderboardData.entries.map((entry: any) => ({
            rank: entry.rank,
            userId: entry.userId,
            username: entry.username,
            avatar: entry.avatar,
            value: entry.value,
            change: entry.change,
            guild: entry.guild || 'Gaming Squad',
            isOnline: entry.isOnline,
            level: entry.level,
            winRate: entry.winRate,
            totalMatches: entry.totalMatches,
            badges: entry.badges
          }))
          setLeaderboard(transformedLeaderboard)
        } else {
          // Fallback to mock data
          setLeaderboard(mockLeaderboard)
        }

        // Use mock announcements for now
        setAnnouncements(mockAnnouncements)

      } catch (error) {
        console.error('Error loading community data:', error)
        setState(prev => ({ ...prev, error: 'Failed to load community data' }))
      } finally {
        setState(prev => ({ ...prev, isLoading: false }))
      }
    }

    loadCommunityData()

    // Set up real-time subscriptions for community updates
    const groupSubscription = supabase
      .channel('community_groups_changes')
      .on('postgres_changes',
        { event: '*', schema: 'public', table: 'community_groups' },
        (payload) => {
          console.log('Group update received:', payload)
          // Refresh groups data when changes occur
          loadCommunityData()
        }
      )
      .subscribe()

    return () => {
      groupSubscription.unsubscribe()
    }
  }, [])



  // Helper function to format timestamps
  const formatTimestamp = (timestamp: string) => {
    const now = new Date()
    const messageTime = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - messageTime.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) return 'now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return `${Math.floor(diffInMinutes / 1440)}d ago`
  }

  // Helper function to determine leaderboard rank changes
  // const getLeaderboardChange = (index: number): string => { // TODO: Confirm usage
  //   const changes = ['+2', '-1', '+1', '0', '+3', '-2', '+1', '0', '+1', '-1']
  //   return changes[index] || '0'
  // }

  // Helper function to check if user was recently active (within 30 minutes)
  // const isRecentlyActive = (lastActive: string): boolean => { // TODO: Confirm usage
  //   const now = new Date()
  //   const lastActiveTime = new Date(lastActive)
  //   const diffInMinutes = Math.floor((now.getTime() - lastActiveTime.getTime()) / (1000 * 60))
  //   return diffInMinutes <= 30
  // }

  // Helper function to calculate user reputation based on performance
  // const calculateReputation = (wins: number, totalMatches: number, diamonds: number): number => { // TODO: Confirm usage
  //   const winRateScore = totalMatches > 0 ? (wins / totalMatches) * 50 : 0
  //   const activityScore = Math.min(totalMatches * 0.5, 25)
  //   const wealthScore = Math.min(diamonds / 1000, 25)
  //   return Math.floor(winRateScore + activityScore + wealthScore)
  // }

  // Enhanced real-time message handler
  const handleNewMessage = useCallback((newMessageData: any) => {
    const transformedMessage: Message = {
      id: newMessageData.id,
      userId: newMessageData.user_id,
      username: newMessageData.user?.username || 'Unknown',
      avatar: (newMessageData.user?.username || 'U').substring(0, 2).toUpperCase(),
      message: newMessageData.message,
      timestamp: formatTimestamp(newMessageData.created_at),
      type: newMessageData.message_type || 'text',
      role: 'member',
      likes: newMessageData.likes || 0,
      reactions: newMessageData.reactions || []
    }

    setMessages(prev => {
      // Check if message already exists to avoid duplicates
      if (prev.some(msg => msg.id === transformedMessage.id)) {
        return prev
      }
      // Add new message and keep last 50 messages
      return [...prev.slice(-49), transformedMessage]
    })
  }, [])

  // API Functions with real Supabase integration
  const sendMessage = useCallback(async (content: string, userId: string) => {
    try {
      const { data, error } = await communityService.sendGlobalMessage(userId, content)
      if (error) {
        console.error('Error sending message:', error)
        return
      }

      // Add the new message to local state
      const newMessage: Message = {
        id: data.id,
        userId: data.user_id,
        username: data.user?.username || 'You',
        avatar: (data.user?.username || 'Y').substring(0, 2).toUpperCase(),
        message: data.message,
        timestamp: 'now',
        type: data.message_type || 'text',
        role: 'member',
        likes: 0,
        reactions: []
      }

      setMessages(prev => [...prev, newMessage])
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }, [])

  const likeMessage = useCallback(async (messageId: string) => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId 
        ? { ...msg, likes: msg.likes + 1 }
        : msg
    ))
  }, [])

  const reactToMessage = useCallback(async (messageId: string, emoji: string) => {
    setMessages(prev => prev.map(msg => {
      if (msg.id === messageId) {
        const existingReaction = msg.reactions.find(r => r.emoji === emoji)
        if (existingReaction) {
          return {
            ...msg,
            reactions: msg.reactions.map(r =>
              r.emoji === emoji
                ? { ...r, count: r.count + 1, users: [...r.users, 'current-user'] }
                : r
            )
          }
        } else {
          return {
            ...msg,
            reactions: [...msg.reactions, { emoji, count: 1, users: ['current-user'] }]
          }
        }
      }
      return msg
    }))
  }, [])

  const createGroup = useCallback(async (groupData: Partial<Group>, userId: string) => {
    setState(prev => ({ ...prev, isLoading: true }))

    try {
      const { data, error } = await communityService.createGroup(userId, {
        name: groupData.name!,
        tag: groupData.tag!,
        description: groupData.description!,
        tagline: groupData.tagline,
        maxMembers: groupData.maxMembers || 50,
        isPrivate: groupData.isPrivate || false,
        requirements: groupData.requirements || { minLevel: 1, minWinRate: 0, minDiamonds: 0 },
        categories: groupData.categories || [],
        perks: { diamondBonus: 5, xpBonus: 10, matchPriority: false, customRoles: false }
      })

      if (error) {
        throw new Error((error as any)?.message || 'Failed to create group')
      }

      const newGroup: Group = {
        id: data.id,
        name: data.name,
        tag: data.tag,
        description: data.description,
        tagline: data.tagline,
        memberCount: 1,
        maxMembers: data.max_members,
        isPrivate: data.is_private,
        level: data.level,
        winRate: data.win_rate,
        rank: data.rank || groups.length + 1,
        onlineMembers: 1,
        createdAt: data.created_at,
        ownerId: userId,
        requirements: data.requirements,
        categories: data.categories,
        perks: data.perks
      }

      setGroups(prev => [...prev, newGroup])
      setState(prev => ({ ...prev, isLoading: false }))
      return newGroup
    } catch (error: any) {
      setState(prev => ({ ...prev, isLoading: false, error: error.message || 'Failed to create group' }))
      throw error
    }
  }, [groups.length])

  const joinGroup = useCallback(async (groupId: string) => {
    setGroups(prev => prev.map(group =>
      group.id === groupId
        ? { ...group, memberCount: group.memberCount + 1 }
        : group
    ))
  }, [])

  const leaveGroup = useCallback(async (groupId: string) => {
    setGroups(prev => prev.map(group =>
      group.id === groupId
        ? { ...group, memberCount: Math.max(0, group.memberCount - 1) }
        : group
    ))
  }, [])

  const dismissAnnouncement = useCallback((announcementId: string) => {
    setAnnouncements(prev => prev.map(ann =>
      ann.id === announcementId
        ? { ...ann, isActive: false }
        : ann
    ))
  }, [])

  const fetchUserProfile = useCallback(async (userId: string): Promise<UserProfile | null> => {
    try {
      const { data, error } = await (communityService as any).getUserProfile(userId)

      if (error) {
        console.error('Error fetching user profile:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
  }, [])

  // Filtered and sorted data
  const filteredGroups = groups.filter(group => {
    const matchesSearch = group.name.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
                         group.description.toLowerCase().includes(state.searchTerm.toLowerCase()) ||
                         group.tag.toLowerCase().includes(state.searchTerm.toLowerCase())

    const matchesFilter = state.groupFilter === 'all' ||
                         (state.groupFilter === 'joined' && group.memberCount > 0) ||
                         (state.groupFilter === 'available' && group.memberCount < group.maxMembers) ||
                         (state.groupFilter === 'recommended' && group.level >= 10)

    return matchesSearch && matchesFilter
  }).sort((a, b) => {
    switch (state.groupSort) {
      case 'rank':
        return a.rank - b.rank
      case 'members':
        return b.memberCount - a.memberCount
      case 'activity':
        return b.onlineMembers - a.onlineMembers
      case 'level':
        return b.level - a.level
      default:
        return 0
    }
  })

  return {
    // State
    state,
    setState,
    
    // Data
    messages,
    groups: filteredGroups,
    leaderboard,
    announcements,
    groupMembers,
    
    // Actions
    sendMessage,
    likeMessage,
    reactToMessage,
    createGroup,
    joinGroup,
    leaveGroup,
    dismissAnnouncement,
    fetchUserProfile,
    handleNewMessage
  }
}
