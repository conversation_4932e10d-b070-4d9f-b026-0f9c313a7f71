// 🎯 TYPE DEFINITIONS
// TypeScript interfaces and types for the Gambets platform

/**
 * 👤 USER TYPES
 * User authentication and profile related types
 */
export interface User {
  id: string
  email: string
  username: string
  displayName: string
  gambetsId: string
  mlbbId?: string
  avatar?: string
  diamond_balance: number
  totalWins: number
  totalLosses: number
  rank: number
  isOnline: boolean
  isReferee: boolean
  isAdmin: boolean
  isVerified?: boolean // Added for verified user badge
  referralCode: string
  referredBy?: string
  createdAt: string
  updatedAt: string
}

export interface UserProfile extends User {
  bio?: string
  favoriteGames: string[]
  achievements: Achievement[]
  matchHistory: Match[]
  referrals: User[]
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface SignupData {
  email: string
  username: string
  displayName: string
  password: string
  mlbbId?: string
  referralCode?: string
}

/**
 * 🎮 GAME TYPES
 * Game and match related types
 */
export type GameType =
  | 'mobile-legends'
  | 'valorant'
  | 'call-of-duty'
  | 'wild-rift'
  | 'dota-2'
  | 'cs-go'
  | 'honor-of-kings'

export type MatchStatus = 
  | 'open'
  | 'full'
  | 'ongoing'
  | 'completed'
  | 'cancelled'

export type MatchType = 
  | 'ranked'
  | 'casual'
  | 'tournament'
  | 'custom'

// Unified Match interface that matches database schema
export interface Match {
  // Core match data (matches database columns exactly)
  id: string
  title: string
  game: string
  mode: string // This is the format (1v1, 2v2, etc.)
  game_type?: string
  status: 'open' | 'waiting' | 'full' | 'in_progress' | 'ongoing' | 'completed' | 'cancelled' | 'disputed'

  // Financial data
  entry_fee: number
  pot_amount: number
  diamond_pot: number

  // Player data
  max_players: number
  current_players: number

  // Host and participants
  host_id: string
  host_username?: string
  host_first_name?: string
  host_last_name?: string

  // Referee data
  referee_id?: string
  referee_username?: string
  referee_first_name?: string
  referee_last_name?: string

  // Winner data
  winner_id?: string
  winner_username?: string

  // Match details
  room_code?: string
  match_link?: string
  region?: string
  rules?: string
  referee_notes?: string

  // Timing
  scheduled_start_time?: string
  actual_start_time?: string
  actual_end_time?: string
  created_at: string
  updated_at?: string

  // Computed fields for UI
  timeLeft?: string
  participants?: MatchPlayer[]

  // Legacy fields for backward compatibility
  format?: string // Maps to mode
  diamondPot?: number // Maps to entry_fee
  totalPot?: number // Maps to pot_amount
  creator?: string // Maps to host_username
  playersJoined?: number // Maps to current_players
  startTime?: string // Maps to scheduled_start_time or created_at
}

export interface MatchPlayer {
  user: User
  joinedAt: string
  position?: number
  score?: number
  isReady: boolean
}

export interface MatchResult {
  playerId: string
  position: number
  score: number
  kills?: number
  deaths?: number
  assists?: number
  mvp: boolean
}

export interface CreateMatchData {
  title: string
  game: GameType
  type: MatchType
  entryFee: number
  maxPlayers: number
  startTime: string
  rules: string[]
  requirements: string[]
}

/**
 * 🏆 LEADERBOARD TYPES
 * Ranking and statistics types
 */
export interface LeaderboardEntry {
  rank: number
  user: User
  wins: number
  losses: number
  winRate: number
  totalMatches: number
  totalEarnings: number
  favoriteGame: GameType
}

export interface GameStats {
  game: GameType
  wins: number
  losses: number
  winRate: number
  totalMatches: number
  averageScore: number
  bestScore: number
  totalEarnings: number
}

export interface Achievement {
  id: string
  title: string
  description: string
  icon: string
  rarity: 'common' | 'rare' | 'epic' | 'legendary'
  unlockedAt?: string
  progress?: number
  maxProgress?: number
}

/**
 * 💎 PAYMENT TYPES
 * Diamond purchase and transaction types
 */
export interface DiamondPackage {
  id: string
  name: string
  diamonds: number
  price: number
  bonus: number
  popular: boolean
  description: string
}

export interface Transaction {
  id: string
  userId: string
  type: 'purchase' | 'match_entry' | 'match_win' | 'referral_bonus'
  amount: number
  diamonds: number
  description: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod?: 'gcash' | 'maya' | 'admin'
  referenceNumber?: string
  createdAt: string
  completedAt?: string
}

export interface PaymentData {
  packageId: string
  paymentMethod: 'gcash' | 'maya'
  phoneNumber: string
  amount: number
}

/**
 * 🛡️ REFEREE TYPES
 * Referee system related types
 */
export interface RefereeApplication {
  id: string
  userId: string
  user: User
  experience: string
  games: GameType[]
  availability: string[]
  motivation: string
  status: 'pending' | 'approved' | 'rejected'
  reviewedBy?: string
  reviewNotes?: string
  submittedAt: string
  reviewedAt?: string
}

export interface RefereeStats {
  totalMatches: number
  completedMatches: number
  averageRating: number
  totalEarnings: number
  specializations: GameType[]
}

/**
 * 💬 CHAT TYPES
 * Community and messaging types
 */
export interface ChatMessage {
  id: string
  userId: string
  user: User
  content: string
  type: 'text' | 'image' | 'system'
  replyTo?: string
  reactions: ChatReaction[]
  createdAt: string
  editedAt?: string
}

export interface ChatReaction {
  emoji: string
  users: string[]
  count: number
}

export interface ChatRoom {
  id: string
  name: string
  type: 'global' | 'game' | 'match' | 'guild'
  game?: GameType
  matchId?: string
  guildId?: string
  participants: User[]
  messages: ChatMessage[]
  isActive: boolean
  createdAt: string
}

/**
 * 🏰 GUILD TYPES
 * Community group and guild types
 */
export interface Guild {
  id: string
  name: string
  description: string
  logo?: string
  banner?: string
  game: GameType
  maxMembers: number
  currentMembers: number
  isPublic: boolean
  requirements: string[]
  owner: User
  moderators: User[]
  members: GuildMember[]
  stats: GuildStats
  createdAt: string
}

export interface GuildMember {
  user: User
  role: 'owner' | 'moderator' | 'member'
  joinedAt: string
  contribution: number
  isActive: boolean
}

export interface GuildStats {
  totalWins: number
  totalLosses: number
  winRate: number
  totalMatches: number
  averageRank: number
  totalEarnings: number
}

/**
 * 🔔 NOTIFICATION TYPES
 * Notification and alert types
 */
export interface Notification {
  id: string
  user_id: string
  type: string
  title: string
  message: string
  data?: any
  is_read: boolean
  created_at: string
}

export interface NotificationSettings {
  matchInvites: boolean
  matchResults: boolean
  payments: boolean
  system: boolean
  email: boolean
  push: boolean
}

/**
 * 🎯 API TYPES
 * API response and request types
 */
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  limit: number
  hasNext: boolean
  hasPrev: boolean
}

export interface ApiError {
  code: string
  message: string
  details?: any
}

/**
 * 🎨 UI TYPES
 * User interface related types
 */
export interface ToastNotification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export interface ModalProps {
  isOpen: boolean
  onClose: () => void
  title?: string
  children: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  message?: string
}

export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea'
  placeholder?: string
  required?: boolean
  options?: { value: string; label: string }[]
  validation?: (value: any) => string | null
}

/**
 * 🔧 UTILITY TYPES
 * Helper and utility types
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type EventHandler<T = any> = (event: T) => void

export type AsyncFunction<T = any, R = any> = (args: T) => Promise<R>

/**
 * 🌐 ROUTE TYPES
 * Navigation and routing types
 */
export interface RouteConfig {
  path: string
  component: React.ComponentType
  isPrivate: boolean
  requiresAuth: boolean
  requiresAdmin?: boolean
  title: string
  description?: string
}

export interface NavigationItem {
  label: string
  path: string
  icon: string
  isPrivate: boolean
  requiresAdmin?: boolean
  children?: NavigationItem[]
}

/**
 * 📊 ANALYTICS TYPES
 * Analytics and tracking types
 */
export interface AnalyticsEvent {
  event: string
  category: string
  action: string
  label?: string
  value?: number
  userId?: string
  timestamp: string
}

export interface UserActivity {
  userId: string
  action: string
  details: any
  timestamp: string
  ipAddress?: string
  userAgent?: string
}

/**
 * ⚙️ CONFIG TYPES
 * Configuration and settings types
 */
export interface AppConfig {
  apiUrl: string
  supabaseUrl: string
  supabaseKey: string
  environment: 'development' | 'staging' | 'production'
  features: {
    chat: boolean
    guilds: boolean
    tournaments: boolean
    referrals: boolean
  }
  limits: {
    maxMatchPlayers: number
    maxDiamondPurchase: number
    maxGuildMembers: number
    chatMessageLength: number
  }
}

export interface GameConfig {
  [key: string]: {
    name: string
    icon: string
    maxPlayers: number
    defaultRules: string[]
    rankSystem: string[]
  }
}
