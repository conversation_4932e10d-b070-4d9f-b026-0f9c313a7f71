import { useState, useEffect } from 'react'
import { Trophy, Crown, Medal, TrendingUp, Calendar, Users } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
// TODO: platformService not yet extracted - using old import temporarily
import { platformService } from '../../services/supabase'

interface LeaderboardEntry {
  id: string
  username: string
  avatar_url?: string
  wins: number
  losses: number
  total_earnings: number
  win_rate: number
  rank: number
}

export default function Leaderboard() {
  const { user } = useAuth()
  const [leaderboardData, setLeaderboardData] = useState<LeaderboardEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [timeframe, setTimeframe] = useState<'weekly' | 'monthly'>('weekly')
  const [userRank, setUserRank] = useState<number | null>(null)

  useEffect(() => {
    loadLeaderboard()
  }, [timeframe])

  const loadLeaderboard = async () => {
    setLoading(true)
    try {
      const result = await platformService.getLeaderboard(timeframe)
      if (result.data) {
        setLeaderboardData(result.data)
        
        // Find user's rank
        const userEntry = result.data.find(entry => entry.id === user?.id)
        setUserRank(userEntry?.rank || null)
      }
    } catch (error) {
      console.error('Error loading leaderboard:', error)
    } finally {
      setLoading(false)
    }
  }

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-6 h-6 text-yellow-500" />
      case 2:
        return <Medal className="w-6 h-6 text-gray-400" />
      case 3:
        return <Medal className="w-6 h-6 text-amber-600" />
      default:
        return (
          <div className="w-6 h-6 rounded-full bg-gray-200 flex items-center justify-center">
            <span className="text-xs font-semibold text-gray-600">{rank}</span>
          </div>
        )
    }
  }

  const getRankBadgeColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-400 to-yellow-600'
      case 2:
        return 'bg-gradient-to-r from-gray-300 to-gray-500'
      case 3:
        return 'bg-gradient-to-r from-amber-400 to-amber-600'
      default:
        return 'bg-gradient-to-r from-blue-500 to-blue-600'
    }
  }

  const formatEarnings = (earnings: number) => {
    if (earnings >= 1000000) {
      return `${(earnings / 1000000).toFixed(1)}M`
    } else if (earnings >= 1000) {
      return `${(earnings / 1000).toFixed(1)}K`
    }
    return earnings.toString()
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(10)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-xl shadow-sm">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
            <Trophy className="w-5 h-5 text-yellow-500" />
            <span>Leaderboard</span>
          </h2>
          <div className="flex space-x-1">
            <button
              onClick={() => setTimeframe('weekly')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeframe === 'weekly'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <Calendar className="w-4 h-4 inline mr-1" />
              Weekly
            </button>
            <button
              onClick={() => setTimeframe('monthly')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                timeframe === 'monthly'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              <Calendar className="w-4 h-4 inline mr-1" />
              Monthly
            </button>
          </div>
        </div>
      </div>

      {/* User's Rank (if not in top 10) */}
      {userRank && userRank > 10 && (
        <div className="px-6 py-3 bg-blue-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                <span className="text-white text-sm font-semibold">{userRank}</span>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Your Rank</p>
                <p className="text-xs text-gray-600">Keep playing to climb higher!</p>
              </div>
            </div>
            <TrendingUp className="w-5 h-5 text-blue-600" />
          </div>
        </div>
      )}

      {/* Leaderboard List */}
      <div className="max-h-96 overflow-y-auto">
        {leaderboardData.length === 0 ? (
          <div className="text-center py-12">
            <Trophy className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Rankings Yet</h3>
            <p className="text-gray-600">Play matches to appear on the leaderboard!</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {leaderboardData.map((entry) => (
              <div
                key={entry.id}
                className={`px-6 py-4 hover:bg-gray-50 ${
                  entry.id === user?.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    {/* Rank */}
                    <div className="flex-shrink-0">
                      {getRankIcon(entry.rank)}
                    </div>

                    {/* User Info */}
                    <div className="flex items-center space-x-3">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getRankBadgeColor(entry.rank)}`}>
                        <span className="text-white font-semibold">
                          {entry.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900 flex items-center space-x-2">
                          <span>{entry.username}</span>
                          {entry.id === user?.id && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                              You
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-gray-500">
                          {entry.wins}W - {entry.losses}L ({entry.win_rate}% win rate)
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Earnings */}
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">
                      {formatEarnings(entry.total_earnings)} 💎
                    </p>
                    <p className="text-xs text-gray-500">Total Earnings</p>
                  </div>
                </div>

                {/* Top 3 Special Styling */}
                {entry.rank <= 3 && (
                  <div className="mt-2 flex items-center justify-center">
                    <div className={`h-1 w-full rounded-full ${
                      entry.rank === 1 ? 'bg-gradient-to-r from-yellow-400 to-yellow-600' :
                      entry.rank === 2 ? 'bg-gradient-to-r from-gray-300 to-gray-500' :
                      'bg-gradient-to-r from-amber-400 to-amber-600'
                    }`}></div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="px-6 py-3 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>{leaderboardData.length} players ranked</span>
          </div>
          <div className="flex items-center space-x-2">
            <TrendingUp className="w-4 h-4" />
            <span>Updated every hour</span>
          </div>
        </div>
      </div>
    </div>
  )
}
