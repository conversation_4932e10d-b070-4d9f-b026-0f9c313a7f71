// 🛡️ ADMIN PROTECTED ROUTE
// Security component for admin-only access

import React, { useState, useEffect } from 'react'
import { Navigate } from 'react-router-dom'
import { Shield, Lock, AlertTriangle, Loader2 } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'

interface AdminProtectedRouteProps {
  children: React.ReactNode
  requiredLevel?: number // Minimum admin level required (1, 2, 3)
}

const AdminProtectedRoute: React.FC<AdminProtectedRouteProps> = ({ 
  children, 
  requiredLevel = 1 
}) => {
  const { user, isLoading } = useAuth()
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null)
  const [adminLevel, setAdminLevel] = useState<number>(0)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    checkAdminAccess()
  }, [user])

  const checkAdminAccess = async () => {
    if (!user) {
      setIsAdmin(false)
      setIsChecking(false)
      return
    }

    try {
      setIsChecking(true)

      // First check if user is admin
      const isUserAdmin = await AdminService.isAdmin(user.id)

      if (!isUserAdmin) {
        console.log('User is not an admin:', user.email)
        setIsAdmin(false)
        setIsChecking(false)
        return
      }

      // Get detailed admin user info
      const adminUser = await AdminService.getAdminUser(user.id)

      if (adminUser && adminUser.admin_level >= requiredLevel) {
        console.log('Admin access granted:', {
          email: user.email,
          level: adminUser.admin_level,
          required: requiredLevel
        })
        setIsAdmin(true)
        setAdminLevel(adminUser.admin_level)

        // Update last admin login
        await AdminService.updateAdminLogin(user.id)

        // Log admin access
        await AdminService.logAdminAction(
          user.id,
          'ADMIN_DASHBOARD_ACCESS',
          'system',
          user.id,
          { required_level: requiredLevel, user_level: adminUser.admin_level }
        )
      } else {
        console.log('Insufficient admin level:', {
          email: user.email,
          level: adminUser?.admin_level || 0,
          required: requiredLevel
        })
        setIsAdmin(false)

        // Log unauthorized access attempt
        if (adminUser) {
          await AdminService.logAdminAction(
            user.id,
            'UNAUTHORIZED_ADMIN_ACCESS_ATTEMPT',
            'system',
            user.id,
            { required_level: requiredLevel, user_level: adminUser.admin_level || 0 }
          )
        }
      }
    } catch (error) {
      console.error('Error checking admin access:', error)
      setIsAdmin(false)
    } finally {
      setIsChecking(false)
    }
  }

  // Show loading state
  if (isLoading || isChecking) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-white/20">
          <Loader2 className="w-12 h-12 text-blue-400 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-bold text-white mb-2">Verifying Admin Access</h2>
          <p className="text-blue-200">Checking your administrative privileges...</p>
        </div>
      </div>
    )
  }

  // Redirect to admin login if not authenticated
  if (!user) {
    return <Navigate to="/admin-login" replace />
  }

  // Show access denied if not admin
  if (isAdmin === false) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-900 via-red-800 to-red-900 flex items-center justify-center p-4">
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 text-center border border-red-400/30 max-w-md">
          <div className="w-20 h-20 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertTriangle className="w-10 h-10 text-red-400" />
          </div>
          
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-red-200 mb-6">
            You don't have administrative privileges to access this area. 
            This incident has been logged for security purposes.
          </p>
          
          <div className="bg-red-500/20 rounded-lg p-4 mb-6 border border-red-400/30">
            <div className="flex items-center space-x-2 text-red-300 text-sm">
              <Lock className="w-4 h-4" />
              <span>Required Admin Level: {requiredLevel}</span>
            </div>
            <div className="flex items-center space-x-2 text-red-300 text-sm mt-1">
              <Shield className="w-4 h-4" />
              <span>Your Level: {adminLevel}</span>
            </div>
          </div>
          
          <button
            onClick={() => window.location.href = '/dashboard'}
            className="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            Return to Dashboard
          </button>
        </div>
      </div>
    )
  }

  // Render admin content
  return <>{children}</>
}

export default AdminProtectedRoute
