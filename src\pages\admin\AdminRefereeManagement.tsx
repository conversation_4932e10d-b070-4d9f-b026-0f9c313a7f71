import { useState, useEffect } from 'react'
import {
  <PERSON>r<PERSON><PERSON><PERSON>,
  <PERSON>,
  CheckCircle,
  XCircle,
  Eye,
  Star,
  Calendar,
  Filter,
  Search,
  RefreshCw,
  User
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService, RefereeApplication } from '../../services/admin'

const AdminRefereeManagement: React.FC = () => {
  const { } = useAuth() // TODO: Confirm user usage
  const [applications, setApplications] = useState<RefereeApplication[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'pending' | 'approved' | 'rejected'>('pending')
  const [searchTerm, setSearchTerm] = useState('')
  const [, setSelectedApplication] = useState<RefereeApplication | null>(null)
  const [,] = useState('')
  const [,] = useState(false)

  useEffect(() => {
    loadApplications()
  }, [filter])

  const loadApplications = async () => {
    try {
      setIsLoading(true)
      const { data } = await AdminService.getRefereeApplications(
        filter === 'all' ? undefined : filter as any
      )
      setApplications(data)
    } catch (error) {
      console.error('Error loading applications:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // const handleReview = async (applicationId: string, decision: 'approved' | 'rejected') => { // TODO: Confirm usage
  //   if (!user) return
  //
  //   try {
  //     setIsReviewing(true)
  //     const { error } = await AdminService.reviewRefereeApplication(
  //       applicationId,
  //       user.id,
  //       decision,
  //       reviewNotes
  //     )
  //
  //     if (!error) {
  //       await loadApplications()
  //       setSelectedApplication(null)
  //       setReviewNotes('')
  //       alert(`Application ${decision} successfully!`)
  //     } else {
  //       alert('Error reviewing application. Please try again.')
  //     }
  //   } catch (error) {
  //     console.error('Error reviewing application:', error)
  //     alert('Error reviewing application. Please try again.')
  //   } finally {
  //     setIsReviewing(false)
  //   }
  // }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            Pending
          </span>
        )
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            Approved
          </span>
        )
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            Rejected
          </span>
        )
      default:
        return null
    }
  }

  const filteredApplications = applications.filter(app =>
    app.user?.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.user?.email?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Referee Management</h1>
            <p className="text-blue-100 mt-1">Review and manage referee applications</p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="bg-white/20 rounded-lg px-4 py-2">
              <div className="text-sm text-blue-100">Pending Applications</div>
              <div className="text-2xl font-bold">{applications.filter(app => app.status === 'pending').length}</div>
            </div>
            <button
              onClick={loadApplications}
              className="flex items-center space-x-2 px-4 py-2 bg-white/20 text-white rounded-lg hover:bg-white/30 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <span className="text-sm font-medium text-gray-700">Filter:</span>
            </div>
            <div className="flex space-x-2">
              {(['all', 'pending', 'approved', 'rejected'] as const).map((status) => (
                <button
                  key={status}
                  onClick={() => setFilter(status)}
                  className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                    filter === status
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {status.charAt(0).toUpperCase() + status.slice(1)}
                </button>
              ))}
            </div>
          </div>
          
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by username or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
      </div>

      {/* Applications Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="bg-white rounded-xl shadow-lg p-6 animate-pulse">
              <div className="flex items-center space-x-4 mb-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
              <div className="space-y-3">
                <div className="h-3 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
              </div>
            </div>
          ))
        ) : filteredApplications.length === 0 ? (
          <div className="col-span-full bg-white rounded-xl shadow-lg p-12 text-center">
            <UserCheck className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No Applications Found</h3>
            <p className="text-gray-600">
              {filter === 'pending' 
                ? 'No pending referee applications at the moment.'
                : `No ${filter} applications found.`
              }
            </p>
          </div>
        ) : (
          filteredApplications.map((application) => (
            <div key={application.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
              {/* Application Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <User className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {application.user?.username || 'Unknown User'}
                      </h3>
                      <p className="text-sm text-gray-600">{application.user?.email}</p>
                    </div>
                  </div>
                  {getStatusBadge(application.status)}
                </div>

                {/* Application Stats */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center justify-center mb-1">
                      <Star className="w-4 h-4 text-yellow-500" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{application.experience_years}</div>
                    <div className="text-xs text-gray-600">Years Experience</div>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center justify-center mb-1">
                      <Clock className="w-4 h-4 text-green-500" />
                    </div>
                    <div className="text-lg font-bold text-gray-900">{application.availability_hours}</div>
                    <div className="text-xs text-gray-600">Hours/Week</div>
                  </div>
                </div>

                {/* Games Expertise */}
                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Game Expertise:</div>
                  <div className="flex flex-wrap gap-1">
                    {application.games_expertise.slice(0, 3).map((game, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                      >
                        {game}
                      </span>
                    ))}
                    {application.games_expertise.length > 3 && (
                      <span className="text-xs text-gray-500 px-2 py-1">
                        +{application.games_expertise.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Application Date */}
                <div className="flex items-center text-sm text-gray-500 mb-4">
                  <Calendar className="w-4 h-4 mr-2" />
                  Applied {formatDate(application.applied_at)}
                </div>
              </div>

              {/* Action Buttons */}
              <div className="p-4 bg-gray-50">
                {application.status === 'pending' ? (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedApplication(application)}
                      className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                      <span>Review</span>
                    </button>
                  </div>
                ) : (
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setSelectedApplication(application)}
                      className="flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                    >
                      <Eye className="w-4 h-4" />
                      <span>View Details</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  )
}

export default AdminRefereeManagement
