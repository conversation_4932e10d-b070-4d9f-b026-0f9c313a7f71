// 🎯 CONSTANTS
// Application-wide constants and configuration values

import type { GameType, DiamondPackage, NavigationItem } from '../types'

/**
 * 🎮 GAME CONSTANTS
 * Supported games and their configurations
 */
export const GAMES: Record<GameType, {
  name: string
  icon: string
  maxPlayers: number
  defaultRules: string[]
  description: string
}> = {
  'mobile-legends': {
    name: 'Mobile Legends: Bang Bang',
    icon: 'Castle',
    maxPlayers: 10,
    defaultRules: [
      'Classic 5v5 match',
      'Draft pick mode',
      'No cheating or hacking',
      'Respect all players'
    ],
    description: 'The most popular MOBA game in Southeast Asia'
  },
  'valorant': {
    name: 'Valorant',
    icon: 'Target',
    maxPlayers: 10,
    defaultRules: [
      'Competitive mode',
      'Best of 25 rounds',
      'No smurfing',
      'Use official game client only'
    ],
    description: 'Tactical FPS with unique agent abilities'
  },
  'call-of-duty': {
    name: 'Call of Duty Mobile',
    icon: 'Zap',
    maxPlayers: 10,
    defaultRules: [
      'Battle Royale or Multiplayer',
      'No emulators allowed',
      'Fair play only',
      'Report any suspicious activity'
    ],
    description: 'Fast-paced mobile FPS action'
  },
  'wild-rift': {
    name: 'League of Legends: Wild Rift',
    icon: 'Swords',
    maxPlayers: 10,
    defaultRules: [
      'Ranked match format',
      'Draft pick mode',
      'No intentional feeding',
      'Communicate respectfully'
    ],
    description: 'Mobile version of the legendary MOBA'
  },
  'dota-2': {
    name: 'Dota 2',
    icon: 'Shield',
    maxPlayers: 10,
    defaultRules: [
      'All Pick or Captain\'s Mode',
      'No scripting or cheats',
      'GG when ancient falls',
      'Respect opponents'
    ],
    description: 'The original competitive MOBA experience'
  },
  'cs-go': {
    name: 'Counter-Strike: Global Offensive',
    icon: 'Bomb',
    maxPlayers: 10,
    defaultRules: [
      'Competitive format',
      'Best of 30 rounds',
      'No external assistance',
      'Use Steam accounts only'
    ],
    description: 'Classic tactical FPS gameplay'
  },
  'honor-of-kings': {
    name: 'Honor of Kings',
    icon: 'Crown',
    maxPlayers: 10,
    defaultRules: [
      'Classic 5v5 match',
      'Ranked mode preferred',
      'No account sharing',
      'Fair play and sportsmanship',
      'Use official game client only'
    ],
    description: 'The world\'s most popular MOBA game'
  }
}

/**
 * 💎 DIAMOND PACKAGES
 * Available diamond purchase options
 */
export const DIAMOND_PACKAGES: DiamondPackage[] = [
  {
    id: 'starter',
    name: 'Starter Pack',
    diamonds: 100,
    price: 50,
    bonus: 0,
    popular: false,
    description: 'Perfect for trying out matches'
  },
  {
    id: 'basic',
    name: 'Basic Pack',
    diamonds: 250,
    price: 120,
    bonus: 25,
    popular: false,
    description: 'Great for casual players'
  },
  {
    id: 'popular',
    name: 'Popular Pack',
    diamonds: 500,
    price: 230,
    bonus: 70,
    popular: true,
    description: 'Most popular choice among players'
  },
  {
    id: 'premium',
    name: 'Premium Pack',
    diamonds: 1000,
    price: 450,
    bonus: 150,
    popular: false,
    description: 'For serious competitive players'
  },
  {
    id: 'ultimate',
    name: 'Ultimate Pack',
    diamonds: 2500,
    price: 1100,
    bonus: 400,
    popular: false,
    description: 'Maximum value for pro players'
  }
]

/**
 * 🧭 NAVIGATION CONSTANTS
 * Application navigation structure
 */
export const PUBLIC_NAVIGATION: NavigationItem[] = [
  {
    label: 'Home',
    path: '/',
    icon: 'Home',
    isPrivate: false
  },
  {
    label: 'Leaderboards',
    path: '/leaderboards',
    icon: 'Trophy',
    isPrivate: false
  },
  {
    label: 'Rules',
    path: '/rules',
    icon: 'FileText',
    isPrivate: false
  },
  {
    label: 'Support',
    path: '/support',
    icon: 'MessageCircle',
    isPrivate: false
  }
]

export const PRIVATE_NAVIGATION: NavigationItem[] = [
  {
    label: 'Dashboard',
    path: '/dashboard',
    icon: 'BarChart3',
    isPrivate: true
  },
  {
    label: 'Matches',
    path: '/matches',
    icon: 'Gamepad2',
    isPrivate: true
  },
  {
    label: 'Profile',
    path: '/profile',
    icon: 'User',
    isPrivate: true
  },
  {
    label: 'Buy Diamonds',
    path: '/buy-diamonds',
    icon: 'Gem',
    isPrivate: true
  },
  {
    label: 'Matchmaking',
    path: '/matchmaking',
    icon: 'Search',
    isPrivate: true
  },
  {
    label: 'Referee',
    path: '/referee-dashboard',
    icon: 'Shield',
    isPrivate: true
  },
  {
    label: 'Referrals',
    path: '/referrals',
    icon: 'UserPlus',
    isPrivate: true
  },
  {
    label: 'Community',
    path: '/community-group',
    icon: 'Users',
    isPrivate: true
  }
]

export const ADMIN_NAVIGATION: NavigationItem[] = [
  {
    label: 'Admin Panel',
    path: '/admin',
    icon: 'Settings',
    isPrivate: true,
    requiresAdmin: true
  }
]

/**
 * 🎨 UI CONSTANTS
 * User interface configuration
 */
export const THEME_COLORS = {
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a'
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d'
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309'
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c'
  }
}

export const BREAKPOINTS = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px'
}

/**
 * 📱 RESPONSIVE CONSTANTS
 * Screen size and device constants
 */
export const SCREEN_SIZES = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280
}

/**
 * ⏱️ TIME CONSTANTS
 * Time-related configuration
 */
export const TIME_CONSTANTS = {
  NOTIFICATION_DURATION: 5000,
  DEBOUNCE_DELAY: 300,
  POLLING_INTERVAL: 30000,
  SESSION_TIMEOUT: 3600000, // 1 hour
  MATCH_PREPARATION_TIME: 300000, // 5 minutes
  CHAT_MESSAGE_LIMIT: 500
}

/**
 * 🔢 LIMITS AND VALIDATION
 * Application limits and validation rules
 */
export const LIMITS = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]{3,20}$/
  },
  PASSWORD: {
    MIN_LENGTH: 8,
    PATTERN: /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{8,}$/
  },
  MLBB_ID: {
    PATTERN: /^\d{8,10}\s*\(\d{4}\)$/
  },
  MATCH: {
    MIN_PLAYERS: 2,
    MAX_PLAYERS: 10,
    MIN_ENTRY_FEE: 10,
    MAX_ENTRY_FEE: 1000,
    TITLE_MAX_LENGTH: 100
  },
  CHAT: {
    MESSAGE_MAX_LENGTH: 500,
    MESSAGES_PER_MINUTE: 10
  },
  GUILD: {
    NAME_MAX_LENGTH: 50,
    DESCRIPTION_MAX_LENGTH: 500,
    MAX_MEMBERS: 50
  }
}

/**
 * 🏆 ACHIEVEMENT CONSTANTS
 * Achievement definitions and requirements
 */
export const ACHIEVEMENTS = {
  FIRST_WIN: {
    id: 'first_win',
    title: 'First Victory',
    description: 'Win your first match',
    icon: 'Medal',
    rarity: 'common' as const
  },
  WIN_STREAK_5: {
    id: 'win_streak_5',
    title: 'Hot Streak',
    description: 'Win 5 matches in a row',
    icon: 'Flame',
    rarity: 'rare' as const
  },
  WIN_STREAK_10: {
    id: 'win_streak_10',
    title: 'Unstoppable',
    description: 'Win 10 matches in a row',
    icon: 'Zap',
    rarity: 'epic' as const
  },
  TOTAL_WINS_100: {
    id: 'total_wins_100',
    title: 'Centurion',
    description: 'Win 100 total matches',
    icon: 'Target',
    rarity: 'epic' as const
  },
  DIAMOND_SPENDER: {
    id: 'diamond_spender',
    title: 'Big Spender',
    description: 'Spend 10,000 diamonds',
    icon: 'Gem',
    rarity: 'rare' as const
  },
  REFEREE_APPROVED: {
    id: 'referee_approved',
    title: 'Trusted Referee',
    description: 'Get approved as a referee',
    icon: 'Shield',
    rarity: 'rare' as const
  },
  TOP_10_RANK: {
    id: 'top_10_rank',
    title: 'Elite Player',
    description: 'Reach top 10 in leaderboards',
    icon: 'Crown',
    rarity: 'legendary' as const
  }
}

/**
 * 💰 PAYMENT CONSTANTS
 * Payment and transaction configuration
 */
export const PAYMENT_METHODS = {
  GCASH: {
    id: 'gcash',
    name: 'GCash',
    icon: 'Smartphone',
    description: 'Pay using GCash mobile wallet',
    minAmount: 50,
    maxAmount: 5000
  },
  MAYA: {
    id: 'maya',
    name: 'Maya (PayMaya)',
    icon: 'CreditCard',
    description: 'Pay using Maya digital wallet',
    minAmount: 50,
    maxAmount: 5000
  }
}

/**
 * 📊 STATUS CONSTANTS
 * Various status definitions
 */
export const MATCH_STATUSES = {
  OPEN: 'open',
  FULL: 'full',
  ONGOING: 'ongoing',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
} as const

export const REFEREE_STATUSES = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected'
} as const

export const TRANSACTION_STATUSES = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  CANCELLED: 'cancelled'
} as const

/**
 * 🔔 NOTIFICATION CONSTANTS
 * Notification types and templates
 */
export const NOTIFICATION_TYPES = {
  MATCH_INVITE: 'match_invite',
  MATCH_START: 'match_start',
  MATCH_RESULT: 'match_result',
  PAYMENT: 'payment',
  SYSTEM: 'system'
} as const

export const NOTIFICATION_TEMPLATES = {
  MATCH_INVITE: {
    title: 'Match Invitation',
    message: 'You have been invited to join a match'
  },
  MATCH_START: {
    title: 'Match Starting',
    message: 'Your match is about to begin'
  },
  MATCH_WIN: {
    title: 'Victory!',
    message: 'Congratulations! You won the match'
  },
  MATCH_LOSS: {
    title: 'Match Complete',
    message: 'Better luck next time!'
  },
  PAYMENT_SUCCESS: {
    title: 'Payment Successful',
    message: 'Your diamonds have been added to your account'
  },
  REFEREE_APPROVED: {
    title: 'Referee Application Approved',
    message: 'Welcome to the referee team!'
  }
}

/**
 * 🌐 API CONSTANTS
 * API endpoints and configuration
 */
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    SIGNUP: '/auth/signup',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile'
  },
  MATCHES: {
    LIST: '/matches',
    CREATE: '/matches',
    JOIN: '/matches/:id/join',
    LEAVE: '/matches/:id/leave',
    DETAILS: '/matches/:id'
  },
  USERS: {
    PROFILE: '/users/:id',
    LEADERBOARD: '/users/leaderboard',
    SEARCH: '/users/search'
  },
  PAYMENTS: {
    PACKAGES: '/payments/packages',
    PURCHASE: '/payments/purchase',
    HISTORY: '/payments/history'
  },
  REFEREE: {
    APPLY: '/referee/apply',
    DASHBOARD: '/referee/dashboard',
    MATCHES: '/referee/matches'
  }
}

/**
 * 🔐 SECURITY CONSTANTS
 * Security and validation configuration
 */
export const SECURITY = {
  JWT_EXPIRY: '24h',
  REFRESH_TOKEN_EXPIRY: '7d',
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 900000, // 15 minutes
  PASSWORD_SALT_ROUNDS: 12,
  SESSION_COOKIE_NAME: 'gambets_session'
}

/**
 * 📈 ANALYTICS CONSTANTS
 * Analytics and tracking configuration
 */
export const ANALYTICS_EVENTS = {
  USER_SIGNUP: 'user_signup',
  USER_LOGIN: 'user_login',
  MATCH_CREATE: 'match_create',
  MATCH_JOIN: 'match_join',
  DIAMOND_PURCHASE: 'diamond_purchase',
  REFEREE_APPLY: 'referee_apply',
  PAGE_VIEW: 'page_view'
}

/**
 * 🎯 FEATURE FLAGS
 * Feature toggle configuration
 */
export const FEATURES = {
  CHAT_SYSTEM: true,
  GUILD_SYSTEM: true,
  TOURNAMENT_MODE: false,
  LIVE_STREAMING: false,
  MOBILE_APP: false,
  CRYPTO_PAYMENTS: false,
  AI_MATCHMAKING: false
}

/**
 * 🌍 LOCALIZATION CONSTANTS
 * Language and region configuration
 */
export const LOCALES = {
  EN: 'en',
  FIL: 'fil'
}

export const CURRENCY = {
  CODE: 'PHP',
  SYMBOL: '₱',
  LOCALE: 'en-PH'
}

export const TIMEZONE = 'Asia/Manila'
