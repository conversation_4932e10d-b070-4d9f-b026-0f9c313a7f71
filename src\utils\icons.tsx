// 🎨 ICON UTILITIES
// Helper functions and components for using Lucide icons consistently

import React from 'react'
import {
  // Game Icons
  Castle,
  Target,
  Zap,
  Swords,
  Shield,
  Bomb,
  Gamepad2,
  
  // Navigation Icons
  Home,
  Trophy,
  FileText,
  MessageCircle,
  BarChart3,
  User,
  Gem,
  Search,
  UserPlus,
  Users,
  Settings,
  
  // Status Icons
  CircleDot,
  AlertCircle,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Circle,
  
  // Rank Icons
  Crown,
  Medal,
  Award,
  Badge,
  Star,
  
  // Achievement Icons
  Flame,
  
  // Payment Icons
  Smartphone,
  CreditCard,
  
  // Common Icons
  Plus,
  Minus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  X,
  Menu,
  Bell,
  Mail,
  Lock,
  Unlock,
  Download,
  Upload,
  Share,
  Copy,
  ExternalLink,
  Info,
  AlertTriangle,
  CheckCircle2,
  XCircle as XCircle2,
  
  type LucideIcon
} from 'lucide-react'

// Icon mapping for dynamic icon rendering
export const ICON_MAP: Record<string, LucideIcon> = {
  // Game Icons
  'Castle': Castle,
  'Target': Target,
  'Zap': Zap,
  'Swords': Swords,
  'Shield': Shield,
  'Bomb': Bomb,
  'Gamepad2': Gamepad2,
  
  // Navigation Icons
  'Home': Home,
  'Trophy': Trophy,
  'FileText': FileText,
  'MessageCircle': MessageCircle,
  'BarChart3': BarChart3,
  'User': User,
  'Gem': Gem,
  'Search': Search,
  'UserPlus': UserPlus,
  'Users': Users,
  'Settings': Settings,
  
  // Status Icons
  'CircleDot': CircleDot,
  'AlertCircle': AlertCircle,
  'Play': Play,
  'CheckCircle': CheckCircle,
  'XCircle': XCircle,
  'Clock': Clock,
  'Circle': Circle,
  
  // Rank Icons
  'Crown': Crown,
  'Medal': Medal,
  'Award': Award,
  'Badge': Badge,
  'Star': Star,
  
  // Achievement Icons
  'Flame': Flame,
  
  // Payment Icons
  'Smartphone': Smartphone,
  'CreditCard': CreditCard,
  
  // Common Icons
  'Plus': Plus,
  'Minus': Minus,
  'Edit': Edit,
  'Trash2': Trash2,
  'Eye': Eye,
  'EyeOff': EyeOff,
  'ChevronDown': ChevronDown,
  'ChevronUp': ChevronUp,
  'ChevronLeft': ChevronLeft,
  'ChevronRight': ChevronRight,
  'X': X,
  'Menu': Menu,
  'Bell': Bell,
  'Mail': Mail,
  'Lock': Lock,
  'Unlock': Unlock,
  'Download': Download,
  'Upload': Upload,
  'Share': Share,
  'Copy': Copy,
  'ExternalLink': ExternalLink,
  'Info': Info,
  'AlertTriangle': AlertTriangle,
  'CheckCircle2': CheckCircle2,
  'XCircle2': XCircle2,
}

// Props for the DynamicIcon component
interface DynamicIconProps {
  name: string
  size?: number
  className?: string
  color?: string
}

/**
 * 🎯 DYNAMIC ICON COMPONENT
 * Renders Lucide icons dynamically by name
 */
export const DynamicIcon: React.FC<DynamicIconProps> = ({ 
  name, 
  size = 24, 
  className = '', 
  color 
}) => {
  const IconComponent = ICON_MAP[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found in ICON_MAP`)
    return <Circle size={size} className={className} color={color} />
  }
  
  return <IconComponent size={size} className={className} color={color} />
}

/**
 * 🎮 GAME ICON COMPONENT
 * Specialized component for game icons with consistent styling
 */
interface GameIconProps {
  game: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

export const GameIcon: React.FC<GameIconProps> = ({ 
  game, 
  size = 'md', 
  className = '' 
}) => {
  const sizeMap = {
    sm: 16,
    md: 24,
    lg: 32,
    xl: 48
  }
  
  const iconName = getGameIconName(game)
  
  return (
    <DynamicIcon 
      name={iconName} 
      size={sizeMap[size]} 
      className={`text-blue-600 ${className}`} 
    />
  )
}

/**
 * 🏆 RANK ICON COMPONENT
 * Specialized component for rank icons with rank-based styling
 */
interface RankIconProps {
  rank: number
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const RankIcon: React.FC<RankIconProps> = ({ 
  rank, 
  size = 'md', 
  className = '' 
}) => {
  const sizeMap = {
    sm: 16,
    md: 20,
    lg: 24
  }
  
  const iconName = getRankIconName(rank)
  const colorClass = getRankColorClass(rank)
  
  return (
    <DynamicIcon 
      name={iconName} 
      size={sizeMap[size]} 
      className={`${colorClass} ${className}`} 
    />
  )
}

/**
 * 📊 STATUS ICON COMPONENT
 * Specialized component for status icons with status-based styling
 */
interface StatusIconProps {
  status: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const StatusIcon: React.FC<StatusIconProps> = ({ 
  status, 
  size = 'md', 
  className = '' 
}) => {
  const sizeMap = {
    sm: 16,
    md: 20,
    lg: 24
  }
  
  const iconName = getStatusIconName(status)
  const colorClass = getStatusColorClass(status)
  
  return (
    <DynamicIcon 
      name={iconName} 
      size={sizeMap[size]} 
      className={`${colorClass} ${className}`} 
    />
  )
}

// Helper functions to get icon names (updated from utils/index.ts)
function getGameIconName(game: string): string {
  const gameIcons: Record<string, string> = {
    'mobile-legends': 'Castle',
    'valorant': 'Target',
    'call-of-duty': 'Zap',
    'wild-rift': 'Swords',
    'dota-2': 'Shield',
    'cs-go': 'Bomb',
    'honor-of-kings': 'Crown'
  }
  return gameIcons[game.toLowerCase()] || 'Gamepad2'
}

function getRankIconName(rank: number): string {
  if (rank === 1) return 'Crown'
  if (rank === 2) return 'Medal'
  if (rank === 3) return 'Award'
  if (rank <= 10) return 'Trophy'
  if (rank <= 50) return 'Badge'
  return 'Star'
}

function getStatusIconName(status: string): string {
  const statusIcons: Record<string, string> = {
    'open': 'CircleDot',
    'full': 'AlertCircle',
    'ongoing': 'Play',
    'completed': 'CheckCircle',
    'cancelled': 'XCircle',
    'pending': 'Clock',
    'approved': 'CheckCircle',
    'rejected': 'XCircle'
  }
  return statusIcons[status.toLowerCase()] || 'Circle'
}

function getRankColorClass(rank: number): string {
  if (rank === 1) return 'text-yellow-500'
  if (rank === 2) return 'text-gray-400'
  if (rank === 3) return 'text-amber-600'
  if (rank <= 10) return 'text-blue-500'
  if (rank <= 50) return 'text-purple-500'
  return 'text-gray-500'
}

function getStatusColorClass(status: string): string {
  const statusColors: Record<string, string> = {
    'open': 'text-green-500',
    'full': 'text-yellow-500',
    'ongoing': 'text-blue-500',
    'completed': 'text-green-600',
    'cancelled': 'text-red-500',
    'pending': 'text-yellow-600',
    'approved': 'text-green-600',
    'rejected': 'text-red-500'
  }
  return statusColors[status.toLowerCase()] || 'text-gray-500'
}

/**
 * 🎨 ICON BUTTON COMPONENT
 * Button component with icon and optional text
 */
interface IconButtonProps {
  icon: string
  label?: string
  onClick?: () => void
  variant?: 'primary' | 'secondary' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  className?: string
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  label,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none'
  
  const variants = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',
    ghost: 'hover:bg-gray-100'
  }
  
  const sizes = {
    sm: 'h-8 px-2 text-sm',
    md: 'h-10 px-3',
    lg: 'h-12 px-4 text-lg'
  }
  
  const iconSizes = {
    sm: 16,
    md: 20,
    lg: 24
  }
  
  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
    >
      <DynamicIcon name={icon} size={iconSizes[size]} />
      {label && <span className="ml-2">{label}</span>}
    </button>
  )
}

// Export commonly used icons for direct import
export {
  // Game Icons
  Castle,
  Target,
  Zap,
  Swords,
  Shield,
  Bomb,
  Gamepad2,
  
  // Navigation Icons
  Home,
  Trophy,
  FileText,
  MessageCircle,
  BarChart3,
  User,
  Gem,
  Search,
  UserPlus,
  Users,
  Settings,
  
  // Status Icons
  CircleDot,
  AlertCircle,
  Play,
  CheckCircle,
  XCircle,
  Clock,
  Circle,
  
  // Rank Icons
  Crown,
  Medal,
  Award,
  Badge,
  Star,
  
  // Common Icons
  Plus,
  Minus,
  Edit,
  Trash2,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  X,
  Menu,
  Bell,
  Mail,
  Lock,
  Unlock,
  Download,
  Upload,
  Share,
  Copy,
  ExternalLink,
  Info,
  AlertTriangle
}
