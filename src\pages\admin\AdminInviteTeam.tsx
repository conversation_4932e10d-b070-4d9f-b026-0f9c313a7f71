// 📧 ADMIN TEAM INVITATION SYSTEM
// Super Admin can invite team members to join as admins

import React, { useState } from 'react'
import {
  Mail,
  UserPlus,
  Send,
  Crown,
  Shield,
  Award
} from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'

const AdminInviteTeam: React.FC = () => {
  const { } = useAuth() // TODO: Confirm user usage
  const [inviteData, setInviteData] = useState({
    email: '',
    role: 'admin',
    admin_level: 2,
    message: 'You have been invited to join the Gambets admin team!'
  })
  // const [inviteLink, setInviteLink] = useState('') // TODO: Confirm usage
  // const [isGenerating] = useState(false) // TODO: Confirm usage
  // const [copied] = useState(false) // TODO: Confirm usage

  // const generateInviteLink = async () => { // TODO: Confirm usage
  //   setIsGenerating(true)
  //
  //   // Simulate invite link generation
  //   const inviteCode = Math.random().toString(36).substring(2, 15)
  //   const link = `${window.location.origin}/admin-invite/${inviteCode}`
  //
  //   setInviteLink(link)
  //   setIsGenerating(false)
  // }

  // const copyToClipboard = async () => { // TODO: Confirm usage
  //   try {
  //     await navigator.clipboard.writeText(inviteLink)
  //     setCopied(true)
  //     setTimeout(() => setCopied(false), 2000)
  //   } catch (error) {
  //     console.error('Failed to copy:', error)
  //   }
  // }

  const sendInviteEmail = () => {
    // For now, this opens the user's email client
    const subject = 'Invitation to Join Gambets Admin Team'
    const body = `Hi there!

${inviteData.message}

To join the admin team:
1. Create a Gambets account at: ${window.location.origin}/signup
2. Use this email address: ${inviteData.email}
3. After signup, contact the admin to activate your privileges

Admin Level: ${inviteData.admin_level === 3 ? 'Super Admin' : inviteData.admin_level === 2 ? 'Platform Admin' : 'Referee Coordinator'}

Welcome to the team!

Best regards,
Gambets Admin Team`

    const mailtoLink = `mailto:${inviteData.email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
    window.open(mailtoLink)
  }

  const adminLevels = [
    {
      level: 1,
      title: 'Referee Coordinator',
      description: 'Manage referee applications and assignments',
      icon: Award,
      color: 'bg-blue-500'
    },
    {
      level: 2,
      title: 'Platform Admin',
      description: 'Full platform management and user oversight',
      icon: Shield,
      color: 'bg-purple-500'
    },
    {
      level: 3,
      title: 'Super Admin',
      description: 'Complete system control and admin management',
      icon: Crown,
      color: 'bg-red-500'
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center">
            <UserPlus className="w-8 h-8" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Invite Team Members</h1>
            <p className="text-green-100 mt-1">Add new administrators to your team</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Invite Form */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Send Team Invitation</h2>
          
          <div className="space-y-4">
            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Team Member Email
              </label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="email"
                  value={inviteData.email}
                  onChange={(e) => setInviteData({...inviteData, email: e.target.value})}
                  placeholder="<EMAIL>"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Admin Level Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Admin Level
              </label>
              <div className="space-y-3">
                {adminLevels.map((level) => {
                  const Icon = level.icon
                  return (
                    <div
                      key={level.level}
                      onClick={() => setInviteData({...inviteData, admin_level: level.level})}
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        inviteData.admin_level === level.level
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-10 h-10 ${level.color} rounded-lg flex items-center justify-center`}>
                          <Icon className="w-5 h-5 text-white" />
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{level.title}</div>
                          <div className="text-sm text-gray-600">{level.description}</div>
                        </div>
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          inviteData.admin_level === level.level
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-300'
                        }`}>
                          {inviteData.admin_level === level.level && (
                            <div className="w-full h-full rounded-full bg-white scale-50"></div>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>

            {/* Custom Message */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Invitation Message
              </label>
              <textarea
                value={inviteData.message}
                onChange={(e) => setInviteData({...inviteData, message: e.target.value})}
                placeholder="Welcome message for your new team member..."
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
              />
            </div>

            {/* Send Button */}
            <button
              onClick={sendInviteEmail}
              disabled={!inviteData.email}
              className="w-full flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send className="w-4 h-4" />
              <span>Send Invitation Email</span>
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-white rounded-xl shadow-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">How Team Invitations Work</h2>
          
          <div className="space-y-6">
            {/* Step 1 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold text-sm">
                1
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Send Invitation</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Enter team member's email and select their admin level. An invitation email will be sent.
                </p>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center font-bold text-sm">
                2
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Team Member Signs Up</h3>
                <p className="text-sm text-gray-600 mt-1">
                  They create a Gambets account using the same email address you invited.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center font-bold text-sm">
                3
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Activate Admin Access</h3>
                <p className="text-sm text-gray-600 mt-1">
                  Use the User Management page to promote them to admin with the appropriate level.
                </p>
              </div>
            </div>

            {/* Step 4 */}
            <div className="flex items-start space-x-4">
              <div className="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center font-bold text-sm">
                4
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Admin Access Granted</h3>
                <p className="text-sm text-gray-600 mt-1">
                  They can now access the admin dashboard using their regular login credentials.
                </p>
              </div>
            </div>
          </div>

          {/* Security Note */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2 text-yellow-800 mb-2">
              <Shield className="w-4 h-4" />
              <span className="font-medium">Security Note</span>
            </div>
            <p className="text-sm text-yellow-700">
              Admin privileges must be manually activated by a Super Admin. This ensures complete control over who gets admin access.
            </p>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-xl shadow-lg p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Admin Setup</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
            <div className="flex items-center space-x-3 mb-2">
              <UserPlus className="w-5 h-5 text-blue-600" />
              <span className="font-medium">Invite Team</span>
            </div>
            <p className="text-sm text-gray-600">Send email invitations to team members</p>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-lg hover:border-green-300 transition-colors">
            <div className="flex items-center space-x-3 mb-2">
              <Shield className="w-5 h-5 text-green-600" />
              <span className="font-medium">Manage Users</span>
            </div>
            <p className="text-sm text-gray-600">Promote existing users to admin roles</p>
          </div>
          
          <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 transition-colors">
            <div className="flex items-center space-x-3 mb-2">
              <Crown className="w-5 h-5 text-purple-600" />
              <span className="font-medium">Admin Levels</span>
            </div>
            <p className="text-sm text-gray-600">Configure different admin permission levels</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminInviteTeam
