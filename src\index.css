@tailwind base;
@tailwind components;
@tailwind utilities;

/* Apple-Inspired Minimalist Design */

/* Clean Status Indicators */
.status-open {
  background: #34d399;
}

.status-full {
  background: #fbbf24;
}

.status-ongoing {
  background: #60a5fa;
}

.status-completed {
  background: #9ca3af;
}

.status-cancelled {
  background: #f87171;
}

/* Simple Progress Bar */
.progress-bar {
  background: #f3f4f6;
  border-radius: 6px;
  overflow: hidden;
}

.progress-fill {
  background: #3b82f6;
  height: 100%;
  border-radius: 6px;
  transition: width 0.3s ease;
}

/* Hide React DevTools Profiler overlay that interferes with sidebar */
[data-react-devtools-portal-root] {
  z-index: 9999 !important;
  pointer-events: none !important;
}

/* Hide any development overlays */
[data-testid*="profiler"],
[class*="profiler"],
[id*="profiler"],
[data-react-devtools*="profiler"],
[data-react-devtools-portal],
.__react-devtools-portal,
[class*="react-devtools"],
[id*="react-devtools"] {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Hide memory/render overlays */
[class*="memory"],
[class*="render"],
[data-testid*="memory"],
[data-testid*="render"] {
  display: none !important;
}

/* Hide performance monitoring overlays */
[class*="performance"],
[data-testid*="performance"],
[class*="monitor"],
[data-testid*="monitor"] {
  display: none !important;
}

/* Hide any fixed bottom-left overlays that might be development tools */
.fixed.bottom-4.left-4,
div[class*="fixed bottom-4 left-4"] {
  display: none !important;
}

/* Custom scrollbar for admin sidebar */
.admin-sidebar-scroll {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
}

.admin-sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.admin-sidebar-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.admin-sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.admin-sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* 🚀 Mobile Performance Optimizations */

/* Disable hover effects on touch devices for better performance */
@media (hover: none) and (pointer: coarse) {
  .hover\:shadow-md:hover,
  .hover\:shadow-lg:hover,
  .hover\:shadow-xl:hover {
    box-shadow: none !important;
  }

  .hover\:scale-105:hover,
  .hover\:scale-110:hover {
    transform: none !important;
  }

  .group:hover .group-hover\:scale-110,
  .group:hover .group-hover\:scale-105 {
    transform: none !important;
  }

  .hover\:bg-gray-50:hover,
  .hover\:bg-gray-100:hover,
  .hover\:bg-gray-200:hover {
    background-color: inherit !important;
  }
}

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  .touch-target-small {
    min-height: 36px;
    min-width: 36px;
  }

  .touch-target-large {
    min-height: 52px;
    min-width: 52px;
  }
}

/* Mobile scroll optimizations */
@media (max-width: 768px) {
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  .mobile-scroll::-webkit-scrollbar {
    display: none;
  }

  .mobile-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
