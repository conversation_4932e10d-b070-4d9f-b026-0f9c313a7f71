// Validation Utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validatePassword = (password: string): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateUsername = (username: string): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []
  
  if (username.length < 3) {
    errors.push('Username must be at least 3 characters long')
  }
  
  if (username.length > 20) {
    errors.push('Username must be no more than 20 characters long')
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(username)) {
    errors.push('Username can only contain letters, numbers, and underscores')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateGameId = (gameId: string, game: string): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []
  
  if (!gameId.trim()) {
    errors.push(`${game} ID is required`)
    return { isValid: false, errors }
  }
  
  // Mobile Legends ID validation
  if (game === 'Mobile Legends') {
    if (!/^\d{8,12}$/.test(gameId.replace(/\s/g, ''))) {
      errors.push('Mobile Legends ID must be 8-12 digits')
    }
  }
  
  // Add other game ID validations as needed
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateAmount = (amount: number, min = 0, max = Infinity): {
  isValid: boolean
  errors: string[]
} => {
  const errors: string[] = []
  
  if (isNaN(amount)) {
    errors.push('Amount must be a valid number')
  }
  
  if (amount < min) {
    errors.push(`Amount must be at least ${min}`)
  }
  
  if (amount > max) {
    errors.push(`Amount must be no more than ${max}`)
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validatePhoneNumber = (phone: string): boolean => {
  // Philippine phone number validation
  const phoneRegex = /^(\+63|0)?9\d{9}$/
  return phoneRegex.test(phone.replace(/\s/g, ''))
}

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '')
}
