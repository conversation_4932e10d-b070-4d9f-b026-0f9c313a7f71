import { useState, useRef, ReactNode } from 'react'

// Enhanced touch button with proper feedback
interface TouchButtonProps {
  children: ReactNode
  onClick: () => void
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  disabled?: boolean
  loading?: boolean
  fullWidth?: boolean
  className?: string
}

export function TouchButton({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  className = ''
}: TouchButtonProps) {
  const [isPressed, setIsPressed] = useState(false)
  const buttonRef = useRef<HTMLButtonElement>(null)

  const handleTouchStart = () => {
    if (!disabled && !loading) {
      setIsPressed(true)
      // Haptic feedback for supported devices
      if ('vibrate' in navigator) {
        navigator.vibrate(10)
      }
    }
  }

  const handleTouchEnd = () => {
    setIsPressed(false)
  }

  const handleClick = () => {
    if (!disabled && !loading) {
      onClick()
    }
  }

  const baseClasses = `
    relative font-medium rounded-lg transition-all duration-150 
    focus:outline-none focus:ring-2 focus:ring-offset-2
    active:scale-95 select-none touch-manipulation
    ${disabled || loading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
    ${fullWidth ? 'w-full' : ''}
    ${isPressed ? 'scale-95' : ''}
  `

  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-lg hover:shadow-xl',
    secondary: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500',
    ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl'
  }

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm min-h-[36px]', // Minimum 36px for touch
    md: 'px-4 py-3 text-base min-h-[44px]', // Minimum 44px for touch
    lg: 'px-6 py-4 text-lg min-h-[52px]'  // Minimum 52px for touch
  }

  return (
    <button
      ref={buttonRef}
      onClick={handleClick}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onMouseDown={handleTouchStart}
      onMouseUp={handleTouchEnd}
      onMouseLeave={handleTouchEnd}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {loading ? (
        <div className="flex items-center justify-center">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
          Loading...
        </div>
      ) : (
        children
      )}
    </button>
  )
}

// Touch-optimized card with swipe gestures
interface TouchCardProps {
  children: ReactNode
  onTap?: () => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  className?: string
  swipeThreshold?: number
}

export function TouchCard({
  children,
  onTap,
  onSwipeLeft,
  onSwipeRight,
  className = '',
  swipeThreshold = 100
}: TouchCardProps) {
  const [startX, setStartX] = useState(0)
  const [currentX, setCurrentX] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX)
    setCurrentX(e.touches[0].clientX)
    setIsDragging(true)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return
    setCurrentX(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (!isDragging) return
    
    const deltaX = currentX - startX
    
    if (Math.abs(deltaX) > swipeThreshold) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    } else if (Math.abs(deltaX) < 10 && onTap) {
      // Small movement, treat as tap
      onTap()
    }
    
    setIsDragging(false)
    setStartX(0)
    setCurrentX(0)
  }

  const translateX = isDragging ? currentX - startX : 0

  return (
    <div
      ref={cardRef}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onClick={!isDragging ? onTap : undefined}
      className={`
        bg-white rounded-lg shadow-sm border border-gray-200 
        transition-transform duration-200 touch-manipulation
        ${onTap ? 'cursor-pointer' : ''}
        ${className}
      `}
      style={{
        transform: `translateX(${translateX}px)`,
        opacity: Math.max(0.7, 1 - Math.abs(translateX) / 200)
      }}
    >
      {children}
    </div>
  )
}

// Mobile-optimized input with better touch targets
interface TouchInputProps {
  label: string
  value: string
  onChange: (value: string) => void
  type?: 'text' | 'email' | 'password' | 'number' | 'tel'
  placeholder?: string
  required?: boolean
  disabled?: boolean
  error?: string
  className?: string
}

export function TouchInput({
  label,
  value,
  onChange,
  type = 'text',
  placeholder,
  required = false,
  disabled = false,
  error,
  className = ''
}: TouchInputProps) {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        placeholder={placeholder}
        disabled={disabled}
        className={`
          w-full px-4 py-3 text-base border rounded-lg
          min-h-[44px] touch-manipulation
          transition-colors duration-200
          ${isFocused ? 'border-blue-500 ring-2 ring-blue-200' : 'border-gray-300'}
          ${error ? 'border-red-500 ring-2 ring-red-200' : ''}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
          focus:outline-none
        `}
      />
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}

// Touch-optimized dropdown/select
interface TouchSelectProps {
  label: string
  value: string
  onChange: (value: string) => void
  options: { value: string; label: string }[]
  placeholder?: string
  disabled?: boolean
  error?: string
  className?: string
}

export function TouchSelect({
  label,
  value,
  onChange,
  options,
  placeholder = 'Select an option',
  disabled = false,
  error,
  className = ''
}: TouchSelectProps) {
  return (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label}
      </label>
      
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        disabled={disabled}
        className={`
          w-full px-4 py-3 text-base border rounded-lg
          min-h-[44px] touch-manipulation
          transition-colors duration-200
          ${error ? 'border-red-500 ring-2 ring-red-200' : 'border-gray-300'}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
          focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:outline-none
        `}
      >
        <option value="">{placeholder}</option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      
      {error && (
        <p className="text-sm text-red-600">{error}</p>
      )}
    </div>
  )
}
