import React, { useEffect, useRef } from 'react'
import {
  X,
  UserPlus,
  MessageCircle,
  Flag,
  Crown,
  Shield,
  Star,
  Trophy,
  Gem,
  Users,
  Calendar,
  Heart,
  Award
} from 'lucide-react'
import { UserPopoverProps } from '../types'

const UserPopover: React.FC<UserPopoverProps> = ({
  user,
  isOpen,
  onClose,
  onAddFriend,
  onMessage,
  onReport,
  position
}) => {
  const popoverRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen || !user) return null

  const getRoleIcon = (role?: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="w-4 h-4 text-yellow-500" />
      case 'moderator':
        return <Shield className="w-4 h-4 text-blue-500" />
      case 'vip':
        return <Star className="w-4 h-4 text-purple-500" />
      default:
        return null
    }
  }

  const getRoleBadge = (role?: string) => {
    if (!role || role === 'member') return null
    
    const colors = {
      admin: 'bg-yellow-100 text-yellow-700 border-yellow-200',
      moderator: 'bg-blue-100 text-blue-700 border-blue-200',
      vip: 'bg-purple-100 text-purple-700 border-purple-200'
    }

    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium border ${colors[role as keyof typeof colors]}`}>
        {role.toUpperCase()}
      </span>
    )
  }

  const getOnlineStatus = () => {
    if (user.user.isOnline) {
      return (
        <div className="flex items-center gap-1 text-xs text-green-600">
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
          <span>Online</span>
        </div>
      )
    }
    
    return (
      <div className="flex items-center gap-1 text-xs text-gray-500">
        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
        <span>Last seen {user.user.lastActive || 'recently'}</span>
      </div>
    )
  }

  // Calculate popover position to stay within viewport
  const calculatePosition = () => {
    const popoverWidth = 320
    const popoverHeight = 400
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    let left = position.x
    let top = position.y + 10
    
    // Adjust horizontal position
    if (left + popoverWidth > viewportWidth - 20) {
      left = position.x - popoverWidth
    }
    
    // Adjust vertical position
    if (top + popoverHeight > viewportHeight - 20) {
      top = position.y - popoverHeight - 10
    }
    
    return { left: Math.max(10, left), top: Math.max(10, top) }
  }

  const popoverPosition = calculatePosition()

  return (
    <div
      ref={popoverRef}
      className="fixed z-50 bg-white rounded-lg shadow-lg border border-gray-200 w-80 max-h-96 overflow-y-auto"
      style={{
        left: `${popoverPosition.left}px`,
        top: `${popoverPosition.top}px`
      }}
    >
      {/* Header */}
      <div className="relative p-4 bg-gradient-to-br from-blue-50 to-purple-50 border-b border-gray-200">
        <button
          onClick={onClose}
          className="absolute top-2 right-2 p-1 hover:bg-white hover:bg-opacity-50 rounded transition-colors"
        >
          <X className="w-4 h-4 text-gray-600" />
        </button>

        <div className="flex items-start gap-3">
          {/* Avatar */}
          <div className="relative">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold text-lg">
                {user.user.avatar || user.user.username.charAt(0).toUpperCase()}
              </span>
            </div>
            {user.user.role && (
              <div className="absolute -bottom-1 -right-1">
                {getRoleIcon(user.user.role)}
              </div>
            )}
          </div>

          {/* User Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                {user.user.username}
              </h3>
              {getRoleBadge(user.user.role)}
            </div>
            {getOnlineStatus()}
            {user.user.mlRank && (
              <div className="flex items-center gap-1 mt-1">
                <Trophy className="w-3 h-3 text-yellow-500" />
                <span className="text-xs text-gray-600">{user.user.mlRank}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="p-4 border-b border-gray-200">
        <div className="grid grid-cols-2 gap-3">
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Gem className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-semibold text-gray-900">
                {user.user.diamonds.toLocaleString()}
              </span>
            </div>
            <p className="text-xs text-gray-600">Diamonds</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Trophy className="w-4 h-4 text-yellow-500" />
              <span className="text-sm font-semibold text-gray-900">
                {user.user.wins}
              </span>
            </div>
            <p className="text-xs text-gray-600">Wins</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Star className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-semibold text-gray-900">
                Level {user.user.level}
              </span>
            </div>
            <p className="text-xs text-gray-600">Level</p>
          </div>
          <div className="text-center">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Award className="w-4 h-4 text-green-500" />
              <span className="text-sm font-semibold text-gray-900">
                {user.user.winRate}%
              </span>
            </div>
            <p className="text-xs text-gray-600">Win Rate</p>
          </div>
        </div>
      </div>

      {/* Guild & Social Info */}
      <div className="p-4 border-b border-gray-200">
        {user.user.guild && (
          <div className="flex items-center gap-2 mb-2">
            <Users className="w-4 h-4 text-blue-500" />
            <span className="text-sm text-gray-900">{user.user.guild}</span>
          </div>
        )}
        
        <div className="flex items-center gap-4 text-xs text-gray-600">
          <div className="flex items-center gap-1">
            <Heart className="w-3 h-3" />
            <span>{user.social.reputation} reputation</span>
          </div>
          <div className="flex items-center gap-1">
            <Users className="w-3 h-3" />
            <span>{user.social.friends.length} friends</span>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="p-4 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 mb-2">Recent Activity</h4>
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <Trophy className="w-3 h-3 text-yellow-500" />
            <span>{user.stats.monthlyWins} wins this month</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <Award className="w-3 h-3 text-purple-500" />
            <span>{user.stats.streak} win streak</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-600">
            <Calendar className="w-3 h-3 text-blue-500" />
            <span>{user.stats.totalMatches} total matches</span>
          </div>
        </div>
      </div>

      {/* Achievements */}
      {user.stats.achievements.length > 0 && (
        <div className="p-4 border-b border-gray-200">
          <h4 className="text-sm font-medium text-gray-900 mb-2">Achievements</h4>
          <div className="flex flex-wrap gap-1">
            {user.stats.achievements.slice(0, 6).map((achievement, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded-full"
                title={achievement}
              >
                🏆
              </span>
            ))}
            {user.stats.achievements.length > 6 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                +{user.stats.achievements.length - 6}
              </span>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="p-4">
        <div className="grid grid-cols-3 gap-2">
          <button
            onClick={onAddFriend}
            className="flex flex-col items-center gap-1 p-2 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors"
          >
            <UserPlus className="w-4 h-4 text-blue-600" />
            <span className="text-xs text-blue-600 font-medium">Add Friend</span>
          </button>
          <button
            onClick={onMessage}
            className="flex flex-col items-center gap-1 p-2 bg-green-50 hover:bg-green-100 rounded-lg transition-colors"
          >
            <MessageCircle className="w-4 h-4 text-green-600" />
            <span className="text-xs text-green-600 font-medium">Message</span>
          </button>
          <button
            onClick={onReport}
            className="flex flex-col items-center gap-1 p-2 bg-red-50 hover:bg-red-100 rounded-lg transition-colors"
          >
            <Flag className="w-4 h-4 text-red-600" />
            <span className="text-xs text-red-600 font-medium">Report</span>
          </button>
        </div>
      </div>
    </div>
  )
}

export default UserPopover
