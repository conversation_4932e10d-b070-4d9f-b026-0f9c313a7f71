{"functions": {"api/crypto-webhook.ts": {"runtime": "@vercel/node"}}, "rewrites": [{"source": "/api/(.*)", "destination": "/api/$1"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}