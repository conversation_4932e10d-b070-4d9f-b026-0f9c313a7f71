// ============================================================================
// MANUAL PAYMENT SERVICE - DEPOSIT REQUESTS & ADMIN PROCESSING
// ============================================================================

import { supabase } from './supabase'
import { calculateDiamonds, phpToUsd, PAYMENT_METHODS } from './cryptoPayments'

// ============================================================================
// TYPES & INTERFACES
// ============================================================================

export interface DepositRequest {
  id?: string
  user_id: string
  fullname: string
  amount_php: number
  amount_usd: number
  total_diamonds: number
  payment_method: string
  reference_number: string
  phone_number: string
  status: 'pending' | 'approved' | 'rejected'
  admin_notes?: string
  processed_by?: string
  processed_at?: string
  created_at?: string
}

export interface PaymentInfo {
  fullname: string
  reference_number: string
  phone_number: string
}

// ============================================================================
// DEPOSIT REQUEST FUNCTIONS
// ============================================================================

export const manualPaymentService = {
  // Submit deposit request
  async submitDepositRequest(
    userId: string,
    amountPhp: number,
    paymentMethod: string,
    paymentInfo: PaymentInfo
  ) {
    try {
      // Calculate diamonds (no bonuses)
      const diamondCalc = calculateDiamonds(amountPhp)
      const amountUsd = phpToUsd(amountPhp)

      // Validate payment method and amount
      const method = PAYMENT_METHODS.find(m => m.id === paymentMethod)
      if (!method) {
        throw new Error('Invalid payment method')
      }

      if (amountPhp < method.min_amount_php || amountPhp > method.max_amount_php) {
        throw new Error(`Amount must be between ₱${method.min_amount_php.toLocaleString()} and ₱${method.max_amount_php.toLocaleString()}`)
      }

      // Validate required payment info
      if (!paymentInfo.fullname || !paymentInfo.reference_number || !paymentInfo.phone_number) {
        throw new Error('Full name, reference number and phone number are required')
      }

      // Create deposit request
      const depositData: Omit<DepositRequest, 'id'> = {
        user_id: userId,
        fullname: paymentInfo.fullname,
        amount_php: amountPhp,
        amount_usd: amountUsd,
        total_diamonds: diamondCalc.total_diamonds,
        payment_method: paymentMethod,
        reference_number: paymentInfo.reference_number,
        phone_number: paymentInfo.phone_number,
        status: 'pending'
      }

      const { data, error } = await supabase
        .from('deposit_requests')
        .insert([depositData])
        .select()
        .single()

      if (error) throw error

      // Create pending transaction record
      await supabase
        .from('transactions')
        .insert([{
          user_id: userId,
          type: 'deposit',
          amount: diamondCalc.total_diamonds,
          amount_usd: amountUsd,
          amount_php: amountPhp,
          description: `Deposit request - ${method.name}`,
          status: 'pending',
          deposit_request_id: data.id,
          reference: data.id
        }])

      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get user's deposit history
  async getUserDepositHistory(userId: string) {
    try {
      const { data, error } = await supabase
        .from('deposit_requests')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get payment method info
  getPaymentMethodInfo(methodId: string) {
    return PAYMENT_METHODS.find(m => m.id === methodId)
  },

  // Calculate diamonds for preview
  calculateDiamondsPreview(amountPhp: number) {
    return calculateDiamonds(amountPhp)
  },

  // Validate amount for payment method
  validateAmount(amountPhp: number, paymentMethod: string) {
    const method = PAYMENT_METHODS.find(m => m.id === paymentMethod)
    if (!method) return { valid: false, error: 'Invalid payment method' }

    if (amountPhp < method.min_amount_php) {
      return { 
        valid: false, 
        error: `Minimum amount is ₱${method.min_amount_php.toLocaleString()}` 
      }
    }

    if (amountPhp > method.max_amount_php) {
      return { 
        valid: false, 
        error: `Maximum amount is ₱${method.max_amount_php.toLocaleString()}` 
      }
    }

    return { valid: true, error: null }
  }
}

// ============================================================================
// ADMIN FUNCTIONS FOR PROCESSING DEPOSITS
// ============================================================================

export const adminDepositService = {
  // Get all pending deposit requests
  async getPendingDeposits() {
    try {
      const { data, error } = await supabase
        .from('deposit_requests')
        .select(`
          *,
          user:users(id, username, email, first_name, last_name, diamond_balance)
        `)
        .eq('status', 'pending')
        .order('created_at', { ascending: true })

      if (error) throw error
      return { data, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get deposit request with payment proof URL
  async getDepositWithProof(depositId: string) {
    try {
      const { data: deposit, error } = await supabase
        .from('deposit_requests')
        .select(`
          *,
          user:users(id, username, email, first_name, last_name, diamond_balance)
        `)
        .eq('id', depositId)
        .single()

      if (error) throw error

      // Get signed URL for payment proof if exists
      let paymentProofUrl = null
      if (deposit.payment_proof) {
        const { data: urlData } = await supabase.storage
          .from('payment-proofs')
          .createSignedUrl(deposit.payment_proof, 3600) // 1 hour expiry

        paymentProofUrl = urlData?.signedUrl
      }

      return { 
        data: { ...deposit, payment_proof_url: paymentProofUrl }, 
        error: null 
      }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Process deposit request (approve/reject)
  async processDeposit(
    depositId: string,
    action: 'approve' | 'reject',
    adminId: string,
    adminNotes?: string
  ) {
    try {
      const { data: deposit, error: getError } = await supabase
        .from('deposit_requests')
        .select('*')
        .eq('id', depositId)
        .eq('status', 'pending')
        .single()

      if (getError) throw getError
      if (!deposit) throw new Error('Deposit request not found or already processed')

      if (action === 'approve') {
        // Update deposit status
        const { error: updateError } = await supabase
          .from('deposit_requests')
          .update({
            status: 'approved',
            processed_by: adminId,
            processed_at: new Date().toISOString(),
            admin_notes: adminNotes
          })
          .eq('id', depositId)

        if (updateError) throw updateError

        // Add diamonds to user balance
        const { data: user, error: userError } = await supabase
          .from('users')
          .select('diamond_balance')
          .eq('id', deposit.user_id)
          .single()

        if (userError) throw userError

        await supabase
          .from('users')
          .update({
            diamond_balance: (user.diamond_balance || 0) + deposit.total_diamonds,
            updated_at: new Date().toISOString()
          })
          .eq('id', deposit.user_id)

        // Update transaction status
        await supabase
          .from('transactions')
          .update({ 
            status: 'completed',
            updated_at: new Date().toISOString()
          })
          .eq('deposit_request_id', depositId)

        // Create completion transaction record
        await supabase
          .from('transactions')
          .insert([{
            user_id: deposit.user_id,
            type: 'deposit_bonus',
            amount: deposit.bonus_diamonds,
            description: `Deposit bonus (${deposit.bonus_percentage}%)`,
            status: 'completed',
            deposit_request_id: depositId
          }])

      } else {
        // Reject deposit
        const { error: updateError } = await supabase
          .from('deposit_requests')
          .update({
            status: 'rejected',
            processed_by: adminId,
            processed_at: new Date().toISOString(),
            admin_notes: adminNotes
          })
          .eq('id', depositId)

        if (updateError) throw updateError

        // Update transaction status
        await supabase
          .from('transactions')
          .update({ 
            status: 'failed',
            updated_at: new Date().toISOString()
          })
          .eq('deposit_request_id', depositId)
      }

      return { data: { success: true }, error: null }
    } catch (error) {
      return { data: null, error }
    }
  },

  // Get deposit statistics
  async getDepositStats() {
    try {
      const { data, error } = await supabase
        .from('deposit_requests')
        .select('status, amount_php, total_diamonds, created_at')

      if (error) throw error

      const stats = {
        total_requests: data.length,
        pending_requests: data.filter(d => d.status === 'pending').length,
        approved_requests: data.filter(d => d.status === 'approved').length,
        rejected_requests: data.filter(d => d.status === 'rejected').length,
        total_amount_php: data.filter(d => d.status === 'approved').reduce((sum, d) => sum + d.amount_php, 0),
        total_diamonds_issued: data.filter(d => d.status === 'approved').reduce((sum, d) => sum + d.total_diamonds, 0)
      }

      return { data: stats, error: null }
    } catch (error) {
      return { data: null, error }
    }
  }
}
