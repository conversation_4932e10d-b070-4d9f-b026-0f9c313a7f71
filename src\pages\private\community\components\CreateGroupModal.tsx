import React, { useState } from 'react'
import {
  X,
  Lock,
  Globe,
  Loader2,
  Hash
} from 'lucide-react'
import { CreateGroupModalProps, Group } from '../types'

const CreateGroupModal: React.FC<CreateGroupModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isLoading = false
}) => {
  const [formData, setFormData] = useState({
    name: '',
    tag: '',
    description: '',
    tagline: '',
    isPrivate: false,
    maxMembers: 50,
    requirements: {
      minLevel: 1,
      minWinRate: 0,
      minDiamonds: 0
    },
    categories: [] as string[],
    banner: null as File | null,
    logo: null as File | null
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  // const [bannerPreview, setBannerPreview] = useState<string | null>(null) // TODO: Confirm usage
  // const [logoPreview, setLogoPreview] = useState<string | null>(null) // TODO: Confirm usage

  const gameCategories = [
    'Mobile Legends',
    'Valorant',
    'Call of Duty',
    'PUBG Mobile',
    'Free Fire',
    'League of Legends: Wild Rift',
    'Dota 2',
    'CS:GO/CS2',
    'Casual Gaming',
    'Competitive',
    'Tournament',
    'Beginner Friendly'
  ]

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handleRequirementChange = (field: string, value: number) => {
    setFormData(prev => ({
      ...prev,
      requirements: {
        ...prev.requirements,
        [field]: value
      }
    }))
  }

  const handleCategoryToggle = (category: string) => {
    setFormData(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }))
  }

  // const handleFileUpload = (type: 'banner' | 'logo', file: File | null) => { // TODO: Confirm usage
  //   if (!file) return
  //
  //   // Validate file size (max 5MB)
  //   if (file.size > 5 * 1024 * 1024) {
  //     setErrors(prev => ({
  //       ...prev,
  //       [type]: 'File size must be less than 5MB'
  //     }))
  //     return
  //   }
  //
  //   // Validate file type
  //   if (!file.type.startsWith('image/')) {
  //     setErrors(prev => ({
  //       ...prev,
  //       [type]: 'Please select an image file'
  //     }))
  //     return
  //   }
  //
  //   setFormData(prev => ({
  //     ...prev,
  //     [type]: file
  //   }))
  //
  //   // Create preview
  //   const reader = new FileReader()
  //   reader.onload = (e) => {
  //     if (type === 'banner') {
  //       setBannerPreview(e.target?.result as string)
  //     } else {
  //       setLogoPreview(e.target?.result as string)
  //     }
  //   }
  //   reader.readAsDataURL(file)
  //
  //   // Clear error
  //   setErrors(prev => ({
  //     ...prev,
  //     [type]: ''
  //   }))
  // }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Group name is required'
    } else if (formData.name.length < 3) {
      newErrors.name = 'Group name must be at least 3 characters'
    }

    if (!formData.tag.trim()) {
      newErrors.tag = 'Group tag is required'
    } else if (formData.tag.length < 2 || formData.tag.length > 4) {
      newErrors.tag = 'Group tag must be 2-4 characters'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters'
    }

    if (formData.maxMembers < 10 || formData.maxMembers > 100) {
      newErrors.maxMembers = 'Max members must be between 10 and 100'
    }

    if (formData.categories.length === 0) {
      newErrors.categories = 'Please select at least one category'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    const groupData: Partial<Group> = {
      name: formData.name.trim(),
      tag: formData.tag.trim().toUpperCase(),
      description: formData.description.trim(),
      tagline: formData.tagline.trim(),
      isPrivate: formData.isPrivate,
      maxMembers: formData.maxMembers,
      requirements: formData.requirements,
      categories: formData.categories,
      // Note: In real implementation, you'd upload files to storage first
      // banner: formData.banner ? 'uploaded-banner-url' : undefined,
      // logo: formData.logo ? 'uploaded-logo-url' : undefined,
    }

    onSubmit(groupData)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Create New Group</h2>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
            disabled={isLoading}
          >
            <X className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-4 space-y-4">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="Enter group name"
                className={`w-full px-3 py-2 border rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.name ? 'border-red-300' : 'border-gray-300'
                }`}
                maxLength={50}
                disabled={isLoading}
              />
              {errors.name && <p className="text-xs text-red-600 mt-1">{errors.name}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Group Tag *
              </label>
              <input
                type="text"
                value={formData.tag}
                onChange={(e) => handleInputChange('tag', e.target.value.toUpperCase())}
                placeholder="TAG"
                className={`w-full px-3 py-2 border rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.tag ? 'border-red-300' : 'border-gray-300'
                }`}
                maxLength={4}
                disabled={isLoading}
              />
              {errors.tag && <p className="text-xs text-red-600 mt-1">{errors.tag}</p>}
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description *
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe your group's purpose and goals..."
              rows={3}
              className={`w-full px-3 py-2 border rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500 ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              maxLength={500}
              disabled={isLoading}
            />
            {errors.description && <p className="text-xs text-red-600 mt-1">{errors.description}</p>}
          </div>

          {/* Tagline */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Tagline (Optional)
            </label>
            <input
              type="text"
              value={formData.tagline}
              onChange={(e) => handleInputChange('tagline', e.target.value)}
              placeholder="A short catchy phrase..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              maxLength={100}
              disabled={isLoading}
            />
          </div>

          {/* Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Max Members
              </label>
              <input
                type="number"
                value={formData.maxMembers}
                onChange={(e) => handleInputChange('maxMembers', parseInt(e.target.value))}
                min={10}
                max={100}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                disabled={isLoading}
              />
            </div>

            <div className="flex items-center gap-3 pt-6">
              <label className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={formData.isPrivate}
                  onChange={(e) => handleInputChange('isPrivate', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  disabled={isLoading}
                />
                <span className="text-sm text-gray-700">Private Group</span>
                {formData.isPrivate ? (
                  <Lock className="w-4 h-4 text-gray-600" />
                ) : (
                  <Globe className="w-4 h-4 text-gray-600" />
                )}
              </label>
            </div>
          </div>

          {/* Requirements */}
          <div>
            <h3 className="text-sm font-medium text-gray-700 mb-2">Join Requirements</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div>
                <label className="block text-xs text-gray-600 mb-1">Min Level</label>
                <input
                  type="number"
                  value={formData.requirements.minLevel}
                  onChange={(e) => handleRequirementChange('minLevel', parseInt(e.target.value))}
                  min={1}
                  max={50}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Min Win Rate (%)</label>
                <input
                  type="number"
                  value={formData.requirements.minWinRate}
                  onChange={(e) => handleRequirementChange('minWinRate', parseInt(e.target.value))}
                  min={0}
                  max={100}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Min Diamonds</label>
                <input
                  type="number"
                  value={formData.requirements.minDiamonds}
                  onChange={(e) => handleRequirementChange('minDiamonds', parseInt(e.target.value))}
                  min={0}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  disabled={isLoading}
                />
              </div>
            </div>
          </div>

          {/* Categories */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Categories * (Select all that apply)
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              {gameCategories.map((category) => (
                <label key={category} className="flex items-center gap-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.categories.includes(category)}
                    onChange={() => handleCategoryToggle(category)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    disabled={isLoading}
                  />
                  <span className="text-sm text-gray-700">{category}</span>
                </label>
              ))}
            </div>
            {errors.categories && <p className="text-xs text-red-600 mt-1">{errors.categories}</p>}
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md font-medium transition-colors"
              disabled={isLoading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md font-medium transition-colors flex items-center justify-center gap-2"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Hash className="w-4 h-4" />
                  Create Group
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CreateGroupModal
