# 🎮 Gambets - Competitive Gaming Platform

**The ultimate platform for competitive gaming tournaments and matches in the Philippines**

[![React](https://img.shields.io/badge/React-18.3.1-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.6.2-blue.svg)](https://www.typescriptlang.org/)
[![Vite](https://img.shields.io/badge/Vite-6.0.1-purple.svg)](https://vitejs.dev/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.4.15-teal.svg)](https://tailwindcss.com/)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-green.svg)](https://supabase.com/)

## 🌟 Overview

Gambets is a comprehensive competitive gaming platform designed specifically for Filipino gamers. It provides a secure, user-friendly environment for organizing tournaments, managing matches, and building gaming communities.

### 🎯 Key Features

- **🏆 Competitive Matches** - Organize and join tournaments across multiple games
- **💎 Diamond System** - Secure virtual currency for entry fees and prizes
- **🛡️ Referee System** - Professional match oversight and dispute resolution
- **👥 Community Groups** - Build and join gaming guilds with chat systems
- **📊 Leaderboards** - Track rankings and statistics across all games
- **💳 Local Payments** - GCash and Maya integration for Filipino users
- **🤝 Referral Program** - Earn rewards for bringing friends to the platform

### 🎮 Supported Games

- **Mobile Legends: Bang Bang** 🏰
- **Valorant** 🎯
- **Call of Duty Mobile** 🔫
- **League of Legends: Wild Rift** ⚔️
- **Dota 2** 🛡️
- **Counter-Strike: Global Offensive** 💥

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18+ 
- **npm** or **yarn**
- **Git**

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/gambets.git
cd gambets

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your configuration

# Start development server
npm run dev
```

### Environment Setup

Create a `.env.local` file with the following variables:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# App Configuration
VITE_APP_URL=http://localhost:5173
VITE_APP_NAME=Gambets
VITE_APP_VERSION=1.0.0

# Payment Configuration (Optional)
VITE_GCASH_MERCHANT_ID=your_gcash_merchant_id
VITE_MAYA_MERCHANT_ID=your_maya_merchant_id
```

## 📁 Project Structure

```
gambets/
├── 📁 database/               # Database scripts and SQL files
├── 📁 public/                 # Static assets
├── 📁 src/                    # Source code
│   ├── 📁 components/         # Reusable UI components
│   │   ├── 📁 common/         # Shared components
│   │   ├── 📁 forms/          # Form components
│   │   ├── 📁 layout/         # Layout components
│   │   └── 📁 ui/             # UI library components
│   ├── 📁 pages/              # Page components
│   │   ├── 📁 public/         # Public pages (no auth required)
│   │   ├── 📁 private/        # Private pages (auth required)
│   │   ├── 📁 admin/          # Admin pages (admin role required)
│   │   └── 📁 common/         # Common pages (404, etc.)
│   ├── 📁 contexts/           # React context providers
│   ├── 📁 services/           # Backend services and API calls
│   ├── 📁 hooks/              # Custom React hooks
│   ├── 📁 utils/              # Utility functions
│   ├── 📁 types/              # TypeScript type definitions
│   ├── 📁 constants/          # Application constants
│   ├── 📁 lib/                # External library configurations
│   └── 📁 assets/             # Images, icons, and other assets
├── 📄 .env.example            # Environment variables template
├── 📄 .gitignore              # Git ignore rules (optimized for Vite)
├── 📄 package.json            # Dependencies and scripts
├── 📄 tsconfig.json           # TypeScript configuration
├── 📄 tailwind.config.js      # TailwindCSS configuration
├── 📄 vite.config.ts          # Vite configuration
└── 📄 README.md               # This file
```

## 🛠️ Development

### Available Scripts

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript checks

# Testing (when implemented)
npm run test         # Run unit tests
npm run test:e2e     # Run end-to-end tests
npm run test:coverage # Generate test coverage report
```

### Code Style and Standards

- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting
- **Conventional Commits** for commit messages
- **Component-driven development** approach

### Git Workflow

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature description"

# Push and create pull request
git push origin feature/your-feature-name
```

## 🏗️ Architecture

### Frontend Stack

- **React 18** - UI library with hooks and concurrent features
- **TypeScript** - Type-safe JavaScript development
- **Vite** - Fast build tool and development server
- **TailwindCSS** - Utility-first CSS framework
- **React Router** - Client-side routing
- **Lucide Icons** - Beautiful, customizable icons

### Backend Integration

- **Supabase** - Backend-as-a-Service
  - Authentication and user management
  - PostgreSQL database
  - Real-time subscriptions
  - File storage
  - Edge functions

### State Management

- **React Context** - Global state management
- **Custom Hooks** - Reusable stateful logic
- **Local Storage** - Client-side persistence

## 🔐 Authentication & Security

### User Authentication

- **Email/Password** authentication via Supabase
- **JWT tokens** for session management
- **Role-based access control** (User, Referee, Admin)
- **Protected routes** for authenticated content

### Security Features

- **Input validation** on all forms
- **XSS protection** through React's built-in escaping
- **CSRF protection** via SameSite cookies
- **Rate limiting** on API endpoints
- **Secure payment processing** through trusted providers

## 💎 Diamond System

### Virtual Currency

- **Diamonds** serve as the platform's virtual currency
- **Secure transactions** through Supabase database
- **Audit trail** for all diamond movements
- **Anti-fraud measures** to prevent abuse

### Payment Integration

- **GCash** - Popular Filipino mobile wallet
- **Maya (PayMaya)** - Digital payment platform
- **Secure processing** through official APIs
- **Transaction verification** and confirmation

## 🎮 Gaming Features

### Match Management

- **Create matches** with custom rules and entry fees
- **Join matches** with automatic player matching
- **Real-time updates** during match progression
- **Result submission** and verification

### Referee System

- **Application process** for becoming a referee
- **Match oversight** and dispute resolution
- **Earnings tracking** for referee activities
- **Rating system** for referee performance

### Community Features

- **Guild system** for team formation
- **Global chat** for community interaction
- **Leaderboards** with rankings and statistics
- **Achievement system** for player progression

## 📱 Responsive Design

### Mobile-First Approach

- **Responsive layouts** that work on all devices
- **Touch-friendly interfaces** for mobile users
- **Progressive Web App** capabilities
- **Optimized performance** for mobile networks

### Supported Devices

- **📱 Mobile** - iOS and Android browsers
- **📟 Tablet** - iPad and Android tablets  
- **💻 Desktop** - Chrome, Firefox, Safari, Edge
- **🖥️ Large Screens** - 4K and ultrawide displays

## 🚀 Deployment

### Production Build

```bash
# Build for production
npm run build

# Preview production build locally
npm run preview
```

### Deployment Options

- **Vercel** - Recommended for easy deployment
- **Netlify** - Alternative static hosting
- **GitHub Pages** - Free hosting for open source
- **Custom Server** - Self-hosted options

### Environment Configuration

```bash
# Production environment variables
VITE_SUPABASE_URL=your_production_supabase_url
VITE_SUPABASE_ANON_KEY=your_production_supabase_key
VITE_APP_URL=https://yourdomain.com
```

## 🧪 Testing Strategy

### Testing Approach

- **Unit Tests** - Component and utility testing
- **Integration Tests** - Feature workflow testing
- **E2E Tests** - Full user journey testing
- **Performance Tests** - Load and speed testing

### Testing Tools (Planned)

- **Vitest** - Fast unit testing framework
- **React Testing Library** - Component testing utilities
- **Playwright** - End-to-end testing
- **Lighthouse CI** - Performance monitoring

## 📊 Analytics & Monitoring

### User Analytics

- **User behavior tracking** for UX improvements
- **Match statistics** for platform insights
- **Performance metrics** for optimization
- **Error tracking** for bug resolution

### Monitoring Tools

- **Supabase Analytics** - Built-in database monitoring
- **Vercel Analytics** - Deployment and performance metrics
- **Custom Dashboards** - Business intelligence reporting

## 🤝 Contributing

### How to Contribute

1. **Fork** the repository
2. **Create** a feature branch
3. **Make** your changes
4. **Test** your changes thoroughly
5. **Submit** a pull request

### Contribution Guidelines

- Follow the existing code style
- Write clear commit messages
- Add tests for new features
- Update documentation as needed
- Respect the code of conduct

### Development Setup

```bash
# Fork and clone your fork
git clone https://github.com/yourusername/gambets.git

# Add upstream remote
git remote add upstream https://github.com/originalowner/gambets.git

# Create feature branch
git checkout -b feature/amazing-feature

# Make changes and push
git push origin feature/amazing-feature
```

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **React Team** - For the amazing React framework
- **Vite Team** - For the lightning-fast build tool
- **Supabase Team** - For the excellent backend platform
- **TailwindCSS Team** - For the utility-first CSS framework
- **Filipino Gaming Community** - For inspiration and feedback

## 📞 Support

### Getting Help

- **📧 Email** - <EMAIL>
- **💬 Discord** - [Join our community](https://discord.gg/gambets)
- **📱 Facebook** - [@GambetsOfficial](https://facebook.com/gambets)
- **🐛 Issues** - [GitHub Issues](https://github.com/yourusername/gambets/issues)

### Documentation

- **📚 Wiki** - [Project Wiki](https://github.com/yourusername/gambets/wiki)
- **🎥 Tutorials** - [YouTube Channel](https://youtube.com/gambets)
- **📖 Blog** - [Development Blog](https://blog.gambets.com)

---

**Made with ❤️ for the Filipino Gaming Community**
