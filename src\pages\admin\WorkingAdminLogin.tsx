// 🔐 WORKING ADMIN LOGIN
// Proper email/password admin authentication

import React, { useState } from 'react'
import { useNavigate, Link } from 'react-router-dom'
import { Crown, Shield, Lock, Eye, EyeOff, AlertTriangle, CheckCircle, Loader2 } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { AdminService } from '../../services/admin'
import { supabase } from '../../services/supabaseClient'

const WorkingAdminLogin: React.FC = () => {
  const { } = useAuth() // TODO: Confirm signIn usage
  const navigate = useNavigate()
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isVerifying, setIsVerifying] = useState(false)
  const [error, setError] = useState('')
  const [step, setStep] = useState<'login' | 'verifying' | 'success'>('login')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setIsLoading(true)
    setStep('login')

    try {
      // Step 1: Authenticate with Supabase
      console.log('🔐 Starting admin login process...')
      
      const { data, error: authError } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password
      })

      if (authError) {
        console.error('❌ Authentication failed:', authError.message)
        setError(authError.message)
        setIsLoading(false)
        return
      }

      if (!data.user) {
        setError('Authentication failed. No user data received.')
        setIsLoading(false)
        return
      }

      console.log('✅ Authentication successful for:', data.user.email)
      setStep('verifying')
      setIsVerifying(true)

      // Step 2: Verify admin privileges
      console.log('🔍 Verifying admin privileges...')
      const isAdmin = await AdminService.isAdmin(data.user.id)
      
      if (!isAdmin) {
        console.log('❌ User is not an admin')
        setError('Access denied. You do not have administrative privileges.')
        
        // Log unauthorized attempt
        await AdminService.logAdminAction(
          data.user.id,
          'UNAUTHORIZED_ADMIN_LOGIN_ATTEMPT',
          'system',
          data.user.id,
          { attempted_email: formData.email, method: 'admin_portal' }
        )
        
        setIsLoading(false)
        setIsVerifying(false)
        setStep('login')
        return
      }

      // Step 3: Get admin details
      const adminUser = await AdminService.getAdminUser(data.user.id)
      
      if (!adminUser || adminUser.admin_level < 2) {
        console.log('❌ Insufficient admin level:', adminUser?.admin_level || 0)
        setError('Access denied. Insufficient administrative privileges.')
        setIsLoading(false)
        setIsVerifying(false)
        setStep('login')
        return
      }

      console.log('✅ Admin verification successful:', {
        level: adminUser.admin_level,
        role: adminUser.role
      })

      // Step 4: Update admin login timestamp
      await AdminService.updateAdminLogin(data.user.id)

      // Step 5: Log successful admin login
      await AdminService.logAdminAction(
        data.user.id,
        'ADMIN_LOGIN_SUCCESS',
        'system',
        data.user.id,
        { 
          login_method: 'admin_portal',
          admin_level: adminUser.admin_level,
          role: adminUser.role
        }
      )

      setStep('success')
      
      // Step 6: Redirect to admin dashboard
      setTimeout(() => {
        navigate('/admin-dashboard-2024')
      }, 1500)

    } catch (error: any) {
      console.error('💥 Admin login error:', error)
      setError('Login failed. Please check your credentials and try again.')
      setIsLoading(false)
      setIsVerifying(false)
      setStep('login')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center p-4">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }} />
      </div>

      <div className="relative w-full max-w-md">
        {/* Admin Login Card */}
        <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20 shadow-2xl">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-10 h-10 text-black font-bold" />
            </div>
            <h1 className="text-3xl font-bold text-white mb-2">Admin Portal</h1>
            <p className="text-blue-200">Exclusive access to administrative dashboard</p>
          </div>

          {/* Login Steps */}
          <div className="flex items-center justify-center space-x-4 mb-6">
            <div className={`flex items-center space-x-2 ${step === 'login' ? 'text-blue-300' : step === 'verifying' ? 'text-yellow-300' : 'text-green-300'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                step === 'login' ? 'bg-blue-500' : step === 'verifying' ? 'bg-yellow-500' : 'bg-green-500'
              }`}>
                {step === 'login' ? '1' : step === 'verifying' ? '2' : '✓'}
              </div>
              <span className="text-sm">Login</span>
            </div>
            <div className="w-8 h-0.5 bg-white/20"></div>
            <div className={`flex items-center space-x-2 ${step === 'verifying' ? 'text-yellow-300' : step === 'success' ? 'text-green-300' : 'text-gray-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                step === 'verifying' ? 'bg-yellow-500' : step === 'success' ? 'bg-green-500' : 'bg-gray-600'
              }`}>
                {step === 'success' ? '✓' : '2'}
              </div>
              <span className="text-sm">Verify</span>
            </div>
          </div>

          {/* Success State */}
          {step === 'success' && (
            <div className="text-center mb-6">
              <CheckCircle className="w-16 h-16 text-green-400 mx-auto mb-4" />
              <h2 className="text-xl font-bold text-white mb-2">Admin Access Granted!</h2>
              <p className="text-green-200">Redirecting to admin dashboard...</p>
            </div>
          )}

          {/* Verification State */}
          {step === 'verifying' && (
            <div className="text-center mb-6">
              <Loader2 className="w-16 h-16 text-yellow-400 animate-spin mx-auto mb-4" />
              <h2 className="text-xl font-bold text-white mb-2">Verifying Admin Access</h2>
              <p className="text-yellow-200">Checking your administrative privileges...</p>
            </div>
          )}

          {/* Login Form */}
          {step === 'login' && (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-blue-200 mb-2">
                  Admin Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all"
                  placeholder="<EMAIL>"
                />
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-blue-200 mb-2">
                  Admin Password
                </label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-blue-300 focus:ring-2 focus:ring-blue-400 focus:border-transparent transition-all pr-12"
                    placeholder="Enter your secure password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300 hover:text-white transition-colors"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-500/20 border border-red-400/30 rounded-lg p-4">
                  <div className="flex items-center space-x-2 text-red-300">
                    <AlertTriangle className="w-5 h-5" />
                    <span className="font-medium">Access Denied</span>
                  </div>
                  <p className="text-red-200 text-sm mt-1">{error}</p>
                </div>
              )}

              {/* Login Button */}
              <button
                type="submit"
                disabled={isLoading || isVerifying}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-transparent transition-all disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    <span>Authenticating...</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center space-x-2">
                    <Lock className="w-5 h-5" />
                    <span>Secure Admin Login</span>
                  </div>
                )}
              </button>
            </form>
          )}

          {/* Footer */}
          <div className="mt-8 pt-6 border-t border-white/10">
            <div className="text-center space-y-3">
              <Link
                to="/"
                className="text-blue-300 hover:text-white transition-colors text-sm block"
              >
                ← Back to Main Site
              </Link>
              <Link
                to="/admin-access"
                className="text-purple-300 hover:text-white transition-colors text-sm block"
              >
                Quick Admin Access
              </Link>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-6 bg-orange-500/10 border border-orange-500/20 rounded-lg p-4 mb-6">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="w-5 h-5 text-orange-400" />
            <h3 className="text-orange-300 font-semibold">Authorized Access Only</h3>
          </div>
          <p className="text-orange-200 text-sm">
            This is the exclusive entry point to the administrative dashboard.
            All access attempts are logged and monitored.
          </p>
        </div>

        {/* Security Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <Shield className="w-6 h-6 text-blue-400 mx-auto mb-2" />
            <p className="text-blue-200 text-sm font-medium">Multi-Level Security</p>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <Lock className="w-6 h-6 text-green-400 mx-auto mb-2" />
            <p className="text-blue-200 text-sm font-medium">Encrypted Sessions</p>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
            <CheckCircle className="w-6 h-6 text-purple-400 mx-auto mb-2" />
            <p className="text-blue-200 text-sm font-medium">Audit Logging</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default WorkingAdminLogin
