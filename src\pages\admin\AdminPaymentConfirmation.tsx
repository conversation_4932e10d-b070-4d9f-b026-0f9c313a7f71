import { useState, useEffect } from 'react'
import {
  CreditCard, CheckCircle, XCircle, Clock, Eye,
  DollarSign, User, Calendar, FileText, Search,
  RefreshCw, TrendingUp, Smartphone, Building, Shield,
  Check, X
} from 'lucide-react'
import { supabase } from '../../services/supabaseClient'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'
import PaymentDetailModal from '../../components/admin/PaymentDetailModal'

interface DepositRequest {
  id: string
  user_id: string
  fullname: string
  amount_php: number
  amount_usd: number
  total_diamonds: number
  payment_method: 'gcash' | 'maya'
  reference_number: string
  phone_number: string
  status: 'pending' | 'approved' | 'rejected'
  admin_notes?: string
  created_at: string
  processed_at?: string
  processed_by?: string
  user: {
    username: string
    email: string
    first_name: string
    last_name: string
    diamond_balance: number
  }
  processor?: {
    username: string
    email: string
  }
}

interface PaymentStats {
  totalRequests: number
  pendingRequests: number
  approvedToday: number
  rejectedToday: number
  totalAmount: number
  averageAmount: number
}

export default function AdminPaymentConfirmation() {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [depositRequests, setDepositRequests] = useState<DepositRequest[]>([])
  const [stats, setStats] = useState<PaymentStats>({
    totalRequests: 0,
    pendingRequests: 0,
    approvedToday: 0,
    rejectedToday: 0,
    totalAmount: 0,
    averageAmount: 0
  })
  const [loading, setLoading] = useState(true)
  const [selectedRequest, setSelectedRequest] = useState<DepositRequest | null>(null)
  const [filterStatus, setFilterStatus] = useState<string>('pending')
  const [filterType, setFilterType] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [processingRequest, setProcessingRequest] = useState<string | null>(null)

  useEffect(() => {
    loadDepositRequests()
    loadStats()
  }, [])

  const loadDepositRequests = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('deposit_requests')
        .select(`
          *,
          user:users!user_id(username, email, first_name, last_name, diamond_balance),
          processor:users!processed_by(username, email)
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setDepositRequests(data || [])
    } catch (error) {
      console.error('Error loading deposit requests:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load deposit requests'
      })
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const today = new Date().toISOString().split('T')[0]

      const { data: depositData } = await supabase
        .from('deposit_requests')
        .select('status, amount_php, created_at, processed_at')

      if (depositData) {
        const stats = {
          totalRequests: depositData.length,
          pendingRequests: depositData.filter(p => p.status === 'pending').length,
          approvedToday: depositData.filter(p =>
            p.status === 'approved' &&
            p.processed_at?.startsWith(today)
          ).length,
          rejectedToday: depositData.filter(p =>
            p.status === 'rejected' &&
            p.processed_at?.startsWith(today)
          ).length,
          totalAmount: depositData.reduce((sum, p) => sum + (p.amount_php || 0), 0),
          averageAmount: depositData.length > 0
            ? depositData.reduce((sum, p) => sum + (p.amount_php || 0), 0) / depositData.length
            : 0
        }
        setStats(stats)
      }
    } catch (error) {
      console.error('Error loading stats:', error)
    }
  }

  const handleDepositAction = async (requestId: string, action: 'approve' | 'reject', notes?: string) => {
    try {
      setProcessingRequest(requestId)

      const updateData = {
        status: action === 'approve' ? 'approved' : 'rejected',
        processed_at: new Date().toISOString(),
        processed_by: user!.id,
        admin_notes: notes || ''
      }

      const { error } = await supabase
        .from('deposit_requests')
        .update(updateData)
        .eq('id', requestId)

      if (error) throw error

      // If approved, update user's diamond balance and create transaction
      if (action === 'approve') {
        const request = depositRequests.find(r => r.id === requestId)
        if (request) {
          // First get current balance
          const { data: userData, error: fetchError } = await supabase
            .from('users')
            .select('diamond_balance')
            .eq('id', request.user_id)
            .single()

          if (fetchError) throw fetchError

          // Update with new balance (1 PHP = 1 Diamond)
          const newBalance = (userData.diamond_balance || 0) + request.total_diamonds
          const { error: balanceError } = await supabase
            .from('users')
            .update({ diamond_balance: newBalance })
            .eq('id', request.user_id)

          if (balanceError) throw balanceError

          // Create transaction record
          const { error: transactionError } = await supabase
            .from('transactions')
            .insert([{
              user_id: request.user_id,
              type: 'deposit',
              amount: request.total_diamonds,
              description: `Manual deposit via ${request.payment_method.toUpperCase()} - Ref: ${request.reference_number}`,
              status: 'completed'
            }])

          if (transactionError) {
            console.error('Transaction creation error:', transactionError)
            // Don't throw error here as the main operation was successful
          }
        }
      }

      addNotification({
        type: 'success',
        title: 'Deposit Request Updated',
        message: `Deposit request ${action}d successfully`
      })

      await loadDepositRequests()
      await loadStats()
      setSelectedRequest(null)
    } catch (error) {
      console.error('Error updating payment:', error)
      addNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to update payment'
      })
    } finally {
      setProcessingRequest(null)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      approved: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      rejected: { color: 'bg-red-100 text-red-800', icon: XCircle },
      processing: { color: 'bg-blue-100 text-blue-800', icon: RefreshCw },
      completed: { color: 'bg-gray-100 text-gray-800', icon: CheckCircle }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.color}`}>
        <Icon className="w-3 h-3 mr-1" />
        {status.toUpperCase()}
      </span>
    )
  }

  const getPaymentMethodIcon = (method: string) => {
    const methodConfig = {
      gcash: { icon: Smartphone, color: 'text-blue-600' },
      maya: { icon: Smartphone, color: 'text-green-600' },
      bank_transfer: { icon: Building, color: 'text-purple-600' },
      crypto: { icon: Shield, color: 'text-orange-600' }
    }

    const config = methodConfig[method as keyof typeof methodConfig] || methodConfig.gcash
    const Icon = config.icon

    return <Icon className={`w-4 h-4 ${config.color}`} />
  }

  const filteredRequests = depositRequests.filter(request => {
    const matchesStatus = filterStatus === 'all' || request.status === filterStatus
    const matchesSearch = searchTerm === '' ||
      request.user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.reference_number?.toLowerCase().includes(searchTerm.toLowerCase())

    return matchesStatus && matchesSearch
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-600 to-blue-600 rounded-xl shadow-lg p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">Payment Confirmation</h1>
            <p className="text-green-100 mt-1">Review and process payment requests</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={loadDepositRequests}
              disabled={loading}
              className="bg-white/20 hover:bg-white/30 rounded-lg px-4 py-2 transition-colors flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Requests</p>
              <p className="text-2xl font-bold text-gray-900">{stats.totalRequests}</p>
            </div>
            <CreditCard className="w-8 h-8 text-blue-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pendingRequests}</p>
            </div>
            <Clock className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Approved Today</p>
              <p className="text-2xl font-bold text-green-600">{stats.approvedToday}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Rejected Today</p>
              <p className="text-2xl font-bold text-red-600">{stats.rejectedToday}</p>
            </div>
            <XCircle className="w-8 h-8 text-red-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Amount</p>
              <p className="text-2xl font-bold text-purple-600">₱{stats.totalAmount.toLocaleString()}</p>
            </div>
            <DollarSign className="w-8 h-8 text-purple-500" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Amount</p>
              <p className="text-2xl font-bold text-indigo-600">₱{Math.round(stats.averageAmount)}</p>
            </div>
            <TrendingUp className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-sm border p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by username, email, or reference..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Statuses</option>
            <option value="pending">Pending</option>
            <option value="approved">Approved</option>
            <option value="rejected">Rejected</option>
            <option value="processing">Processing</option>
            <option value="completed">Completed</option>
          </select>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="all">All Types</option>
            <option value="deposit">Deposits</option>
            <option value="withdrawal">Withdrawals</option>
          </select>
        </div>
      </div>

      {/* Payments List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            Deposit Requests ({filteredRequests.length})
          </h2>
        </div>

        <div className="divide-y divide-gray-200">
          {loading ? (
            <div className="p-8 text-center">
              <RefreshCw className="w-8 h-8 text-gray-400 animate-spin mx-auto mb-4" />
              <p className="text-gray-600">Loading deposit requests...</p>
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="p-8 text-center">
              <CreditCard className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No deposit requests found</h3>
              <p className="text-gray-600">No requests match your current filters.</p>
            </div>
          ) : (
            filteredRequests.map((request) => (
              <div key={request.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        {getPaymentMethodIcon(request.payment_method)}
                        <h3 className="text-lg font-semibold text-gray-900 capitalize">
                          Deposit - {request.payment_method.replace('_', ' ')}
                        </h3>
                      </div>
                      {getStatusBadge(request.status)}
                      {request.status === 'pending' && (
                        <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">
                          NEEDS REVIEW
                        </span>
                      )}
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <User className="w-4 h-4" />
                        <span>{request.user.username}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <DollarSign className="w-4 h-4" />
                        <span className="font-semibold">₱{request.amount_php.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-blue-600 font-medium">{request.total_diamonds} 💎</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span>{new Date(request.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <FileText className="w-4 h-4" />
                        <span>Ref: {request.reference_number}</span>
                      </div>
                    </div>

                    <div className="mt-2 grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Full Name:</span>
                        <span>{request.fullname || 'Not provided'}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="font-medium">Phone:</span>
                        <span>{request.phone_number}</span>
                      </div>
                    </div>

                    {request.admin_notes && (
                      <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center space-x-2 text-blue-800">
                          <FileText className="w-4 h-4" />
                          <span className="font-medium">Admin Notes:</span>
                        </div>
                        <p className="text-blue-700 mt-1">{request.admin_notes}</p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-2 ml-4">
                    {request.status === 'pending' && (
                      <>
                        <button
                          onClick={() => handleDepositAction(request.id, 'approve')}
                          disabled={processingRequest === request.id}
                          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                        >
                          <Check className="w-4 h-4" />
                          <span>Approve</span>
                        </button>
                        <button
                          onClick={() => handleDepositAction(request.id, 'reject')}
                          disabled={processingRequest === request.id}
                          className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                        >
                          <X className="w-4 h-4" />
                          <span>Reject</span>
                        </button>
                      </>
                    )}
                    <button
                      onClick={() => setSelectedRequest(request)}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                    >
                      <Eye className="w-4 h-4" />
                      <span>Details</span>
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Deposit Request Detail Modal */}
      {selectedRequest && (
        <PaymentDetailModal
          payment={selectedRequest}
          onClose={() => setSelectedRequest(null)}
          onAction={handleDepositAction}
          getStatusBadge={getStatusBadge}
          processingPayment={processingRequest}
        />
      )}
    </div>
  )
}
