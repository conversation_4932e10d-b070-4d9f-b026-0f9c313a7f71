import React, { useState } from 'react'
import { X } from 'lucide-react'
import { useAuth } from '../../contexts/AuthContext'
import { useNotifications } from '../../contexts/NotificationContext'
import { marketplaceService, MarketplaceListing, MarketplaceCategory } from '../../services/marketplace'
import { MARKETPLACE_GAMES, ITEM_TYPES } from '../../constants/marketplace'
// import { RARITY_LEVELS, SERVICE_TYPES, ITEM_CONDITIONS } from '../../constants/marketplace' // TODO: Confirm usage

interface ListingFormModalProps {
  isOpen: boolean
  onClose: () => void
  categories: MarketplaceCategory[]
  onListingCreated: (listing: MarketplaceListing) => void
}

const ListingFormModal: React.FC<ListingFormModalProps> = ({
  isOpen,
  onClose,
  categories: _categories, // TODO: Confirm categories usage
  onListingCreated
}) => {
  const { user } = useAuth()
  const { addNotification } = useNotifications()
  const [isSubmitting, setIsSubmitting] = useState(false)

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    game: 'mobile-legends',
    item_type: 'account',

    // Account fields
    account_level: '',
    rank: '',
    heroes_count: '',
    skins_count: '',
    game_id: '',           // In-game username/ID
    current_password: '',  // Account password
    email: '',            // Associated email

    // Item fields
    item_name: '',
    item_rarity: '',
    item_condition: '',
    quantity: '',

    // Service fields
    service_type: '',
    completion_time: '',
    service_details: '',

    // Pricing fields
    price_diamonds: '',
    original_price_diamonds: '',
    is_negotiable: false,

    // General fields
    category: 'account',
    tags: '',
    expires_at: ''
  })

  const selectedGame = MARKETPLACE_GAMES.find(g => g.id === formData.game)
  // const selectedItemType = ITEM_TYPES.find(t => t.id === formData.item_type) // TODO: Confirm usage

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Basic validation
    if (!formData.title.trim()) {
      addNotification({
        type: 'error',
        title: 'Validation Error',
        message: 'Please enter a title for your listing.'
      })
      return
    }

    if (!formData.price_diamonds || parseInt(formData.price_diamonds) <= 0) {
      addNotification({
        type: 'error',
        title: 'Validation Error',
        message: 'Please enter a valid price in diamonds.'
      })
      return
    }

    setIsSubmitting(true)
    try {
      const listingData = {
        seller_id: user.id,
        title: formData.title,
        description: formData.description,
        game: formData.game,
        item_type: formData.item_type as "account" | "skin" | "item" | "currency" | "service" | "other",

        // Account fields
        account_level: formData.account_level ? parseInt(formData.account_level) : undefined,
        rank: formData.rank || undefined,
        heroes_count: formData.heroes_count ? parseInt(formData.heroes_count) : undefined,
        skins_count: formData.skins_count ? parseInt(formData.skins_count) : undefined,
        game_id: formData.game_id || undefined,
        current_password: formData.current_password || undefined,
        email: formData.email || undefined,

        // Item fields
        item_name: formData.item_name || undefined,
        item_rarity: formData.item_rarity || undefined,
        item_condition: formData.item_condition || undefined,
        quantity: formData.quantity ? parseInt(formData.quantity) : undefined,

        // Service fields
        service_type: formData.service_type || undefined,
        completion_time: formData.completion_time || undefined,
        service_details: formData.service_details || undefined,

        // Pricing
        price_diamonds: parseInt(formData.price_diamonds),
        original_price_diamonds: formData.original_price_diamonds ? parseInt(formData.original_price_diamonds) : undefined,
        is_negotiable: formData.is_negotiable,

        // General
        category: formData.category,
        status: 'active' as const,
        is_verified: false,
        is_featured: false,
        inquiries_count: 0,
        tags: formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0) : [],
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
      }

      const { data, error } = await marketplaceService.createListing({
        ...listingData,
        item_rarity: listingData.item_rarity as "common" | "rare" | "epic" | "legendary" | "mythic" | undefined,
        item_condition: listingData.item_condition as "new" | "excellent" | "good" | "fair" | "poor" | undefined,
        service_type: listingData.service_type as "other" | "rank_boost" | "coaching" | "account_leveling" | "skin_unlock" | undefined
      })

      if (error) {
        addNotification({
          type: 'error',
          title: 'Creation Failed',
          message: (error as any)?.message || 'Failed to create listing. Please try again.'
        })
        return
      }

      if (data) {
        onListingCreated(data)
        addNotification({
          type: 'success',
          title: 'Listing Created!',
          message: 'Your listing has been posted successfully and is now live.'
        })
        
        // Reset form
        setFormData({
          title: '',
          description: '',
          game: 'mobile-legends',
          item_type: 'account',

          // Account fields
          account_level: '',
          rank: '',
          heroes_count: '',
          skins_count: '',
          game_id: '',
          current_password: '',
          email: '',

          // Item fields
          item_name: '',
          item_rarity: '',
          item_condition: '',
          quantity: '',

          // Service fields
          service_type: '',
          completion_time: '',
          service_details: '',

          // Pricing fields
          price_diamonds: '',
          original_price_diamonds: '',
          is_negotiable: false,

          // General fields
          category: 'account',
          tags: '',
          expires_at: ''
        })

        onClose()
      }
    } catch (error: any) {
      addNotification({
        type: 'error',
        title: 'Creation Failed',
        message: error.message || 'Failed to create listing. Please try again.'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Create New Listing</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                required
                placeholder="e.g., Mythic ML Account with 150+ Skins"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                placeholder="Describe your item in detail..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          {/* Game and Type Selection */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Game & Type</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Game *</label>
                <select
                  name="game"
                  value={formData.game}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {MARKETPLACE_GAMES.map((game) => (
                    <option key={game.id} value={game.id}>
                      {game.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Item Type *</label>
                <select
                  name="item_type"
                  value={formData.item_type}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {ITEM_TYPES.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Conditional Fields Based on Item Type */}
          {formData.item_type === 'account' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Account Details</h3>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Account Level</label>
                  <input
                    type="number"
                    name="account_level"
                    value={formData.account_level}
                    onChange={handleInputChange}
                    placeholder="e.g., 45"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rank</label>
                  <select
                    name="rank"
                    value={formData.rank}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Rank</option>
                    {selectedGame?.ranks.map((rank) => (
                      <option key={rank} value={rank}>
                        {rank}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Heroes Count</label>
                  <input
                    type="number"
                    name="heroes_count"
                    value={formData.heroes_count}
                    onChange={handleInputChange}
                    placeholder="e.g., 85"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Skins Count</label>
                  <input
                    type="number"
                    name="skins_count"
                    value={formData.skins_count}
                    onChange={handleInputChange}
                    placeholder="e.g., 150"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">In-Game ID/Username *</label>
                <input
                  type="text"
                  name="game_id"
                  value={formData.game_id}
                  onChange={handleInputChange}
                  required
                  placeholder="e.g., PlayerName123"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Account Password *</label>
                  <input
                    type="password"
                    name="current_password"
                    value={formData.current_password}
                    onChange={handleInputChange}
                    required
                    placeholder="Account password"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Associated Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Item-specific fields */}
          {(formData.item_type === 'skin' || formData.item_type === 'item' || formData.item_type === 'currency') && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Item Details</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Item Name *</label>
                <input
                  type="text"
                  name="item_name"
                  value={formData.item_name}
                  onChange={handleInputChange}
                  required
                  placeholder="e.g., Legendary Skin, Epic Weapon"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Rarity</label>
                  <select
                    name="item_rarity"
                    value={formData.item_rarity}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Rarity</option>
                    <option value="common">Common</option>
                    <option value="rare">Rare</option>
                    <option value="epic">Epic</option>
                    <option value="legendary">Legendary</option>
                    <option value="mythic">Mythic</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Condition</label>
                  <select
                    name="item_condition"
                    value={formData.item_condition}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select Condition</option>
                    <option value="new">New</option>
                    <option value="excellent">Excellent</option>
                    <option value="good">Good</option>
                    <option value="fair">Fair</option>
                    <option value="poor">Poor</option>
                  </select>
                </div>
              </div>

              {(formData.item_type === 'item' || formData.item_type === 'currency') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Quantity</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleInputChange}
                    min="1"
                    placeholder="e.g., 1000"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}
            </div>
          )}

          {/* Service-specific fields */}
          {formData.item_type === 'service' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Service Details</h3>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Service Type *</label>
                <select
                  name="service_type"
                  value={formData.service_type}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select Service Type</option>
                  <option value="rank_boost">Rank Boosting</option>
                  <option value="coaching">Coaching/Training</option>
                  <option value="account_leveling">Account Leveling</option>
                  <option value="skin_unlock">Skin Unlocking</option>
                  <option value="other">Other Service</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Completion Time</label>
                <input
                  type="text"
                  name="completion_time"
                  value={formData.completion_time}
                  onChange={handleInputChange}
                  placeholder="e.g., 1-3 days, 1 week"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Service Details</label>
                <textarea
                  name="service_details"
                  value={formData.service_details}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Detailed description of what's included in the service..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
          )}

          {/* Pricing */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Pricing & Options</h3>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Price (Diamonds) *</label>
                <input
                  type="number"
                  name="price_diamonds"
                  value={formData.price_diamonds}
                  onChange={handleInputChange}
                  required
                  min="1"
                  placeholder="e.g., 2500"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Original Price (Optional)</label>
                <input
                  type="number"
                  name="original_price_diamonds"
                  value={formData.original_price_diamonds}
                  onChange={handleInputChange}
                  min="1"
                  placeholder="e.g., 3000"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="is_negotiable"
                name="is_negotiable"
                checked={formData.is_negotiable}
                onChange={(e) => setFormData(prev => ({ ...prev, is_negotiable: e.target.checked }))}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
              />
              <label htmlFor="is_negotiable" className="text-sm font-medium text-gray-700">
                Price is negotiable
              </label>
            </div>
          </div>

          {/* Additional Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Additional Information</h3>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Tags (comma-separated)</label>
              <input
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleInputChange}
                placeholder="e.g., mythic, rare skins, high rank"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <p className="text-xs text-gray-500 mt-1">Separate tags with commas to help buyers find your listing</p>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? 'Creating...' : 'Create Listing'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ListingFormModal
