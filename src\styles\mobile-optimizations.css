/* 🚀 Mobile Performance Optimizations */

/* Disable hover effects on touch devices for better performance */
@media (hover: none) and (pointer: coarse) {
  .hover\:shadow-md:hover,
  .hover\:shadow-lg:hover,
  .hover\:shadow-xl:hover {
    box-shadow: none !important;
  }
  
  .hover\:scale-105:hover,
  .hover\:scale-110:hover {
    transform: none !important;
  }
  
  .group:hover .group-hover\:scale-110,
  .group:hover .group-hover\:scale-105 {
    transform: none !important;
  }
  
  .hover\:bg-gray-50:hover,
  .hover\:bg-gray-100:hover,
  .hover\:bg-gray-200:hover {
    background-color: inherit !important;
  }
}

/* Touch-friendly button sizes */
@media (max-width: 768px) {
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
  
  .touch-target-small {
    min-height: 36px;
    min-width: 36px;
  }
  
  .touch-target-large {
    min-height: 52px;
    min-width: 52px;
  }
}

/* Optimize animations for mobile */
@media (max-width: 768px) {
  .animate-spin,
  .animate-pulse,
  .animate-bounce {
    animation-duration: 1s;
  }
  
  /* Reduce complex animations on mobile */
  .transition-all {
    transition-property: opacity, transform;
    transition-duration: 0.2s;
  }
  
  /* Disable expensive transforms on mobile */
  .group:hover .group-hover\:rotate-3,
  .group:hover .group-hover\:-translate-y-2 {
    transform: none !important;
  }
}

/* Mobile-first responsive images */
.responsive-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  loading: lazy;
}

@media (max-width: 768px) {
  .responsive-image {
    image-rendering: optimizeSpeed;
    image-rendering: -webkit-optimize-contrast;
  }
}

/* Mobile scroll optimizations */
@media (max-width: 768px) {
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }
  
  .mobile-scroll::-webkit-scrollbar {
    display: none;
  }
  
  .mobile-scroll {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Mobile card optimizations */
@media (max-width: 768px) {
  .mobile-card {
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: none;
  }
  
  .mobile-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

/* Mobile typography optimizations */
@media (max-width: 768px) {
  .mobile-text-sm {
    font-size: 14px;
    line-height: 1.4;
  }
  
  .mobile-text-base {
    font-size: 16px;
    line-height: 1.5;
  }
  
  .mobile-text-lg {
    font-size: 18px;
    line-height: 1.4;
  }
}

/* Mobile spacing optimizations */
@media (max-width: 768px) {
  .mobile-p-2 { padding: 8px; }
  .mobile-p-3 { padding: 12px; }
  .mobile-p-4 { padding: 16px; }
  
  .mobile-m-2 { margin: 8px; }
  .mobile-m-3 { margin: 12px; }
  .mobile-m-4 { margin: 16px; }
  
  .mobile-gap-2 { gap: 8px; }
  .mobile-gap-3 { gap: 12px; }
  .mobile-gap-4 { gap: 16px; }
}

/* Mobile grid optimizations */
@media (max-width: 768px) {
  .mobile-grid-1 {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .mobile-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
}

/* Mobile modal optimizations */
@media (max-width: 768px) {
  .mobile-modal {
    position: fixed;
    inset: 0;
    background: white;
    border-radius: 16px 16px 0 0;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }
  
  .mobile-modal.open {
    transform: translateY(0);
  }
  
  .mobile-modal-backdrop {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
  }
}

/* Mobile form optimizations */
@media (max-width: 768px) {
  .mobile-input {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #d1d5db;
    min-height: 44px;
  }
  
  .mobile-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
  
  .mobile-select {
    font-size: 16px;
    padding: 12px 16px;
    border-radius: 8px;
    border: 1px solid #d1d5db;
    min-height: 44px;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
  }
}

/* Mobile navigation optimizations */
@media (max-width: 768px) {
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e5e7eb;
    padding: 8px 0;
    z-index: 50;
  }
  
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 8px 4px;
    min-height: 60px;
    text-decoration: none;
    color: #6b7280;
    transition: color 0.2s ease;
  }
  
  .mobile-nav-item.active {
    color: #3b82f6;
  }
  
  .mobile-nav-item:active {
    background-color: #f3f4f6;
    border-radius: 8px;
  }
}

/* Performance optimizations */
@media (max-width: 768px) {
  /* Use GPU acceleration for smooth animations */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }
  
  /* Optimize repaints */
  .mobile-optimized {
    contain: layout style paint;
  }
  
  /* Reduce motion for users who prefer it */
  @media (prefers-reduced-motion: reduce) {
    .transition-all,
    .transition-transform,
    .transition-opacity {
      transition: none !important;
    }
    
    .animate-spin,
    .animate-pulse,
    .animate-bounce {
      animation: none !important;
    }
  }
}

/* Dark mode mobile optimizations */
@media (max-width: 768px) and (prefers-color-scheme: dark) {
  .mobile-card {
    background-color: #1f2937;
    border-color: #374151;
  }
  
  .mobile-input,
  .mobile-select {
    background-color: #1f2937;
    border-color: #374151;
    color: #f9fafb;
  }
  
  .mobile-nav {
    background-color: #1f2937;
    border-top-color: #374151;
  }
}
